package vn.zenity.betacineplex.view.film

import android.annotation.SuppressLint
import android.location.Location
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import com.afollestad.sectionedrecyclerview.SectionedRecyclerViewAdapter
import com.afollestad.sectionedrecyclerview.SectionedViewHolder
import com.thoughtbot.expandablerecyclerview.ExpandableRecyclerViewAdapter
import com.thoughtbot.expandablerecyclerview.models.ExpandableGroup
import com.thoughtbot.expandablerecyclerview.viewholders.ChildViewHolder
import com.thoughtbot.expandablerecyclerview.viewholders.GroupViewHolder
import kotlinx.android.synthetic.main.fragment_bookbyfilm.*
import kotlinx.android.synthetic.main.item_area_in_list.view.*
import kotlinx.android.synthetic.main.item_time_booking.view.*
import kotlinx.android.synthetic.main.item_time_booking_of_cinema.view.*
import load
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.base.BaseActivity
import vn.zenity.betacineplex.base.BaseFragment
import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.global.Constant
import vn.zenity.betacineplex.global.Global
import vn.zenity.betacineplex.global.Tracking
import vn.zenity.betacineplex.helper.extension.*
import vn.zenity.betacineplex.helper.view.ItemTimeBooking
import vn.zenity.betacineplex.model.*
import vn.zenity.betacineplex.view.auth.LoginFragment
import java.lang.ref.WeakReference
import java.util.*

/**
 * Created by Zenity.
 */

class BookByFilmFragment : BaseFragment(), BookByFilmContractor.View,
    ItemTimeBooking.TimeSelectListener {

    companion object {
        fun getInstance(film: FilmModel?): BookByFilmFragment {
            val frag = BookByFilmFragment()
            frag.film = film
            return frag
        }

        fun getInstance(filmId: String?, isShowOpenDetail: Boolean = true): BookByFilmFragment {
            val frag = BookByFilmFragment()
            frag.filmId = filmId
            frag.isShowOpenDetail = isShowOpenDetail
            return frag
        }
    }

    private var isShowOpenDetail = true

    override fun showTimeBooking(listTime: List<GroupFilmBooking>) {
        activity?.runOnUiThread {
            listBooking = listTime
            btnLoadMore.visible(presenter.showButtonLoadMore)
            adapter = if (selectedArea != null) {
                val newFilterList = listBooking.filter { not ->
                    (selectedArea?.ListCinema?.count { it.CinemaId == not.CinemaId } ?: 0) > 0
                }
                BookAdapter(newFilterList)
            } else
                BookAdapter(listBooking)
            recyclerView.adapter = adapter
            adapter?.notifyDataSetChanged()
        }
    }

    override fun showShowDates(dates: List<Calendar>) {
        dateAdapter.selectedIndex = 0
        dateAdapter.showDates = dates.sorted()
        activity?.runOnUiThread {
            dateAdapter.notifyDataSetChanged()
        }
        if (dates.isNotEmpty()) {
            //-- Init showButtonLoadMore
            presenter.getTimeBooking(film ?: return, dates[0], currentLocation, false)
        }
    }

    override fun showFilmModel(film: FilmModel) {
        this.film = film
        activity?.runOnUiThread {
            showDataFilm()
        }
    }

    override fun showLoadMore() {
    }

    override fun hideLoadMore() {
    }

    private val presenter = BookByFilmPresenter()
    private lateinit var dateAdapter: DateAdapter
    private var film: FilmModel? = null
    private var filmId: String? = null
    private var currentLocation: Location? = null
    private var selectedArea: CinemaProvinceModel? = null
    private var listBooking: List<GroupFilmBooking> = listOf()

    override fun getPresenter(): IBasePresenter<IBaseView>? {
        return presenter as? IBasePresenter<IBaseView>
    }

    override fun isShowToolbar(): Boolean {
        return false
    }

    override fun getLayoutRes(): Int {
        return R.layout.fragment_bookbyfilm
    }

    private var adapter: BookAdapter? = null
    override fun onRefresh() {
        super.onRefresh()
//        refreshView?.finishRefreshing()
        if (dateAdapter.showDates.isNotEmpty()) {
            //-- Refresh getTimeBooking
            presenter.getTimeBooking(
                film
                    ?: return,
                dateAdapter.showDates[dateAdapter.selectedIndex], currentLocation, false,
            )
        }
    }

    @SuppressLint("SetTextI18n")
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        btnFilmDetail.click {
            if (film != null) {
                openFragment(FilmDetailFragment.getInstance(film!!))
            } else {
                openFragment(FilmDetailFragment.getInstance(filmId ?: return@click))
            }
        }
        if (film == null) {
            filmId?.let {
                presenter.getFilmModel(it)
            }
            return
        }
        btnLoadMore.click {
            presenter.getTimeBooking(
                film
                    ?: return@click,
                dateAdapter.showDates[dateAdapter.selectedIndex], currentLocation, true,
            )
        }
        showDataFilm()
    }

    override fun onLoadmore() {
//        activity?.runOnUiThread {
//
//        }
    }

    private fun showDataFilm() {
        btnFilmDetail.visible(isShowOpenDetail)
        currentLocation = (activity as BaseActivity).getMyLocation(true, true)
        film?.filmPosterUrl?.let {
            ivBanner.load(it)
        }
        film?.Name?.let {
            tvFilmTitle.text = it
        }
        tvFilmType.text = (film?.filmGenner
            ?: "") + " | " + ((film?.Duration
            ?: 0).toString() + " " + R.string.minute.getString())

        btnLoadMore.visible(presenter.showButtonLoadMore)

        recyclerView.layoutManager = LinearLayoutManager(context)
        recyclerDate.layoutManager =
            LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
        dateAdapter = DateAdapter {
            //-- Init getTimeBooking
            presenter.getTimeBooking(film ?: return@DateAdapter, it, currentLocation, false)
        }
        recyclerDate.adapter = dateAdapter

        adapter = BookAdapter(listBooking)
        recyclerView.adapter = adapter
        presenter.getShowDates(film?.FilmGroupId ?: return)
        (activity as BaseActivity).listenerLocationChange.add(listenerLocation)
        llSelectArea?.setOnClickListener {
            openFragment(SelectCenimaByAreaFragment.getInstance {
                selectedArea = it
                val newFilterList = if (selectedArea != null) listBooking.filter { not ->
                    selectedArea?.ListCinema?.count { it.CinemaId == not.CinemaId } ?: 0 > 0
                } else listBooking
                activity?.runOnUiThread {
                    tvTitleArea.text = it?.CityName ?: R.string.All.getString()
                    adapter = BookAdapter(newFilterList)
                    recyclerView.adapter = adapter
                    adapter?.notifyDataSetChanged()
                }
            })
        }
    }

    private val listenerLocation: WeakReference<(Location?) -> Unit> =
        WeakReference { currentLocation ->
            this.currentLocation = currentLocation
//            onRefresh()
        }

    override fun onTimeSelected(time: ShowModel?, film: FilmModel?) {
        if (time?.getIsShowScreenIntro() == true) {
            showNoticeVipRoom(time) {
                handleSelectShowTime(time, film)
            }
        } else {
            handleSelectShowTime(time, film)
        }
    }

    private fun handleSelectShowTime(time: ShowModel?, film: FilmModel?) {
        val startTime = time?.getStartDate()?.time ?: return
        if (startTime > (System.currentTimeMillis() + (time.TimeToLock * 60 * 1000))) {
            if (Global.share().isLogin) {
                Tracking.share().selectShowtimeComplete(
                    context,
                    film?.CinemaId,
                    film?.CinemaName,
                    film?.FilmId,
                    film?.Name,
                    time.getStartDate(),
                    time.getStartDate()
                )
                openFragment(SelectChairFragment.getInstance(time, film))
            } else {
                openFragment(LoginFragment())
            }
        } else {
            showNotice(getString(R.string.time_booking_is_expried))
        }
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        adapter?.onSaveInstanceState(outState)
    }

    override fun onViewStateRestored(savedInstanceState: Bundle?) {
        super.onViewStateRestored(savedInstanceState)
        adapter?.onRestoreInstanceState(savedInstanceState)
    }

    inner class BookByFilmAdapter(val films: List<GroupFilmBooking>) :
        SectionedRecyclerViewAdapter<SectionedViewHolder>() {
        override fun onBindHeaderViewHolder(
            holder: SectionedViewHolder,
            section: Int,
            expanded: Boolean
        ) {
            val group = films[section]
            holder.itemView.apply {
                tvAreaTitle.text = group.CinemaName
                val distance = group.Distance ?: 0f
                if (distance >= 10000f) {
                    tvAreaCountCinema.gone()
                } else {
                    tvAreaCountCinema.visible()
                    tvAreaCountCinema.text = "${String.format(Locale.ENGLISH, "%.1f", distance)} km"
                }
            }
        }

        override fun getSectionCount(): Int {
            return films.size
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SectionedViewHolder {
            val view = when (viewType) {
                VIEW_TYPE_HEADER -> {
                    parent.inflate(R.layout.item_area_in_list)
                }

                else -> {
                    parent.inflate(R.layout.item_time_booking_of_cinema)
                }
            }
            return SectionHolder(view)
        }

        override fun getItemCount(section: Int): Int {
            return films[section].ListFilm.size
        }

        override fun onBindViewHolder(
            holder: SectionedViewHolder,
            section: Int,
            relativePosition: Int,
            absolutePosition: Int
        ) {
            holder.itemView.apply {
                viewItemTimeRoot.setBackgroundColor(android.R.color.transparent.getColor())
                listTimeMorning.removeAllViews()
                morningScroll.visible()
                val filmBook = films[section].ListFilm[relativePosition]
                tvTypeFilm.text = filmBook.FilmFormatName
                filmBook.ListShow?.let {
                    val shows = it.sortedBy { show -> show.getStartDate() }
                    for (show in shows) {
                        show.FilmFormat = filmBook.FilmFormatName
                        val item = ItemTimeBooking(
                            context,
                            show.getStartDate()?.toStringFormat(Constant.DateFormat.hourMinute)
                                ?: "",
                            "${
                                if (show.TotalSeat == null) 0 else (show.TotalSeat!! - (show.SeatSolded
                                    ?: 0))
                            } ${R.string.empty.getString()}",
                            show,
                            film,
                            this@BookByFilmFragment
                        )
                        if (listTimeMorning.childCount == 0) {
                            item.viewTimeRoot.setPadding(0, 0, 5.px, 0)
                        } else {
                            item.viewTimeRoot.setPadding(5.px, 0, 5.px, 0)
                        }
                        listTimeMorning.addChild(item)
                    }
                }

                if (listTimeMorning.childCount <= 0) {
                    morningScroll.gone()
                }
                bottomLine.visible(relativePosition < (filmBook.ListShow?.size ?: 0) - 1)
            }
        }

        override fun showFooters(): Boolean {
            return false
        }

        override fun onBindFooterViewHolder(holder: SectionedViewHolder, section: Int) {

        }
    }

    inner class SectionHolder(view: View) : SectionedViewHolder(view)

    inner class BookAdapter(notifes: List<GroupFilmBooking>) :
        ExpandableRecyclerViewAdapter<CinemaHolder, TimeHolder>(notifes) {

        init {
            if (expandableList?.expandedGroupIndexes?.isNotEmpty() == true) {
                expandableList?.expandedGroupIndexes!![0] = true
            }
        }

        override fun onCreateGroupViewHolder(parent: ViewGroup, viewType: Int): CinemaHolder {
            val view = parent.inflate(R.layout.item_area_in_list)
            return CinemaHolder(view)
        }

        override fun onCreateChildViewHolder(parent: ViewGroup, viewType: Int): TimeHolder {
            val view = parent.inflate(R.layout.item_time_booking_of_cinema)
            return TimeHolder(view)
        }

        override fun onBindChildViewHolder(
            holder: TimeHolder,
            flatPosition: Int,
            group: ExpandableGroup<*>,
            childIndex: Int
        ) {
            holder.itemView.apply {
                viewItemTimeRoot.setBackgroundColor(android.R.color.transparent.getColor())
                listTimeMorning.removeAllViews()
                morningScroll.visible()
                val filmBook: FilmBooking? = try {
                    (group as GroupFilmBooking).ListFilm[childIndex]

                } catch (e: Exception) {
                    null
                }
                tvTypeFilm.text = filmBook?.FilmFormatName?:""
                filmBook?.ListShow?.let {
                    val shows = it.sortedBy { show -> show.getStartDate() }
                    listTimeMorning.removeAllViews()
                    for (show in shows) {
                        show.FilmFormat = filmBook.FilmFormatName
                        val item = ItemTimeBooking(
                            context,
                            show.getStartDate()?.toStringFormat(Constant.DateFormat.hourMinute)
                                ?: "",
                            "${
                                if (show.TotalSeat == null) 0 else (show.TotalSeat!! - (show.SeatSolded
                                    ?: 0))
                            } ${R.string.empty.getString()}",
                            show,
                            film,
                            this@BookByFilmFragment
                        )
                        if (listTimeMorning.childCount == 0) {
                            item.viewTimeRoot.setPadding(0, 0, 5.px, 0)
                        } else {
                            item.viewTimeRoot.setPadding(5.px, 0, 5.px, 0)
                        }
                        listTimeMorning.addChild(item)
                    }
                }

                if (listTimeMorning.childCount <= 0) {
                    morningScroll.gone()
                }
                bottomLine.visible(flatPosition < (filmBook?.ListShow?.size ?: 0) - 1)
            }
        }

        @SuppressLint("SetTextI18n")
        override fun onBindGroupViewHolder(
            holder: CinemaHolder,
            flatPosition: Int,
            group: ExpandableGroup<*>
        ) {
            holder.itemView.tvAreaTitle.text = (group as GroupFilmBooking).CinemaName
            val distance = group.Distance ?: 0f
            if (distance >= 10000f) {
                holder.itemView.tvAreaCountCinema.gone()
            } else {
                holder.itemView.tvAreaCountCinema.visible()
                holder.itemView.tvAreaCountCinema.text =
                    "${String.format(Locale.ENGLISH, "%.1f", distance)} km"
            }
            holder.itemView.ivDropdown?.setImageResource(
                if (isGroupExpanded(flatPosition)) {
                    R.drawable.ic_dropdown
                } else {
                    R.drawable.ic_zipup
                }
            )
        }
    }

    inner class CinemaHolder(itemView: View) : GroupViewHolder(itemView) {

        override fun expand() {
            super.expand()
            itemView.ivDropdown?.setImageResource(R.drawable.ic_dropdown)
        }

        override fun collapse() {
            super.collapse()
            itemView.ivDropdown?.setImageResource(R.drawable.ic_zipup)
        }

    }

    inner class TimeHolder(itemView: View) : ChildViewHolder(itemView)
}
