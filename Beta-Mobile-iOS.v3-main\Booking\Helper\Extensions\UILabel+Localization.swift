//
//  UILabel+Localization.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 4/8/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation

extension UILabel{
    func setText(vi: String, en: String){
        self.text = Utils.shared.isEng() ? en : vi
    }
}

// MARK: - text in UILabel
extension UILabel {
    func isPoint(pos: CGPoint, at range: NSRange) -> Bool {
        guard let index = indexOfAttributedTextCharacterAtPoint(point: pos) else {
            return false
        }

        return NSLocationInRange(index, range)
    }

    func boundingRectForCharacterRange(range: NSRange) -> CGRect? {
        guard let attributedText = attributedText else {
            return nil
        }
        let mAttributedText = NSMutableAttributedString(attributedString: attributedText)
        let allRange = NSMakeRange(0, attributedText.length)

        let font = attributedText.attribute(NSAttributedStringKey.font, at: 0, longestEffectiveRange: nil, in: allRange)
        if font == nil {
            mAttributedText.addAttribute(NSAttributedStringKey.font, value: font!, range: allRange)
        }

        let textStorage = NSTextStorage(attributedString: attributedText)
        let layoutManager = NSLayoutManager()

        textStorage.addLayoutManager(layoutManager)

        let textContainer = NSTextContainer(size: frame.size)
        textContainer.lineFragmentPadding = 0.0

        layoutManager.addTextContainer(textContainer)

        var glyphRange = NSRange()

        // Convert the range for glyphs.
        layoutManager.characterRange(forGlyphRange: range, actualGlyphRange: &glyphRange)

        return layoutManager.boundingRect(forGlyphRange: glyphRange, in: textContainer)
    }

    ///Find the index of character (in the attributedText) at point
    func indexOfAttributedTextCharacterAtPoint(point: CGPoint) -> Int? {
        guard let attributedText = attributedText else {
            return nil
        }
        let textStorage = NSTextStorage(attributedString: attributedText)
        let layoutManager = NSLayoutManager()
        textStorage.addLayoutManager(layoutManager)
        let textContainer = NSTextContainer(size: self.frame.size)
        textContainer.lineFragmentPadding = 0
        textContainer.maximumNumberOfLines = self.numberOfLines
        textContainer.lineBreakMode = self.lineBreakMode
        layoutManager.addTextContainer(textContainer)

        let index = layoutManager.characterIndex(for: point, in: textContainer, fractionOfDistanceBetweenInsertionPoints: nil)
        return index
    }
}
