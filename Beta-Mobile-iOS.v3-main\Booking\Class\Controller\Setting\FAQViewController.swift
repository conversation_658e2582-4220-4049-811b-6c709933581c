//
//  FAQViewController.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 4/12/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit

class FAQViewController: BaseViewController {
    @IBOutlet weak var tableView: UITableView!

    let dataSource = SimpleTableViewDataSource()

    fileprivate let cellId = "SettingTableCell"
    private var items: [TopicModel] = []{
        didSet{
            let rows = items.map{ TableItem(title: $0.Title, cellId: cellId) }
            dataSource.addRows(rows)
            tableView.reloadData()
        }
    }
    override func viewDidLoad() {
        super.viewDidLoad()
        localizableTitle = "FAQ.Title"

        tableView.dataSource = dataSource
        dataSource.register(tableView: tableView, cellId: cellId)
        
        getTopic()
    }

    override func didReceiveMemoryWarning() {
        super.didReceiveMemoryWarning()
        // Dispose of any resources that can be recreated.
    }

}

extension FAQViewController{
    private func getTopic(){
        self.showLoading()
        EcmProvider.rx.request(.getTopic).mapObject(DDKCResponse<TopicModel>.self)
            
            .subscribe(onNext:{[weak self] response in
                guard let `self` = self else {return}
                self.dismissLoading()
                self.handlerResponse(response, success: {
                    guard let items = response.ListObject else {return}
                    self.items = items
                })
            }).disposed(by: disposeBag)
    }
}

extension FAQViewController: UITableViewDelegate{
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: false)
        
        let vc = UIStoryboard.setting[.faqDetails] as! FAQDetailViewController
        vc.topicId = items[indexPath.row].FeedbackThreadId
        vc.topicTitle = items[indexPath.row].Title
        show(vc, sender: nil)
    }
}
