//
//  AddVoucherViewController.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 8/3/19.
//  Copyright © 2019 ddkc. All rights reserved.
//

import UIKit

class AddVoucherViewController: BaseViewController {

    @IBOutlet weak var codeTextField: RoundTextField!
    @IBOutlet weak var pinCodeTextfield: RoundTextField!
    override func viewDidLoad() {
        super.viewDidLoad()

        localizableTitle = "add_new_voucher"
    }

    private func validate() -> Bool{
        if let text = codeTextField.text, text.isEmpty{
            UIAlertController.showAlert(self, message: "Alert.VoucherCodeNotEmpty".localized)
            return false
        }

        if let text = pinCodeTextfield.text, text.isEmpty{
            UIAlertController.showAlert(self, message: "Alert.PinCodeNotEmpty".localized)
            return false
        }

        return true
    }

    private func registerVoucher(){
        if self.validate(){
            self.showLoading()
            guard let user = Global.shared.user, let userId = user.UserId, let userCard = user.CardNumber else{
                return
            }
            let model = RegisterVoucherModel(pinCode: pinCodeTextfield.text!, voucherCode: codeTextField.text!, customerId: userId, customerCard: userCard, cardTypeName: .Voucher)
            VoucherProvider.rx.request(.registerVoucher(model)).mapObject(DDKCResponse<VoucherModel>.self)

                .subscribe(onNext: {[weak self] (response) in
                    guard let `self` = self else {return}
                    self.dismissLoading()
                    guard response.isSuccess() else{
                        print("Data wrong")
                        UIAlertController.showAlert(self, message: response.Message ?? "")
                        return
                    }
                    let alertType = MyAlertType.successNormal("add_success", "add_voucher_success")
                    self.showCustomizeAlert(type: alertType, okAction: {
                        self.navigationController?.popViewController(animated: true)
                    })
                }).disposed(by: disposeBag)
        }
    }

    @IBAction func scanTapped(_ sender: Any) {
        let scanVC = ScanBarCodeViewController()
        scanVC.scanResult = { [weak self] result in
            if result.isEmpty {
                return
            }
            if result.contains("-") {
                let components = result.components(separatedBy: "-")
                if components.count > 1 {
                    self?.codeTextField.text = components[0]
                    self?.pinCodeTextfield.text = components[1]
                }
            } else {
                self?.codeTextField.text = result
            }
        }
        self.present(scanVC, animated: true, completion: nil)
    }

    @IBAction func addTapped(_ sender: Any) {
        registerVoucher()
    }

}
