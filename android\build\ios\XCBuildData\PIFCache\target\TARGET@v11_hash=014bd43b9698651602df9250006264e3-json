{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9838402d31f902c4695916b350e1da79ff", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a364ef074c9f007d7722a4592c362003", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989d777f0ba5487392c39e2e1a4ff04b31", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9864e418283fe44f96b2a5088b176545fc", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989d777f0ba5487392c39e2e1a4ff04b31", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989797b9f6d741017df5503f098417ae71", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987eae91cf8bcd23089c877465146a6409", "guid": "bfdfe7dc352907fc980b868725387e984362ec7851f866e38f29f9c9db28679d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98760cb230a3cf57a7082714639c43ce03", "guid": "bfdfe7dc352907fc980b868725387e9887defd30793abec7bca5db5f99893d05", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea07a5bbd0b31e8dd998ae55d3252f32", "guid": "bfdfe7dc352907fc980b868725387e98a928d186c81f0d8d3325edb29f40032a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849b6247f1c050a844d7ffe060fddcbcf", "guid": "bfdfe7dc352907fc980b868725387e9814d8a10d2e84273d6fda06e327a0e254", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ebaa6d19a05df9c2e7f86227676cbc6f", "guid": "bfdfe7dc352907fc980b868725387e98d4537421ab341f81a9d1c8cf8de9673a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dee171a683e4cac5145cce25aa651849", "guid": "bfdfe7dc352907fc980b868725387e98385cfd8ad75e5ea7f92e6987d4d26064", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbd2e8c6dfe89fea43157dce2f80c055", "guid": "bfdfe7dc352907fc980b868725387e98bdffaa35579120e565ecd979dd324654", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c84bccc34dad8a5b67ca0f8bbe1e948", "guid": "bfdfe7dc352907fc980b868725387e981cd3e5921cd7d30217c063284069002f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819ad8d7637233e6d7381477f3ab6f1cf", "guid": "bfdfe7dc352907fc980b868725387e985dbd071706c8a6374e87b1709859e96a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98faa28aecc1ac56fe56c2f1bf826ff649", "guid": "bfdfe7dc352907fc980b868725387e9867353596e35399cdff1e7615a43a1a17", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989453ae1133511b72ffeaeecb54ee387b", "guid": "bfdfe7dc352907fc980b868725387e987f357b48c04aa8f877a58d1de2abcf09", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f75160d602fb8d0816819395aca0787", "guid": "bfdfe7dc352907fc980b868725387e98bcc7b7dd714eedcd2382caaa07f46b37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b91221b054676074a6c30d6b28c55024", "guid": "bfdfe7dc352907fc980b868725387e984d5675da2b319505a321e695cd04be12", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a274b15d1965a41f102f6f978ffdaae0", "guid": "bfdfe7dc352907fc980b868725387e98d031d9dcd7f83f0f7be04d06d9bc9a5c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0f968be3d20a21e19a1735bfd35d6de", "guid": "bfdfe7dc352907fc980b868725387e982ecca111a8d9e172e5a5c5e06e23ec27", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98efdc2413f12fd7ceffb06d644fbef6f0", "guid": "bfdfe7dc352907fc980b868725387e98f93881634a7cdaf970039ac2276d403e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac158d492af72365c335d59bf7f32dbb", "guid": "bfdfe7dc352907fc980b868725387e98203e96e54d5493fb82eb87edc693de42", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f0b4dbfe1278a1309f5a26ed91be425", "guid": "bfdfe7dc352907fc980b868725387e985eafdb406d9c2c7c65484354895688a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98129378f477d92927996ffbac36b7e576", "guid": "bfdfe7dc352907fc980b868725387e98d9d3b2835cbc3d8a61108c52ad53ec7f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da07f5699f471e6ae9c391860cb16384", "guid": "bfdfe7dc352907fc980b868725387e98c3a69958376980c00d8ef4883b2bf3a1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874d1ef6411a9a44ba1f9f5fe9f9a2c38", "guid": "bfdfe7dc352907fc980b868725387e98cc5643c129b3b7e4a7274af167cc0942", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983be2a2683f8307e67493735de6f7a744", "guid": "bfdfe7dc352907fc980b868725387e98577abaaa228826f7a527f5d7a1b5066b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b7c0dffcf17b552e664169f9cd7a88d", "guid": "bfdfe7dc352907fc980b868725387e98b3d83fcf85ce126509f698b963314d88", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985320588decfd72ce2f45c3893b738217", "guid": "bfdfe7dc352907fc980b868725387e98d1eb35e1c6ae22d97d2b5dfb2919bf6f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98313c5334a8093487d329c0d5518f6f3b", "guid": "bfdfe7dc352907fc980b868725387e9813f0b4c0087ba62a8f25e121181f1f4d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98553c50b5f8d3e0097422b51cf20ea8f6", "guid": "bfdfe7dc352907fc980b868725387e98578ccf753482f6f3d8c2341708c27a5c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c20d42f4c3ed47324dd8b9778068778c", "guid": "bfdfe7dc352907fc980b868725387e985a62054ff61c66c83518bdcef5e08fa1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98745b6956af406557803e48ecedea8e13", "guid": "bfdfe7dc352907fc980b868725387e98364779cc4f7a108c77d68b76dcb4610b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b94e83c53af6fe802c168b81ad2b013d", "guid": "bfdfe7dc352907fc980b868725387e98036d61c863b77707b2d2841d9f76bf77", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98800d9ee126ed28f24745af7d5d42c4a3", "guid": "bfdfe7dc352907fc980b868725387e98c66e20f26a73c3743e61b9b4c576309e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983459a2d8acac5e8a9d0a4d44bee2f8d1", "guid": "bfdfe7dc352907fc980b868725387e980e2397646d7d244c0bb746292ce9c970"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fdc3845294c94233e0a16989df8bc5c8", "guid": "bfdfe7dc352907fc980b868725387e9814194d82ecbe71457b020945a2fc7804", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98d174551df753dbfb1c4ea212b7786188", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9807708e1e877b4debfbd737d55dd0e9ab", "guid": "bfdfe7dc352907fc980b868725387e98324f56725f6c3b5b30822baa51aba072"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f0011b5ea6d0541d41f6dddbc7f4224", "guid": "bfdfe7dc352907fc980b868725387e9879bb69cbb2e48c54682e7a4f4b87e4e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824dd4ebe1065262d6f09fed515c33aad", "guid": "bfdfe7dc352907fc980b868725387e98d838dfc9e2b79e8885dd44e894765e3a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b251eb6aea0f8a7ec670542115c89a3", "guid": "bfdfe7dc352907fc980b868725387e98edfad87225e34aae79f9e6024a47bb3a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987160831d1fb8d6a24f3fbedfdfcb085c", "guid": "bfdfe7dc352907fc980b868725387e98805961135c62627325ee0c05605a57fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843cf6fc669b912641fd0cf9fa17359d6", "guid": "bfdfe7dc352907fc980b868725387e98e6b840887fb77572c72851d6aecb07ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865adcb956c9fd7f669493eeb9c7c1556", "guid": "bfdfe7dc352907fc980b868725387e985fc5d12e0de53ed05731c1318de829d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f39aadb9469f8a2667afdedfb68553e", "guid": "bfdfe7dc352907fc980b868725387e9804ece7a413e10424cf2c28c8bda8d199"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981595700ae078e263c9148e93f09abb4f", "guid": "bfdfe7dc352907fc980b868725387e989882eebb8b7125f96d39c2735d8bf22d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982fea3fdfb4fc4fcce77216db85b7df37", "guid": "bfdfe7dc352907fc980b868725387e9842777eb789e09af30e5fd4061bc6674d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826287e09b2156acb425d9970993535f9", "guid": "bfdfe7dc352907fc980b868725387e989bb2cdcb6ddeb0dddc79dbbd780c7183"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889dec225fd715eef87c7fa9e05e08dca", "guid": "bfdfe7dc352907fc980b868725387e9835ee6cc9c9292aef24d74e51ba28d94d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98420d7485553b2f64ce4628f5ff222496", "guid": "bfdfe7dc352907fc980b868725387e9881ed03eaefe61ca67bcda307f96de7a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844d44ba10f7ed9ab4a42c7ca5baacbee", "guid": "bfdfe7dc352907fc980b868725387e985eb0a67f42dc6fc99e0cb6340792169a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f84398a429a22db13816f6cea3f5502", "guid": "bfdfe7dc352907fc980b868725387e98aa399a001a43cb1a97655a700da578c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98475eeaff1868cb57c60ee2aa72452292", "guid": "bfdfe7dc352907fc980b868725387e98e15791e8cb70901a77af19d5f14c6de4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985761c9abe1bf1b578c48095bce5870c1", "guid": "bfdfe7dc352907fc980b868725387e98f1ad8a073c9c945dd1b25c3b13596817"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826bc559549551dcb67d8e4da709bf674", "guid": "bfdfe7dc352907fc980b868725387e98e7934a758ad00786be55bb71d3517db5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f8cc68cec3f51960f705e08293547a9", "guid": "bfdfe7dc352907fc980b868725387e983429d2ee7b09066c3d219333c4a613de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899a2ac890362342c1f9b71bb4fb33036", "guid": "bfdfe7dc352907fc980b868725387e9838ecd1ffbe6985b196731f7670568c3f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bec20e5c978990d3543676ef2e9ea3be", "guid": "bfdfe7dc352907fc980b868725387e98a35195dccc64cc5829fc8434fa0316bc"}], "guid": "bfdfe7dc352907fc980b868725387e985a3cce2c47efd9d75deb6f0e570f3ce6", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e9861db2de078082526ab77dc1fc97d1557"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821c5a86bdaf5129f0bfbd51564017ddb", "guid": "bfdfe7dc352907fc980b868725387e98dcc6bff4bcefe663de4911400a97cc98"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5f5979f0786f4a5ad9848f0349b1159", "guid": "bfdfe7dc352907fc980b868725387e98c9e89c2877d0722330c67f5f18625dd3"}], "guid": "bfdfe7dc352907fc980b868725387e9824143810897d00be4c7a40ea5ef288a5", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e989db90796882e226a422758b636e84a0f", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e98fc5af3f26ccc690f6851691380e0baad", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}