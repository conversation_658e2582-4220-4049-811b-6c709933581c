import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import '/constants/src/captcha_config.dart';

/// Service để handle Google reCAPTCHA validation
/// Tương tự như Android BaseActivity.checkCaptcha và iOS ReCaptcha setup
class CaptchaService {
  
  /// Hiển thị reCAPTCHA và trả về token
  /// Tương tự Android BaseActivity.checkCaptcha callback
  static Future<String?> showCaptcha(BuildContext context) async {
    // Check if captcha is configured
    if (!CaptchaConfig.isConfigured) {
      debugPrint('⚠️ reCAPTCHA site key not configured. Please update CaptchaConfig.siteKey');
      // For development, return a dummy token
      return 'development_token';
    }

    final completer = Completer<String?>();

    // Tạo HTML content cho reCAPTCHA
    final htmlContent = _generateCaptchaHtml();
    
    // Hiển thị dialog với WebView
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return CaptchaDialog(
          htmlContent: htmlContent,
          onTokenReceived: (token) {
            Navigator.of(context).pop();
            completer.complete(token);
          },
          onError: (error) {
            Navigator.of(context).pop();
            completer.complete(null);
          },
        );
      },
    );
    
    return completer.future;
  }
  
  /// Tạo HTML content cho reCAPTCHA
  /// Tương tự iOS ReCaptcha WebView setup
  static String _generateCaptchaHtml() {
    return '''
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>reCAPTCHA</title>
    <script src="https://www.google.com/recaptcha/api.js" async defer></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .title {
            margin-bottom: 20px;
            color: #333;
        }
        .g-recaptcha {
            margin: 20px 0;
        }
        .error {
            color: #d32f2f;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2 class="title">Xác thực bảo mật</h2>
        <p>Vui lòng hoàn thành xác thực để tiếp tục</p>
        <div class="g-recaptcha" data-sitekey="${CaptchaConfig.siteKey}" data-callback="onCaptchaSuccess" data-error-callback="onCaptchaError"></div>
        <div id="error-message" class="error" style="display: none;"></div>
    </div>
    
    <script>
        function onCaptchaSuccess(token) {
            // Gửi token về Flutter app
            if (window.flutter_inappwebview) {
                window.flutter_inappwebview.callHandler('captchaSuccess', token);
            } else if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.captchaSuccess) {
                window.webkit.messageHandlers.captchaSuccess.postMessage(token);
            } else {
                // Fallback cho WebView thông thường
                window.postMessage(JSON.stringify({type: 'captchaSuccess', token: token}), '*');
            }
        }
        
        function onCaptchaError() {
            const errorDiv = document.getElementById('error-message');
            errorDiv.textContent = 'Có lỗi xảy ra. Vui lòng thử lại.';
            errorDiv.style.display = 'block';
            
            // Gửi error về Flutter app
            if (window.flutter_inappwebview) {
                window.flutter_inappwebview.callHandler('captchaError', 'Captcha error');
            } else if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.captchaError) {
                window.webkit.messageHandlers.captchaError.postMessage('Captcha error');
            } else {
                window.postMessage(JSON.stringify({type: 'captchaError', error: 'Captcha error'}), '*');
            }
        }
        
        // Xử lý expired captcha
        function onCaptchaExpired() {
            grecaptcha.reset();
        }
    </script>
</body>
</html>
    ''';
  }
}

/// Dialog widget để hiển thị reCAPTCHA
/// Tương tự iOS ReCaptcha WebView configuration
class CaptchaDialog extends StatefulWidget {
  final String htmlContent;
  final Function(String) onTokenReceived;
  final Function(String) onError;
  
  const CaptchaDialog({
    Key? key,
    required this.htmlContent,
    required this.onTokenReceived,
    required this.onError,
  }) : super(key: key);
  
  @override
  State<CaptchaDialog> createState() => _CaptchaDialogState();
}

class _CaptchaDialogState extends State<CaptchaDialog> {
  late final WebViewController _controller;
  bool _isLoading = true;
  
  @override
  void initState() {
    super.initState();
    _initializeWebView();
  }
  
  void _initializeWebView() {
    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            setState(() {
              _isLoading = true;
            });
          },
          onPageFinished: (String url) {
            setState(() {
              _isLoading = false;
            });
          },
        ),
      )
      ..addJavaScriptChannel(
        'captchaSuccess',
        onMessageReceived: (JavaScriptMessage message) {
          widget.onTokenReceived(message.message);
        },
      )
      ..addJavaScriptChannel(
        'captchaError',
        onMessageReceived: (JavaScriptMessage message) {
          widget.onError(message.message);
        },
      )
      ..loadHtmlString(widget.htmlContent);
  }
  
  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.7,
        child: Column(
          children: [
            // Header với nút đóng
            Container(
              padding: const EdgeInsets.all(16),
              decoration: const BoxDecoration(
                border: Border(bottom: BorderSide(color: Colors.grey, width: 0.5)),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Xác thực bảo mật',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () {
                      widget.onError('User cancelled');
                    },
                  ),
                ],
              ),
            ),
            // WebView content
            Expanded(
              child: Stack(
                children: [
                  WebViewWidget(controller: _controller),
                  if (_isLoading)
                    const Center(
                      child: CircularProgressIndicator(),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
