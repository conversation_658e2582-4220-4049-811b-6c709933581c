//
//  HomeViewController.swift
//  Booking
//
//  Created by <PERSON><PERSON> Vu on 3/10/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit
import iCarousel
import ObjectMapper
import RxSwift
import CoreLocation
import SwiftDate
import AVKit
import youtube_ios_player_helper

class HomeViewController: BaseViewController {
    @IBOutlet weak var bannerCarousel: iCarousel!
    @IBOutlet weak var bannerBackgroundView: UIView!
    @IBOutlet weak var vTopicTab: UIView!
    @IBOutlet weak var topicTab: ScrollPager!
    @IBOutlet weak var indicatorTopicView: UIView!
    @IBOutlet weak var nowShowingButton: UIButton!
    @IBOutlet weak var btBuyTicket: GradientButton!
    @IBOutlet weak var vFilmDate: RoundView!
    @IBOutlet weak var lbFilmDate: UILabel!

    @IBOutlet weak var searchTextField: UITextField!

    @IBOutlet weak var tbPromotion: UITableView!
    @IBOutlet weak var tbPromotionHeight: NSLayoutConstraint!
    @IBOutlet weak var searchCinemaView: RoundView!
    
    fileprivate var bannerMaskLayer: CAShapeLayer!

    fileprivate let newsCellId = "NewsTableViewCell"
    var locationManager: CLLocationManager!

    fileprivate var films: [FilmModel] = []
    fileprivate var filmsPrepare: [FilmModel] = []
    fileprivate var filmsShowing: [FilmModel] = []
    fileprivate var filmsSpecial: [FilmModel] = []
    fileprivate var promotions: [NewsModel] = []

    override func viewDidLoad() {
        super.viewDidLoad()

        initLocation()
        initTitle()
        initTopicTab()
        initBanner()
        initSearchBar()

        addLeftButton(#imageLiteral(resourceName: "ic_account_white"))

        tbPromotion.register(UINib(nibName: NewsTableViewCell.className, bundle: nil), forCellReuseIdentifier: newsCellId)

        tbPromotion.addObserver(self, forKeyPath: "contentSize", options: .new, context: nil)
    }

    override func willMove(toParentViewController parent: UIViewController?) {
        super.willMove(toParentViewController: parent)
        if parent == nil {
            tbPromotion.removeObserver(self, forKeyPath: "contentSize")
        }
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        if films.isEmpty {
            self.getListFilm()
            self.getPromotionCategory()
        }
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)

        if Global.shared.isLogined {
            let csCopy = CharacterSet(bitmapRepresentation: CharacterSet.urlPathAllowed.bitmapRepresentation)
            let imageURL = Config.BaseURLImage + (Global.shared.user?.Picture?.addingPercentEncoding(withAllowedCharacters: csCopy) ?? "")
            changeLeftButton(imageURL, placeholderImage: #imageLiteral(resourceName: "ic_account_white"), title: " " + "Home.Hi".localized + " " + (Global.shared.user?.FullName ?? ""))
            self.navigationItem.titleView = nil
        } else {
            initTitle()
            addLeftButton(#imageLiteral(resourceName: "ic_account_white"))
        }
    }

    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()

        layoutBannerMask()
    }

    override func leftButtonPressed(_ sender: UIBarButtonItem) {
        if Global.shared.isLogined {
            self.navigationController?.pushViewController(UIStoryboard.member[.member], animated: true)
        } else {
            self.navigationController?.pushViewController(UIStoryboard.authen[.login], animated: true)
        }
    }

    override func localizationDidChange() {
        super.localizationDidChange()
        bannerCarousel.reloadData()
    }

    override func observeValue(forKeyPath keyPath: String?, of object: Any?, change: [NSKeyValueChangeKey : Any]?, context: UnsafeMutableRawPointer?) {
        if keyPath == "contentSize" {
            tbPromotionHeight.constant = tbPromotion.contentSize.height
        }
    }
}

// Init
extension HomeViewController {
    func initLocation(){
        LocationManager.shared.startTracking()
    }
    
    func initTitle() {
        let titleView = UIImageView(image: #imageLiteral(resourceName: "imgLogobeta"))
        self.navigationItem.titleView = titleView
    }

    func initTopicTab() {
        topicTab.addSegmentsWithTitles(segmentTitles: ["Home.UpComing", "Home.NowShowing", "Home.Special"])
        topicTab.font = UIFont(fontName: .Oswald, size: 20)
        topicTab.selectedFont = UIFont(fontName: .Oswald, size: 20)
        topicTab.setSelectedIndex(index: 1, animated: false)
    }

    func initBanner() {
        bannerMaskLayer = CAShapeLayer()
        bannerBackgroundView.layer.mask = bannerMaskLayer

        layoutBannerMask()

        bannerCarousel.type = .coverFlow
    }

    func initSearchBar() {
        let searchIcon = UIButton(frame: CGRect(x: 0, y: 0, width: 24, height: 24))
        searchIcon.setImage(#imageLiteral(resourceName: "ic_search"), for: .normal)
        searchIcon.addTarget(self, action: #selector(searchButtonPressed(_:)), for: .touchUpInside)
        searchTextField.rightView = searchIcon
        searchTextField.rightViewMode = .always
        
        searchTextField.isEnabled = false
        
//        let tapguesture = UITapGestureRecognizer(target: self, action: #selector(searchButtonPressed(_:)))
//        searchCinemaView.addGestureRecognizer(tapguesture)
    }

    func layoutBannerMask() {
        let frame = bannerBackgroundView.bounds

        let bezier = UIBezierPath(rect: frame)
        bezier.move(to: .zero)
        bezier.addQuadCurve(to: CGPoint(x: frame.maxX, y: 0), controlPoint: CGPoint(x: frame.midX, y: 30))
        bezier.move(to: CGPoint(x: frame.maxX, y: 0))
        bezier.addLine(to: CGPoint(x: frame.maxX, y: frame.maxY))
        bezier.move(to: CGPoint(x: frame.maxX, y: frame.maxY))
        bezier.addQuadCurve(to: CGPoint(x: 0, y: frame.maxY), controlPoint: CGPoint(x: frame.midX, y: frame.maxY - 30))
        bezier.move(to: CGPoint(x: 0, y: frame.maxY))
        bezier.addLine(to: CGPoint(x: 0, y: 0))

        bannerMaskLayer.path = bezier.cgPath
    }

    func playVideo(_ urlString: String) {
        if urlString.isYoutubeURL {
            let vc = UIStoryboard.home[.youtube] as! YoutubeViewController
            vc.videoURL = urlString
            self.present(vc, animated: true, completion: nil)
            return
        }

        guard let url = URL(string: urlString) else {
            self.showAlert(message: "Alert.NoFilmTrailerURL".localized)
            return
        }

        let player = AVPlayer(url: url)
        let playerViewController = AVPlayerViewController()
        playerViewController.player = player
        self.present(playerViewController, animated: true) {
            playerViewController.player!.play()
        }
    }
}

// API
extension HomeViewController {
    
    private func getListFilm(isShowing: Bool = true){
        self.showLoading()
        FilmProvider.rx.request(.listFilm(isShowing)).mapObject(DDKCResponse<FilmModel>.self)
            
            .subscribeOn(MainScheduler.instance)
            .subscribe(onNext:{ response in
                self.handlerResponse(response, success: {
                    guard let objects = response.ListObject else{
                        print("Data wrong")
                        self.dismissLoading()
                        return
                    }
                    if isShowing{
                        self.filmsShowing = objects
                        self.films = self.filmsShowing
                    }else{
                        self.filmsPrepare = objects
                        self.films = self.filmsPrepare
                    }
                    self.bannerCarousel.reloadData()
                    self.btBuyTicket.isHidden = self.topicTab.selectedIndex == 0 || self.films.isEmpty
                    self.dismissLoading()
                }, error: {
                    self.dismissLoading()
                })
                
            }, onError:{[weak self] error in
                guard let `self` = self else { return }
                self.dismissLoading()
            }).disposed(by: disposeBag)
    }
    
    private func getListFilmSpecial(){
        self.showLoading()
        FilmProvider.rx.request(.showFilm(true)).mapObject(DDKCResponse<ShowFilmModel>.self)
            
            .subscribeOn(MainScheduler.instance)
            .subscribe(onNext:{ response in
                self.handlerResponse(response, success: {
                    guard let objects = response.ListObject else{
                        print("Data wrong")
                        self.dismissLoading()
                        return
                    }
                    self.films = self.filmsSpecial
                    self.bannerCarousel.reloadData()
                    self.btBuyTicket.isHidden = self.topicTab.selectedIndex == 2 && self.films.isEmpty
                    self.dismissLoading()
                }, error: {
                    self.dismissLoading()
                })
                
            }).disposed(by: disposeBag)
    }
    
    private func getPromotionCategory(){
        EcmProvider.rx.request(.getNewPromotion).mapObject(DDKCResponse<NewModel>.self)
            
            .subscribe(onNext:{[weak self] response in
                self?.handlerResponse(response, success: {
                    guard let objects = response.ListObject else{
                        print("Data wrong")
                        return
                    }
                    if objects.count > 0{
                        self?.getListPromotion(objects.first?.CategoryId)
                    }
                })
            }).disposed(by: disposeBag)
    }
    
    private func getListPromotion(_ categoryId: String?){
        guard let id = categoryId else {
            return
        }
        EcmProvider.rx.request(.getNewForCategory(id, 3, nil)).mapObject(DDKCResponse<NewsModel>.self)
            
            .subscribe(onNext:{[weak self] response in
                self?.handlerResponse(response, success: {
                    guard let items = response.ListObject else {return}
                    self?.promotions = items
                    DispatchQueue.main.async {
                        self?.tbPromotion.reloadData()
                    }
                })
            }).disposed(by: disposeBag)
    }
}

// Actions
extension HomeViewController {
    @IBAction func buyTicketBtPressed(_ sender: Any) {
        let index = bannerCarousel.currentItemIndex
        guard index >= 0, index < films.count else { return }

        let film = films[index]
        let vc = UIStoryboard.film[.filmChooseTime] as! FilmChooseTimeViewController
        vc.film = film
        Tracking.shared.selectMovie(movieId:vc.film?.FilmId,movieName: vc.film?.getName())
        show(vc)
    }
    
    @IBAction func searchButtonPressed(_ sender: Any) {
        let vc = UIStoryboard.cinema[.listAllCinema]
        show(vc, sender: sender)
    }

    @IBAction func showAllNewsButtonPressed(_ sender: Any) {
        let vc = UIStoryboard.home[.newsAndDeals]
        show(vc, sender: sender)
    }
}

extension HomeViewController: iCarouselDataSource {
    func numberOfItems(in carousel: iCarousel) -> Int {
        return films.count
    }

    func carousel(_ carousel: iCarousel, viewForItemAt index: Int, reusing view: UIView?) -> UIView {
        let film = films[index]
        let width = self.carouselItemWidth(carousel)
        guard let bannerView = view as? BannerCardView else {
            let bannerView = BannerCardView.nib()
            bannerView.fillData(film: film)
            bannerView.fitIn(width: width)
            bannerView.imvTop.isHidden = index != 0
            bannerView.onPlayButtonPressed = { [weak self] in
                self?.playVideo(film.TrailerURL ?? "")
            }
            return bannerView
        }
        
        bannerView.fillData(film: film)
        bannerView.fitIn(width: width)
        bannerView.imvTop.isHidden = index != 0
        bannerView.onPlayButtonPressed = { [weak self] in
            self?.playVideo(film.TrailerURL ?? "")
        }
        return bannerView
    }
}

extension HomeViewController: iCarouselDelegate {
    func carouselItemWidth(_ carousel: iCarousel) -> CGFloat {
        let width = view.frame.width * 272.0 / 375.0
        return width
    }

    func carousel(_ carousel: iCarousel, valueFor option: iCarouselOption, withDefault value: CGFloat) -> CGFloat {
        switch option {
        case .wrap:
            return 1
        case .spacing:
            return 0.5
        default:
            return value
        }
    }

    func carousel(_ carousel: iCarousel, didSelectItemAt index: Int) {
        let vc = UIStoryboard.film[.filmDetail] as! FilmDetailViewController
        vc.film = films[index]
        vc.newsData = self.promotions
        show(vc, sender: carousel)
    }

    func carouselCurrentItemIndexDidChange(_ carousel: iCarousel) {
        let index = carousel.currentItemIndex
        if index > films.count || index < 0 {
            vFilmDate.isHidden = true
            btBuyTicket.isHidden = true
            return
        }
        vFilmDate.isHidden = false
        let film = films[index]
        lbFilmDate.text = film.OpeningDate?.toDate("yyyy-MM-dd'T'HH:mm:ss")?.toString(dateFormat: "dd. MM. yyyy")
        btBuyTicket.isHidden = topicTab.selectedIndex == 0
    }
}

extension HomeViewController: UIScrollViewDelegate {
    func scrollViewDidScroll(_ scrollView: UIScrollView) {
        if scrollView.contentOffset.y < 0 {
            scrollView.contentOffset.y = 0
        }
    }
}

extension HomeViewController: UITableViewDataSource {
    func numberOfSections(in tableView: UITableView) -> Int {
        return 1
    }

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return promotions.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: newsCellId, for: indexPath) as! NewsTableViewCell
        cell.fillData(promotions[indexPath.row])

        return cell
    }
}

extension HomeViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: false)

        let vc = UIStoryboard.home[.newsDetail] as! NewsDetailViewController
        vc.type = NewType.news(promotions[indexPath.row])
        show(vc, sender: nil)
    }
}

extension HomeViewController: CLLocationManagerDelegate{
    func locationManager(_ manager: CLLocationManager, didChangeAuthorization status: CLAuthorizationStatus) {
        if status == .authorizedAlways || status == .authorizedWhenInUse {
            if CLLocationManager.isMonitoringAvailable(for: CLBeaconRegion.self) {
                if CLLocationManager.isRangingAvailable() {
                    guard let lat = locationManager.location?.coordinate.latitude, let lng = locationManager.location?.coordinate.longitude else {return}
                    print("LatLng:\(lat) - \(lng)")
                    UserDefaults.standard.set(lat, forKey: Constant.Lat)
                    UserDefaults.standard.set(lng, forKey: Constant.Lng)
                    UserDefaults.standard.synchronize()                                                 
                }
            }
        }
    }

    func locationManager(_ manager: CLLocationManager, didFailWithError error: Error) {

    }

    func locationManager(_ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]) {

    }
}

extension HomeViewController: ScrollPagerDelegate {
    func scrollPager(scrollPager: ScrollPager, changedIndex: Int) {
        switch changedIndex {
        case 0:// Prepare
            if self.filmsPrepare.count == 0{
                self.getListFilm(isShowing: false)
            }else{
                self.films = self.filmsPrepare
                self.bannerCarousel.reloadData()
            }
            btBuyTicket.isHidden = true
        case 1:// Showing
            if self.filmsShowing.count == 0{
                self.getListFilm(isShowing: true)
            }else{
                self.films = self.filmsShowing
                self.bannerCarousel.reloadData()
            }
            btBuyTicket.isHidden = self.filmsShowing.isEmpty
        case 2:// Special
            if self.filmsSpecial.count == 0{
                self.getListFilmSpecial()
            }else{
                self.films = self.filmsSpecial
                self.bannerCarousel.reloadData()
            }
            btBuyTicket.isHidden = self.filmsSpecial.isEmpty
        default:
            break
        }
    }
}
