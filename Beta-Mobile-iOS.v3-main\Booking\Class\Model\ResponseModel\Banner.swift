//
//  Banner.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 8/10/19.
//  Copyright © 2019 ddkc. All rights reserved.
//

import Foundation
import ObjectMapper

class Banner : Mappable {
    var id : Int?
    var sreenCode : Int?
    var name : String?
    var imageUrl : String?
    var refId : String?
    var refModel : String?
    var description : String?

    var url: String {
        guard let string = imageUrl else {
            return ""
        }
        return Config.BaseURLResource + string
    }

    var type: RouteType {
        guard let code = sreenCode, let type = RouteType(rawValue: code) else {
            return .none
        }
        return type
    }

    var params: [String] {
        guard let id = refId else {
            return []
        }
        return [id]
    }

    required init?(map: Map) {

    }

    func mapping(map: Map) {

        id <- map["Id"]
        sreenCode <- map["SreenCode"]
        name <- map["Name"]
        imageUrl <- map["ImageUrl"]
        refId <- map["RefId"]
        refModel <- map["RefModel"]
        description <- map["Description"]
    }

}
