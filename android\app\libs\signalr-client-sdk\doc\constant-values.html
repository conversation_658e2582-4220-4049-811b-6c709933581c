<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<title>Constant Field Values</title>
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="Constant Field Values";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="microsoft/aspnet/signalr/client/package-summary.html">Package</a></li>
<li>Class</li>
<li>Use</li>
<li><a href="microsoft/aspnet/signalr/client/package-tree.html">Tree</a></li>
<li><a href="index-files/index-1.html">Index</a></li>
<li><a href="help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="index.html?constant-values.html" target="_top">Frames</a></li>
<li><a href="constant-values.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Constant Field Values" class="title">Constant Field Values</h1>
<h2 title="Contents">Contents</h2>
<ul>
<li><a href="#microsoft.aspnet">microsoft.aspnet.*</a></li>
</ul>
</div>
<div class="constantValuesContainer"><a name="microsoft.aspnet">
<!--   -->
</a>
<h2 title="microsoft.aspnet">microsoft.aspnet.*</h2>
<ul class="blockList">
<li class="blockList">
<table border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>microsoft.aspnet.signalr.client.<a href="microsoft/aspnet/signalr/client/Constants.html" title="class in microsoft.aspnet.signalr.client">Constants</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="microsoft.aspnet.signalr.client.Constants.HTTP_GET">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="microsoft/aspnet/signalr/client/Constants.html#HTTP_GET">HTTP_GET</a></code></td>
<td class="colLast"><code>"GET"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="microsoft.aspnet.signalr.client.Constants.HTTP_POST">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="microsoft/aspnet/signalr/client/Constants.html#HTTP_POST">HTTP_POST</a></code></td>
<td class="colLast"><code>"POST"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="microsoft.aspnet.signalr.client.Constants.UTF8_NAME">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="microsoft/aspnet/signalr/client/Constants.html#UTF8_NAME">UTF8_NAME</a></code></td>
<td class="colLast"><code>"UTF-8"</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="microsoft/aspnet/signalr/client/package-summary.html">Package</a></li>
<li>Class</li>
<li>Use</li>
<li><a href="microsoft/aspnet/signalr/client/package-tree.html">Tree</a></li>
<li><a href="index-files/index-1.html">Index</a></li>
<li><a href="help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="index.html?constant-values.html" target="_top">Frames</a></li>
<li><a href="constant-values.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
