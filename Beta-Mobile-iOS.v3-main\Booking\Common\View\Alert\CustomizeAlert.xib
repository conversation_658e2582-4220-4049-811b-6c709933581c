<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14490.70" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14490.49"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="Oswald-Regular.ttf">
            <string>Oswald-Regular</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="CustomizeAlert" customModule="Booking_dev" customModuleProvider="target">
            <connections>
                <outlet property="alertHeight" destination="dPj-Lx-K9c" id="uu5-hC-wj7"/>
                <outlet property="alertView" destination="ojV-MV-IWo" id="BRT-9V-WMb"/>
                <outlet property="contentLabel" destination="sjJ-nU-kGC" id="wcL-o2-v43"/>
                <outlet property="okButton" destination="M4O-yJ-WAK" id="ATv-vi-XHl"/>
                <outlet property="successImageView" destination="vcr-KB-QSz" id="MR2-99-6hZ"/>
                <outlet property="titleLabel" destination="BXx-LM-USt" id="oUj-aR-a9q"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ojV-MV-IWo" customClass="RoundView" customModule="Booking_dev" customModuleProvider="target">
                    <rect key="frame" x="24" y="323" width="366" height="250"/>
                    <subviews>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="M4O-yJ-WAK">
                            <rect key="frame" x="0.0" y="210" width="366" height="40"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="40" id="0xd-sU-7SU"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <state key="normal" title="Ok"/>
                            <connections>
                                <action selector="didTapOK:" destination="-1" eventType="touchUpInside" id="rbi-vI-wWc"/>
                            </connections>
                        </button>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_success" translatesAutoresizingMaskIntoConstraints="NO" id="vcr-KB-QSz">
                            <rect key="frame" x="163" y="16" width="40" height="40"/>
                            <constraints>
                                <constraint firstAttribute="width" secondItem="vcr-KB-QSz" secondAttribute="height" id="MyZ-HQ-nW5"/>
                                <constraint firstAttribute="width" constant="40" id="PIa-4t-EaS"/>
                            </constraints>
                        </imageView>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="TẶNG VOUCHER THÀNH CÔNG" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="BXx-LM-USt">
                            <rect key="frame" x="8" y="68" width="350" height="24"/>
                            <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="16"/>
                            <color key="textColor" red="0.49411764705882355" green="0.82745098039215681" blue="0.12941176470588234" alpha="1" colorSpace="calibratedRGB"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="sjJ-nU-kGC">
                            <rect key="frame" x="16" y="108" width="334" height="20.5"/>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <color key="textColor" red="0.011764705882352941" green="0.011764705882352941" blue="0.011764705882352941" alpha="1" colorSpace="calibratedRGB"/>
                            <nil key="highlightedColor"/>
                        </label>
                    </subviews>
                    <color key="backgroundColor" red="0.97647058819999999" green="0.97647058819999999" blue="0.97647058819999999" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <constraints>
                        <constraint firstItem="BXx-LM-USt" firstAttribute="leading" secondItem="ojV-MV-IWo" secondAttribute="leading" constant="8" id="3Wm-7c-HWP"/>
                        <constraint firstAttribute="trailing" secondItem="BXx-LM-USt" secondAttribute="trailing" constant="8" id="Awm-ue-Uqk"/>
                        <constraint firstAttribute="trailing" secondItem="M4O-yJ-WAK" secondAttribute="trailing" id="PJj-QM-8T0"/>
                        <constraint firstAttribute="bottom" secondItem="M4O-yJ-WAK" secondAttribute="bottom" id="QCk-Do-ph2"/>
                        <constraint firstItem="vcr-KB-QSz" firstAttribute="top" secondItem="ojV-MV-IWo" secondAttribute="top" constant="16" id="UMw-6u-kPT"/>
                        <constraint firstItem="M4O-yJ-WAK" firstAttribute="leading" secondItem="ojV-MV-IWo" secondAttribute="leading" id="Y5c-l5-HLz"/>
                        <constraint firstItem="sjJ-nU-kGC" firstAttribute="leading" secondItem="ojV-MV-IWo" secondAttribute="leading" constant="16" id="a9T-E5-zch"/>
                        <constraint firstItem="vcr-KB-QSz" firstAttribute="centerX" secondItem="ojV-MV-IWo" secondAttribute="centerX" id="bHr-8R-JrP"/>
                        <constraint firstAttribute="height" constant="250" id="dPj-Lx-K9c"/>
                        <constraint firstAttribute="trailing" secondItem="sjJ-nU-kGC" secondAttribute="trailing" constant="16" id="ghn-9N-jUT"/>
                        <constraint firstItem="sjJ-nU-kGC" firstAttribute="top" secondItem="BXx-LM-USt" secondAttribute="bottom" constant="16" id="mJO-LN-7ni"/>
                        <constraint firstItem="BXx-LM-USt" firstAttribute="top" secondItem="vcr-KB-QSz" secondAttribute="bottom" constant="12" id="vgl-K3-SPa"/>
                    </constraints>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                            <real key="value" value="8"/>
                        </userDefinedRuntimeAttribute>
                    </userDefinedRuntimeAttributes>
                </view>
            </subviews>
            <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
            <constraints>
                <constraint firstItem="ojV-MV-IWo" firstAttribute="centerX" secondItem="i5M-Pr-FkT" secondAttribute="centerX" id="6DM-E5-BXc"/>
                <constraint firstItem="ojV-MV-IWo" firstAttribute="centerY" secondItem="i5M-Pr-FkT" secondAttribute="centerY" id="LZM-1L-2sE"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="ojV-MV-IWo" secondAttribute="trailing" constant="24" id="P0r-cD-1aM"/>
                <constraint firstItem="ojV-MV-IWo" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" constant="24" id="lh1-t3-erR"/>
            </constraints>
            <viewLayoutGuide key="safeArea" id="fnl-2z-Ty3"/>
        </view>
    </objects>
    <resources>
        <image name="ic_success" width="42" height="42"/>
    </resources>
</document>
