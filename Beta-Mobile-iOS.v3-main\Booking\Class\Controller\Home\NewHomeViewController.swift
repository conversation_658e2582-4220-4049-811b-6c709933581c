//
//  NewHomeViewController.swift
//  Booking
//
//  Created by <PERSON><PERSON> Vu on 8/5/19.
//  Copyright © 2019 ddkc. All rights reserved.
//

import UIKit
import ObjectMapper
import RxSwift
import ImageSlideshow
import AlamofireImage
import PullToRefreshKit
import CoreLocation

enum FilmStateType {
    case comming
    case showing
    case early
}

class NewHomeViewController: BaseViewController {

    @IBOutlet weak var collectionView: UICollectionView!

    @IBOutlet weak var commingSoonButton: UIButton!
    @IBOutlet weak var showingButton: UIButton!
    @IBOutlet weak var earlyShowButton: UIButton!

    @IBOutlet weak var avatarImageView: RoundImageView!
    @IBOutlet weak var nameLabel: UILabel!
    @IBOutlet weak var levelButton: UIButton!
    @IBOutlet weak var pointButton: UIButton!
    @IBOutlet weak var voucherButton: UIButton!
    @IBOutlet weak var slideShow: ImageSlideshow!
    @IBOutlet weak var pageControl: UIPageControl!
    @IBOutlet weak var loginButton: RoundButton!
    @IBOutlet weak var profileView: UIView!
    @IBOutlet weak var scrollView: UIScrollView!
    
    @IBOutlet weak var contentViewHeight: NSLayoutConstraint!
    @IBOutlet weak var topViewTop: NSLayoutConstraint!
    @IBOutlet weak var stackViewWidth: NSLayoutConstraint!

    private let cellRatio: CGFloat = 252/127
    private let numberOfCellInRow: CGFloat = 3

    fileprivate var films: [FilmModel] = []
    fileprivate var filmsPrepare: [FilmModel] = []
    fileprivate var filmsShowing: [FilmModel] = []
    fileprivate var filmsSpecial: [FilmModel] = []
    fileprivate var promotions: [NewsModel] = []
    private var banners: [Banner] = []

    private var stateType: FilmStateType = .showing
    private var gradientLayer: CAGradientLayer?
    private var gradientView: UIView?
    private var buttonLabel: UILabel?

    let header = DefaultRefreshHeader.header()

    override func viewDidLoad() {
        super.viewDidLoad()

        setupViews()
        initLocation()
        getData()


    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        self.navigationController?.isNavigationBarHidden = true
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)

        if Global.shared.isLogined {
            getProfile()
        } else {
            profileView.isHidden = true
            loginButton.isHidden = false
        }
    }

    override var preferredStatusBarStyle: UIStatusBarStyle {
        return .default
    }

    override func localizationDidChange() {
        updateButtonState()
        configRefresHeader()
    }

    func initLocation(){
        LocationManager.shared.startTracking()
    }

    private func getData() {
        let group = DispatchGroup()
        group.enter()
        switch stateType {
        case .comming:
            self.getListFilm(isShowing: false) {
                group.leave()
            }
        case .showing:
            self.getListFilm(completion: {
                group.leave()
            })
        case .early:
            self.getListFilm(isShowing: true, isSpecial: true) {
                group.leave()
            }
        }

        group.enter()
        self.getBanners {
            group.leave()
        }

        group.notify(queue: .main) {
            self.scrollView.switchRefreshHeader(to: .normal(.success, 0.3))
        }
    }

    private func setupViews() {

        collectionView.register(UINib(nibName: HomeCollectionViewCell.id, bundle: nil), forCellWithReuseIdentifier: HomeCollectionViewCell.id)

        let tapGestureRecognizer = UITapGestureRecognizer(target: self, action: #selector(imageTapped(tapGestureRecognizer:)))
        avatarImageView.isUserInteractionEnabled = true
        avatarImageView.addGestureRecognizer(tapGestureRecognizer)

        pageControl.currentPageIndicatorTintColor = UIColor.pageControlActive
        pageControl.pageIndicatorTintColor = UIColor.lightGray
        slideShow.pageIndicator = pageControl
        slideShow.slideshowInterval = 5.0
        slideShow.pageIndicatorPosition = PageIndicatorPosition(horizontal: .left(padding: 8.0), vertical: .bottom)
        slideShow.layer.cornerRadius = 5.0
        slideShow.clipsToBounds = true

        let gestureRecognizer = UITapGestureRecognizer(target: self, action: #selector(routeTapped))
        slideShow.addGestureRecognizer(gestureRecognizer)

        updateButtonState()

        configRefresHeader()
        scrollView.configRefreshHeader(with: header, container: self) {
            self.getData()
        }

        scrollView.contentInset = UIEdgeInsets(top: 0, left: 0, bottom: 70, right: 0)

        contentViewHeight.constant = screenHeight - 130
        if UIDevice.current.isIp4Inch {
            stackViewWidth.constant = 180
        }

        guard #available(iOS 11.0, *) else {
            topViewTop.constant = 20
            return
        }
    }

    private func configRefresHeader() {
        header.setText("pull_to_refresh".localized, mode: .pullToRefresh)
        header.setText("release_to_refresh".localized, mode: .releaseToRefresh)
        header.setText("success".localized, mode: .refreshSuccess)
        header.setText("refreshing".localized, mode: .refreshing)
        header.setText("failed".localized, mode: .refreshFailure)
        header.durationWhenHide = 0.4
    }

    private func updateButtonState() {

        switch stateType {
        case .comming:
            commingSoonButton.setLocalizableImage("tab_coming_vi_selected")
            showingButton.setLocalizableImage("tab_nowshowing_vi")
            earlyShowButton.setLocalizableImage("tab_sneak_vi")
        case .showing:
            commingSoonButton.setLocalizableImage("tab_coming_vi")
            showingButton.setLocalizableImage("tab_nowshowing_vi_selected")
            earlyShowButton.setLocalizableImage("tab_sneak_vi")
        case .early:
            commingSoonButton.setLocalizableImage("tab_coming_vi")
            showingButton.setLocalizableImage("tab_nowshowing_vi")
            earlyShowButton.setLocalizableImage("tab_sneak_vi_selected")
        }
    }

    @objc private func imageTapped(tapGestureRecognizer: UITapGestureRecognizer) {
        let memberVC = UIStoryboard.member[.member]
        memberVC.hidesBottomBarWhenPushed = true
        self.navigationController?.pushViewController(memberVC, animated: true)
    }

    @objc private func routeTapped() {
      if banners.isEmpty {
        return
      }
        let banner = banners[slideShow.currentPage]
        RouteManager(vc: self, type: banner.type, params: banner.params).route()
    }

    @IBAction func loginTapped(_ sender: Any) {
        let loginVC = UIStoryboard.authen[.login]
        loginVC.hidesBottomBarWhenPushed = true
        self.navigationController?.pushViewController(loginVC, animated: true)
    }


    @IBAction func commingTapped(_ sender: Any) {
        if stateType == .comming {
            return
        }
        stateType = .comming
        updateButtonState()
        getListFilm(isShowing: false)
    }

    @IBAction func showingTapped(_ sender: Any) {
        if stateType == .showing {
            return
        }
        stateType = .showing
        updateButtonState()
        getListFilm()
    }

    @IBAction func earlyTapped(_ sender: Any) {
        if stateType == .early {
            return
        }
        stateType = .early
        updateButtonState()
        getListFilm(isShowing: true, isSpecial: true)
    }

    @IBAction func levelTapped(_ sender: Any) {
        let memberVC = UIStoryboard.member[.member]
//        memberVC.hidesBottomBarWhenPushed = true
        show(memberVC)
    }

    @IBAction func pointTapped(_ sender: Any) {
        let vc = UIStoryboard.member[.rewardPoints] as! RewardPointsViewController
        vc.isFromRouter = true
//        vc.hidesBottomBarWhenPushed = true
        show(vc, sender: nil)
    }

    @IBAction func voucherTapped(_ sender: Any) {
        AppDelegate.shared.gotoMyVoucher()
    }
}

extension NewHomeViewController {

    private func getProfile(){
        AccountProvider.rx.request(.homeInfo).mapObject(DDKCResponse<HomePage>.self)
            .subscribe(onNext:{ [weak self] response in
                guard let object = response.Object else{
                    print("Data wrong")
                    return
                }
                if response.isSuccess(){
                    if let user = Global.shared.user{
                        let fullUser = user
                        fullUser.Picture = object.picture
                        fullUser.ClassCode = object.classCode
                        fullUser.QuantityOfVoucher = object.quantityOfVoucher
                        fullUser.AvailablePoint = object.availablePoint
                        Global.shared.saveUser(fullUser)
                    }

                    guard let self = self, let user = Global.shared.user else {
                        return
                    }

                    let csCopy = CharacterSet(bitmapRepresentation: CharacterSet.urlPathAllowed.bitmapRepresentation)
                    let imageURL = Config.BaseURLImage + (user.Picture?.addingPercentEncoding(withAllowedCharacters: csCopy) ?? "")
                    if let url = URL(string: imageURL) {
                        self.avatarImageView.af_setImage(withURL: url)
                    }

                    let description1 = "\("Home.Hi".localized)# \((user.FullName ?? ""))"
                    let strings = description1.components(separatedBy: "#")
                    let attributedString = NSMutableAttributedString()

                    attributedString.string(strings[0], font: UIFont(fontName: .SourceSansPro, style: .Regular, size: 14))
                    attributedString.string(strings[1], font: UIFont(fontName: .SourceSansPro, style: .Bold, size: 14))

                    self.nameLabel.attributedText = attributedString

                    self.levelButton.setTitle(user.ClassCode ?? "", for: .normal)
                    self.voucherButton.setTitle("\(user.QuantityOfVoucher ?? 0)", for: .normal)
                    self.pointButton.setTitle("\((user.AvailablePoint ?? 0).toCurrency(""))", for: .normal)

                    self.profileView.isHidden = false
                    self.loginButton.isHidden = true
                }

                }).disposed(by: disposeBag)
    }
    
    private func getBanners(completion: @escaping () -> Void) {
        FilmProvider.rx.request(.banner).mapObject(DDKCResponse<Banner>.self)
        .asObservable()
            .subscribe(onNext: { (response) in
                completion()
                self.handlerResponse(response, success: {
                    guard let objects = response.ListObject else{
                        print("Data wrong")
                        return
                    }
                    self.banners = objects
                    let images = objects.map{ AlamofireSource(urlString: $0.url)! }
                    self.slideShow.setImageInputs(images)
                }, error: {
                })
            }).disposed(by: disposeBag)
    }

    private func getListFilm(isShowing: Bool = true, isSpecial: Bool = false, completion: (() -> Void)? = nil){
        self.showLoading()
        FilmProvider.rx.request(.listFilm(isShowing)).mapObject(DDKCResponse<FilmModel>.self)
            .asObservable()
            .subscribeOn(MainScheduler.instance)
            .subscribe(onNext:{ response in
                completion?()
                self.handlerResponse(response, success: {
                    guard let objects = response.ListObject else{
                        print("Data wrong")
                        self.dismissLoading()
                        return
                    }

                    if isSpecial {
                        self.filmsSpecial = objects.filter{ $0.HasSneakShow == true }
                        self.films = self.filmsSpecial
                    } else {
                        if isShowing{
                            self.filmsShowing = objects
                            self.films = self.filmsShowing
                        }else{
                            self.filmsPrepare = objects
                            self.films = self.filmsPrepare
                        }
                    }
                    self.collectionView.isScrollEnabled = false
                    let rowNum = (Double(self.films.count) / Double(self.numberOfCellInRow)).rounded(.up)
                    let cellWidth = (self.collectionView.frame.size.width - 32) / self.numberOfCellInRow
                    let cellHeight = cellWidth * self.cellRatio + 37
                    self.view.layoutIfNeeded()
                    let height: CGFloat = CGFloat(rowNum) * cellHeight + 32 + self.slideShow.frame.height
                    self.contentViewHeight.constant = height
                    self.collectionView.reloadData()
                    self.dismissLoading()
                }, error: {
                    self.dismissLoading()
                })

            }, onError:{[weak self] error in
                guard let `self` = self else { return }
                self.dismissLoading()
                UIAlertController.showAlert(self, message: error.localizedDescription)
            }).disposed(by: disposeBag)
    }
}

extension NewHomeViewController: UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return films.count
    }

    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        guard let cell = collectionView.dequeueReusableCell(withReuseIdentifier: HomeCollectionViewCell.id, for: indexPath) as? HomeCollectionViewCell else {
            return HomeCollectionViewCell()
        }

        cell.fillData(film: films[indexPath.row], type: stateType)

        return cell
    }

    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        let width = (collectionView.frame.size.width - 32) / numberOfCellInRow
        let height = width * self.cellRatio + 37
        return CGSize(width: width, height: height)
    }

    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        collectionView.deselectItem(at: indexPath, animated: true)
        let film = films[indexPath.row]

        if film.HasShow == true {
            let vc = UIStoryboard.film[.filmChooseTime] as! FilmChooseTimeViewController
            vc.film = film
            vc.isFromRouter = true
            vc.fromHome = true
            vc.hidesBottomBarWhenPushed = true
            Tracking.shared.selectMovie(movieId:vc.film?.FilmId,movieName: vc.film?.getName())
            show(vc)
        } else {
            let vc = UIStoryboard.film[.filmDetail] as! FilmDetailViewController
            vc.film = film
            vc.newsData = self.promotions
            vc.hidesBottomBarWhenPushed = true
            self.navigationController?.pushViewController(vc, animated: true)
        }
    }
}
