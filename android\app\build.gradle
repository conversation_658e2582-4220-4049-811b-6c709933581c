plugins {
  id "com.android.application"
  id "kotlin-android"
  id "dev.flutter.flutter-gradle-plugin"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
  localPropertiesFile.withReader('UTF-8') { reader ->
    localProperties.load(reader)
  }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
  // ✅ CRITICAL: Must be higher than Android repo version (48) for CH Play update
  flutterVersionCode = '61'  // Increased to ensure update compatibility
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
  // ✅ CRITICAL: Must be higher than Android repo version (2.7.6) for CH Play update
  flutterVersionName = '2.8.1'  // Increased to ensure update compatibility
}

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
  keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

android {
  namespace "com.beta.betacineplex"  // ✅ Match production Android repo
  compileSdk 35
  ndkVersion flutter.ndkVersion

  compileOptions {
    sourceCompatibility JavaVersion.VERSION_17
    targetCompatibility JavaVersion.VERSION_17
  }

  kotlinOptions {
    jvmTarget = '17'
  }

  // JVM Toolchain to ensure consistency
  java {
    toolchain {
      languageVersion = JavaLanguageVersion.of(17)
    }
  }

  // R8 configuration to avoid compilation issues
  buildFeatures {
    buildConfig true
  }

  sourceSets {
    main.java.srcDirs += 'src/main/kotlin'
  }
  packagingOptions {
    exclude 'META-INF/proguard/androidx-annotations.pro'
    exclude 'lib/getLibs.ps1'
    exclude 'lib/getLibs.sh'
    pickFirst '**/lib/**'
  }
  defaultConfig {
    // ✅ Updated to match Android repo package name
    applicationId "com.beta.betacineplex"
    // You can update the following values to match your application needs.
    // For more information, see: https://docs.flutter.dev/deployment/android#reviewing-the-gradle-build-configuration.
    minSdkVersion 21
    targetSdkVersion 35
    versionCode flutterVersionCode.toInteger()
    versionName flutterVersionName
    multiDexEnabled true
  }

  signingConfigs {
    // 🔑 ===== PRODUCTION KEYSTORE FOR CH PLAY UPDATE =====
    // This MUST match the keystore used in Android repo for CH Play

    release {
      keyAlias 'beta cineplex'
      keyPassword 'Betacorpvn@123'
      storeFile file('keystore/beta_cineplex_app_key.jks')
      storePassword 'Betacorpvn@123'
    }

//    // 🔧 DEBUG KEYSTORE (for development only)
//    debug {
//      keyAlias 'debug'
//      keyPassword 'sdfoafojasdfji'
//      storeFile file('keystore/debug.keystore')
//      storePassword 'sdfoafojasdfji'
//    }
  }

  buildTypes {
    release {
      // Use keystore from key.properties (for simple switching method)
      signingConfig signingConfigs.release

      // Disable minification and shrinking to avoid R8 issues
      minifyEnabled false
      shrinkResources false

      // Disable ProGuard/R8 rules
      // proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
    }

    debug {
      // Use keystore from key.properties (for simple switching method)
      signingConfig signingConfigs.release
    }
  }
}

flutter {
  source '../..'
}

dependencies {
  implementation 'androidx.multidex:multidex:2.0.1'

  // SignalR dependencies
  implementation files('libs/signalr-client-sdk.jar')
  implementation files('libs/signalr-client-sdk-android.jar')
  implementation fileTree(dir: 'libs', include: ['*.jar'])
  implementation 'com.google.code.gson:gson:2.9.0'
  implementation 'io.reactivex.rxjava2:rxjava:2.2.21'
  implementation 'io.reactivex.rxjava2:rxandroid:2.1.1'
  implementation 'io.reactivex.rxjava2:rxkotlin:2.4.0'
}
