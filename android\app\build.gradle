plugins {
  id "com.android.application"
  id "kotlin-android"
  id "dev.flutter.flutter-gradle-plugin"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
  localPropertiesFile.withReader('UTF-8') { reader ->
    localProperties.load(reader)
  }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
  // ✅ Set higher than Android repo version (48) to allow updates
  flutterVersionCode = '60'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
  // ✅ Set higher than Android repo version (2.7.6) to allow updates
  flutterVersionName = '2.8.0'
}

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
  keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

android {
  namespace "com.beta.betacineplex"  // ✅ Match production Android repo
  compileSdk 35
  ndkVersion flutter.ndkVersion

  compileOptions {
    sourceCompatibility JavaVersion.VERSION_17
    targetCompatibility JavaVersion.VERSION_17
  }

  kotlinOptions {
    jvmTarget = '17'
  }

  // JVM Toolchain to ensure consistency
  java {
    toolchain {
      languageVersion = JavaLanguageVersion.of(17)
    }
  }

  // R8 configuration to avoid compilation issues
  buildFeatures {
    buildConfig true
  }

  sourceSets {
    main.java.srcDirs += 'src/main/kotlin'
  }

  defaultConfig {
    // ✅ Updated to match Android repo package name
    applicationId "com.beta.betacineplex"
    // You can update the following values to match your application needs.
    // For more information, see: https://docs.flutter.dev/deployment/android#reviewing-the-gradle-build-configuration.
    minSdkVersion 21
    targetSdkVersion 35
    versionCode flutterVersionCode.toInteger()
    versionName flutterVersionName
    multiDexEnabled true
  }

  signingConfigs {
    // 🔑 ===== KEYSTORE SELECTION =====
    // Uncomment ONE of the following configs to select keystore:

    // 🏭 PRODUCTION KEYSTORE (for release builds) - COMMENTED
     release {
         keyAlias 'beta cineplex'
         keyPassword 'Betacorpvn@123'
         storeFile file('../keystore/beta_cineplex_app_key.jks')
         storePassword 'Betacorpvn@123'
     }

    // 🔧 DEBUG KEYSTORE (for development) - ACTIVE
//    release {
//      keyAlias 'debug'
//      keyPassword 'sdfoafojasdfji'
//      storeFile file('../keystore/debug.keystore')
//      storePassword 'sdfoafojasdfji'
//    }

    // 👥 CUSTOMER KEYSTORE (for customer testing)
    // release {
    //     keyAlias 'customer'
    //     keyPassword 'sdiidfjieiurier'
    //     storeFile file('keystore/customer.keystore')
    //     storePassword 'sdiidfjieiurier'
    // }
  }

  buildTypes {
    release {
      // Use keystore from key.properties (for simple switching method)
      signingConfig signingConfigs.release

      // Disable minification and shrinking to avoid R8 issues
      minifyEnabled false
      shrinkResources false

      // Disable ProGuard/R8 rules
      // proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
    }

    debug {
      // Use keystore from key.properties (for simple switching method)
      signingConfig signingConfigs.release
    }
  }
}

flutter {
  source '../..'
}

dependencies {
  implementation 'androidx.multidex:multidex:2.0.1'

  // SignalR dependencies
  implementation files('libs/signalr-client-sdk.jar')
  implementation files('libs/signalr-client-sdk-android.jar')
  implementation fileTree(dir: 'libs', include: ['*.jar'])
  implementation 'com.google.code.gson:gson:2.9.0'
  implementation 'io.reactivex.rxjava2:rxjava:2.2.21'
  implementation 'io.reactivex.rxjava2:rxandroid:2.1.1'
  implementation 'io.reactivex.rxjava2:rxkotlin:2.4.0'

  // OkHttp for network requests
  implementation 'com.squareup.okhttp3:okhttp:4.9.3'
  implementation 'com.squareup.okhttp3:logging-interceptor:4.9.3'


  // For modern ASP.NET Core SignalR (if needed)
  // implementation 'com.microsoft.signalr:signalr:5.0.17'
}
