//
//  UIAlertController+Ext.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 4/8/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit

extension UIAlertController {
    static func showAlert(_ viewController: UIViewController, title: String? = nil, message: String, button: String = "Bt.OK".localized, handler: ((UIAlertAction) -> Swift.Void)? = nil) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: button.localized, style: .default, handler: handler))
        viewController.present(alert, animated: true, completion: nil)
    }
    
    static func showConfirm(_ viewController: UIViewController, title: String? = nil, message: String? = nil, okButton: String = "Bt.Yes".localized, cancelButton: String = "Bt.OK".localized, okHandler: ((UIAlertAction) -> Void)? = nil, cancelHandler: ((UIAlertAction) -> Void)? = nil) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: okButton, style: .cancel, handler: okHandler))
        alert.addAction(UIAlertAction(title: cancelButton, style: .default, handler: cancelHandler))
        viewController.present(alert, animated: true, completion: nil)
    }
    
    static func showConfirmRed(_ viewController: UIViewController, title: String? = nil, message: String? = nil, okButton: String = "Bt.Yes".localized, cancelButton: String = "Bt.OK".localized, okHandler: ((UIAlertAction) -> Void)? = nil, cancelHandler: ((UIAlertAction) -> Void)? = nil) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: okButton, style: .destructive, handler: okHandler))
        alert.addAction(UIAlertAction(title: cancelButton, style: .default, handler: cancelHandler))
        viewController.present(alert, animated: true, completion: nil)
    }

    static func showActionSheet(in viewController: UIViewController, title: String? = nil, message: String? = nil, list: [String] = [], hanlder: ((Int?) -> Void)? = nil) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .actionSheet)
        for item in list {
            let action = UIAlertAction(title: item, style: .default, handler: { (action) in
                hanlder?(list.index(of: item)!)
            })
            alert.addAction(action)
        }
        let cancelAction = UIAlertAction(title: "Bt.Cancel".localized, style: .cancel) { (action) in
            hanlder?(nil)
        }
        alert.addAction(cancelAction)
        viewController.present(alert, animated: true, completion: nil)
    }

    static func showAttributeMessage(_ viewController: UIViewController, messages: [String]) {
        let alert = UIAlertController(title: nil, message: "", preferredStyle: .alert)

        guard messages.count == 4 else {
            return
        }

        if messages[3].isEmpty  {
            let message = messages[0] + "\n" + messages[2]
            let highlight = messages[1]

            let contents = message.replacingOccurrences(of: highlight, with: "\(highlight)#")

            let strings = contents.components(separatedBy: "#")
            let attributedString = NSMutableAttributedString()

            attributedString.string(strings[0], font: UIFont(fontName: .SourceSansPro, style: .Bold, size: 16))
            attributedString.string(strings[1], font: UIFont(fontName: .SourceSansPro, style: .Regular, size: 16))

            alert.setValue(attributedString, forKey: "attributedMessage")
        } else if messages.count == 4 {
            let message1 = messages[0]
            let highlight1 = messages[1]

            let message2 = messages[2]
            let highlight2 = messages[3]

            let contents1 = message1.replacingOccurrences(of: highlight1, with: "#\(highlight1) \n")
            let contents2 = message2.replacingOccurrences(of: highlight2, with: "#\(highlight2)")

            let strings1 = contents1.components(separatedBy: "#")
            let strings2 = contents2.components(separatedBy: "#")
            let attributedString = NSMutableAttributedString()

            attributedString.string(strings1[0], font: UIFont(fontName: .SourceSansPro, style: .Regular, size: 16))
            attributedString.string(strings1[1], font: UIFont(fontName: .SourceSansPro, style: .Bold, size: 16))

            attributedString.string(strings2[0], font: UIFont(fontName: .SourceSansPro, style: .Regular, size: 16))
            attributedString.string(strings2[1], font: UIFont(fontName: .SourceSansPro, style: .Bold, size: 16))

            alert.setValue(attributedString, forKey: "attributedMessage")
        }

        let okAction = UIAlertAction(title: "Bt.Yes".localized, style: .cancel)
        alert.addAction(okAction)
        viewController.present(alert, animated: true, completion: nil)
    }
}
