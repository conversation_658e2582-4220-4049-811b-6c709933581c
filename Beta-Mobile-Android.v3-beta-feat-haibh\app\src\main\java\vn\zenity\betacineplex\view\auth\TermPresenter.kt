package vn.zenity.betacineplex.view.auth

import vn.zenity.betacineplex.Manager.Network.APIClient
import vn.zenity.betacineplex.app.App
import vn.zenity.betacineplex.helper.extension.applyOn
import java.lang.ref.WeakReference

/**
 * Created by Zenity.
 */

class TermPresenter : TermContractor.Presenter {
    override fun getTerm() {
        val lang = App.shared().getCurrentLang()
        APIClient.shared.ecmAPI.getTermId("mobile:app:dieukhoan:$lang").applyOn()
                .subscribe({
                    if (it.isSuccess) {
                        it.Data?.ParameterValue?.let {
                            APIClient.shared.ecmAPI.getNewWithId(it).applyOn()
                                    .subscribe({
                                        if (it.Data != null) {
                                            view?.get()?.showTerm(it.Data!!)
                                        }
                                    }, {
                                    })
                        }
                    }
                }, {

                })
    }

    private var view: WeakReference<TermContractor.View?>? = null
    override fun attachView(view: TermContractor.View) {
        this.view = WeakReference(view)
    }

    override fun detachView() {
        this.view?.clear()
        this.view = null
    }
}
