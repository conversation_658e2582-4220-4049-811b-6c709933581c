package vn.zenity.betacineplex.view.auth

import android.content.Context
import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView

interface LoginContractor {
    interface View : IBaseView {
        fun showHome()
        fun showForgotPassword(email: String?)
        fun showRegister()
        fun showLoginError(error: String)
        fun showUpdateFBPassword()
        fun getViewContext(): Context?
    }

    interface Presenter : IBasePresenter<View> {
        fun login(email: String, password: String, captchaToken: String)
        fun forgotPassword(email: String?)
        fun register()
        fun loginFacebook(fbId: String, token: String, captchaToken: String)
        fun registerFCMToken(token: String)
    }
}