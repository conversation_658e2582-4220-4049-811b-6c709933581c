<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14490.70" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14490.49"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="Oswald-Bold.ttf">
            <string>Oswald-Bold</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="TabOtherViewController" customModule="Booking_dev" customModuleProvider="target">
            <connections>
                <outlet property="collectionView" destination="uHQ-4S-Zdp" id="jiL-jX-gw1"/>
                <outlet property="customizeTitleLabel" destination="2be-8p-a07" id="rUV-ke-Wd1"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <collectionView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" dataMode="none" translatesAutoresizingMaskIntoConstraints="NO" id="uHQ-4S-Zdp">
                    <rect key="frame" x="0.0" y="108" width="414" height="754"/>
                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <collectionViewFlowLayout key="collectionViewLayout" minimumLineSpacing="10" minimumInteritemSpacing="10" id="QeE-Y1-Hvh">
                        <size key="itemSize" width="50" height="50"/>
                        <size key="headerReferenceSize" width="0.0" height="0.0"/>
                        <size key="footerReferenceSize" width="0.0" height="0.0"/>
                        <inset key="sectionInset" minX="0.0" minY="0.0" maxX="0.0" maxY="0.0"/>
                    </collectionViewFlowLayout>
                    <connections>
                        <outlet property="dataSource" destination="-1" id="oP6-L2-Pum"/>
                        <outlet property="delegate" destination="-1" id="MGO-S2-dF2"/>
                    </connections>
                </collectionView>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ZJe-Xq-NOk">
                    <rect key="frame" x="0.0" y="44" width="414" height="64"/>
                    <subviews>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Khác" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="2be-8p-a07">
                            <rect key="frame" x="16" y="0.5" width="65" height="47.5"/>
                            <fontDescription key="fontDescription" name="Oswald-Bold" family="Oswald" pointSize="32"/>
                            <color key="textColor" red="0.13333333333333333" green="0.2196078431372549" blue="0.28627450980392155" alpha="1" colorSpace="calibratedRGB"/>
                            <nil key="highlightedColor"/>
                        </label>
                    </subviews>
                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstAttribute="bottom" secondItem="2be-8p-a07" secondAttribute="bottom" constant="16" id="ONq-He-lCK"/>
                        <constraint firstAttribute="height" constant="64" id="mD2-9A-5ys"/>
                        <constraint firstItem="2be-8p-a07" firstAttribute="leading" secondItem="ZJe-Xq-NOk" secondAttribute="leading" constant="16" id="qfM-w1-HP5"/>
                    </constraints>
                </view>
            </subviews>
            <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
            <constraints>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="ZJe-Xq-NOk" secondAttribute="trailing" id="3ko-pb-J2J"/>
                <constraint firstItem="uHQ-4S-Zdp" firstAttribute="top" secondItem="ZJe-Xq-NOk" secondAttribute="bottom" id="9fq-YF-k9Q"/>
                <constraint firstItem="uHQ-4S-Zdp" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" id="DZn-LL-j2l"/>
                <constraint firstItem="ZJe-Xq-NOk" firstAttribute="top" secondItem="fnl-2z-Ty3" secondAttribute="top" id="MuO-6Y-URw"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="bottom" secondItem="uHQ-4S-Zdp" secondAttribute="bottom" id="NE1-Lg-9g4"/>
                <constraint firstItem="ZJe-Xq-NOk" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" id="n4X-CX-ikl"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="uHQ-4S-Zdp" secondAttribute="trailing" id="v3W-gm-qr7"/>
            </constraints>
            <viewLayoutGuide key="safeArea" id="fnl-2z-Ty3"/>
            <point key="canvasLocation" x="137.68115942028987" y="124.55357142857142"/>
        </view>
    </objects>
</document>
