//
//  FilmModel.swift
//  Booking
//
//  Created by Tinh Vu on 4/3/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation
import ObjectMapper

protocol FilmInformation {
    func getName() -> String?
    func getFormatName() -> String?
    func getFilmPoster() -> String
    func getFullOptions() -> String?
    func getFilmRestrictAgeName() -> String?
    func getFinalOptions(_ format: String?) -> String?

  var formatName: String {get}
}

class FilmFormat: Mappable {
    var FilmFormatName : String?
    var FilmFormatName_F : String?
    var FilmFormatCode : String?

    required init?(map: Map) {

    }

    func mapping(map: Map) {
        FilmFormatName       <- map["FilmFormatName"]
        FilmFormatName_F     <- map["FilmFormatName_F"]
        FilmFormatCode <- map["FilmFormatCode"]
    }
}

class FilmModel : Mappable, FilmInformation {

    var formatName: String {
        return getFilmFormatFromList() ?? getFilmFormatName() ?? ""
    }

    var FilmId : String?
    var Code : String?
    var Name : String?
    var Name_F : String?
    var ShortDescription : String?
    var ShortDescription_F : String?
    var OpeningDate : String?
    var Rate : String?
    var Duration : Int?
    var FilmGenreName : String?
    var FilmGenreName_F : String?
    var FilmRestrictAgeName : String?
    var SubtitleName : String?
    var SubtitleName_F : String?
    var FilmFormatName : String?
    var FilmFormatName_F : String?
    var TrailerURL : String?
    var Order : String?
    var Status : Bool?
    var NowShowing : Bool?
    var Director : String?
    var Actors: String?
    var MainPosterUrl : String?
    var ListPosterUrl : [ListPosterUrlModel]?
    var ListFilmGenre : [ListFilmGenreModel]?
    var MainLanguage: String?
    var RestrictAgeString: String?
    var DubbingName: String?
    var HasShow: Bool?
    var HasSneakShow: Bool?
    var IsHot: Bool = false

    var filmFormat: [FilmFormat]?

    required init?(map: Map) {

    }
    
    init() {
        
    }

    func mapping(map: Map) {
        FilmId               <- map["FilmGroupId"]
        Code                 <- map["Code"]
        Name                 <- map["Name"]
        Name_F               <- map["Name_F"]
        ShortDescription     <- map["ShortDescription"]
        ShortDescription_F   <- map["ShortDescription_F"]
        OpeningDate          <- map["OpeningDate"]
        Rate                 <- map["Rate"]
        Duration             <- map["Duration"]
        FilmGenreName        <- map["FilmGenreName"]
        FilmGenreName_F      <- map["FilmGenreName_F"]
        FilmRestrictAgeName  <- map["FilmRestrictAgeName"]
        SubtitleName         <- map["SubtitleName"]
        SubtitleName_F       <- map["SubtitleName_F"]
        FilmFormatName       <- map["FilmFormatName"]
        FilmFormatName_F     <- map["FilmFormatName_F"]
        TrailerURL           <- map["TrailerURL"]
        Order                <- map["Order"]
        Status               <- map["Status"]
        NowShowing           <- map["NowShowing"]
        Director             <- map["Director"]
        Actors             <- map["Actors"]
        MainPosterUrl        <- map["MainPosterUrl"]
        ListPosterUrl        <- map["ListPosterUrl"]
        ListFilmGenre        <- map["ListFilmGenre"]
        MainLanguage    <- map["MainLanguage"]
        RestrictAgeString    <- map["RestrictAge"]
        DubbingName    <- map["DubbingName"]
        HasShow <- map["HasShow"]
        HasSneakShow <- map["HasSneakShow"]
        IsHot <- map["IsHot"]
        filmFormat <- map["ListFilmFormat"]
        // temp fix
        if let count = OpeningDate?.count, count > 19 {
            let range = OpeningDate!.index(OpeningDate!.startIndex, offsetBy: 19)..<OpeningDate!.endIndex
            OpeningDate?.removeSubrange(range)
        }
    }

    func getFilmRestrictAgeName() -> String? {
        return FilmRestrictAgeName
    }
    
    func getName() -> String? {
        return Utils.shared.isEng() ? Name_F : Name
    }
    
    func getFormatName() -> String?{
        let isEn = Utils.shared.isEng()
        guard let formatName = isEn ? FilmFormatName_F : FilmFormatName else {
            return ""
        }
        return "(" + formatName + ")"
    }

    func getFilmPoster() -> String {
        var poster = MainPosterUrl
        if poster == nil {
            poster = ListPosterUrl?.compactMap { $0.AbsolutePath }.first
        }

        return Config.BaseURLResource + (poster ?? "")
    }
    
    func getFormatNameInDetail() -> String?{
        guard let formatName = DubbingName else{
            return nil
        }

        return " \(formatName)"
    }
    
    func getShortDescription() -> String?{
        return Utils.shared.isEng() ? ShortDescription_F : ShortDescription
    }
    
    func getSubtitle() -> String?{
        return Utils.shared.isEng() ? SubtitleName_F : SubtitleName
    }
    
    func getFilmFormatName() -> String? {
        return Utils.shared.isEng() ? FilmFormatName_F : FilmFormatName
    }

    func getFilmFormatFromList() -> String? {
        return Utils.shared.isEng() ? filmFormat?.first?.FilmFormatName_F : filmFormat?.first?.FilmFormatName
    }
    
    func getFilmGenre() -> String?{
        if let filmsGenre = self.ListFilmGenre, !filmsGenre.isEmpty {
            let genre = Array(filmsGenre.prefix(2)).compactMap{ Utils.shared.isEng() ? $0.Name_F : $0.Name }.joined(separator: ", ")
            print("first 2 genre: \(filmsGenre.prefix(2).count)")
            return genre
        }
        return Utils.shared.isEng() ? FilmGenreName_F : FilmGenreName
    }

    func getFilmGenreList() -> String {
        return ListFilmGenre?.compactMap { Utils.shared.isEng() ? $0.Name_F : $0.Name }.joined(separator: ", ") ?? ""
    }

    func getLanguage() -> String? {
        return MainLanguage
    }

    func getOpenDate() -> String {
        return Date.dateFromServerSavis(OpeningDate ?? "").toString(dateFormat: "HH:mm dd/MM/yyyy")
    }

    func getFullOptions() -> String? {
        var option = ""
        option += (getFilmFormatFromList() ?? getFilmFormatName() ?? "") + " | "
        option += (getOpenDate()) + " | "
        option += "\(Duration ?? 0) " + "Home.Minute".localized

        return option
    }

    func getHalfOptions() -> String? {
        var option = ""
        option += (getFilmGenre() ?? "") + " | "
        option += "\(Duration ?? 0) " + "Home.Minute".localized

        return option
    }

    func getFinalOptions(_ format: String?) -> String? {
        var option = ""
        option += (format ?? getFilmFormatFromList() ?? getFilmFormatName() ?? "") + " | "
        option += (getFilmGenre() ?? "") + " | "
        option += "\(Duration ?? 0) " + "Home.Minute".localized

        return option
    }

    func getFilmURL() -> String {
        return Config.BaseURLWeb + "/chi-tiet-phim.htm?gf=\(FilmId ?? "")"
    }

    enum RestrictAge: String {
        case c13, c16, c18, p

        static func ==(_ left: RestrictAge, right: String?) -> Bool {
            return left.rawValue == right?.lowercased()
        }

        static func ==(_ left: String?, right: RestrictAge) -> Bool {
            return left?.lowercased() == right.rawValue
        }
    }

}
