package vn.zenity.betacineplex.view.auth

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import com.facebook.*
import com.facebook.login.LoginResult
import kotlinx.android.synthetic.main.fragment_login.*
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.base.BaseActivity
import vn.zenity.betacineplex.base.BaseFragment
import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.global.Constant
import vn.zenity.betacineplex.global.Tracking
import vn.zenity.betacineplex.helper.extension.*
import vn.zenity.betacineplex.service.social.FacebookService
import vn.zenity.betacineplex.view.HomeActivity
import java.lang.ref.WeakReference
import kotlin.text.isEmpty

/**
 * Created by vinh on 4/3/18.
 */
class LoginFragment : BaseFragment(), LoginContractor.View {

    private val presenter = LoginPresenter()

    override fun getPresenter(): IBasePresenter<IBaseView>? {
        return presenter as IBasePresenter<IBaseView>
    }

    override fun getLayoutRes(): Int = R.layout.fragment_login
    override val isTransfStatus = false

    private var callbackManager = CallbackManager.Factory.create()
    private var facebookService = FacebookService()
    private val self = WeakReference(this)
    private val fbCallback = object : FacebookCallback<LoginResult> {
        override fun onSuccess(loginResult: LoginResult) {
            logD("LoginFacebook onSuccess")
            Tracking.share().authComplete(getViewContext(),"facebook")
            self.get()?.requestFacebookUserData(loginResult.accessToken)
        }

        override fun onCancel() {
            logD("LoginFacebook canceled")
            self.get()?.hideLoading()
        }

        override fun onError(exception: FacebookException) {
            self.get()?.hideLoading()
            logD("Loginfacebook onError $exception")
            exception.printStackTrace()
            toast(exception.localizedMessage ?: (exception.message ?: return))
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        btnRegister?.setOnClickListener {
            presenter.register()
        }

        btnLogin?.setOnClickListener {
            val email = edtEmail.text.trim()
            val password = edtPassword.text
            if (email.isEmpty()) {
                return@setOnClickListener
            }

            if (password.isEmpty()) {
                return@setOnClickListener
            }
            (activity as? BaseActivity)?.checkCaptcha {
                presenter.login(edtEmail.text.trim(), edtPassword.text, it ?: "")
            }
        }

        forgotPass?.setOnClickListener {
            presenter.forgotPassword(edtEmail.text?.trim())
        }
        facebookService.setFragment(this)
        btnLoginFacebook?.setOnClickListener {
            showLoading()
            Tracking.share().authBegin(getViewContext(),"facebook")
            if (facebookService.isLogin()) {
                val accessToken = facebookService.accessToken()
                requestFacebookUserData(accessToken!!)
            } else {
                facebookService.performLogin()
            }
        }

        facebookService.registerCallback(callbackManager, fbCallback)
        context?.let { facebookService.performLogout(it) }
    }

    private fun requestFacebookUserData(accessToken: AccessToken) {
        val request = GraphRequest.newMeRequest(accessToken) { json, data ->
            if (json?.has("email") == true) {
                self.get()?.presenter?.loginFacebook(accessToken.userId, accessToken.token, "")
            } else {
                self.get()?.context?.let { self.get()?.facebookService?.performLogout(it) }
                self.get()?.activity?.runOnUiThread {
                    self.get()?.hideLoading()
                    self.get()?.showNotice("Không thể lấy email từ facebook của bạn")
                }
            }
        }
        val parameters = Bundle()
        parameters.putString("fields", "id,email")
        request.parameters = parameters
        request.executeAsync()
    }

    override fun showHome() {
        if (activity != null && activity is HomeActivity) {
            super.back()
        } else if (activity != null && activity is LoginActivity){
            val intent = Intent(activity, HomeActivity::class.java)
            startActivity(intent)
            activity?.finish()
        }
    }

    override fun back() {
        if (activity != null && activity is HomeActivity) {
            super.back()
        } else if (activity != null && activity is LoginActivity){
            (activity as LoginActivity).finish()
        }
    }

    override fun showForgotPassword(email: String?) {
        openFragment(ForgotPasswordFragment())
    }

    override fun showRegister() {
        openFragment(RegisterFragment())
    }

    override fun showLoginError(error: String) {
        activity?.runOnUiThread {
            showNotice(error)
        }
    }

    override fun showUpdateFBPassword() {
        openFragment(ChangePasswordFragment.getInstance {
            activity?.runOnUiThread {
                btnLogin?.postDelayed({
                    showHome()
                }, 200)
            }
        })
    }

    override fun onResume() {
        super.onResume()
        presenter.attachView(this)
    }

    override fun onPause() {
        super.onPause()
        presenter.detachView()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        self.clear()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        logD("LoginFragment onActivityResult requestCode = $requestCode,resultCode = $resultCode")
        logD("LoginFragment onActivityResult data = $data")
        callbackManager.onActivityResult(requestCode, resultCode, data)
        super.onActivityResult(requestCode, resultCode, data)
    }

    override fun getViewContext(): Context? {
        return context;
    }
}