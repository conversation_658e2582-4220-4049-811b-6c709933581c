<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14109" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14088"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="SourceSansPro-Regular.ttf">
            <string>SourceSansPro-Regular</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="none" indentationWidth="10" rowHeight="86" id="Ovm-54-cCV" customClass="SwitchTableCell" customModule="Booking_dev" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="375" height="86"/>
            <autoresizingMask key="autoresizingMask"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="Ovm-54-cCV" id="pwi-qD-rnc">
                <rect key="frame" x="0.0" y="0.0" width="375" height="85.5"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="QL5-LV-xuT" customClass="RoundView" customModule="Booking_dev" customModuleProvider="target">
                        <rect key="frame" x="8" y="3" width="359" height="79.5"/>
                        <subviews>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="fYw-oT-tJX">
                                <rect key="frame" x="12" y="15" width="276" height="49.5"/>
                                <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                <color key="textColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <switch opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="750" verticalHuggingPriority="750" contentHorizontalAlignment="center" contentVerticalAlignment="center" on="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Rhm-cn-RBB">
                                <rect key="frame" x="300" y="25" width="51" height="31"/>
                            </switch>
                        </subviews>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="Rhm-cn-RBB" firstAttribute="leading" secondItem="fYw-oT-tJX" secondAttribute="trailing" constant="12" id="2hN-W7-Pe1"/>
                            <constraint firstItem="Rhm-cn-RBB" firstAttribute="centerY" secondItem="QL5-LV-xuT" secondAttribute="centerY" id="M39-2M-IXB"/>
                            <constraint firstItem="fYw-oT-tJX" firstAttribute="top" secondItem="QL5-LV-xuT" secondAttribute="top" constant="15" id="QQv-Sw-cNb"/>
                            <constraint firstAttribute="bottom" secondItem="fYw-oT-tJX" secondAttribute="bottom" constant="15" id="c6J-Kx-RR7"/>
                            <constraint firstAttribute="trailing" secondItem="Rhm-cn-RBB" secondAttribute="trailing" constant="10" id="wl1-gs-XcU"/>
                            <constraint firstItem="fYw-oT-tJX" firstAttribute="leading" secondItem="QL5-LV-xuT" secondAttribute="leading" constant="12" id="xdd-bl-WgZ"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                <real key="value" value="2"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </view>
                </subviews>
                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                <constraints>
                    <constraint firstAttribute="trailing" secondItem="QL5-LV-xuT" secondAttribute="trailing" constant="8" id="arh-ig-vCx"/>
                    <constraint firstItem="QL5-LV-xuT" firstAttribute="top" secondItem="pwi-qD-rnc" secondAttribute="top" constant="3" id="fdz-ee-hVZ"/>
                    <constraint firstItem="QL5-LV-xuT" firstAttribute="leading" secondItem="pwi-qD-rnc" secondAttribute="leading" constant="8" id="qJD-jl-ciK"/>
                    <constraint firstAttribute="bottom" secondItem="QL5-LV-xuT" secondAttribute="bottom" constant="3" id="s81-0O-raP"/>
                </constraints>
            </tableViewCellContentView>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <connections>
                <outlet property="lbTitle" destination="fYw-oT-tJX" id="X7j-bc-lXK"/>
                <outlet property="switchView" destination="Rhm-cn-RBB" id="fw2-pt-2a0"/>
            </connections>
            <point key="canvasLocation" x="-75.5" y="96"/>
        </tableViewCell>
    </objects>
</document>
