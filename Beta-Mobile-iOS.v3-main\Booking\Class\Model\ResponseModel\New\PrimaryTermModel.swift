//
//  PrimaryTermModel.swift
//  Booking
//
//  Created by <PERSON><PERSON>u on 4/5/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation
import ObjectMapper

class PrimaryTermModel : Mappable {
     var CategoryId : String?
     var ParentCategoryId : String?
     var Description : String?
     var Order : Int?
     var SubChild : [AnyObject]?
     var Level : Int?
     var termId : String?
     var Name : String?


    required init?(map: Map) {

    }

    func mapping(map: Map) {
        CategoryId           <- map["CategoryId"]
        ParentCategoryId     <- map["ParentCategoryId"]
        Description          <- map["Description"]
        Order                <- map["Order"]
        SubChild             <- map["SubChild"]
        Level                <- map["Level"]
        termId               <- map["termId"]
        Name                 <- map["Name"]
    }
}
