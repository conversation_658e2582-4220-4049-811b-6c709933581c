import '../../utils/src/error_code_mapper.dart';

class MApi {
  MApi({
    num? code,
    String? message,
    num? totalTime,
    bool? isSuccess,
    dynamic data,
    dynamic listObject,
    dynamic object,
    String? errorDetail,
  }) {
    _code = code;
    _message = message;
    _totalTime = totalTime;
    _isSuccess = isSuccess;
    _data = data;
    _listObject = listObject;
    _object = object;
    _errorDetail = errorDetail;
  }

  MApi.fromJson(dynamic json) {
    if (json is List) {
      _code = 0;
      _message;
      _totalTime = 0;
      _isSuccess;
      _data = json;
      _errorDetail = '';
    } else {
      _code = json['Status'] ?? 0;
      _message = ApiErrorHandler.handleError(
        json['message'] ?? json['Message'] ?? '',
        showOriginalIfNotMapped: true,
      );
      _totalTime = json['SUCCESS Time'] ?? 0;
      _isSuccess = json['isSuccess'] ?? false;
      // _data = json['data'] ?? json;
      _data = json['Data'] ?? json;
      _listObject = json['ListObject'] ?? [];
      _object = json['Object'];
      _errorDetail = json['errorDetail'] ?? '';
    }
  }

  num? _code;
  String? _message;
  num? _totalTime;
  bool? _isSuccess;
  dynamic _data;
  dynamic _listObject;
  dynamic _object;
  String? _errorDetail;

  MApi copyWith({
    num? code,
    String? message,
    num? totalTime,
    bool? isSuccess,
    dynamic data,
    dynamic listObject,
    dynamic object,
    String? errorDetail,
  }) =>
      MApi(
        code: code ?? _code,
        message: message ?? _message,
        totalTime: totalTime ?? _totalTime,
        isSuccess: isSuccess ?? _isSuccess,
        data: data ?? _data,
        listObject: listObject ?? _listObject,
        object: object ?? _object,
        errorDetail: errorDetail ?? _errorDetail,
      );

  num? get code => _code;

  String? get message => _message;

  num? get totalTime => _totalTime;

  bool get isSuccess => _isSuccess ?? false;

  dynamic get data => _data;

  dynamic get listObject => _listObject;

  dynamic get object => _object;

  String? get errorDetail => _errorDetail;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['code'] = _code;
    map['message'] = _message;
    map['totalTime'] = _totalTime;
    map['isSuccess'] = _isSuccess;
    map['Data'] = _data;
    map['ListObject'] = _listObject;
    map['Object'] = _object;
    map['errorDetail'] = _errorDetail;
    return map;
  }
}
