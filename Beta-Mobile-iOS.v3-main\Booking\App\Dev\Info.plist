<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>Beta Cinemas Dev</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>Betacineplex</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLName</key>
			<string>com.beta.betacineplex.dev</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>betacineplexx</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>fb367174740769877</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>Fabric</key>
	<dict>
		<key>APIKey</key>
		<string>da44f5feea4b0f520db5482d4c4c4243137ab224</string>
		<key>Kits</key>
		<array>
			<dict>
				<key>KitInfo</key>
				<dict/>
				<key>KitName</key>
				<string>Crashlytics</string>
			</dict>
		</array>
	</dict>
	<key>FacebookAppID</key>
	<string>367174740769877</string>
    <key>FacebookClientToken</key>
    <string>********************************</string>
	<key>FacebookDisplayName</key>
	<string>Betacineplex</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>itms</string>
		<string>fbapi</string>
		<string>fb-messenger-share-api</string>
		<string>fbauth2</string>
		<string>fbshareextension</string>
		<string>googlechromes</string>
		<string>comgooglemaps</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
		<key>NSExceptionDomains</key>
		<dict>
			<key>**************:8802</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<true/>
				<key>NSExceptionMinimumTLSVersion</key>
				<string>TLSv1.2</string>
				<key>NSExceptionRequiresForwardSecrecy</key>
				<true/>
				<key>NSIncludesSubdomains</key>
				<true/>
				<key>NSRequiresCertificateTransparency</key>
				<false/>
				<key>NSThirdPartyExceptionAllowsInsecureHTTPLoads</key>
				<false/>
				<key>NSThirdPartyExceptionMinimumTLSVersion</key>
				<string>TLSv1.2</string>
				<key>NSThirdPartyExceptionRequiresForwardSecrecy</key>
				<true/>
			</dict>
			<key>**************:8803</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<true/>
				<key>NSExceptionMinimumTLSVersion</key>
				<string>TLSv1.2</string>
				<key>NSExceptionRequiresForwardSecrecy</key>
				<true/>
				<key>NSIncludesSubdomains</key>
				<true/>
				<key>NSRequiresCertificateTransparency</key>
				<false/>
				<key>NSThirdPartyExceptionAllowsInsecureHTTPLoads</key>
				<false/>
				<key>NSThirdPartyExceptionMinimumTLSVersion</key>
				<string>TLSv1.2</string>
				<key>NSThirdPartyExceptionRequiresForwardSecrecy</key>
				<true/>
			</dict>
			<key>dev.api.betacorp.vn</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<true/>
				<key>NSExceptionMinimumTLSVersion</key>
				<string>TLSv1.2</string>
				<key>NSExceptionRequiresForwardSecrecy</key>
				<true/>
				<key>NSIncludesSubdomains</key>
				<true/>
				<key>NSRequiresCertificateTransparency</key>
				<false/>
				<key>NSThirdPartyExceptionAllowsInsecureHTTPLoads</key>
				<false/>
				<key>NSThirdPartyExceptionMinimumTLSVersion</key>
				<string>TLSv1.2</string>
				<key>NSThirdPartyExceptionRequiresForwardSecrecy</key>
				<true/>
			</dict>
			<key>dev.betacineplex.vn</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<true/>
				<key>NSExceptionMinimumTLSVersion</key>
				<string>TLSv1.2</string>
				<key>NSExceptionRequiresForwardSecrecy</key>
				<true/>
				<key>NSIncludesSubdomains</key>
				<true/>
				<key>NSRequiresCertificateTransparency</key>
				<false/>
				<key>NSThirdPartyExceptionAllowsInsecureHTTPLoads</key>
				<false/>
				<key>NSThirdPartyExceptionMinimumTLSVersion</key>
				<string>TLSv1.2</string>
				<key>NSThirdPartyExceptionRequiresForwardSecrecy</key>
				<true/>
			</dict>
			<key>dev.files.tms.betacorp.vn</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<true/>
				<key>NSExceptionMinimumTLSVersion</key>
				<string>TLSv1.2</string>
				<key>NSExceptionRequiresForwardSecrecy</key>
				<true/>
				<key>NSIncludesSubdomains</key>
				<true/>
				<key>NSRequiresCertificateTransparency</key>
				<false/>
				<key>NSThirdPartyExceptionAllowsInsecureHTTPLoads</key>
				<false/>
				<key>NSThirdPartyExceptionMinimumTLSVersion</key>
				<string>TLSv1.2</string>
				<key>NSThirdPartyExceptionRequiresForwardSecrecy</key>
				<true/>
			</dict>
		</dict>
	</dict>
	<key>NSCameraUsageDescription</key>
	<string>Use to capture user's avatar</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>Use to get nearby cinema</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>Use to get nearby cinema</string>
	<key>NSLocationUsageDescription</key>
	<string>Use to get nearby theater</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Use to get nearby cinema</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>Use to capture avatar for user</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Use to get avatar for user</string>
	<key>ReCaptchaDomain</key>
	<string>http://dev.api.betacorp.vn</string>
	<key>ReCaptchaKey</key>
	<string>6Ld52bQUAAAAAI_CZuZqi7ULiGAjQTWPo-vwrEV6</string>
	<key>UIAppFonts</key>
	<array>
		<string>Oswald-Bold.ttf</string>
		<string>Oswald-ExtraLight.ttf</string>
		<string>Oswald-Light.ttf</string>
		<string>Oswald-Medium.ttf</string>
		<string>Oswald-Regular.ttf</string>
		<string>Oswald-SemiBold.ttf</string>
		<string>SourceSansPro-Black.ttf</string>
		<string>SourceSansPro-BlackItalic.ttf</string>
		<string>SourceSansPro-Bold.ttf</string>
		<string>SourceSansPro-BoldItalic.ttf</string>
		<string>SourceSansPro-ExtraLight.ttf</string>
		<string>SourceSansPro-ExtraLightItalic.ttf</string>
		<string>SourceSansPro-Italic.ttf</string>
		<string>SourceSansPro-Light.ttf</string>
		<string>SourceSansPro-LightItalic.ttf</string>
		<string>SourceSansPro-Regular.ttf</string>
		<string>SourceSansPro-SemiBold.ttf</string>
		<string>SourceSansPro-SemiBoldItalic.ttf</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UIRequiresFullScreen</key>
	<true/>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIUserInterfaceStyle</key>
	<string>Light</string>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<true/>
</dict>
</plist>
