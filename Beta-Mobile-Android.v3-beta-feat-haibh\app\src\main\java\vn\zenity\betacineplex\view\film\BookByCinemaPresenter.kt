package vn.zenity.betacineplex.view.film

import io.reactivex.disposables.Disposable
import vn.zenity.betacineplex.Manager.Network.APIClient
import vn.zenity.betacineplex.global.Constant
import vn.zenity.betacineplex.global.Tracking
import vn.zenity.betacineplex.helper.extension.applyOn
import vn.zenity.betacineplex.helper.extension.toCalendar
import vn.zenity.betacineplex.helper.extension.toStringFormat
import vn.zenity.betacineplex.model.Film
import vn.zenity.betacineplex.model.FilmModel
import java.lang.ref.WeakReference
import java.util.*

/**
 * Created by Zenity.
 */

class BookByCinemaPresenter : BookByCinemaContractor.Presenter {
    var po: Disposable? = null
    override fun getCinemaDetail(id: String) {
        this.view?.get()?.showLoading()
        po = APIClient.shared.cinemaAPI.getCinemaDetail(id)
                .applyOn()
                .subscribe({ data ->
                    data.Data?.let {
                        this.view?.get()?.showCinemaDetail(it)
                    }
                    this.view?.get()?.hideLoading()
                }, {
                    this.view?.get()?.hideLoading()
                })
    }
    private var disposable: Disposable? = null

    override fun getListFilm(date: Calendar, cinemaId: String) {
        this.view?.get()?.showLoading()
        disposable = APIClient.shared.cinemaAPI.getShows(cinemaId, date.toStringFormat(Constant.DateFormat.date) ?: return)
                .applyOn().subscribe({ response ->
            response?.Data?.let {
                this.view?.get()?.showListFilm(it)
            }
            this.view?.get()?.hideLoading()
        }, { error ->
            this.view?.get()?.hideLoading()
            this.view?.get()?.showError(error.localizedMessage ?: return@subscribe)
        })
    }

    override fun getShowDate(cinemaId: String) {
        this.view?.get()?.showLoading()
        disposable = APIClient.shared.cinemaAPI.getShowDates(cinemaId).applyOn().subscribe({ response ->
            this.view?.get()?.hideLoading()
            response.Data?.let {
                this.view?.get()?.showShowDates(it.map { it.toCalendar() ?: Calendar.getInstance() })
            }
        }, { error ->
            this.view?.get()?.hideLoading()
            this.view?.get()?.showError(error.localizedMessage ?: return@subscribe)
        })
    }

    private var view: WeakReference<BookByCinemaContractor.View?>? = null
    override fun attachView(view: BookByCinemaContractor.View) {
        this.view = WeakReference(view)
    }

    override fun detachView() {
        disposable?.dispose()
        po?.dispose()
        this.view?.clear()
        this.view = null
    }
}
