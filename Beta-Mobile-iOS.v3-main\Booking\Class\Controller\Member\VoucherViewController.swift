//
//  VoucherViewController.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 3/14/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit

class VoucherViewController: BaseViewController {
    @IBOutlet weak var tableView: UITableView!
    @IBOutlet weak var headerView: UIView!

    @IBOutlet weak var tfVoucherCode: RoundTextField!
    @IBOutlet weak var tfPinCode: RoundTextField!
    
    private var items:[VoucherModel] = []
    
    let cellId = "VoucherTableCell"
    let headerViewId = "TitleHeaderView"

    override func viewDidLoad() {
        super.viewDidLoad()

        localizableTitle = "Voucher.Title"

        initTableView()
        tableView.register(UINib(nibName: cellId, bundle: nil), forCellReuseIdentifier: cellId)
        tableView.register(UINib(nibName: headerViewId, bundle: nil), forHeaderFooterViewReuseIdentifier: headerViewId)
        
        
        getVoucher()
    }

    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()

        initTableView()
    }

    func initTableView() {
        headerView.autoFitSize()
        tableView.tableHeaderView = headerView
    }
    
    @IBAction func didTouchRegisterVoucher(_ sender: Any) {
        registerVoucher()
    }
    
    
}

extension VoucherViewController{
    private func getVoucher(){
        self.showLoading()
        VoucherProvider.rx.request(.getVoucher).mapObject(DDKCResponse<VoucherModel>.self)
            
            .subscribe(onNext: {[weak self] response in
                guard let `self` = self else {return}
                self.dismissLoading()
                guard response.isSuccess() else{
                    if let message = response.Message, !message.isEmpty {
                        self.flashError(title: message)
                    } else {
                        self.flashError(title: "Error".localized)
                    }
                    return
                }
                
                self.items = response.ListObject?.sorted(by: {$0.getDateStatus() > $1.getDateStatus()}) ?? []
                self.tableView.reloadData()
            }).disposed(by: disposeBag)
    }
    
    private func registerVoucher(){
        if self.validate(){
            self.showLoading()
            guard let user = Global.shared.user, let userId = user.UserId, let userCard = user.CardNumber else{
                return
            }
            let model = RegisterVoucherModel(pinCode: tfPinCode.text!, voucherCode: tfVoucherCode.text!, customerId: userId, customerCard: userCard, cardTypeName: .Voucher)
            VoucherProvider.rx.request(.registerVoucher(model)).mapObject(DDKCResponse<VoucherModel>.self)
                
                .subscribe(onNext: {[weak self] (response) in
                    guard let `self` = self else {return}
                    self.dismissLoading()
                    guard response.isSuccess() else{
                        print("Data wrong")
                        if let message = response.Message, !message.isEmpty {
                            self.flashError(title: message)
                        } else {
                            self.flashError(title: "Error".localized)
                        }
                        return
                    }

                    self.showAlert(message: "Alert.RegisterVoucherSuccess".localized)
                    self.tfPinCode.text = ""
                    self.tfVoucherCode.text = ""
                    self.flashSuccess()
//                    self.items.insert(data, at: 0)
//                    self.tableView.reloadData()
                    self.getVoucher()
                }).disposed(by: disposeBag)
        }
    }
    
    private func validate() -> Bool{
        if let text = tfVoucherCode.text, text.isEmpty{
            UIAlertController.showAlert(self, message: "Alert.VoucherCodeNotEmpty".localized)
            return false
        }
        
        if let text = tfPinCode.text, text.isEmpty{
            UIAlertController.showAlert(self, message: "Alert.PinCodeNotEmpty".localized)
            return false
        }
        
        return true
    }
}

extension VoucherViewController: UITableViewDelegate, UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return self.items.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: cellId) as! VoucherTableCell
        let item = items[indexPath.row]
        cell.fillData(item)
        return cell
    }
    
    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        let view = tableView.dequeueReusableHeaderFooterView(withIdentifier: headerViewId) as! TitleHeaderView
        view.lbTitle.text = "YourVoucher".localized
        return view
    }

    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        return 50
    }

    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: false)
    }
}
