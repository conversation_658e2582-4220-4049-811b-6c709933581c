package vn.zenity.betacineplex.model

import vn.zenity.betacineplex.app.App

/**
 * Created by tinhvv on 4/14/18.
 */
class CardModel {
    var CardId : String? = null
    var CardNumber : String? = null
    var CardTypeId : String? = null
    var Status : Int? = null
    var IsSuspendCard : Boolean? = null
    var DateEntered : String? = null
    var ExpirationDate : String? = null
    var AllocationReasonId : String? = null
    var IsCreateOnline : Boolean? = null
    var Description : String? = null
    var CreatedByUserId : String? = null
    var CreatedOnDate : String? = null
    var LastModifiedByUserId : String? = null
    var LastModifiedOnDate : String? = null
    var ApplicationId : String? = null
    var ClassName: String? = null
        get() {
            if (!App.shared().isLangVi() && ClassNameF != null) {
                return ClassNameF
            }
            return field
        }
    var ClassNameF: String? = null
}