//
//  PointModel.swift
//  Booking
//
//  Created by <PERSON>h Vu on 4/4/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation
import ObjectMapper

class PointModel : Mappable {
     var Status : Int?
     var TotalAccumulatedPoints : Int?
     var TotalSpentPoints : Int?
     var TotalBillPayment : Int?
     var AvailablePoint : Int?
     var TotalPoint : Int?
     var AccountId : String?
     var FullName : String?
     var Code : String?
     var ClassId : String?
     var CardId : String?
     var CardNumber : String?
     var ApplicationId : String?
     var AlmostExpiredPoint : Int?
     var AlmostExpiredPointDate : String?


    required init?(map: Map) {

    }

    func mapping(map: Map) {
        Status               <- map["Status"]
        TotalAccumulatedPoints <- map["TotalAccumulatedPoints"]
        TotalSpentPoints     <- map["TotalSpentPoints"]
        TotalBillPayment     <- map["TotalBillPayment"]
        AvailablePoint       <- map["AvailablePoint"]
        TotalPoint           <- map["TotalPoint"]
        AccountId            <- map["AccountId"]
        FullName             <- map["FullName"]
        Code                 <- map["Code"]
        ClassId              <- map["ClassId"]
        CardId               <- map["CardId"]
        CardNumber           <- map["CardNumber"]
        ApplicationId        <- map["ApplicationId"]
        AlmostExpiredPoint   <- map["AlmostExpiredPoint"]
        AlmostExpiredPointDate  <- map["AlmostExpiredPointDate"]
    }
}
