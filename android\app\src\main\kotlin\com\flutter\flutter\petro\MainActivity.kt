package com.flutter.flutter.petro

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.util.Log
import androidx.annotation.NonNull
import com.betacineplex.SignalRPlugin
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel

class MainActivity: FlutterActivity() {
    private val CHANNEL = "com.flutter.flutter.petro/payment"
    // 🚫 PAYMENT_REQUEST_CODE and pendingResult removed - no longer needed

    override fun configureFlutterEngine(@NonNull flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        // Register the SignalR plugin
        flutterEngine.plugins.add(SignalRPlugin())

        // Register payment WebView method channel
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "launchPaymentWebView" -> {
                    val bookingData = call.argument<String>("bookingData")
                    launchPaymentWebView(bookingData, result)
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
    }

    private fun launchPaymentWebView(bookingData: String?, result: MethodChannel.Result) {
        if (bookingData == null) {
            result.error("INVALID_ARGUMENT", "Booking data is required", null)
            return
        }

        Log.d("MainActivity", "🚫 PaymentWebViewActivity removed - using Flutter WebView instead")

        // Since PaymentWebViewActivity is removed, return error to use Flutter WebView
        result.error("NATIVE_WEBVIEW_UNAVAILABLE", "Native WebView removed, use Flutter WebView instead", null)
    }

    // 🚫 onActivityResult removed - no longer needed without PaymentWebViewActivity
}
