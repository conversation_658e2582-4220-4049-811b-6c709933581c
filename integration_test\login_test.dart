import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:flutter_app/main.dart';
import 'package:flutter_app/cubit/index.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_app/utils/index.dart';
import 'package:flutter_app/widgets/src/button/index.dart';

import '../test/common.dart';

Future<void> initAppWidgetTest(WidgetTester tester) async {
  SharedPreferences.setMockInitialValues({});
  await dotenv.load(fileName: Environment.fileName);
  await EasyLocalization.ensureInitialized();
  await tester.pumpWidget(EasyLocalization(supportedLocales: const [
    Locale('vi'),
  ], path: 'assets/translations', fallbackLocale: const Locale('vi'), child: MyApp()));
  await tester.pumpAndSettle();
}

Future<void> tapButtonPump(
    WidgetTester tester,
    String text, {
      Type type = WButton
    }) async {
  await tester.tap(find.widgetWithText(type, text));
  await tester.pumpAndSettle();
}

// Debug helper function
Future<void> debugPause({Duration duration = const Duration(seconds: 2)}) async {
  debugPrint('⏸️ DEBUG PAUSE: Waiting ${duration.inSeconds} seconds...');
  await Future.delayed(duration);
  debugPrint('▶️ DEBUG PAUSE: Continuing...');
}

// Debug screenshot function (optional)
Future<void> debugScreenshot(WidgetTester tester, String name) async {
  debugPrint('📸 Taking screenshot: $name');
  // You can implement screenshot capture here if needed
}

// void main() {
//   IntegrationTestWidgetsFlutterBinding.ensureInitialized();
//
//   testWidgets('Login Flow Test', (WidgetTester tester) async {
//     print('🚀 Starting login flow test...');
//
//     // DEBUG POINT 1: App initialization
//     debugPrint('DEBUG POINT 1: Initializing app...');
//     await initAppWidgetTest(tester);
//     await tester.pumpAndSettle();
//     debugPrint('DEBUG POINT 1: App initialized ✅');
//
//     // DEBUG POINT 2: Logout user
//     debugPrint('DEBUG POINT 2: Ensuring user is logged out...');
//     try {
//       final authCubit = tester.element(find.byType(MyApp)).read<AuthC>();
//       authCubit.logout();
//       await tester.pumpAndSettle();
//       print('✅ User logged out');
//       debugPrint('DEBUG POINT 2: User logout successful ✅');
//     } catch (e) {
//       print('⚠️ Could not access auth cubit: $e');
//       debugPrint('DEBUG POINT 2: User logout failed ⚠️');
//     }
//
//     // DEBUG POINT 3: Wait for app to load
//     debugPrint('DEBUG POINT 3: Waiting for app to load completely...');
//     await tester.pump(const Duration(seconds: 3));
//     await tester.pumpAndSettle();
//     debugPrint('DEBUG POINT 3: App loading complete ✅');
//
//     // Debug: Check current state
//     print('🔍 Checking app state...');
//     print('OutlinedButton widgets: ${find.byType(OutlinedButton).evaluate().length}');
//     print('ElevatedButton widgets: ${find.byType(ElevatedButton).evaluate().length}');
//
//     // Look for login button
//     Finder? loginButtonFinder;
//
//     // Method 1: By key (most reliable)
//     if (find.byKey(const Key('login_button_homePage')).evaluate().isNotEmpty) {
//       loginButtonFinder = find.byKey(const Key('login_button_homePage'));
//       print('✅ Found login button by key');
//     }
//     // Method 2: By OutlinedButton type
//     else if (find.byType(OutlinedButton).evaluate().isNotEmpty) {
//       loginButtonFinder = find.byType(OutlinedButton).first;
//       print('✅ Found login button by OutlinedButton type');
//     }
//     // Method 3: Check bottom navigation "Khác" tab
//     else {
//       print('🔍 Checking bottom navigation...');
//       try {
//         await tester.tap(find.text('Khác\n '));
//         await tester.pumpAndSettle();
//
//         if (find.text('pages.login.login.Log in'.tr()).evaluate().isNotEmpty) {
//           loginButtonFinder = find.text('pages.login.login.Log in'.tr());
//           print('✅ Found login button in Khác tab');
//         }
//       } catch (e) {
//         print('⚠️ Could not navigate to Khác tab: $e');
//       }
//     }
//
//     if (loginButtonFinder == null) {
//       print('❌ Login button not found, taking screenshot...');
//       // You can add screenshot capture here if needed
//       throw Exception('Login button not found anywhere in the app');
//     }
//
//     // DEBUG POINT 4: Tap login button
//     debugPrint('DEBUG POINT 4: Tapping login button...');
//     print('🎯 Tapping login button...');
//     await tester.tap(loginButtonFinder);
//     await tester.pumpAndSettle();
//     debugPrint('DEBUG POINT 4: Login button tapped ✅');
//
//     // DEBUG POINT 5: Verify login screen
//     debugPrint('DEBUG POINT 5: Verifying login screen...');
//     print('🔍 Verifying login screen...');
//     expect(find.byKey(ValueKey('pages.login.login.Email address'.tr())), findsOneWidget);
//     expect(find.byKey(ValueKey('pages.login.login.Password'.tr())), findsOneWidget);
//     expect(find.text('pages.login.login.Remember me'.tr()), findsOneWidget);
//     expect(find.text('pages.login.login.Forgot password'.tr()), findsOneWidget);
//     expect(find.widgetWithText(ElevatedButton, 'pages.login.login.Log in'.tr()), findsOneWidget);
//
//     print('✅ Login screen verified successfully!');
//
//     // Test login with invalid credentials
//     print('🧪 Testing login with invalid credentials...');
//     await tester.enterText(find.byKey(ValueKey('pages.login.login.Email address'.tr())), '<EMAIL>');
//     await tester.enterText(find.byKey(ValueKey('pages.login.login.Password'.tr())), 'wrongpassword');
//
//     await SystemChannels.textInput.invokeMethod('TextInput.hide');
//     await tester.pumpAndSettle(const Duration(milliseconds: 300));
//
//     await tester.tap(find.widgetWithText(ElevatedButton, 'pages.login.login.Log in'.tr()));
//     await tester.pumpAndSettle();
//
//     print('✅ Login test completed successfully!');
//   });
// }
void main() async {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();
  testWidgets('Login', (WidgetTester tester) async {
    await initAppWidgetTest(tester);

    // Wait for app to load and API calls to complete
    // The app calls sFilm.getInfoHomePage() which might set homeModel
    // We need to wait for this to complete and ensure user is not logged in
    await tester.pump(const Duration(seconds: 5));
    await tester.pumpAndSettle();

    // Debug: Check current state
    print('🔍 Checking app state...');
    print('OutlinedButton widgets: ${find.byType(OutlinedButton).evaluate().length}');
    print('InkWell widgets: ${find.byType(InkWell).evaluate().length}');
    print('ElevatedButton widgets: ${find.byType(ElevatedButton).evaluate().length}');

    // Look for login button in different ways
    Finder? loginButtonFinder;

    // Method 1: By key (most reliable)
    if (find.byKey(const Key('login_button_homePage')).evaluate().isNotEmpty) {
      loginButtonFinder = find.byKey(const Key('login_button_homePage'));
      print('✅ Found login button by key');
    }
    // Method 2: By OutlinedButton type (login button is OutlinedButton.icon)
    else if (find.byType(OutlinedButton).evaluate().isNotEmpty) {
      loginButtonFinder = find.byType(OutlinedButton).first;
      print('✅ Found login button by OutlinedButton type');
    }
    // Method 3: Look in bottom navigation or drawer
    else {
      print('🔍 Login button not found in main area, checking other locations...');

      // Check if there's a drawer or menu
      final scaffoldFinder = find.byType(Scaffold);
      if (scaffoldFinder.evaluate().isNotEmpty) {
        // Try to open drawer
        try {
          await tester.tap(find.byTooltip('Open navigation menu'));
          await tester.pumpAndSettle();

          // Look for login button in drawer
          if (find.text('pages.login.login.Log in'.tr()).evaluate().isNotEmpty) {
            loginButtonFinder = find.text('pages.login.login.Log in'.tr());
            print('✅ Found login button in drawer');
          }
        } catch (e) {
          print('⚠️ No drawer found or failed to open: $e');
        }
      }

      // If still not found, check bottom navigation
      if (loginButtonFinder == null) {
        // Navigate to "Khác" tab which might have login
        final bottomNavItems = find.byType(BottomNavigationBar);
        if (bottomNavItems.evaluate().isNotEmpty) {
          await tester.tap(find.text('Khác\n '));
          await tester.pumpAndSettle();

          // Look for login button in "Khác" tab
          if (find.text('pages.login.login.Log in'.tr()).evaluate().isNotEmpty) {
            loginButtonFinder = find.text('pages.login.login.Log in'.tr());
            print('✅ Found login button in Khác tab');
          }
        }
      }
    }

    if (loginButtonFinder == null) {
      throw Exception('❌ Login button not found anywhere in the app');
    }

    // Tap login button
    print('🎯 Tapping login button to navigate to login screen...');
    await tester.tap(loginButtonFinder);
    await tester.pumpAndSettle();
    print('✅ Navigated to login screen');
    // await tester.tap(find.byKey( ValueKey('pages.login.login.Log in'.tr())));
    // await tester.pumpAndSettle();
    // expect(find.byKey(const Key("login_button_homePage")), findsOneWidget);
    // await tester.tap(find.widgetWithText(OutlinedButton, 'pages.login.login.Log in'.tr()));

    // Check login form elements using correct hintText as keys
    expect(find.byKey(const ValueKey('Email hoặc tên đăng nhập')), findsOneWidget);
    expect(find.byKey(ValueKey('pages.login.login.Password'.tr())), findsOneWidget);
    expect(find.text('${'pages.login.login.Forgot password'.tr()}?'), findsOneWidget);

    // Find login button by key instead of text (more reliable)
    expect(find.byKey(const Key('loginButton')), findsOneWidget);

    // Test login with empty fields first
    await tester.tap(find.byKey(const Key('loginButton')));
    await tester.pumpAndSettle();
    expect(find.descendant(of: find.byKey(const ValueKey("Email hoặc tên đăng nhập")), matching: find.text('Vui lòng email hoặc tên đăng nhập')), findsOneWidget);
    expect(find.descendant(of: find.byKey(const ValueKey("Mật khẩu")), matching: find.text('Vui lòng mật khẩu')), findsOneWidget);

    // Enter test credentials using correct field keys
    await tester.enterText(find.byKey(const ValueKey('Email hoặc tên đăng nhập')), '<EMAIL>');
    await tester.enterText(find.byKey(ValueKey('pages.login.login.Password'.tr())), '123123');

    await SystemChannels.textInput.invokeMethod('TextInput.hide');
    await tester.pumpAndSettle(const Duration(seconds: 3));

    // Tap login button using key
    await tester.tap(find.byKey(const Key('loginButton')));
    await tester.pumpAndSettle();

    // Wait for API response and error message
    await pumpUntilFound(tester, find.text("ACCOUNT_NOT_EXISTS"));
    // await tester.tap(find.widgetWithText(TextButton, "Thoát"));

    await tester.pumpAndSettle();

    await tester.enterText(find.byKey(const ValueKey('Email hoặc tên đăng nhập')), '<EMAIL>');
    await tester.enterText(find.byKey(ValueKey('pages.login.login.Password'.tr())), 'trungnd232');

    await SystemChannels.textInput.invokeMethod('TextInput.hide');
    await tester.pumpAndSettle(const Duration(milliseconds: 3000));

    // Tap login button using key
    await tester.tap(find.byKey(const Key('loginButton')));
    await tester.pumpAndSettle();
    // expect(find.text("Tài khoản <EMAIL> không tồn tại trong hệ thống. Vui lòng đăng ký mới."), findsOneWidget);
    // await tester.tap(find.widgetWithText(TextButton, "Thoát"));
    // await tester.pumpAndSettle();
    //
    // await tester.enterText(find.byKey(const ValueKey("Địa chỉ Email")), '<EMAIL>');
    // await tester.enterText(find.byKey(const ValueKey("Mật khẩu")), '123123123');
    //
    // await SystemChannels.textInput.invokeMethod('TextInput.hide');
    // await tester.pumpAndSettle(const Duration(milliseconds: 300));
    // await tapButtonPump(tester, 'Đăng nhập');
    // await pumpUntilFound(tester, find.text("Sai mật khẩu cho tài khoản <EMAIL>"));
    //
    //
    // expect(find.text("Sai mật khẩu cho tài khoản <EMAIL>"), findsOneWidget);
    // await tester.tap(find.widgetWithText(TextButton, "Thoát"));
    // await tester.pumpAndSettle();
    //
    // await tester.enterText(find.byKey(const ValueKey("Địa chỉ Email")), '<EMAIL>');
    // await tester.enterText(find.byKey(const ValueKey("Mật khẩu")), '123123');
    //
    // await SystemChannels.textInput.invokeMethod('TextInput.hide');
    // await tester.pumpAndSettle(const Duration(milliseconds: 300));
    // await tapButtonPump(tester, 'Đăng nhập');
    // await pumpUntilFound(tester, find.text("Home"));

    // expect(find.text("Home"), findsOneWidget);
  });
}
