//
//  SeatCollectionViewCell.swift
//  Booking-dev
//
//  Created by <PERSON><PERSON> on 5/27/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit

class SeatCollectionViewCell: UICollectionViewCell {
    @IBOutlet weak var ivSeat: UIImageView!
    @IBOutlet weak var lbSeatNumber: UILabel!

    fileprivate let emptyColor = UIColor(red: 186, green: 187, blue: 195)
    fileprivate let soldColor = UIColor(red: 253, green: 40, blue: 2)
    fileprivate let bookedColor = UIColor(red: 253, green: 202, blue: 2)
    fileprivate let processingColor = UIColor(red: 63, green: 183, blue: 249)

    override var isSelected: Bool {
        didSet {
            setSelected(isSelected)
        }
    }

    var seat: SeatModel? {
        didSet {
            updateStatus()
        }
    }

    override func awakeFromNib() {
        super.awakeFromNib()
        ivSeat.tintColor = UIColor(red: 186, green: 187, blue: 195)
        lbSeatNumber.text = nil
    }

    func updateStatus() {
        if seat?.Status?.isWay == true || seat?.Status?.isBroken == true || seat?.Status?.isNotUsed == true || seat?.coupleSeat?.Status?.isWay == true || seat?.coupleSeat?.Status?.isBroken == true || seat?.coupleSeat?.Status?.isNotUsed == true {
            ivSeat.image = nil
        } else if seat?.seatType?.isNormal == true {
            ivSeat.image = #imageLiteral(resourceName: "ic_empty_normal_seat").withRenderingMode(.alwaysTemplate)
        } else if seat?.seatType?.isVip == true {
            ivSeat.image = #imageLiteral(resourceName: "ic_empty_vip_seat").withRenderingMode(.alwaysTemplate)
        } else if seat?.seatType?.isCouple == true {
            ivSeat.image = #imageLiteral(resourceName: "ic_empty_couple_seat").withRenderingMode(.alwaysTemplate)
        }
        if seat?.SoldStatus == SeatSoldStatus.EMPTY {
            ivSeat.tintColor = emptyColor
        } else if seat?.SoldStatus == SeatSoldStatus.BOOKED {
            ivSeat.tintColor = bookedColor
        } else if seat?.SoldStatus == SeatSoldStatus.SELECTED {
            ivSeat.tintColor = processingColor
        } else if seat?.SoldStatus == SeatSoldStatus.WAITING {
            ivSeat.tintColor = processingColor
        } else if seat?.SoldStatus == SeatSoldStatus.SELECTING {
            ivSeat.tintColor = .selected
        } else if seat?.SoldStatus == SeatSoldStatus.SOLD {
            ivSeat.tintColor = soldColor
        } else if seat?.SoldStatus == SeatSoldStatus.SOLDED {
            ivSeat.tintColor = soldColor
        }
        updateSeatNumber()
    }

    func setSelected(_ selected: Bool) {
        ivSeat.tintColor = selected ? .selected : emptyColor
        updateSeatNumber()
    }

    func updateSeatNumber() {
//        lbSeatNumber.text = isSelected ? seat?.SeatNumber  : nil
        lbSeatNumber.text = seat?.SeatNumber
    }
}
