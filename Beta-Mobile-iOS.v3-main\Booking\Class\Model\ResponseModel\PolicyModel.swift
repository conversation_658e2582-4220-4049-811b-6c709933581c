//
//  PolicyModel.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 4/15/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation
import ObjectMapper

class PolicyModel : Mappable {
    var ParameterCode: String?
    var ParameterValue: String?


    required init?(map: Map) {

    }

    func mapping(map: Map) {
        ParameterCode        <- map["ParameterCode"]
        ParameterValue       <- map["ParameterValue"]
    }
}
