package vn.zenity.betacineplex.view.user.share

import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.model.VoucherModel

/**
 * Created by Zenity.
 */

interface ShareFriendContractor {
    interface View : IBaseView {
        fun registerCodeSuccess(message: String)
    }

    interface Presenter : IBasePresenter<View> {
        fun registerCode(code: String)
    }
}
