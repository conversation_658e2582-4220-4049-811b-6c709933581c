//
//  MyVoucherTableViewCell.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 8/3/19.
//  Copyright © 2019 ddkc. All rights reserved.
//

import UIKit

class MyVoucherTableViewCell: UITableViewCell {

    @IBOutlet weak var codeLabel: UILabel!
    @IBOutlet weak var stateLabel: UILabel!
    @IBOutlet weak var contentLabel: UILabel!

    @IBOutlet weak var titleLabel: UILabel!
    @IBOutlet weak var dateLabel: UILabel!
    @IBOutlet weak var tButton: RoundButton!

    @IBOutlet weak var useButton: GradientButton!
    @IBOutlet weak var contentHeight: NSLayoutConstraint!

    let buttonColor: UIColor = 0x03599d.toColor

    private var indexPath: IndexPath?

    var useHandler: (IndexPath) -> Void = {_ in}
    var donateHandler: (IndexPath) -> Void = {_ in}

    override func awakeFromNib() {
        super.awakeFromNib()
    }

    func configure(_ voucher: VoucherModel, index: IndexPath) {
        indexPath = index
        codeLabel.text = voucher.VoucherCode
        stateLabel.text = voucher.type.description.localized
        stateLabel.textColor = voucher.type.color
        titleLabel.text = voucher.VoucherPackageName
        dateLabel.text = voucher.expiredDateString
        if voucher.Description == nil {
            contentHeight.constant = 0
        } else {
            contentLabel.text = voucher.Description
            contentHeight.constant = 40
        }
        tButton.isEnabled = voucher.IsAvaiableForGift
        if !voucher.IsAvaiableForGift {
            tButton.setTitleColor(.lightGray, for: .normal)
            tButton.layer.borderColor = UIColor.lightGray.cgColor
        } else {
            tButton.setTitleColor(buttonColor, for: .normal)
            tButton.layer.borderColor = buttonColor.cgColor
        }
    }


    @IBAction func useTapped(_ sender: Any) {
        guard let index = indexPath else {
            return
        }
        useHandler(index)
    }

    @IBAction func tTapped(_ sender: Any) {
        guard let index = indexPath else {
            return
        }
        donateHandler(index)
    }
}
