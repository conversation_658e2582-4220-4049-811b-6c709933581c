<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<title>Uses of Class microsoft.aspnet.signalr.client.SignalRFuture</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="Uses of Class microsoft.aspnet.signalr.client.SignalRFuture";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../microsoft/aspnet/signalr/client/package-summary.html">Package</a></li>
<li><a href="../../../../../microsoft/aspnet/signalr/client/SignalRFuture.html" title="class in microsoft.aspnet.signalr.client">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?microsoft/aspnet/signalr/client/class-use/SignalRFuture.html" target="_top">Frames</a></li>
<li><a href="SignalRFuture.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class microsoft.aspnet.signalr.client.SignalRFuture" class="title">Uses of Class<br>microsoft.aspnet.signalr.client.SignalRFuture</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="microsoft.aspnet.signalr.client">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../microsoft/aspnet/signalr/client/SignalRFuture.html" title="class in microsoft.aspnet.signalr.client">SignalRFuture</a> in <a href="../../../../../microsoft/aspnet/signalr/client/package-summary.html">microsoft.aspnet.signalr.client</a></h3>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing subclasses, and an explanation">
<caption><span>Subclasses of <a href="../../../../../microsoft/aspnet/signalr/client/SignalRFuture.html" title="class in microsoft.aspnet.signalr.client">SignalRFuture</a> in <a href="../../../../../microsoft/aspnet/signalr/client/package-summary.html">microsoft.aspnet.signalr.client</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><strong><a href="../../../../../microsoft/aspnet/signalr/client/UpdateableCancellableFuture.html" title="class in microsoft.aspnet.signalr.client">UpdateableCancellableFuture</a>&lt;V&gt;</strong></code>
<div class="block">An updateable SignalRFuture that, when cancelled, triggers cancellation on an
 internal instance</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../microsoft/aspnet/signalr/client/package-summary.html">microsoft.aspnet.signalr.client</a> that return <a href="../../../../../microsoft/aspnet/signalr/client/SignalRFuture.html" title="class in microsoft.aspnet.signalr.client">SignalRFuture</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../microsoft/aspnet/signalr/client/SignalRFuture.html" title="class in microsoft.aspnet.signalr.client">SignalRFuture</a>&lt;<a href="../../../../../microsoft/aspnet/signalr/client/SignalRFuture.html" title="type parameter in SignalRFuture">V</a>&gt;</code></td>
<td class="colLast"><span class="strong">SignalRFuture.</span><code><strong><a href="../../../../../microsoft/aspnet/signalr/client/SignalRFuture.html#done(microsoft.aspnet.signalr.client.Action)">done</a></strong>(<a href="../../../../../microsoft/aspnet/signalr/client/Action.html" title="interface in microsoft.aspnet.signalr.client">Action</a>&lt;<a href="../../../../../microsoft/aspnet/signalr/client/SignalRFuture.html" title="type parameter in SignalRFuture">V</a>&gt;&nbsp;action)</code>
<div class="block">Handles the completion of the Future.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../microsoft/aspnet/signalr/client/SignalRFuture.html" title="class in microsoft.aspnet.signalr.client">SignalRFuture</a>&lt;<a href="../../../../../microsoft/aspnet/signalr/client/SignalRFuture.html" title="type parameter in SignalRFuture">V</a>&gt;</code></td>
<td class="colLast"><span class="strong">SignalRFuture.</span><code><strong><a href="../../../../../microsoft/aspnet/signalr/client/SignalRFuture.html#onError(microsoft.aspnet.signalr.client.ErrorCallback)">onError</a></strong>(<a href="../../../../../microsoft/aspnet/signalr/client/ErrorCallback.html" title="interface in microsoft.aspnet.signalr.client">ErrorCallback</a>&nbsp;errorCallback)</code>
<div class="block">Handles error during the execution of the Future.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../microsoft/aspnet/signalr/client/SignalRFuture.html" title="class in microsoft.aspnet.signalr.client">SignalRFuture</a>&lt;java.lang.Void&gt;</code></td>
<td class="colLast"><span class="strong">Connection.</span><code><strong><a href="../../../../../microsoft/aspnet/signalr/client/Connection.html#send(java.lang.Object)">send</a></strong>(java.lang.Object&nbsp;object)</code>
<div class="block">Sends a serialized object</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../microsoft/aspnet/signalr/client/SignalRFuture.html" title="class in microsoft.aspnet.signalr.client">SignalRFuture</a>&lt;java.lang.Void&gt;</code></td>
<td class="colLast"><span class="strong">ConnectionBase.</span><code><strong><a href="../../../../../microsoft/aspnet/signalr/client/ConnectionBase.html#send(java.lang.String)">send</a></strong>(java.lang.String&nbsp;data)</code>
<div class="block">Sends data using the connection</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../microsoft/aspnet/signalr/client/SignalRFuture.html" title="class in microsoft.aspnet.signalr.client">SignalRFuture</a>&lt;java.lang.Void&gt;</code></td>
<td class="colLast"><span class="strong">Connection.</span><code><strong><a href="../../../../../microsoft/aspnet/signalr/client/Connection.html#send(java.lang.String)">send</a></strong>(java.lang.String&nbsp;data)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../microsoft/aspnet/signalr/client/SignalRFuture.html" title="class in microsoft.aspnet.signalr.client">SignalRFuture</a>&lt;java.lang.Void&gt;</code></td>
<td class="colLast"><span class="strong">Connection.</span><code><strong><a href="../../../../../microsoft/aspnet/signalr/client/Connection.html#start()">start</a></strong>()</code>
<div class="block">Starts the connection using the best available transport</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../microsoft/aspnet/signalr/client/SignalRFuture.html" title="class in microsoft.aspnet.signalr.client">SignalRFuture</a>&lt;java.lang.Void&gt;</code></td>
<td class="colLast"><span class="strong">ConnectionBase.</span><code><strong><a href="../../../../../microsoft/aspnet/signalr/client/ConnectionBase.html#start(microsoft.aspnet.signalr.client.transport.ClientTransport)">start</a></strong>(microsoft.aspnet.signalr.client.transport.ClientTransport&nbsp;transport)</code>
<div class="block">Starts the connection</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../microsoft/aspnet/signalr/client/SignalRFuture.html" title="class in microsoft.aspnet.signalr.client">SignalRFuture</a>&lt;java.lang.Void&gt;</code></td>
<td class="colLast"><span class="strong">Connection.</span><code><strong><a href="../../../../../microsoft/aspnet/signalr/client/Connection.html#start(microsoft.aspnet.signalr.client.transport.ClientTransport)">start</a></strong>(microsoft.aspnet.signalr.client.transport.ClientTransport&nbsp;transport)</code>&nbsp;</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../microsoft/aspnet/signalr/client/package-summary.html">microsoft.aspnet.signalr.client</a> with parameters of type <a href="../../../../../microsoft/aspnet/signalr/client/SignalRFuture.html" title="class in microsoft.aspnet.signalr.client">SignalRFuture</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><span class="strong">FutureHelper.</span><code><strong><a href="../../../../../microsoft/aspnet/signalr/client/FutureHelper.html#copyHandlers(microsoft.aspnet.signalr.client.http.HttpConnectionFuture, microsoft.aspnet.signalr.client.SignalRFuture)">copyHandlers</a></strong>(microsoft.aspnet.signalr.client.http.HttpConnectionFuture&nbsp;sourceFuture,
            <a href="../../../../../microsoft/aspnet/signalr/client/SignalRFuture.html" title="class in microsoft.aspnet.signalr.client">SignalRFuture</a>&lt;?&gt;&nbsp;targetFuture)</code>
<div class="block">Copy the Cancellation and Error handlers between two SignalRFuture
 instances, where the source is an HttpConnectionFuture</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><span class="strong">FutureHelper.</span><code><strong><a href="../../../../../microsoft/aspnet/signalr/client/FutureHelper.html#copyHandlers(microsoft.aspnet.signalr.client.SignalRFuture, microsoft.aspnet.signalr.client.SignalRFuture)">copyHandlers</a></strong>(<a href="../../../../../microsoft/aspnet/signalr/client/SignalRFuture.html" title="class in microsoft.aspnet.signalr.client">SignalRFuture</a>&lt;?&gt;&nbsp;sourceFuture,
            <a href="../../../../../microsoft/aspnet/signalr/client/SignalRFuture.html" title="class in microsoft.aspnet.signalr.client">SignalRFuture</a>&lt;?&gt;&nbsp;targetFuture)</code>
<div class="block">Copy the Cancellation and Error handlers between two SignalRFuture
 instances</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><span class="strong">FutureHelper.</span><code><strong><a href="../../../../../microsoft/aspnet/signalr/client/FutureHelper.html#copyHandlers(microsoft.aspnet.signalr.client.SignalRFuture, microsoft.aspnet.signalr.client.SignalRFuture)">copyHandlers</a></strong>(<a href="../../../../../microsoft/aspnet/signalr/client/SignalRFuture.html" title="class in microsoft.aspnet.signalr.client">SignalRFuture</a>&lt;?&gt;&nbsp;sourceFuture,
            <a href="../../../../../microsoft/aspnet/signalr/client/SignalRFuture.html" title="class in microsoft.aspnet.signalr.client">SignalRFuture</a>&lt;?&gt;&nbsp;targetFuture)</code>
<div class="block">Copy the Cancellation and Error handlers between two SignalRFuture
 instances</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">UpdateableCancellableFuture.</span><code><strong><a href="../../../../../microsoft/aspnet/signalr/client/UpdateableCancellableFuture.html#setFuture(microsoft.aspnet.signalr.client.SignalRFuture)">setFuture</a></strong>(<a href="../../../../../microsoft/aspnet/signalr/client/SignalRFuture.html" title="class in microsoft.aspnet.signalr.client">SignalRFuture</a>&lt;?&gt;&nbsp;token)</code>&nbsp;</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing constructors, and an explanation">
<caption><span>Constructors in <a href="../../../../../microsoft/aspnet/signalr/client/package-summary.html">microsoft.aspnet.signalr.client</a> with parameters of type <a href="../../../../../microsoft/aspnet/signalr/client/SignalRFuture.html" title="class in microsoft.aspnet.signalr.client">SignalRFuture</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><strong><a href="../../../../../microsoft/aspnet/signalr/client/UpdateableCancellableFuture.html#UpdateableCancellableFuture(microsoft.aspnet.signalr.client.SignalRFuture)">UpdateableCancellableFuture</a></strong>(<a href="../../../../../microsoft/aspnet/signalr/client/SignalRFuture.html" title="class in microsoft.aspnet.signalr.client">SignalRFuture</a>&lt;?&gt;&nbsp;token)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../microsoft/aspnet/signalr/client/package-summary.html">Package</a></li>
<li><a href="../../../../../microsoft/aspnet/signalr/client/SignalRFuture.html" title="class in microsoft.aspnet.signalr.client">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?microsoft/aspnet/signalr/client/class-use/SignalRFuture.html" target="_top">Frames</a></li>
<li><a href="SignalRFuture.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
