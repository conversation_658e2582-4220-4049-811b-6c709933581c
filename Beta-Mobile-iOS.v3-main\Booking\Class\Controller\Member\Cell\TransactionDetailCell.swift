//
//  TransactionDetailCell.swift
//  Booking
//
//  Created by admin on 20/10/2023.
//  Copyright © 2023 ddkc. All rights reserved.
//

import UIKit

struct KeyValueEntry {
    let key: String
    let value: String
}

class TransactionDetailCell: UITableViewCell {
    
    @IBOutlet weak var lbTitle: LocalizableLabel!
    @IBOutlet weak var lbValue: UILabel!
    
    func fillData(_ entry: KeyValueEntry) {
        lbTitle.text = entry.key
        lbValue.text = entry.value
    }
    
}
