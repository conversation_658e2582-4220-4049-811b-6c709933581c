package vn.zenity.betacineplex.view.notification

import android.provider.Settings
import io.reactivex.Observable
import io.reactivex.disposables.Disposable
import vn.zenity.betacineplex.Manager.Network.APIClient
import vn.zenity.betacineplex.app.App
import vn.zenity.betacineplex.global.Global
import vn.zenity.betacineplex.helper.extension.applyOn
import vn.zenity.betacineplex.model.NewModel
import vn.zenity.betacineplex.model.NewNotification
import vn.zenity.betacineplex.model.NewsModel
import vn.zenity.betacineplex.model.Notification
import java.lang.ref.WeakReference

/**
 * Created by Zenity.
 */

class NotificationPresenter : NotificationContractor.Presenter {
    private var disposable: Disposable? = null
    override fun getListNotification(page: Int) {
        view?.get()?.showLoading()

//        disposable = getCategories().flatMap { items: ArrayList<NewModel> ->
//            val observables: List<Observable<ArrayList<NewsModel>>> = items.map { getNotificationDetail(it.CategoryId ?: "") }
//            Observable.zip(observables, { array ->
//                return@zip array
//            })
//        }.applyOn()
//                .subscribe({ list ->
//                    view?.get()?.hideLoading()
//                    val news = list[0] as? ArrayList<NewsModel>
//                    val notifications = news?.map { Notification(it.Tieu_de ?: "", it .Tom_tat_noi_dung ?: "", it.dateString, it.date, it) }?.sortedByDescending { it.dateTime }
//                    view?.get()?.showListNotifications(notifications ?: return@subscribe)
//                }, { _ ->
//                    view?.get()?.hideLoading()
//                })

        disposable = getNotifications().applyOn()
                .subscribe({ list ->
                    view?.get()?.hideLoading()
                    val notifications = list?.map {
                        Notification(it.Title ?: "", "",
                                isRead = it.ReadStatus ?: true,
                                date = it.CreatedOnDate,
                                newNotification = it,
                                Id = it.Id ?: 0,
                                ScreenCode = it.ScreenCode,
                                RefId = it.RefId
                        )
                    }
                    view?.get()?.showListNotifications(notifications ?: return@subscribe)
                }, { _ ->
                    view?.get()?.hideLoading()
                })
//        view?.get()?.showListNotifications(listOf(Notification("Quà tặng từ Mr & Mrs Grey-Special gift from Mr & Mrs Grey", "Tặng gối tình yêu khi mua 2 vé xem phim Năm Mươi Sắc Thái.", "30/03/2018"),
//                Notification("Tặng BETA Combo cho rạp đặc biệt", "Tặng gối tình yêu khi mua 2 vé xem phim Năm Mươi Sắc Thái.", "30/03/2018")))

    }

    private var view: WeakReference<NotificationContractor.View?>? = null
    override fun attachView(view: NotificationContractor.View) {
        this.view = WeakReference(view)
    }

    override fun detachView() {
        disposable?.dispose()
        this.view?.clear()
        this.view = null
    }

    private fun getNotifications(): Observable<ArrayList<NewNotification>> {
        val accountId = Global.share().user?.AccountId
        if (accountId != null) {
            return APIClient.shared.ecmAPI.getNotifications().map { it.Data }
        }
        return Observable.empty()
    }

    private fun getCategories(): Observable<ArrayList<NewModel>?> {
        val lang = App.shared().getCurrentLang()
        return APIClient.shared.ecmAPI.getNotification(if (lang == "en") lang else "").map { it.Data }
    }

    private fun getNotificationDetail(id: String): Observable<ArrayList<NewsModel>> {
        return APIClient.shared.ecmAPI.getNewForCategory(id = id).map { it.Data }
    }

    override fun readNotification(id: Int, screenCode: Int) {
        disposable = APIClient.shared.ecmAPI.readNotificationV2(mapOf("id" to "$id", "ScreenCode" to "$screenCode"))
                .applyOn().subscribe()
    }
}
