//
//  ConfirmBookAgeViewController.swift
//  Booking-dev
//
//  Created by <PERSON><PERSON> on 5/31/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit
import ActiveLabel

class ConfirmBookAgeViewController: BaseViewController {
    @IBOutlet weak var lbMessage: UILabel!
    var ageTextLocalized: String = ""

    override func viewDidLoad() {
        super.viewDidLoad()

        updateMessage()
    }

    override func localizationDidChange() {
        super.localizationDidChange()
        updateMessage()
    }

    func updateMessage() {
//        let ruleType = ActiveType.custom(pattern: "\\s" + "ConfirmAge.Rule".localized + "\\b")
//        let ageType = ActiveType.custom(pattern: "\\s" + ageTextLocalized.localized + "\\b")
//        lbMessage.enabledTypes = [ruleType, ageType]
//        lbMessage.customColor[ruleType] = .selected

        let message = "ConfirmAge.Message".localized.replacingOccurrences(of: "XXX", with: ageTextLocalized.localized)

        let attributedText = NSMutableAttributedString(string: message, attributes: [.font: UIFont.systemFont(ofSize: 14), .foregroundColor: UIColor(red: 3, green: 3, blue: 3)])
        attributedText.addAttributes([.font: UIFont.boldSystemFont(ofSize: 14)], range: NSRange(message.range(of: ageTextLocalized.localized)!, in: message))
        attributedText.addAttributes([NSAttributedStringKey.underlineStyle: NSNumber(value: NSUnderlineStyle.styleSingle.rawValue), .foregroundColor: UIColor.linkText], range: NSRange(message.range(of: "ConfirmAge.Rule".localized)!, in: message))
        lbMessage.attributedText = attributedText

        let tap = UITapGestureRecognizer(target: self, action: #selector(tapOnPolicyLabel(gesture:)))
        lbMessage.addGestureRecognizer(tap)
        lbMessage.isUserInteractionEnabled = true
    }

    func openRule() {
        let vc = UIStoryboard.setting[.other] as! OtherViewController
        vc.type = .PolicyPayment
        show(BaseNavigationViewController(rootViewController: vc))
    }
}

extension ConfirmBookAgeViewController {
    @objc func tapOnPolicyLabel(gesture: UITapGestureRecognizer) {
        if checkTouchOnPolicy(gesture) {
            openRule()
        }
    }

    func checkTouchOnPolicy(_ gesture: UIGestureRecognizer) -> Bool {
        guard let text = lbMessage.text else {
            return false
        }
        let pos = gesture.location(in: lbMessage)
        let textRange = (text as NSString).range(of: "ConfirmAge.Rule".localized)
        return lbMessage.isPoint(pos: pos, at: textRange)
    }
}
