//
//  CalendarHeaderView.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 4/22/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit
import SwiftDate

protocol CalendarHeaderViewDelegate: class {
    func calendarView(_ calendarView: CalendarHeaderView, didSelected date: Date)
}

class CalendarHeaderView: UIView {
    lazy var collectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        layout.sectionInset = UIEdgeInsets(top: 0, left: 10, bottom: 0, right: 0)
        let collectionView = UICollectionView(frame: self.bounds, collectionViewLayout: layout)
        collectionView.autoresizingMask = [.flexibleWidth, .flexibleHeight]
        collectionView.dataSource = self
        collectionView.delegate = self
        collectionView.showsHorizontalScrollIndicator = false
        collectionView.register(UINib(nibName: cellId, bundle: nil), forCellWithReuseIdentifier: cellId)
        return collectionView
    }()
    fileprivate let cellId = "CalendarHeaderViewCell"
    var dates: [Date] = [] {
        didSet {
            updateData()
            collectionView.reloadData()
        }
    }
//    var selectedIndex: Int = 0

    fileprivate var data: [Date] = []
    weak var delegate: CalendarHeaderViewDelegate?

    override func awakeFromNib() {
        super.awakeFromNib()

        setUp()
        updateData()
        collectionView.reloadData()
    }

    func setUp() {
        addSubview(collectionView)

        backgroundColor = .clear
        collectionView.backgroundColor = .clear
    }

    func updateData() {
        data.removeAll()
        data.append(contentsOf: dates)
    }

    func selectDate(_ date: Date) {
        guard let index = data.index(where: { $0.isInside(date: date, granularity: .day) }) else {
            return
        }
//        selectedIndex = index
        collectionView.selectItem(at: IndexPath(row: index, section: 0), animated: true, scrollPosition: .left)
    }

    func selectIndex(_ index: Int) {
        guard index >= 0 && index < data.count else {
            return
        }
        collectionView.selectItem(at: IndexPath(row: index, section: 0), animated: true, scrollPosition: .left)
    }
}

extension CalendarHeaderView: UICollectionViewDataSource {
    func numberOfSections(in collectionView: UICollectionView) -> Int {
        return 1
    }

    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return data.count
    }

    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: cellId, for: indexPath) as! CalendarHeaderViewCell
        cell.date = data[indexPath.row]
//        cell.isSelected = selectedIndex == indexPath.row
        return cell
    }
}

extension CalendarHeaderView: UICollectionViewDelegate {
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
//        selectedIndex = indexPath.row
        delegate?.calendarView(self, didSelected: data[indexPath.row])
    }
}

extension CalendarHeaderView: UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        let size = CGSize(width: 60, height: collectionView.frame.height)
        return size
    }
}
