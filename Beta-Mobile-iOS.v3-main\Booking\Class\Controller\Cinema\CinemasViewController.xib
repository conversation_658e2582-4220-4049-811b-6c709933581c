<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14490.70" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14490.49"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="Oswald-Regular.ttf">
            <string>Oswald-Regular</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="CinemasViewController" customModule="Booking_dev" customModuleProvider="target">
            <connections>
                <outlet property="byAreaLabel" destination="sAq-qi-y7U" id="Tcw-QQ-X0H"/>
                <outlet property="collectionView" destination="boO-XW-cFN" id="5GU-f3-d9y"/>
                <outlet property="nearbyLabel" destination="Xne-sU-lHX" id="8r6-aJ-8pG"/>
                <outlet property="tableView" destination="ekl-qm-TOs" id="3Mq-Uc-Csk"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Xne-sU-lHX" customClass="LocalizableLabel" customModule="Booking_dev" customModuleProvider="target">
                    <rect key="frame" x="8" y="68" width="38.5" height="30"/>
                    <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="20"/>
                    <color key="textColor" red="0.28627450980392155" green="0.29803921568627451" blue="0.38431372549019605" alpha="1" colorSpace="calibratedRGB"/>
                    <nil key="highlightedColor"/>
                </label>
                <collectionView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" dataMode="none" translatesAutoresizingMaskIntoConstraints="NO" id="boO-XW-cFN">
                    <rect key="frame" x="8" y="114" width="398" height="134"/>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="134" id="zja-Cz-2nt"/>
                    </constraints>
                    <collectionViewFlowLayout key="collectionViewLayout" scrollDirection="horizontal" minimumLineSpacing="10" minimumInteritemSpacing="8" id="6VZ-Tn-4Xb">
                        <size key="itemSize" width="50" height="50"/>
                        <size key="headerReferenceSize" width="0.0" height="0.0"/>
                        <size key="footerReferenceSize" width="0.0" height="0.0"/>
                        <inset key="sectionInset" minX="0.0" minY="0.0" maxX="0.0" maxY="0.0"/>
                    </collectionViewFlowLayout>
                    <connections>
                        <outlet property="dataSource" destination="-1" id="DAY-Qn-2Kf"/>
                        <outlet property="delegate" destination="-1" id="Y8u-uI-4ge"/>
                    </connections>
                </collectionView>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="sAq-qi-y7U" customClass="LocalizableLabel" customModule="Booking_dev" customModuleProvider="target">
                    <rect key="frame" x="8" y="264" width="38.5" height="30"/>
                    <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="20"/>
                    <color key="textColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="calibratedRGB"/>
                    <nil key="highlightedColor"/>
                </label>
                <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" style="plain" separatorStyle="default" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="ekl-qm-TOs">
                    <rect key="frame" x="8" y="310" width="398" height="544"/>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <connections>
                        <outlet property="dataSource" destination="-1" id="yW3-Fi-MN8"/>
                        <outlet property="delegate" destination="-1" id="cT4-c7-8ze"/>
                    </connections>
                </tableView>
            </subviews>
            <color key="backgroundColor" red="0.95294117647058818" green="0.95294117647058818" blue="0.95294117647058818" alpha="1" colorSpace="calibratedRGB"/>
            <constraints>
                <constraint firstItem="Xne-sU-lHX" firstAttribute="top" secondItem="fnl-2z-Ty3" secondAttribute="top" constant="24" id="60g-k7-ku8"/>
                <constraint firstItem="boO-XW-cFN" firstAttribute="top" secondItem="Xne-sU-lHX" secondAttribute="bottom" constant="16" id="68U-8y-qYg"/>
                <constraint firstItem="sAq-qi-y7U" firstAttribute="top" secondItem="boO-XW-cFN" secondAttribute="bottom" constant="16" id="Dsn-i2-TB1"/>
                <constraint firstItem="sAq-qi-y7U" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" constant="8" id="T6X-1w-k4J"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="ekl-qm-TOs" secondAttribute="trailing" constant="8" id="W7g-Sg-bxr"/>
                <constraint firstItem="ekl-qm-TOs" firstAttribute="top" secondItem="sAq-qi-y7U" secondAttribute="bottom" constant="16" id="ZXe-k3-MrG"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="bottom" secondItem="ekl-qm-TOs" secondAttribute="bottom" constant="8" id="Ztn-R7-hwf"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="boO-XW-cFN" secondAttribute="trailing" constant="8" id="jqs-od-WzC"/>
                <constraint firstItem="Xne-sU-lHX" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" constant="8" id="qQ5-3U-6Av"/>
                <constraint firstItem="boO-XW-cFN" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" constant="8" id="r6z-jV-dzD"/>
                <constraint firstItem="ekl-qm-TOs" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" constant="8" id="ujc-TZ-TZ1"/>
            </constraints>
            <viewLayoutGuide key="safeArea" id="fnl-2z-Ty3"/>
        </view>
    </objects>
</document>
