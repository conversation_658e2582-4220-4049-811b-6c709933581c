package vn.zenity.betacineplex.view.film

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.core.content.res.ResourcesCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.afollestad.sectionedrecyclerview.SectionedRecyclerViewAdapter
import com.afollestad.sectionedrecyclerview.SectionedViewHolder
import com.thoughtbot.expandablerecyclerview.ExpandableRecyclerViewAdapter
import com.thoughtbot.expandablerecyclerview.models.ExpandableGroup
import com.thoughtbot.expandablerecyclerview.viewholders.ChildViewHolder
import com.thoughtbot.expandablerecyclerview.viewholders.GroupViewHolder
import kotlinx.android.synthetic.main.fragment_bookbycinema.*
import kotlinx.android.synthetic.main.item_date_in_cinema.view.*
import kotlinx.android.synthetic.main.item_film_in_cinema.view.*
import kotlinx.android.synthetic.main.item_time_booking.view.*
import kotlinx.android.synthetic.main.item_time_booking_of_cinema.view.*
import load
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.base.BaseFragment
import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.global.Constant
import vn.zenity.betacineplex.global.Global
import vn.zenity.betacineplex.global.Tracking
import vn.zenity.betacineplex.helper.extension.*
import vn.zenity.betacineplex.helper.support.TrailerPlayActivity
import vn.zenity.betacineplex.helper.view.ItemTimeBooking
import vn.zenity.betacineplex.model.*
import vn.zenity.betacineplex.view.auth.LoginFragment
import java.util.*

/**
 * Created by Zenity.
 */

class BookByCinemaFragment : BaseFragment(), BookByCinemaContractor.View,
    ItemTimeBooking.TimeSelectListener {
    override fun showCinemaDetail(cinema: CinemaModel) {
        this.cinema = cinema
        activity?.runOnUiThread {
            showData()
        }
    }

    override fun getViewContext(): Context? {
        return this.context
    }

    companion object {
        fun getInstance(cinema: CinemaModel?, cinemaId: String? = null): BookByCinemaFragment {
            val frag = BookByCinemaFragment()
            frag.cinema = cinema
            frag.cinemaId = cinemaId
            return frag
        }
    }

    override fun showListFilm(films: List<FilmModel>) {
        adapter = BookByCinemaAdapter(films)
        recyclerView.adapter = adapter
    }

    override fun showShowDates(dates: List<Calendar>) {
        dateAdapter.selectedIndex = 0
        dateAdapter.showDates = dates.sorted()
        activity?.runOnUiThread {
            dateAdapter.notifyDataSetChanged()
        }
        if (dates.isNotEmpty()) {
            presenter.getListFilm(dates[0], cinema?.CinemaId ?: "")
        }
    }

    private val presenter = BookByCinemaPresenter()
    private lateinit var adapter: BookByCinemaAdapter
    private lateinit var dateAdapter: DateAdapter
    private var cinema: CinemaModel? = null
    private var cinemaId: String? = null

    override fun getPresenter(): IBasePresenter<IBaseView>? {
        return presenter as? IBasePresenter<IBaseView>
    }

    override fun isShowToolbar(): Boolean {
        return false
    }

    override fun getLayoutRes(): Int {
        return R.layout.fragment_bookbycinema
    }

    override fun onRefresh() {
        super.onRefresh()
//        activity?.runOnUiThread {
//            refreshView?.finishRefreshing()
//        }
        if (dateAdapter.showDates.isNotEmpty()) {
            presenter.getListFilm(
                dateAdapter.showDates[dateAdapter.selectedIndex], cinema?.CinemaId
                    ?: (cinemaId ?: "")
            )
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        if (cinema == null) {
            cinemaId?.let {
                presenter.getCinemaDetail(it)
            }
            return
        }
        showData()
    }

    private fun showData() {
        tvFilmTitle.text = cinema?.Name
        recyclerView.layoutManager = LinearLayoutManager(context)
        recyclerDate.layoutManager =
            LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
        dateAdapter = DateAdapter {
            presenter.getListFilm(it, cinema?.CinemaId ?: "")
        }
        recyclerDate.adapter = dateAdapter
        adapter = BookByCinemaAdapter(listOf())
        recyclerView.adapter = adapter
        presenter.getShowDate(cinema?.CinemaId ?: return)
    }

    override fun onTimeSelected(time: ShowModel?, film: FilmModel?) {
        if (time?.getIsShowScreenIntro() == true) {
            showNoticeVipRoom(time) {
                handleSelectShowTime(time, film)
            }
        } else {
            handleSelectShowTime(time, film)
        }
    }

    private fun handleSelectShowTime(time: ShowModel?, film: FilmModel?) {
        val startTime = time?.getStartDate()?.time ?: return
        if (startTime > (System.currentTimeMillis() + (time.TimeToLock * 60 * 1000))) {

            if (Global.share().isLogin) {
                Tracking.share().selectShowtimeComplete(
                    context,
                    film?.CinemaId,
                    film?.CinemaName,
                    film?.FilmId,
                    film?.Name,
                    time.getStartDate(),
                    time.getStartDate()
                )
                openFragment(SelectChairFragment.getInstance(time, film))
            } else {
                openFragment(LoginFragment())
            }
        } else {
            showNotice(getString(R.string.time_booking_is_expried))
        }
    }


    inner class BookByCinemaAdapter(var films: List<FilmModel>) :
        SectionedRecyclerViewAdapter<SectionedViewHolder>() {
        override fun onBindHeaderViewHolder(
            holder: SectionedViewHolder,
            section: Int,
            expanded: Boolean
        ) {
            holder.itemView.apply {
                val film = films[section]
                ivBanner.load(film.MainPosterUrl)
                tvFilmTitle.text = film.Name
                tvFilmDuration.text = "${film.Duration} ${R.string.minute.getString()}"
                tvFilmType.text = film.filmGenner ?: ""
                ivIsHot.visible(film.IsHot)
                ivIsHot2.visible(film.IsHot)
                if (film.FilmRestrictAgeName?.isNotEmpty() == true) {
                    ivTypeAge.visible()
                    ivTypeAge.setImageResource(
                        when (film?.FilmRestrictAgeName?.toLowerCase() ?: "p") {
                            "c18" -> R.drawable.ic_age_c_18
                            "c16" -> R.drawable.c_16
                            "c13" -> R.drawable.ic_age_c_13
                            else -> R.drawable.ic_age_p
                        }
                    )
                } else {
                    ivTypeAge.gone()
                }

                if (section == 0) {
                    fillSTT.visible()
                } else {
                    fillSTT.gone()
                }

                btnPlayTrailer?.setOnClickListener {
                    val intent = Intent(activity, TrailerPlayActivity::class.java)
                    intent.putExtra(Constant.Key.trailerId, film.TrailerURL)
                    activity?.startActivity(intent)
                }
            }
        }

        override fun getSectionCount(): Int {
            return films.size
        }

        override fun showFooters(): Boolean {
            return false
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SectionedViewHolder {
            val view = when (viewType) {
                VIEW_TYPE_HEADER -> {
                    parent.inflate(R.layout.item_film_in_cinema)
                }

                else -> {
                    parent.inflate(R.layout.item_time_booking_of_cinema)
                }
            }
            return SectionHolder(view)
        }

        override fun getItemCount(section: Int): Int {
            return films[section].ListFilm.size
        }

        override fun onBindViewHolder(
            holder: SectionedViewHolder,
            section: Int,
            relativePosition: Int,
            absolutePosition: Int
        ) {
            holder.itemView.apply {
                listTimeMorning.removeAllViews()
                morningScroll.visible()
                val filmBook = films[section].ListFilm[relativePosition]
                tvTypeFilm.text = filmBook.FilmFormatName
                filmBook.ListShow?.let {
                    val shows = it.sortedBy { show -> show.getStartDate() }
                    for (show in shows) {
                        show.FilmFormat = filmBook.FilmFormatName
                        val item = ItemTimeBooking(
                            context,
                            show.getStartDate()?.toStringFormat(Constant.DateFormat.hourMinute)
                                ?: "",
                            "${
                                if (show.TotalSeat == null) 0 else (show.TotalSeat!! - (show.SeatSolded
                                    ?: 0))
                            } ${R.string.empty.getString()}",
                            show,
                            films[section],
                            this@BookByCinemaFragment
                        )
                        if (listTimeMorning.childCount == 0) {
                            item.viewTimeRoot.setPadding(0, 0, 5.px, 0)
                        } else {
                            item.viewTimeRoot.setPadding(5.px, 0, 5.px, 0)
                        }
                        listTimeMorning.addChild(item)
                    }
                }

                if (listTimeMorning.childCount <= 0) {
                    morningScroll.gone()
                }
                bottomLine.visible(relativePosition < (filmBook.ListShow?.size ?: 0) - 1)
            }
        }

        override fun onBindFooterViewHolder(holder: SectionedViewHolder, section: Int) {

        }

    }

    inner class SectionHolder(view: View) : SectionedViewHolder(view)

}

class DateAdapter(
    var selectedIndex: Int = 0,
    var showDates: List<Calendar> = listOf(),
    private var onDateChanged: ((Calendar) -> Unit)?
) : RecyclerView.Adapter<DateHolder>() {

    override fun getItemCount(): Int {
        return showDates.size
    }

    @SuppressLint("SetTextI18n")
    override fun onBindViewHolder(holder: DateHolder, position: Int) {
        val date = showDates[position]
        holder.itemView.tvDate.text = date.toStringFormat("dd")
        holder.itemView.tvDay.text = date.toStringFormat("MM - EEE")
        val today = Calendar.getInstance()
        if (date.get(Calendar.YEAR) == today.get(Calendar.YEAR) &&
            date.get(Calendar.MONTH) == today.get(Calendar.MONTH) &&
            date.get(Calendar.DAY_OF_MONTH) == today.get(Calendar.DAY_OF_MONTH)
        ) {
            holder.itemView.tvDay.text = R.string.today.getString()
        }
        holder.itemView.context?.let {
            if (position == selectedIndex) {
                holder.itemView.tvDate.setTextColor(
                    ResourcesCompat.getColor(
                        it.resources,
                        R.color.textRed,
                        null
                    )
                )
                holder.itemView.tvDay.setTextColor(
                    ResourcesCompat.getColor(
                        it.resources,
                        R.color.textRed,
                        null
                    )
                )
            } else {
                holder.itemView.tvDate.setTextColor(
                    ResourcesCompat.getColor(
                        it.resources,
                        R.color.textGray,
                        null
                    )
                )
                holder.itemView.tvDay.setTextColor(
                    ResourcesCompat.getColor(
                        it.resources,
                        R.color.textGray,
                        null
                    )
                )
            }
        }
        holder.itemView.setOnClickListener {
            selectedIndex = position
            onDateChanged?.invoke(date)
            notifyDataSetChanged()
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): DateHolder {
        val item =
            LayoutInflater.from(parent.context).inflate(R.layout.item_date_in_cinema, parent, false)
        return DateHolder(item)
    }
}

class DateHolder(itemView: View) : RecyclerView.ViewHolder(itemView)
