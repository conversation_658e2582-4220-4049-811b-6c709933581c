package vn.zenity.betacineplex.view.cenima

import android.os.Bundle
import android.view.View
import android.webkit.WebView
import kotlinx.android.synthetic.main.fragment_price.*
import vn.zenity.betacineplex.BuildConfig
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.base.BaseFragment
import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.helper.extension.loadBetaHtml
import vn.zenity.betacineplex.model.NewsModel

/**
 * Created by Zenity.
 */

class CinemaPriceFragment : BaseFragment(), CinemaPriceContractor.View {

    companion object {
        fun getInstance(newId: String?): CinemaPriceFragment {
            val frag = CinemaPriceFragment()
            frag.newId = newId
            return frag
        }
    }

    override val isUsingViewBase = false
    private var newId: String? = null

    override fun showPrivacyPolicy(profile: NewsModel) {
        activity?.runOnUiThread {
//            if (newId != null) {
////                webView?.loadUrl("${BuildConfig.SHARE_DOMAIN}${profile.NewsURI}")
//                webView?.gone()
//                ivPrice?.visible()
//                ivPrice?.load(profile.Duong_dan_anh_dai_dien)
//                return@runOnUiThread
//            }
            val content = profile.getFullContent() ?: profile.Tom_tat_noi_dung
            content?.let {
//                webView?.loadDataWithBaseURL(BuildConfig.BASE_URL, it, "text/html", "utf-8", null)
                webView?.loadBetaHtml(it, true)
            }
        }
    }

    private val presenter = CinemaPricePresenter()

    override fun getPresenter(): IBasePresenter<IBaseView>? {
        return presenter as? IBasePresenter<IBaseView>
    }

    override fun isShowToolbar(): Boolean {
        return false
    }

    override fun getLayoutRes(): Int {
        return R.layout.fragment_price
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        presenter.getPrivacyPolicy(newId)
        webView.settings.loadWithOverviewMode = true
        webView.settings.useWideViewPort = true
        webView.scrollBarStyle = WebView.SCROLLBARS_OUTSIDE_OVERLAY
        webView.isScrollbarFadingEnabled = false
        webView.settings.textZoom = 200
    }
}
