import 'package:flutter/services.dart';
import 'package:geolocator/geolocator.dart';

/// Service to handle native location dialogs and services
class NativeLocationService {
  static const MethodChannel _channel = MethodChannel('native_location');

  /// Request location services with native dialog
  /// Returns true if location services are enabled
  static Future<bool> requestLocationServices() async {
    try {
      // Check current status first
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (serviceEnabled) {
        return true;
      }

      // Try multiple approaches to trigger native dialog
      
      // Approach 1: Request permission (often triggers native dialog on Android)
      try {
        LocationPermission permission = await Geolocator.requestPermission();
        
        // Check if service is now enabled
        serviceEnabled = await Geolocator.isLocationServiceEnabled();
        if (serviceEnabled) {
          return true;
        }
      } catch (e) {
        print('⚠️ Permission request approach failed: $e');
      }

      // Approach 2: Try to get current position (triggers dialog on some devices)
      try {
        await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.high,
          timeLimit: const Duration(seconds: 2),
        );
        
        // If successful, location is enabled
        return true;
      } catch (e) {
        print('⚠️ getCurrentPosition approach failed (expected): $e');
        
        // Wait a moment for potential dialog interaction
        await Future.delayed(const Duration(milliseconds: 1000));
        
        // Check if location is now enabled
        serviceEnabled = await Geolocator.isLocationServiceEnabled();
        if (serviceEnabled) {
          return true;
        }
      }

      // Approach 3: Platform-specific native dialog (if implemented)
      try {
        final result = await _channel.invokeMethod('showLocationDialog');
        if (result == true) {
          serviceEnabled = await Geolocator.isLocationServiceEnabled();
          return serviceEnabled;
        }
      } catch (e) {
        print('⚠️ Native dialog approach failed: $e');
        // This is expected if platform channel is not implemented
      }

      return false;
    } catch (e) {
      print('❌ Error requesting location services: $e');
      return false;
    }
  }

  /// Check if location services are enabled
  static Future<bool> isLocationServiceEnabled() async {
    try {
      return await Geolocator.isLocationServiceEnabled();
    } catch (e) {
      print('❌ Error checking location service: $e');
      return false;
    }
  }

  /// Check location permission status
  static Future<LocationPermission> checkLocationPermission() async {
    try {
      return await Geolocator.checkPermission();
    } catch (e) {
      print('❌ Error checking location permission: $e');
      return LocationPermission.denied;
    }
  }

  /// Request location permission
  static Future<LocationPermission> requestLocationPermission() async {
    try {
      return await Geolocator.requestPermission();
    } catch (e) {
      print('❌ Error requesting location permission: $e');
      return LocationPermission.denied;
    }
  }

  /// Open location settings
  static Future<bool> openLocationSettings() async {
    try {
      return await Geolocator.openLocationSettings();
    } catch (e) {
      print('❌ Error opening location settings: $e');
      return false;
    }
  }

  /// Open app settings
  static Future<bool> openAppSettings() async {
    try {
      return await Geolocator.openAppSettings();
    } catch (e) {
      print('❌ Error opening app settings: $e');
      return false;
    }
  }

  /// Get current position
  static Future<Position?> getCurrentPosition({
    LocationAccuracy accuracy = LocationAccuracy.high,
    Duration? timeLimit,
  }) async {
    try {
      return await Geolocator.getCurrentPosition(
        desiredAccuracy: accuracy,
        timeLimit: timeLimit ?? const Duration(seconds: 10),
      );
    } catch (e) {
      print('❌ Error getting current position: $e');
      return null;
    }
  }

  /// Complete location setup flow
  /// Returns LocationSetupResult with detailed status
  static Future<LocationSetupResult> setupLocation() async {
    try {
      // Step 1: Check if location services are enabled
      bool serviceEnabled = await isLocationServiceEnabled();
      
      if (!serviceEnabled) {
        // Try to enable location services with native dialog
        serviceEnabled = await requestLocationServices();
        
        if (!serviceEnabled) {
          return LocationSetupResult(
            success: false,
            message: 'Dịch vụ định vị chưa được bật',
            action: LocationSetupAction.openLocationSettings,
          );
        }
      }

      // Step 2: Check and request permission
      LocationPermission permission = await checkLocationPermission();
      
      if (permission == LocationPermission.denied) {
        permission = await requestLocationPermission();
      }

      if (permission == LocationPermission.deniedForever) {
        return LocationSetupResult(
          success: false,
          message: 'Quyền truy cập vị trí bị từ chối vĩnh viễn',
          action: LocationSetupAction.openAppSettings,
        );
      }

      if (permission == LocationPermission.denied) {
        return LocationSetupResult(
          success: false,
          message: 'Quyền truy cập vị trí bị từ chối',
          action: LocationSetupAction.requestPermission,
        );
      }

      // Step 3: Success
      return LocationSetupResult(
        success: true,
        message: 'Định vị đã được bật thành công',
        action: LocationSetupAction.none,
      );
    } catch (e) {
      return LocationSetupResult(
        success: false,
        message: 'Lỗi khi thiết lập định vị: ${e.toString()}',
        action: LocationSetupAction.none,
      );
    }
  }
}

/// Result of location setup operation
class LocationSetupResult {
  final bool success;
  final String message;
  final LocationSetupAction action;

  LocationSetupResult({
    required this.success,
    required this.message,
    required this.action,
  });
}

/// Actions that can be taken based on location setup result
enum LocationSetupAction {
  none,
  openLocationSettings,
  openAppSettings,
  requestPermission,
}
