<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14113" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14088"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="Oswald-Regular.ttf">
            <string>Oswald-Regular</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionReusableView opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" id="U6b-Vx-4bR" customClass="SeatCollectionHeaderView" customModule="Booking_dev" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="163" height="151"/>
            <autoresizingMask key="autoresizingMask"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="VgS-Eg-DaR" customClass="GradientView" customModule="Booking_dev" customModuleProvider="target">
                    <rect key="frame" x="0.0" y="0.0" width="163" height="151"/>
                    <subviews>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="A" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="60u-sc-F0R">
                            <rect key="frame" x="0.0" y="0.0" width="163" height="151"/>
                            <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="14"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                    </subviews>
                    <color key="backgroundColor" white="1" alpha="0.5" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstItem="60u-sc-F0R" firstAttribute="leading" secondItem="VgS-Eg-DaR" secondAttribute="leading" id="BDF-5P-XLZ"/>
                        <constraint firstAttribute="trailing" secondItem="60u-sc-F0R" secondAttribute="trailing" id="MqS-Sg-TRo"/>
                        <constraint firstAttribute="bottom" secondItem="60u-sc-F0R" secondAttribute="bottom" id="Rsd-n4-xxh"/>
                        <constraint firstItem="60u-sc-F0R" firstAttribute="top" secondItem="VgS-Eg-DaR" secondAttribute="top" id="ZI6-UG-s3I"/>
                    </constraints>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="color" keyPath="firstColor">
                            <color key="value" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        </userDefinedRuntimeAttribute>
                        <userDefinedRuntimeAttribute type="color" keyPath="secondColor">
                            <color key="value" white="1" alpha="0.10000000000000001" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        </userDefinedRuntimeAttribute>
                        <userDefinedRuntimeAttribute type="point" keyPath="startPoint">
                            <point key="value" x="0.0" y="0.5"/>
                        </userDefinedRuntimeAttribute>
                        <userDefinedRuntimeAttribute type="point" keyPath="endPoint">
                            <point key="value" x="1" y="0.5"/>
                        </userDefinedRuntimeAttribute>
                    </userDefinedRuntimeAttributes>
                </view>
            </subviews>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="VgS-Eg-DaR" firstAttribute="leading" secondItem="U6b-Vx-4bR" secondAttribute="leading" id="4kl-5t-JEd"/>
                <constraint firstItem="VgS-Eg-DaR" firstAttribute="top" secondItem="VXr-Tz-HHm" secondAttribute="top" id="Py2-gX-Sbn"/>
                <constraint firstItem="VXr-Tz-HHm" firstAttribute="bottom" secondItem="VgS-Eg-DaR" secondAttribute="bottom" id="Rvu-br-IJz"/>
                <constraint firstAttribute="trailing" secondItem="VgS-Eg-DaR" secondAttribute="trailing" id="XxR-5u-S4X"/>
            </constraints>
            <viewLayoutGuide key="safeArea" id="VXr-Tz-HHm"/>
            <connections>
                <outlet property="lbText" destination="60u-sc-F0R" id="Egn-hO-twI"/>
            </connections>
            <point key="canvasLocation" x="654.5" y="47.5"/>
        </collectionReusableView>
    </objects>
</document>
