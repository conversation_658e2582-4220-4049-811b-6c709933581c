# API Error Code Mapping Implementation

## Tổ<PERSON> <PERSON>uan

<PERSON> implement hệ thống mapping API error codes thành localized messages tương tự như Android và iOS repositories. Hệ thống này tự động chuyển đổi các error codes như "PHONE_NUMBER_EXISTED", "REGISTRATION_SUCCESS" thành messages đã được localized.

## Phân Tích Cách Xử Lý của Android & iOS

### **Android Implementation:**
```kotlin
// String+Extension.kt
fun String.mapCode(): String {
    return when (this.toUpperCase()) {
        "PHONE_NUMBER_EXISTED" -> R.string.PHONE_NUMBER_EXISTED.getString()
        "EMAIL_EXISTED" -> R.string.EMAIL_EXISTED.getString()
        "REGISTRATION_SUCCESSFULLY" -> R.string.REGISTRATION_SUCCESSFULLY.getString()
        "LOGIN_FAILED" -> R.string.LOGIN_FAILED.getString()
        // ... more mappings
        else -> this
    }
}
```

### **iOS Implementation:**
```swift
// Localizable.strings
"PHONE_NUMBER_EXISTED" = "Phone number is exist";
"EMAIL_EXISTED" = "Email is exist";
"REGISTRATION_SUCCESSFULLY" = "Register successfully";
"LOGIN_FAILED" = "The username or password is incorrect";
```

## Flutter Implementation

### **1. Error Code Mapper** (`lib/utils/src/error_code_mapper.dart`)

#### **String Extension:**
```dart
extension ErrorCodeMapper on String {
  /// Map error code to localized message
  String mapErrorCode() {
    final upperCode = toUpperCase();
    final localizationKey = 'ApiErrors.$upperCode';
    final localizedMessage = localizationKey.tr();
    
    // If translation exists, return it; otherwise return original
    if (localizedMessage != localizationKey) {
      return localizedMessage;
    }
    return this;
  }
  
  /// Check if error code has localized message
  bool hasLocalizedErrorMessage() {
    final upperCode = toUpperCase();
    final localizationKey = 'ApiErrors.$upperCode';
    return localizationKey.tr() != localizationKey;
  }
}
```

#### **API Error Handler:**
```dart
class ApiErrorHandler {
  /// Handle API error response and return appropriate message
  static String handleError(String? errorMessage, {String? fallbackMessage}) {
    if (errorMessage == null || errorMessage.isEmpty) {
      return fallbackMessage ?? 'ApiErrors.UNKNOWN_ERROR'.tr();
    }
    
    final mappedMessage = errorMessage.mapErrorCode();
    return mappedMessage != errorMessage ? mappedMessage : 
           (errorMessage.isNotEmpty ? errorMessage : fallbackMessage ?? 'ApiErrors.UNKNOWN_ERROR'.tr());
  }
  
  /// Handle success message
  static String handleSuccess(String? successMessage) {
    return successMessage?.mapErrorCode() ?? 'ApiErrors.UNKNOWN_ERROR'.tr();
  }
  
  /// Check if message is success
  static bool isSuccessMessage(String? message) {
    if (message == null) return false;
    final upper = message.toUpperCase();
    return upper.contains('SUCCESS') || upper.contains('SUCCESSFULLY');
  }
  
  /// Get message type for UI display
  static MessageType getMessageType(String? message) {
    if (isSuccessMessage(message)) return MessageType.success;
    // ... error detection logic
    return MessageType.error;
  }
}
```

### **2. Translation Keys** (`assets/translations/`)

#### **Vietnamese (vi.json):**
```json
"ApiErrors": {
  "PHONE_NUMBER_EXISTED": "Số điện thoại đã được sử dụng",
  "EMAIL_EXISTED": "Email đã được sử dụng",
  "PERSONAL_ID_EXISTED": "Số chứng minh/hộ chiếu đã được sử dụng",
  "REGISTRATION_SUCCESSFULLY": "Đăng ký thành công",
  "REGISTRATION_SUCCESS": "Đăng ký thành công",
  "LOGIN_FAILED": "Tên đăng nhập hoặc mật khẩu không đúng",
  "LOGIN_SUCCESSFULLY": "Đăng nhập thành công",
  "PASSWORD_INCORRECT": "Mật khẩu không đúng",
  "ACCOUNT_NOT_EXISTED": "Tài khoản không tồn tại",
  "ACCOUNT_LOCKED": "Tài khoản bị khóa",
  "PASSWORD_CHANGED_SUCCESSFULLY": "Thay đổi mật khẩu thành công",
  "UPDATE_SUCCESSFULLY": "Cập nhật thông tin thành công",
  "TIMEOUT": "Lỗi kết nối mạng",
  "SERVER_ERROR": "Có lỗi xảy ra từ máy chủ. Vui lòng thử lại sau.",
  "UNKNOWN_ERROR": "Có lỗi không xác định xảy ra"
}
```

#### **English (en.json):**
```json
"ApiErrors": {
  "PHONE_NUMBER_EXISTED": "Phone number is exist",
  "EMAIL_EXISTED": "Email is exist", 
  "PERSONAL_ID_EXISTED": "Personal ID is exist",
  "REGISTRATION_SUCCESSFULLY": "Register successfully",
  "REGISTRATION_SUCCESS": "Register successfully",
  "LOGIN_FAILED": "The username or password is incorrect",
  "LOGIN_SUCCESSFULLY": "Login successfully",
  "PASSWORD_INCORRECT": "Password is incorrect",
  "ACCOUNT_NOT_EXISTED": "Account is not existed",
  "ACCOUNT_LOCKED": "Account is locked",
  "PASSWORD_CHANGED_SUCCESSFULLY": "Password changed successfully",
  "UPDATE_SUCCESSFULLY": "Update successfully",
  "TIMEOUT": "Network error",
  "SERVER_ERROR": "Some errors occurred. Please try again later.",
  "UNKNOWN_ERROR": "An unknown error occurred"
}
```

### **3. Integration with API Layer**

#### **Updated api.dart:**
```dart
// lib/utils/src/api.dart
if (response.code != 1 && response.message != null && !isThongBao) {
  // Map error code to localized message
  final localizedMessage = ApiErrorHandler.handleError(response.message);
  Timer(const Duration(milliseconds: 50), () => UDialog().showError(text: localizedMessage));
  return null;
}
```

#### **Updated register.dart:**
```dart
// Error handling
final errorMessage = ApiErrorHandler.handleError(
  response?.message, 
  fallbackMessage: 'Register.RegistrationFailed'.tr()
);
ScaffoldMessenger.of(context).showSnackBar(
  SnackBar(content: Text(errorMessage)),
);

// Success handling
Text(
  ApiErrorHandler.handleSuccess('REGISTRATION_SUCCESSFULLY'),
  style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
),
```

## Usage Examples

### **1. Registration Flow:**
```dart
// API returns: "PHONE_NUMBER_EXISTED"
// Vietnamese: "Số điện thoại đã được sử dụng"
// English: "Phone number is exist"

final message = ApiErrorHandler.handleError("PHONE_NUMBER_EXISTED");
ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text(message)));
```

### **2. Login Flow:**
```dart
// API returns: "LOGIN_SUCCESSFULLY" 
// Vietnamese: "Đăng nhập thành công"
// English: "Login successfully"

if (ApiErrorHandler.isSuccessMessage(response.message)) {
  // Navigate to home
} else {
  final errorMessage = ApiErrorHandler.handleError(response.message);
  // Show error dialog
}
```

### **3. Password Change:**
```dart
// API returns: "PASSWORD_CHANGED_SUCCESSFULLY"
// Vietnamese: "Thay đổi mật khẩu thành công"  
// English: "Password changed successfully"

final message = ApiErrorHandler.handleSuccess(response.message);
final messageType = ApiErrorHandler.getMessageType(response.message);
// Show appropriate UI based on message type
```

## Benefits

### **✅ Consistency with Android/iOS:**
- Same error code mapping approach
- Identical localized messages
- Consistent user experience across platforms

### **✅ Automatic Localization:**
- Error codes automatically mapped to current language
- No need to manually handle each error code
- Fallback to original message if no mapping exists

### **✅ Type Safety:**
- MessageType enum for UI styling
- Helper methods for success/error detection
- Centralized error handling logic

### **✅ Easy Maintenance:**
- All error messages in translation files
- Easy to add new error codes
- Consistent naming convention

## Error Code Coverage

### **Authentication:**
- `LOGIN_SUCCESSFULLY` / `LOGIN_FAILED`
- `ACCOUNT_NOT_EXISTED` / `ACCOUNT_LOCKED`
- `PASSWORD_INCORRECT` / `PASSWORD_CHANGED_SUCCESSFULLY`

### **Registration:**
- `REGISTRATION_SUCCESSFULLY` / `REGISTRATION_SUCCESS`
- `REGISTRATION_FAIL`
- `EMAIL_EXISTED` / `PHONE_NUMBER_EXISTED` / `PERSONAL_ID_EXISTED`

### **General:**
- `UPDATE_SUCCESSFULLY`
- `TIMEOUT` / `NETWORK_ERROR` / `SERVER_ERROR`
- `FILE_UPLOAD_TOO_LARGE_1MB`

## Testing

### **Test Widget Available:**
```dart
// lib/utils/src/api_error_example.dart
class ErrorCodeTestWidget extends StatelessWidget {
  // Provides buttons to test different error codes
  // Shows how messages appear in current language
}
```

### **Console Testing:**
```dart
ApiErrorExample.testAllErrorCodes();
// Prints all error codes and their mappings
```

## Ready for Production

Hệ thống error code mapping đã sẵn sàng sử dụng:

1. ✅ **Integrated with API layer** - Tự động áp dụng cho tất cả API calls
2. ✅ **Updated registration flow** - Error và success messages đã được localized  
3. ✅ **Complete translation coverage** - 20+ error codes được support
4. ✅ **Fallback handling** - Unknown codes vẫn hiển thị properly
5. ✅ **Type-safe implementation** - MessageType enum cho UI styling

Users sẽ thấy error messages phù hợp với ngôn ngữ đã chọn, giống hệt như Android và iOS! 🎉
