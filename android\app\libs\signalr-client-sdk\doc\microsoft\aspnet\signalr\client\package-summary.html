<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<title>microsoft.aspnet.signalr.client</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="microsoft.aspnet.signalr.client";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../microsoft/aspnet/signalr/client/package-summary.html">Package</a></li>
<li>Class</li>
<li><a href="package-use.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev Package</li>
<li>Next Package</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?microsoft/aspnet/signalr/client/package-summary.html" target="_top">Frames</a></li>
<li><a href="package-summary.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Package" class="title">Package&nbsp;microsoft.aspnet.signalr.client</h1>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="packageSummary" border="0" cellpadding="3" cellspacing="0" summary="Interface Summary table, listing interfaces, and an explanation">
<caption><span>Interface Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Interface</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../microsoft/aspnet/signalr/client/Action.html" title="interface in microsoft.aspnet.signalr.client">Action</a>&lt;E&gt;</td>
<td class="colLast">
<div class="block">Represents a generic executable action</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../microsoft/aspnet/signalr/client/ConnectionBase.html" title="interface in microsoft.aspnet.signalr.client">ConnectionBase</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../microsoft/aspnet/signalr/client/Credentials.html" title="interface in microsoft.aspnet.signalr.client">Credentials</a></td>
<td class="colLast">
<div class="block">Interface for credentials to be sent in a request</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../microsoft/aspnet/signalr/client/ErrorCallback.html" title="interface in microsoft.aspnet.signalr.client">ErrorCallback</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../microsoft/aspnet/signalr/client/Logger.html" title="interface in microsoft.aspnet.signalr.client">Logger</a></td>
<td class="colLast">
<div class="block">Interface to define a Logger</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../microsoft/aspnet/signalr/client/MessageReceivedHandler.html" title="interface in microsoft.aspnet.signalr.client">MessageReceivedHandler</a></td>
<td class="colLast">
<div class="block">Interface to define a handler for a "Message received" event</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../microsoft/aspnet/signalr/client/PlatformComponent.html" title="interface in microsoft.aspnet.signalr.client">PlatformComponent</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../microsoft/aspnet/signalr/client/StateChangedCallback.html" title="interface in microsoft.aspnet.signalr.client">StateChangedCallback</a></td>
<td class="colLast">
<div class="block">Callback invoked when a connection changes its state</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="packageSummary" border="0" cellpadding="3" cellspacing="0" summary="Class Summary table, listing classes, and an explanation">
<caption><span>Class Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Class</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../microsoft/aspnet/signalr/client/CalendarSerializer.html" title="class in microsoft.aspnet.signalr.client">CalendarSerializer</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../microsoft/aspnet/signalr/client/Connection.html" title="class in microsoft.aspnet.signalr.client">Connection</a></td>
<td class="colLast">
<div class="block">Represents a basic SingalR connection</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../microsoft/aspnet/signalr/client/Constants.html" title="class in microsoft.aspnet.signalr.client">Constants</a></td>
<td class="colLast">
<div class="block">Constants used through the framework</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../microsoft/aspnet/signalr/client/DateSerializer.html" title="class in microsoft.aspnet.signalr.client">DateSerializer</a></td>
<td class="colLast">
<div class="block">Date Serializer/Deserializer to make .NET and Java dates compatible</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../microsoft/aspnet/signalr/client/FutureHelper.html" title="class in microsoft.aspnet.signalr.client">FutureHelper</a></td>
<td class="colLast">
<div class="block">Helper for Future operations</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../microsoft/aspnet/signalr/client/HeartbeatMonitor.html" title="class in microsoft.aspnet.signalr.client">HeartbeatMonitor</a></td>
<td class="colLast">
<div class="block">Heartbeat Monitor to detect slow or timed out connections</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../microsoft/aspnet/signalr/client/MessageResult.html" title="class in microsoft.aspnet.signalr.client">MessageResult</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../microsoft/aspnet/signalr/client/NullLogger.html" title="class in microsoft.aspnet.signalr.client">NullLogger</a></td>
<td class="colLast">
<div class="block">Null logger implementation</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../microsoft/aspnet/signalr/client/Platform.html" title="class in microsoft.aspnet.signalr.client">Platform</a></td>
<td class="colLast">
<div class="block">Platform specific classes and operations</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../microsoft/aspnet/signalr/client/SignalRFuture.html" title="class in microsoft.aspnet.signalr.client">SignalRFuture</a>&lt;V&gt;</td>
<td class="colLast">
<div class="block">Represents long running SignalR operations</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../microsoft/aspnet/signalr/client/SimpleEntry.html" title="class in microsoft.aspnet.signalr.client">SimpleEntry</a>&lt;K,V&gt;</td>
<td class="colLast">
<div class="block">Simple Entry<K,V> implementation</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../microsoft/aspnet/signalr/client/UpdateableCancellableFuture.html" title="class in microsoft.aspnet.signalr.client">UpdateableCancellableFuture</a>&lt;V&gt;</td>
<td class="colLast">
<div class="block">An updateable SignalRFuture that, when cancelled, triggers cancellation on an
 internal instance</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../microsoft/aspnet/signalr/client/Version.html" title="class in microsoft.aspnet.signalr.client">Version</a></td>
<td class="colLast">
<div class="block">Represents a Version of a Product or Library</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="packageSummary" border="0" cellpadding="3" cellspacing="0" summary="Enum Summary table, listing enums, and an explanation">
<caption><span>Enum Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Enum</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../microsoft/aspnet/signalr/client/ConnectionState.html" title="enum in microsoft.aspnet.signalr.client">ConnectionState</a></td>
<td class="colLast">
<div class="block">Represents the state of a connection</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../microsoft/aspnet/signalr/client/LogLevel.html" title="enum in microsoft.aspnet.signalr.client">LogLevel</a></td>
<td class="colLast">
<div class="block">Represents a logging level</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="packageSummary" border="0" cellpadding="3" cellspacing="0" summary="Exception Summary table, listing exceptions, and an explanation">
<caption><span>Exception Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Exception</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../microsoft/aspnet/signalr/client/InvalidProtocolVersionException.html" title="class in microsoft.aspnet.signalr.client">InvalidProtocolVersionException</a></td>
<td class="colLast">
<div class="block">Exception to indicate that the protocol version is different than expected</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../microsoft/aspnet/signalr/client/InvalidStateException.html" title="class in microsoft.aspnet.signalr.client">InvalidStateException</a></td>
<td class="colLast">
<div class="block">Exception to indicate that an operation is not allowed with the connection in
 a specific state</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../microsoft/aspnet/signalr/client/package-summary.html">Package</a></li>
<li>Class</li>
<li><a href="package-use.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev Package</li>
<li>Next Package</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?microsoft/aspnet/signalr/client/package-summary.html" target="_top">Frames</a></li>
<li><a href="package-summary.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
