import 'package:geolocator/geolocator.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Enhanced location service using geolocator
/// Provides comprehensive location management with settings integration
class GeolocatorService {
  static final GeolocatorService _instance = GeolocatorService._internal();
  factory GeolocatorService() =>  _instance;
  GeolocatorService._internal();

  static const String _locationEnabledKey = 'locationEnabled';

  /// Check if location services are enabled and permission is granted
  Future<bool> isLocationEnabled() async {
    try {
      // Check if location services are enabled on device
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        return false;
      }

      // Check location permission
      LocationPermission permission = await Geolocator.checkPermission();
      return permission == LocationPermission.always ||
             permission == LocationPermission.whileInUse;
    } catch (e) {
      print('❌ Error checking location status: $e');
      return false;
    }
  }

  /// Request location permission
  Future<LocationPermission> requestLocationPermission() async {
    try {
      LocationPermission permission = await Geolocator.checkPermission();

      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
      }

      return permission;
    } catch (e) {
      print('❌ Error requesting location permission: $e');
      return LocationPermission.denied;
    }
  }

  /// Enable location services
  Future<LocationServiceResult> enableLocation() async {
    try {
      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        return LocationServiceResult(
          success: false,
          message: 'Dịch vụ định vị bị tắt',
          action: LocationServiceAction.openLocationSettings,
        );
      }

      // Request permission
      LocationPermission permission = await requestLocationPermission();

      if (permission == LocationPermission.deniedForever) {
        return LocationServiceResult(
          success: false,
          message: 'Quyền định vị bị từ chối vĩnh viễn',
          action: LocationServiceAction.openAppSettings,
        );
      }

      if (permission == LocationPermission.denied) {
        return LocationServiceResult(
          success: false,
          message: 'Quyền định vị bị từ chối',
          action: LocationServiceAction.none,
        );
      }

      // Save enabled state
      await _saveLocationEnabled(true);

      return LocationServiceResult(
        success: true,
        message: 'Định vị đã được bật thành công',
        action: LocationServiceAction.none,
      );
    } catch (e) {
      print('❌ Error enabling location: $e');
      return LocationServiceResult(
        success: false,
        message: 'Lỗi khi bật định vị: ${e.toString()}',
        action: LocationServiceAction.none,
      );
    }
  }

  /// Disable location services
  Future<LocationServiceResult> disableLocation() async {
    try {
      await _saveLocationEnabled(false);

      return LocationServiceResult(
        success: true,
        message: 'Định vị đã được tắt',
        action: LocationServiceAction.none,
      );
    } catch (e) {
      print('❌ Error disabling location: $e');
      return LocationServiceResult(
        success: false,
        message: 'Lỗi khi tắt định vị: ${e.toString()}',
        action: LocationServiceAction.none,
      );
    }
  }

  /// Get current position
  Future<Position?> getCurrentPosition({
    LocationAccuracy accuracy = LocationAccuracy.high,
    Duration? timeLimit,
  }) async {
    try {
      // Check if location is enabled
      bool enabled = await isLocationEnabled();
      if (!enabled) {
        throw Exception('Định vị chưa được bật');
      }

      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: accuracy,
        timeLimit: timeLimit ?? const Duration(seconds: 10),
      );

      return position;
    } catch (e) {
      print('❌ Error getting current position: $e');
      rethrow;
    }
  }

  /// Open location settings
  Future<bool> openLocationSettings() async {
    try {
      return await Geolocator.openLocationSettings();
    } catch (e) {
      print('❌ Error opening location settings: $e');
      return false;
    }
  }

  /// Open app settings
  Future<bool> openAppSettings() async {
    try {
      return await Geolocator.openAppSettings();
    } catch (e) {
      print('❌ Error opening app settings: $e');
      return false;
    }
  }

  /// Get distance between two points
  double getDistanceBetween(
    double startLatitude,
    double startLongitude,
    double endLatitude,
    double endLongitude,
  ) {
    return Geolocator.distanceBetween(
      startLatitude,
      startLongitude,
      endLatitude,
      endLongitude,
    );
  }

  /// Save location enabled state to preferences
  Future<void> _saveLocationEnabled(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_locationEnabledKey, enabled);
  }

  /// Get location enabled state from preferences
  Future<bool> getLocationEnabledFromPrefs() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_locationEnabledKey) ?? false;
  }

  /// Check if location permission is permanently denied
  Future<bool> isLocationPermissionPermanentlyDenied() async {
    try {
      LocationPermission permission = await Geolocator.checkPermission();
      return permission == LocationPermission.deniedForever;
    } catch (e) {
      print('❌ Error checking permission status: $e');
      return false;
    }
  }

  /// Get location permission status
  Future<LocationPermission> getLocationPermissionStatus() async {
    try {
      return await Geolocator.checkPermission();
    } catch (e) {
      print('❌ Error getting permission status: $e');
      return LocationPermission.denied;
    }
  }
}

/// Result of location service operations
class LocationServiceResult {
  final bool success;
  final String message;
  final LocationServiceAction action;

  LocationServiceResult({
    required this.success,
    required this.message,
    required this.action,
  });
}

/// Actions that can be taken based on location service results
enum LocationServiceAction {
  none,
  openLocationSettings,
  openAppSettings,
}
