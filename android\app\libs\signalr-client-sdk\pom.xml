<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>microsoft.aspnet.signalr</groupId>
  <artifactId>signalr-client-sdk</artifactId>
  <version>1.0</version>
  <name>SignalR Client Library for Java</name>
  <description>SignalR Client Library for Java</description>
  <repositories>
      <repository>
          <id>central</id>
          <name>Central</name>
          <url>http://maven.eclipse.org/build</url>
      </repository>
  </repositories>
  <build>
    <sourceDirectory>src</sourceDirectory>
    <resources>
      <resource>
        <directory>src</directory>
        <excludes>
          <exclude>**/*.java</exclude>
        </excludes>
      </resource>
    </resources>
    <plugins>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.0</version>
        <configuration>
          <source>1.6</source>
          <target>1.6</target>
        </configuration>
      </plugin>
    </plugins>
  </build>
  <dependencies>
  	<dependency>
  		<groupId>com.google.code.gson</groupId>
  		<artifactId>gson</artifactId>
  		<version>2.2.2</version>
  	</dependency>
  </dependencies>
</project>