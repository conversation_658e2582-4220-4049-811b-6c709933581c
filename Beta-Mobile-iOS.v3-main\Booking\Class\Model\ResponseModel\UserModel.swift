//
//  UserModel.swift
//  Booking
//
//  Created by Tinh Vu on 4/4/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation
import ObjectMapper

class UserModel : Mappable {
     var FirstName : String?
     var LastName : String?
     var AccountTypeId : String?
     var AccountTypeName : String?
     var TeamId : String?
     var Picture : String?
     var PersonalIncomeTaxCode : String?
     var EnterpriseId : String?
     var Rating : Int?
     var IsDeleted : Bool?
     var Status : Int?
     var CreatedByUserId : String?
     var CreatedOnDate : String?
     var LastModifiedByUserId : String?
     var LastModifiedOnDate : String?
     var Description : String?
     var PhoneOffice : String?
     var Email : String?
     var PersonalId : String?
     var BirthDate : String?
     var Gender : Int?
     var RegisterPlaceId : String?
     var RegisterPlaceName : String?
     var RegisterSiteId : String?
     var RegisterSiteName : String?
     var ReferenceCode : String?
     var ReferenceAccountId : String?
     var ReferenceAccountRefCode : String?
     var AddressStreet : String?
     var AddressDistrict : String?
     var AddressCityId : String?
     var AddressDistrictId : String?
     var AddressCity : String?
     var BillingAddressStreet : String?
     var BillingAddressDistrict : String?
     var BillingAddressCity : String?
     var ShippingAddressDistrict : String?
     var ShippingAddressStreet : String?
     var ShippingAddressCity : String?
     var IsReceiveEmail : Bool?
     var IsReceiveSMS : Bool?
     var OnlineId : String?
     var IsInvalidEmail : Bool?
     var TotalAccumulatedPoints : Int?
    var TotalSpentPoints : Int?
     var TotalBillPayment : Int64?
     var AvailablePoint : Int?
     var TotalPoint : Int?
     var AccountId : String?
     var FullName : String?
     var Code : String?
     var ClassId : String?
     var CardId : String?
     var CardNumber : String?
     var ApplicationId : String?
    var Token: String?
    var UserId: String?
    var IsUpdatedFacebookPassword: Bool?
    var IsUpdatedApplePassword: Bool?
    var TotalRemainingBillsToUpgradeClass: Double?
    var QuantityOfVoucher: Int?
    var ClassCode: String?
    var ClassName: String?
    var AlmostExpiredPoint : Int?
    var AlmostExpiredPointDate : String?


    required init?(map: Map) {
        
    }

    func mapping(map: Map) {
        FirstName            <- map["FirstName"]
        LastName             <- map["LastName"]
        AccountTypeId        <- map["AccountTypeId"]
        AccountTypeName      <- map["AccountTypeName"]
        TeamId               <- map["TeamId"]
        Picture              <- map["Picture"]
        PersonalIncomeTaxCode <- map["PersonalIncomeTaxCode"]
        EnterpriseId         <- map["EnterpriseId"]
        Rating               <- map["Rating"]
        IsDeleted            <- map["IsDeleted"]
        Status               <- map["Status"]
        CreatedByUserId      <- map["CreatedByUserId"]
        CreatedOnDate        <- map["CreatedOnDate"]
        LastModifiedByUserId <- map["LastModifiedByUserId"]
        LastModifiedOnDate   <- map["LastModifiedOnDate"]
        Description          <- map["Description"]
        PhoneOffice          <- map["PhoneOffice"]
        Email                <- map["Email"]
        PersonalId           <- map["PersonalId"]
        BirthDate            <- map["BirthDate"]
        Gender               <- map["Gender"]
        RegisterPlaceId      <- map["RegisterPlaceId"]
        RegisterPlaceName    <- map["RegisterPlaceName"]
        RegisterSiteId       <- map["RegisterSiteId"]
        RegisterSiteName     <- map["RegisterSiteName"]
        ReferenceCode        <- map["ReferenceCode"]
        ReferenceAccountId   <- map["ReferenceAccountId"]
        ReferenceAccountRefCode <- map["ReferenceAccountRefCode"]
        AddressStreet        <- map["AddressStreet"]
        AddressDistrict      <- map["AddressDistrict"]
        AddressCityId        <- map["AddressCityId"]
        AddressDistrictId    <- map["AddressDistrictId"]
        AddressCity          <- map["AddressCity"]
        BillingAddressStreet <- map["BillingAddressStreet"]
        BillingAddressDistrict <- map["BillingAddressDistrict"]
        BillingAddressCity   <- map["BillingAddressCity"]
        ShippingAddressDistrict <- map["ShippingAddressDistrict"]
        ShippingAddressStreet <- map["ShippingAddressStreet"]
        ShippingAddressCity  <- map["ShippingAddressCity"]
        IsReceiveEmail       <- map["IsReceiveEmail"]
        IsReceiveSMS         <- map["IsReceiveSMS"]
        OnlineId             <- map["OnlineId"]
        IsInvalidEmail       <- map["IsInvalidEmail"]
        TotalAccumulatedPoints <- map["TotalAccumulatedPoints"]
        TotalBillPayment     <- map["TotalBillPayment"]
        AvailablePoint       <- map["AvailablePoint"]
        TotalPoint           <- map["TotalPoint"]
        AccountId            <- map["AccountId"]
        FullName             <- map["FullName"]
        Code                 <- map["Code"]
        ClassId              <- map["ClassId"]
        CardId               <- map["CardId"]
        CardNumber           <- map["CardNumber"]
        ApplicationId        <- map["ApplicationId"]
        Token <- map["Token"]
        UserId <- map["UserId"]
        TotalSpentPoints <- map["TotalSpentPoints"]
        IsUpdatedFacebookPassword <- map["IsUpdatedFacebookPassword"]
        IsUpdatedApplePassword <- map["IsUpdatedApplePassword"]
        TotalRemainingBillsToUpgradeClass <- map["TotalRemainingBillsToUpgradeClass"]
        QuantityOfVoucher <- map["QuantityOfVoucher"]
        ClassCode <- map["ClassCode"]
        ClassName <- map["ClassName"]
        AlmostExpiredPoint   <- map["AlmostExpiredPoint"]
        AlmostExpiredPointDate  <- map["AlmostExpiredPointDate"]
    }
}
