package vn.zenity.betacineplex.view.auth

import io.reactivex.disposables.Disposable
import vn.zenity.betacineplex.Manager.Network.APIClient
import vn.zenity.betacineplex.app.App
import vn.zenity.betacineplex.helper.extension.applyOn
import java.lang.ref.WeakReference

/**
 * Created by Zenity.
 */

class PaymentPolicyPresenter : PaymentPolicyContractor.Presenter {

    private var disposable: Disposable? = null
    override fun getPaymentPolicy() {
        view?.get()?.showLoading()
        val lang = App.shared().getCurrentLang()
        disposable = APIClient.shared.ecmAPI.getPaymentPolicyId("mobile:app:dieukhoan-thanhtoan:$lang").applyOn()
                .subscribe({
                    if (it.isSuccess) {
                        it.Data?.ParameterValue?.let {
                            APIClient.shared.ecmAPI.getNewWithId(it).applyOn()
                                    .subscribe({
                                        if (it.Data != null) {
                                            view?.get()?.showPaymentPolicy(it.Data!!)
                                        }
                                    }, {
                                    })
                        }
                    }
                    this.view?.get()?.hideLoading()
                }, {
                    this.view?.get()?.hideLoading()
                })
    }

    private var view: WeakReference<PaymentPolicyContractor.View?>? = null
    override fun attachView(view: PaymentPolicyContractor.View) {
        this.view = WeakReference(view)
    }

    override fun detachView() {
        disposable?.dispose()
        this.view?.clear()
        this.view = null
    }
}
