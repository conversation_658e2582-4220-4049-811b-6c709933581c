# 🔄 Payment Flow Toggle - Hướng dẫn sử dụng

## 🎯 **Tổng quan**

Payment Flow Toggle cho phép bạn dễ dàng chuyển đổi giữa 2 loại payment flow:
- **Direct Payment**: WebView → Payment App → App (callback)
- **Web-based Payment**: WebView → Web → Payment App → Web → WebView

## 🚀 **Cách sử dụng nhanh**

### **1. Toggle nhanh trong code:**

```dart
// Toggle giữa direct và web-based payment
await PaymentFlowToggle.quickToggle();

// Hoặc set cụ thể
await PaymentFlowToggle.setFlowType(PaymentFlowType.direct);
await PaymentFlowToggle.setFlowType(PaymentFlowType.webBased);
```

### **2. Sử dụng Debug Panel (UI):**

```dart
// Thêm debug panel vào bất kỳ screen nào
class PaymentScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: YourPaymentContent(),
    ).withPaymentDebug(); // ← Thêm dòng này
  }
}

// Hoặc sử dụng trực tiếp
Stack(
  children: [
    YourMainContent(),
    PaymentDebugPanel(), // ← Debug panel sẽ xuất hiện góc phải
  ],
)
```

### **3. Sử dụng Smart Payment WebView:**

```dart
// Thay thế WebView cũ bằng SmartPaymentWebView
SmartPaymentWebView(
  htmlData: paymentHtml,
  baseUrl: apiBaseUrl,
  onPaymentMethodSelected: (method) {
    print('Payment method: $method');
  },
  onPaymentResult: (result) {
    print('Payment result: $result');
  },
)
```

## 🛠️ **Các tính năng chính**

### **1. PaymentFlowToggle Service**

```dart
// Kiểm tra flow hiện tại
final flowType = await PaymentFlowToggle.getCurrentFlowType();
final isDirect = await PaymentFlowToggle.isDirectPaymentEnabled();

// Thay đổi flow
await PaymentFlowToggle.setFlowType(PaymentFlowType.direct);

// Toggle nhanh
final newFlow = await PaymentFlowToggle.toggleFlow();

// Reset về default
await PaymentFlowToggle.resetToDefault();

// Enable debug mode
await PaymentFlowToggle.setDebugMode(true);
```

### **2. Debug Panel Features**

- 🎛️ **Visual Toggle**: Click vào icon payment góc phải màn hình
- 📊 **Flow Status**: Hiển thị flow hiện tại và mô tả
- 🔄 **Quick Toggle**: Nút toggle nhanh giữa direct/web
- ⚙️ **Settings**: Chọn flow type cụ thể
- 🐛 **Debug Mode**: Enable/disable debug logging
- 🔄 **Reset**: Reset về settings mặc định

### **3. Smart WebView Features**

- 🧠 **Auto Detection**: Tự động detect payment URLs
- 🚀 **Smart Routing**: Route theo flow type đã chọn
- 🔄 **Fallback**: Tự động fallback về web nếu direct fail
- 📊 **Analytics**: Log events cho tracking
- 🎨 **Visual Indicator**: Hiển thị flow type trên app bar

## 📱 **Demo Usage**

### **Scenario 1: Testing Direct Payment**

```dart
// 1. Enable direct payment
await PaymentFlowToggle.setFlowType(PaymentFlowType.direct);

// 2. Navigate to payment screen
Navigator.push(context, MaterialPageRoute(
  builder: (context) => SmartPaymentWebView(
    htmlData: paymentHtml,
    baseUrl: baseUrl,
    onPaymentMethodSelected: (method) {
      print('🚀 Direct payment: $method');
    },
    onPaymentResult: (result) {
      print('✅ Payment result: $result');
    },
  ),
));

// 3. Khi user chọn Momo → App sẽ mở Momo app trực tiếp
// 4. Sau khi thanh toán → Callback trực tiếp về app
```

### **Scenario 2: Testing Web-based Payment**

```dart
// 1. Enable web-based payment
await PaymentFlowToggle.setFlowType(PaymentFlowType.webBased);

// 2. Navigate to payment screen (same code)
// 3. Khi user chọn Momo → App sẽ mở web browser
// 4. Web browser → Momo app → Web browser → App
```

### **Scenario 3: Quick Toggle During Development**

```dart
// Thêm vào FloatingActionButton hoặc debug menu
FloatingActionButton(
  onPressed: () async {
    await PaymentFlowToggle.quickToggle();
    
    final newFlow = await PaymentFlowToggle.getCurrentFlowType();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Switched to: ${newFlow.displayName}')),
    );
  },
  child: Icon(Icons.payment),
)
```

## 🎛️ **Debug Panel Usage**

### **Cách mở Debug Panel:**

1. **Automatic**: Panel tự động hiện trong debug mode
2. **Manual**: Click vào icon payment góc phải màn hình
3. **Force Show**: Set `forceShow: true` để hiện cả trong release mode

### **Các controls trong panel:**

- **🟢 Direct**: Chuyển sang direct payment
- **🔵 Web-based**: Chuyển sang web-based payment  
- **🟠 Auto**: Để system tự chọn
- **🐛 Debug Mode**: Enable detailed logging
- **🔄 Quick Toggle**: Toggle nhanh giữa direct/web
- **🔄 Reset**: Reset về default settings

## 📊 **Monitoring & Logging**

### **Enable Logging:**

```dart
// Enable debug mode để xem logs
await PaymentFlowToggle.setDebugMode(true);

// Logs sẽ hiển thị:
// 🔄 Payment flow changed to: Direct Payment
// 💳 Direct payment attempt: momo
// 🌐 Web payment fallback: momo - Payment app not installed
```

### **Custom Analytics:**

```dart
// Track flow changes
PaymentFlowEvents.logFlowChange(
  PaymentFlowType.webBased, 
  PaymentFlowType.direct
);

// Track payment attempts
PaymentFlowEvents.logDirectPaymentAttempt('momo');

// Track fallbacks
PaymentFlowEvents.logWebPaymentFallback('momo', 'App not installed');
```

## 🧪 **Testing Scenarios**

### **Test Case 1: Direct Payment Success**
```dart
// 1. Set direct payment
await PaymentFlowToggle.setFlowType(PaymentFlowType.direct);

// 2. Ensure Momo app is installed
// 3. Go to payment screen
// 4. Select Momo payment
// 5. Verify: Momo app opens directly
// 6. Complete payment in Momo
// 7. Verify: App receives callback directly
```

### **Test Case 2: Direct Payment Fallback**
```dart
// 1. Set direct payment
await PaymentFlowToggle.setFlowType(PaymentFlowType.direct);

// 2. Uninstall Momo app (or use emulator without Momo)
// 3. Go to payment screen
// 4. Select Momo payment
// 5. Verify: Falls back to web-based payment
// 6. Verify: Web browser opens instead
```

### **Test Case 3: Quick Toggle**
```dart
// 1. Start with web-based payment
// 2. Use debug panel to toggle to direct
// 3. Verify: Flow changes immediately
// 4. Test payment with new flow
// 5. Toggle back and test again
```

## 🔧 **Configuration**

### **Feature Flags:**

```dart
// lib/services/payment_flow_toggle.dart
class PaymentFlowConfig {
  static const bool enableMomoDirectPayment = true;
  static const bool enableZaloPayDirectPayment = true;
  static const bool enableAirPayDirectPayment = true;
  static const bool enableFallbackToWeb = true;
  static const bool showPaymentFlowIndicator = true;
}
```

### **Environment Variables:**

```bash
# Enable direct payment for all users
flutter run --dart-define=DIRECT_PAYMENT=true

# Enable for 50% of users (A/B testing)
flutter run --dart-define=DIRECT_PAYMENT_ROLLOUT=50.0

# Force show debug panel in release
flutter run --dart-define=PAYMENT_DEBUG=true
```

## 🎯 **Best Practices**

### **1. Development:**
- Luôn enable debug mode khi develop
- Sử dụng debug panel để test nhanh
- Test cả direct và web-based flows

### **2. Testing:**
- Test trên device có và không có payment apps
- Test network conditions khác nhau
- Test với các payment methods khác nhau

### **3. Production:**
- Sử dụng auto selection hoặc A/B testing
- Monitor fallback rates
- Có fallback mechanism cho mọi trường hợp

## 🚀 **Quick Start Commands**

```dart
// Enable direct payment
await PaymentFlowToggle.setFlowType(PaymentFlowType.direct);

// Quick toggle
await PaymentFlowToggle.quickToggle();

// Check current status
final isDirect = await PaymentFlowToggle.isDirectPaymentEnabled();
print('Direct payment: $isDirect');

// Reset everything
await PaymentFlowToggle.resetToDefault();
```

**Bây giờ bạn có thể dễ dàng toggle giữa 2 payment flows để test!** 🎉
