package vn.zenity.betacineplex.view.user.point

import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.model.PaymentHistory
import vn.zenity.betacineplex.model.PointHistoryModel
import vn.zenity.betacineplex.model.VoucherHistoryModel
import vn.zenity.betacineplex.model.VoucherModel

/**
 * Created by Zenity.
 */

interface PointHistoryContractor {
    interface View : IBaseView {
        fun showUsedPointHistories(histories: List<PointHistoryModel>)
    }

    interface Presenter : IBasePresenter<View> {
        fun getUsedPointHistories()
    }
}
