import android.graphics.Bitmap
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.Drawable
import android.widget.ImageView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.request.target.ImageViewTarget
import com.bumptech.glide.request.transition.Transition
import vn.zenity.betacineplex.helper.extension.getColorByString
import vn.zenity.betacineplex.helper.extension.initialsFromString
import vn.zenity.betacineplex.helper.thirtypart.TextDrawable
import com.google.zxing.common.BitMatrix
import com.google.zxing.MultiFormatWriter
import com.google.zxing.EncodeHintType
import com.google.zxing.WriterException
import com.google.zxing.BarcodeFormat
import vn.zenity.betacineplex.helper.extension.px
import java.util.*


fun ImageView.load(resource: Any?,
                   animate: Boolean = true,
                   placeHolder: Any? = null,
                   error: Any? = null,
                   thumbnail: Float = 0f,
                   fitCenter: Boolean = false,
                   centerCrop: Boolean = false,
                   isCirular: Boolean = false,
                   diskCacheStrategy: DiskCacheStrategy? = null, callback: ((Bitmap) -> Unit)? = null) {

    var request = Glide.with(context).load(resource)
    val options = RequestOptions()
    if (!animate) {
        options.dontAnimate()
    }

    if (fitCenter) {
        options.fitCenter()
    }

    if (centerCrop) {
        options.centerCrop()
    }

    if (thumbnail != 0f) {
        request.thumbnail(thumbnail)
    }

    diskCacheStrategy?.apply {
        options.diskCacheStrategy(diskCacheStrategy)
    }

    placeHolder?.apply {
        if (placeHolder is Drawable) {
            options.placeholder(placeHolder)
        } else if (placeHolder is Int) {
            options.placeholder(placeHolder)
        }
    }

    error?.apply {
        if (error is Drawable) {
            options.error(error)
        } else if (error is Int) {
            options.error(error)
        }
    }

    if (isCirular) {
        options.circleCrop()
                .autoClone()
    }
    request.apply(options)

    if (callback != null) {
        request.into(object : ImageViewTarget<Drawable>(this) {
            override fun setResource(resource: Drawable?) {
                <EMAIL>(resource)
            }

            override fun onResourceReady(resource: Drawable?, transition: Transition<in Drawable>?) {
                super.onResourceReady(resource, transition)
                val bitmap = (resource as? BitmapDrawable)?.bitmap
                bitmap?.let {
                    <EMAIL>(it)
                    callback.invoke(it)
                }
            }
        })
    } else {
        request.into(this)
    }

}

fun ImageView.setImageForName(string: String, backgroundColor: Int? = null, circuler: Boolean = true, customWidth: Int? = null, customHeight: Int? = null) {
    val builder = TextDrawable.builder().beginConfig()
            .height(if (height > 0) height else customWidth ?: 100)
            .width(if (width > 0) width else customHeight ?: 100)
            .endConfig()
    var color = backgroundColor
    val text = string.initialsFromString()
    if (color == null) {
        color = getColorByString(text)
    }
    if (circuler) {
        setImageDrawable(builder.buildRound(text, color))
    } else {
        setImageDrawable(builder.buildRect(text, color))
    }
}

fun ImageView.showBarcode(barcode: String) {
    try {
        val bitmap = barcode.encodeAsBitmap(BarcodeFormat.CODE_128, if(width > 10) width else 600, if(height > 10) height else 200 )
        this.setImageBitmap(bitmap ?: return)
    } catch (ex : Exception){
        ex.printStackTrace()
    }
}

fun ImageView.showQRcode(qrCode: String) {
    try {
        val bitmap = qrCode.encodeAsQRCode(if(width > 10) width else 600)
        this.setImageBitmap(bitmap ?: return)
    } catch (ex : Exception){
        ex.printStackTrace()
    }
}

@Throws(WriterException::class)
fun String.encodeAsQRCode(codeWidth: Int): Bitmap? {
    val WHITE: Int = 0xFFFFFFFF.toInt()
    val BLACK: Int = 0xFF000000.toInt()
    var hints: MutableMap<EncodeHintType, Any>? = null
    val encoding = this.guessAppropriateEncoding()
    if (encoding != null) {
        hints = EnumMap<EncodeHintType, Any>(EncodeHintType::class.java)
        hints[EncodeHintType.CHARACTER_SET] = encoding
    }
    val writer = MultiFormatWriter()
    val result: BitMatrix
    try {
        result = writer.encode(this, BarcodeFormat.QR_CODE, codeWidth, codeWidth, hints)
    } catch (iae: IllegalArgumentException) {
        // Unsupported format
        return null
    }

    val width = result.width
    val height = result.height

    val pixels = IntArray(width * height)
    for (y in 0 until height) {
        val offset = y * width
        for (x in 0 until width) {
            val isBlack = result.get(x, y)
            pixels[offset + x] = if (isBlack) BLACK else WHITE
        }
    }


    val bitmap = Bitmap.createBitmap(width, height,
            Bitmap.Config.ARGB_8888)
    bitmap.setPixels(pixels, 0, width, 0, 0, width, height)
    return bitmap
}

@Throws(WriterException::class)
fun String.encodeAsBitmap(format: BarcodeFormat, img_width: Int, img_height: Int): Bitmap? {
    val WHITE: Int = 0xFFFFFFFF.toInt()
    val BLACK: Int = 0xFF000000.toInt()
    var hints: MutableMap<EncodeHintType, Any>? = null
    val encoding = this.guessAppropriateEncoding()
    if (encoding != null) {
        hints = EnumMap<EncodeHintType, Any>(EncodeHintType::class.java)
        hints[EncodeHintType.CHARACTER_SET] = encoding
    }
    val writer = MultiFormatWriter()
    val result: BitMatrix
    try {
        result = writer.encode(this, format, img_width, img_height, hints)
    } catch (iae: IllegalArgumentException) {
        // Unsupported format
        return null
    }

    val width = result.width
    val height = result.height

    var minBlack = width
    var maxBlack = 0
//    var minBlack = 0
//    var maxBlack = width
    for (y in 0 until height) {
        for (x in 0 until width) {
            val isBlack = result.get(x, y)
            if(isBlack) {
                if (x > maxBlack) maxBlack = x
                if (x < minBlack) minBlack = x
            }
        }
    }

    val pixels = IntArray((maxBlack - minBlack) * height)
    for (y in 0 until height) {
        val offset = y * (maxBlack - minBlack)
        for (x in 0 until (maxBlack - minBlack)) {
            val isBlack = result.get(x + minBlack, y)
            pixels[offset + x] = if (isBlack) BLACK else WHITE
        }
    }


    val bitmap = Bitmap.createBitmap(maxBlack - minBlack, height,
            Bitmap.Config.ARGB_8888)
    bitmap.setPixels(pixels, 0, maxBlack - minBlack, 0, 0, maxBlack - minBlack, height)
    return bitmap
}

fun CharSequence.guessAppropriateEncoding(): String? {
    // Very crude at the moment
    for (i in 0 until this.length) {
        if (this[i].toInt() > 0xFF) {
            return "UTF-8"
        }
    }
    return null
}

fun ImageView.
        loadRounded(url: Any?, radius: Int = 5.px) {
    if (url == null) {
        return
    }
    val options = RequestOptions()
    options.transforms(CenterCrop(), RoundedCorners(radius))
    Glide.with(this.context)
            .load(url)
            .apply(options)
            .into(this)
}
