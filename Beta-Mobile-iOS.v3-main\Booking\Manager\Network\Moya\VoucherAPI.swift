//
//  VoucherAPI.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 4/25/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation
import Moya

public enum CardType: CustomStringConvertible{
    public var description: String{
        switch self {
        case .Coupon:
            return "Coupon"
        default:
            return "Voucher"
        }
    }
    
    case Coupon, Voucher
    
}

enum Voucher{
    case getVoucher
    case registerVoucher(RegisterVoucherModel)
    case donate(String, String)
    case getFreeVoucher(String)
    case getFreeVoucherDetail(String)
    case history
    case donatePoint(String, Int)
    case historyPoint
}

let VoucherProvider = MoyaProvider<Voucher>(plugins: [NetworkLoggerPlugin(verbose: true, responseDataFormatter: nil)])

extension Voucher: TargetType {
    
    public var baseURL: URL { return URL(string: Config.BaseURL)! }
    
    
    public var path: String {
        switch self {
        case .getVoucher:
            return "api/v2/erp/voucher"
        case .registerVoucher:
            return "api/v2/erp/voucher/assign"
        case .donate:
            return "api/v2/erp/voucher/donate"
        case .getFreeVoucher(let storylineId):
            return "api/v2/erp/storyline/{\(storylineId)}/voucher-code"
        case .history:
            return "api/v2/erp/voucher/history"
        case .donatePoint:
            return "api/v2/erp/point/donate"
        case .historyPoint:
            return "api/v2/erp/point/history"
        case .getFreeVoucherDetail(let id):
            return "api/v2/erp/voucher/voucher-info/\(id)"
        }
    }

    public var method: Moya.Method {
        switch self {
        case .getVoucher, .history, .historyPoint, .getFreeVoucherDetail:
            return .get
        case .registerVoucher:
            return .put
        default:
            return .post
        }
    }
    public var parameters: [String: Any]? {
        switch self {
        case .registerVoucher(let model):
            return model.toJSON()
        case .donate(let email, let voucherId):
            return ["email": email,
                    "voucherId": voucherId]
        case .donatePoint(let email, let point):
            return ["email": email,
                    "point": point]
        default:
            return [:]
        }
    }
    public var task: Task {
        switch self {
        case .registerVoucher, .donate, .donatePoint:
            return Task.requestParameters(parameters: self.parameters!, encoding: JSONEncoding.default)
        default:
            return Task.requestParameters(parameters: self.parameters!, encoding: URLEncoding.default)
        }
        
    }
    public var headers: [String : String]? {
        return Global.shared.headers()
    }
    
    public var sampleData: Data{
        return "".data(using: String.Encoding.utf8)!
    }
}
