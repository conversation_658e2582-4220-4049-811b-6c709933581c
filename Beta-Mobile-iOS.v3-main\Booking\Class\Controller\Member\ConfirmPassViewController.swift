//
//  ConfirmPassViewController.swift
//  Booking-dev
//
//  Created by <PERSON><PERSON> on 4/18/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit

class ConfirmPassViewController: BaseViewController {
    @IBOutlet weak var tfPassword: InputTextField!
    
    override func viewDidLoad() {
        super.viewDidLoad()
        localizableTitle = "AccountInfo.Title"

        tfPassword.becomeFirstResponder()
    }

    override func didReceiveMemoryWarning() {
        super.didReceiveMemoryWarning()
    }

    @IBAction func confirmBtPressed(_ sender: Any) {
        guard let text = tfPassword.text, !text.isEmpty else{
            UIAlertController.showAlert(self, message: "Alert.PasswordNotEmpty".localized)
            return
        }
        
        guard let email = Global.shared.user?.Email else {
            print("Can't get email from logged User")
            return
        }
        self.view.endEditing(true)
        self.showLoading()
        AccountProvider.rx.request(.confirmPass(email, text)).mapObject(DDKCResponse<ConfirmModel>.self)
        
            .subscribe(onNext: {[weak self] (response) in
                guard let `self` = self else {return}
                self.dismissLoading()
                guard let confirm = response.Object else{
                    print("Data wrong")
                    self.flashError(title: "Alert.Error".localized, message: "Alert.ErrorServer".localized)
                    return
                }
                
                if let confirm = confirm.Result, confirm{
                    let vc = UIStoryboard.member[.accountInfo]
                    self.show(vc, sender: nil)
                }else{
                    self.flashError(title: "Alert.Error".localized, message: response.Message ?? "Alert.ErrorServer".localized)
                }
            }).disposed(by: disposeBag)
    }
}

extension ConfirmPassViewController: UITextFieldDelegate {
    func textFieldShouldReturn(_ textField: UITextField) -> Bool {
        confirmBtPressed(textField)
        return true
    }
}
