//
//  VoucherModel.swift
//  Booking
//
//  Created by <PERSON><PERSON> Vu on 4/26/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation
import ObjectMapper

enum MyVoucherType: Int, CustomStringConvertible {
    case buy = 1
    case free = 2

    var description: String {
        switch self {
        case .buy:
            return "buy"
        case .free:
            return "free"
        }
    }

    var color: UIColor {
        switch self {
        case .buy:
            return 0xfd7c02.toColor
        default:
            return 0x03599d.toColor
        }
    }
}

class VoucherModel  : Mappable {
     var Voucher_Card_Id : String?
     var PinCode : String?
     var VoucherCode : String?
     var Unit : String?
     var VoucherName : String?
     var CardTypeName : String?
     var ConditionOfUse : String?
     var UnitPrice : Int?
     var StartDate : String?
     var EndDate : String?
     var DateOfStatus : String?
     var Status : Int?
     var StatusName : String?
     var CustomerId : String?
     var CustomerCard : String?
    var VoucherId: String?
    var VoucherPackageName: String?

    var Description : String?
    var ExpirationDate : String?
    var VoucherStatus: Int = 0
    var StorylineId: String?
    var IsSell: Bool = false
    var IsAvaiableForGift: Bool = false
    var StorylineContent: String?
    var type: MyVoucherType {
        guard let voucherType = MyVoucherType(rawValue: IsSell ? 1 : 2) else {
            return .buy
        }
        return voucherType
    }

    required init?(map: Map) {

    }

    func mapping(map: Map) {
        VoucherId      <- map["VoucherId"]
        Voucher_Card_Id      <- map["Voucher_Card_Id"]
        PinCode              <- map["PinCode"]
        VoucherCode          <- map["VoucherCode"]
        Unit                 <- map["Unit"]
        VoucherName          <- map["VoucherName"]
        CardTypeName         <- map["CardTypeName"]
        ConditionOfUse       <- map["ConditionOfUse"]
        UnitPrice            <- map["UnitPrice"]
        StartDate            <- map["StartDate"]
        EndDate              <- map["EndDate"]
        DateOfStatus         <- map["DateOfStatus"]
        Status               <- map["Status"]
        StatusName           <- map["StatusName"]
        CustomerId           <- map["CustomerId"]
        CustomerCard         <- map["CustomerCard"]
        Description         <- map["Description"]
        ExpirationDate         <- map["ExpirationDate"]
        VoucherStatus         <- map["VoucherStatus"]
        StorylineId         <- map["Storyline.StorylineID"]
        IsSell         <- map["IsSell"]
        VoucherPackageName         <- map["VoucherPackageName"]
        IsAvaiableForGift         <- map["IsAvaiableForGift"]
        StorylineContent         <- map["StorylineContent"]
    }

    var expiredDateString: String {
        guard let date = ExpirationDate else {
            return "-"
        }
        return "hsd".localized + Date.dateFromServerSavis(date).toStringStandard()
    }
    
    func getDateStatus() -> Date{
        guard let dateString = DateOfStatus else {
            return Date()
        }
        
        return Date.dateFromServerSavis(dateString)
    }
}
