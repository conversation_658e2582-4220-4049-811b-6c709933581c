//
//  CityAPI.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 4/7/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation
import Moya


public enum City{
    case listCity
    case listDistrict(String)
}

let CityProvider = MoyaProvider<City>(plugins: [NetworkLoggerPlugin(verbose: true, responseDataFormatter: nil)])

extension City: TargetType {
    
    public var baseURL: URL { return URL(string: Config.BaseURL)! }
    
    
    public var path: String {
        switch self {
        case .listCity:
            return "api/v1/erp/cities"
        case .listDistrict(let cityId):
            return "api/v1/erp/cities/{\(cityId)}/districts"
        }
    }
    public var method: Moya.Method {
        return .get
    }
    public var parameters: [String: Any]? {
        return [:]
    }
    public var task: Task {
        return Task.requestParameters(parameters: self.parameters!, encoding: URLEncoding.default)
    }
    public var headers: [String : String]? {
        return Global.shared.headers()
    }
    
    public var sampleData: Data{
        return "".data(using: String.Encoding.utf8)!
    }
}
