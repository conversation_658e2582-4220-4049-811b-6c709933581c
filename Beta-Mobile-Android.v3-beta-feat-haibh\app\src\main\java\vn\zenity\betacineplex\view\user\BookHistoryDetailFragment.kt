package vn.zenity.betacineplex.view.user

import android.annotation.SuppressLint
import android.os.Bundle
import android.text.Spannable
import android.text.TextPaint
import android.text.style.ClickableSpan
import android.view.View
import android.view.View.GONE
import kotlinx.android.synthetic.main.fragment_bookhistorydetail.*
import kotlinx.android.synthetic.main.fragment_selectchair.textView11
import kotlinx.android.synthetic.main.item_book_history.view.tvDateExpiredPoint
import kotlinx.android.synthetic.main.item_book_history.view.tvDiemTichLuy
import showBarcode
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.base.BaseFragment
import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.global.Constant
import vn.zenity.betacineplex.helper.extension.dateConvertFormat
import vn.zenity.betacineplex.helper.extension.getColor
import vn.zenity.betacineplex.helper.extension.getString
import vn.zenity.betacineplex.helper.extension.toVNDCurrency
import vn.zenity.betacineplex.helper.extension.visible
import vn.zenity.betacineplex.model.PaymentHistory
import vn.zenity.betacineplex.model.PaymentHistoryDetailModel
import vn.zenity.betacineplex.model.PaymentType
import vn.zenity.betacineplex.view.HomeActivity


/**
 * Created by Zenity.
 */

class BookHistoryDetailFragment : BaseFragment(), BookHistoryDetailContractor.View {

    companion object {
        fun getInstance(paymentHistory: PaymentHistory): BookHistoryDetailFragment {
            val frag = BookHistoryDetailFragment()
            frag.paymentHistory = paymentHistory
            return frag
        }

        fun getInstance(transactionId: String?): BookHistoryDetailFragment {
            val frag = BookHistoryDetailFragment()
            frag.transactionId = transactionId
            frag.isPaymentSuccess = true
            return frag
        }
    }

    var isPaymentSuccess = false
    private val presenter = BookHistoryDetailPresenter()
    private var paymentHistory: PaymentHistory? = null
    private var transactionId: String? = null

    override fun getPresenter(): IBasePresenter<IBaseView>? {
        return presenter as? IBasePresenter<IBaseView>
    }

    override fun isShowToolbar(): Boolean {
        return false
    }

    override fun getLayoutRes(): Int {
        return R.layout.fragment_bookhistorydetail
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        presenter.paymentHistory?.Invoice_Id ?: (transactionId ?: return))
        setupNotice()
    }

    override fun onGetDetailSuccess(model: PaymentHistoryDetailModel) {
        activity?.runOnUiThread {getDetails(
            fillData(model)
        }
    }

    override fun back() {
        if (isPaymentSuccess) {
            (activity as? HomeActivity)?.backToHome()
        } else super.back()
    }

    @SuppressLint("SetTextI18n")
    private fun fillData(model: PaymentHistoryDetailModel) {
        tvFilmName.text = model.FilmModel?.Name
        tvTypeFile.text = "${
            model.FilmModel?.RestrictAge ?: ""
        } | ${
            (model.FilmModel?.FilmFormatName ?: "")
        } - ${(model.FilmModel?.DubbingCode ?: "")} | ${
            model.FilmModel?.Duration ?: 0
        } ${R.string.minute.getString()}"
        tvRapChieu.text = model.CinemaName
        tvNgayChieu.text = model.showTime?.first
        tvGioChieu.text = model.showTime?.second
        tvPhongChieu.text = model.ScreenName

        if (model.paymentValue(PaymentType.CASH) > 0) {
            tvPrice.text = model.paymentValue(PaymentType.CASH).toVNDCurrency()
        } else {
            llCash.visibility = GONE
        }

        if (model.paymentValue(PaymentType.VOUCHER) > 0) {
            tvVoucher.text = model.paymentValue(PaymentType.VOUCHER).toVNDCurrency()
        } else {
            llVoucher.visibility = GONE
        }

        if (model.paymentValue(PaymentType.BETAID) > 0) {
            tvPoint.text = "${model.SpendingPoint} - ${model.paymentValue(PaymentType.BETAID).toVNDCurrency()}"
        } else {
            llPoint.visibility = GONE
        }

        if (model.paymentValue(PaymentType.CARD) > 0) {
            tvCard.text = model.paymentValue(PaymentType.CARD).toVNDCurrency()
        } else {
            llCard.visibility = GONE
        }

        if (model.paymentValue(PaymentType.SHOPEEPAY_ONLINE) > 0) {
            tvShopeePay.text = model.paymentValue(PaymentType.SHOPEEPAY_ONLINE).toVNDCurrency()
        } else {
            llShopeePay.visibility = GONE
        }

        if (model.paymentValue(PaymentType.MOMO) > 0) {
            tvMomo.text = model.paymentValue(PaymentType.MOMO).toVNDCurrency()
        } else {
            llMomo.visibility = GONE
        }

        if (model.paymentValue(PaymentType.ZALOPAY) > 0) {
            tvZaloPay.text = model.paymentValue(PaymentType.ZALOPAY).toVNDCurrency()
        } else {
            llZaloPay.visibility = GONE
        }

        tvSeatTitle.text = "${tvSeatTitle.text} " + "(${model.seatName?.first})"
        tvSeats.text = model.seatName?.second

        tvComboTitle.text = "${tvComboTitle.text} (${model.combos?.first})"
        tvCombo.text = model.combos?.second

        tvTotal.text = model.TotalPayment.toVNDCurrency()
        tvDiemTichLuy.text = "${model.QuantityPoint}"
        tvDateExpiredPoint.text = model.DateExpiredPoint?.dateConvertFormat(Constant.DateFormat.defaultFull, Constant.DateFormat.dateSavis)

        model.No?.let {
            ivCode.showBarcode(it)
            tvCode.text = it
        }
    }

    private fun setupNotice() {
        val note = R.string.Note.getString()
        val termandcondition = R.string.TransactionDetail_NoticeText.getString()
        val textSpan = Spannable.Factory.getInstance().newSpannable(termandcondition)
        val startT = termandcondition.indexOf(note)
        textSpan.setSpan(object : ClickableSpan() {
            override fun onClick(ab: View) {
            }

            override fun updateDrawState(ds: TextPaint) {
                super.updateDrawState(ds)
                ds.color = R.color.textRed.getColor()
                ds.isUnderlineText = false
            }

        }, startT, startT + note.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)

        tvNote?.text = textSpan
    }
}
