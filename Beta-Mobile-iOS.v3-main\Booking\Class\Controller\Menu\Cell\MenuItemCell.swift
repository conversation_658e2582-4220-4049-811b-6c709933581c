//
//  MenuItemCell.swift
//  Booking-dev
//
//  Created by <PERSON><PERSON> on 4/6/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit

class MenuItemCell: UITableViewCell {
    @IBOutlet weak var ivIcon: UIImageView!
    @IBOutlet weak var lbTitle: UILabel!
    @IBOutlet weak var vUnderline: UIView!

    var item: TableItem?

    override func awakeFromNib() {
        super.awakeFromNib()
    }

    override func setSelected(_ selected: <PERSON><PERSON>, animated: Bool) {
        vUnderline.isHidden = !selected
        lbTitle.textColor = selected ? UITableViewCell.selectedColor : UITableViewCell.unselectedColor

        if let selectedIcon = item?.selectedIcon, let icon = item?.icon {
            ivIcon.image = selected ? UIImage(named: selectedIcon)?.withRenderingMode(.alwaysTemplate) : UIImage(named: icon)
        }
        ivIcon.tintColor = selected ? UITableViewCell.selectedColor : UIT<PERSON><PERSON>iewCell.unselectedColor
    }

//    override func setHighlighted(_ highlighted: <PERSON><PERSON>, animated: <PERSON><PERSON>) {
//    }

    override func updateViewWithItem(_ item: TableItem, indexPath: IndexPath) {
        self.item = item
        if let icon = item.icon, let image = UIImage(named: icon) {
            ivIcon.image = icon == "notificationUnselectRed" ? image.withRenderingMode(.alwaysOriginal) : image.withRenderingMode(.alwaysTemplate)
        }
        lbTitle.text = item.title
    }
}
