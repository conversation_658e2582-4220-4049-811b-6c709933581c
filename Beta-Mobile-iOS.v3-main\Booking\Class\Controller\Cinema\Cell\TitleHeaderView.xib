<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="19529" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="19519"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="Oswald-Regular.ttf">
            <string>Oswald-Regular</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="iN0-l3-epB" customClass="TitleHeaderView" customModule="Beta_Cinemas" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="375" height="112"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="jd2-Qt-5Rm">
                    <rect key="frame" x="0.0" y="0.0" width="375" height="112"/>
                    <subviews>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Chọn rạp theo khu vực" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Yvo-3w-vbD">
                            <rect key="frame" x="8" y="12" width="359" height="92"/>
                            <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="20"/>
                            <color key="textColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            <nil key="highlightedColor"/>
                        </label>
                    </subviews>
                    <color key="backgroundColor" red="0.95294117649999999" green="0.95294117649999999" blue="0.95294117649999999" alpha="1" colorSpace="calibratedRGB"/>
                    <constraints>
                        <constraint firstAttribute="bottom" secondItem="Yvo-3w-vbD" secondAttribute="bottom" priority="750" constant="8" id="Lb1-Zc-doe"/>
                        <constraint firstAttribute="trailing" secondItem="Yvo-3w-vbD" secondAttribute="trailing" priority="750" constant="8" id="jTs-OH-gZ5"/>
                        <constraint firstItem="Yvo-3w-vbD" firstAttribute="top" secondItem="jd2-Qt-5Rm" secondAttribute="top" priority="750" constant="12" id="nZ9-PZ-CmN"/>
                        <constraint firstItem="Yvo-3w-vbD" firstAttribute="leading" secondItem="jd2-Qt-5Rm" secondAttribute="leading" priority="750" constant="8" id="q5d-XD-WQ2"/>
                    </constraints>
                </view>
            </subviews>
            <constraints>
                <constraint firstItem="jd2-Qt-5Rm" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" id="Nrp-ig-nK9"/>
                <constraint firstAttribute="trailing" secondItem="jd2-Qt-5Rm" secondAttribute="trailing" id="ngj-eY-SFD"/>
                <constraint firstAttribute="bottom" secondItem="jd2-Qt-5Rm" secondAttribute="bottom" id="wts-Xt-dgo"/>
                <constraint firstItem="jd2-Qt-5Rm" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" id="zaX-rY-Ld3"/>
            </constraints>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <connections>
                <outlet property="lbTitle" destination="Yvo-3w-vbD" id="cnc-Ln-Goh"/>
            </connections>
            <point key="canvasLocation" x="24.5" y="-133"/>
        </view>
    </objects>
</document>
