//
//  Utils.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 3/10/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation
import CoreLocation

extension UIStoryboard {
    static let home: UIStoryboard = UIStoryboard(name: "Home", bundle: nil)
    static let authen: UIStoryboard = UIStoryboard(name: "Authen", bundle: nil)
    static let cinema = UIStoryboard(name: "Cinema", bundle: nil)
    static let payment = UIStoryboard(name: "Payment", bundle: nil)
    static let film = UIStoryboard(name: "Film", bundle: nil)
    static let setting = UIStoryboard(name: "Setting", bundle: nil)
    static let member = UIStoryboard(name: "Member", bundle: nil)

    enum Id: String {
        // authen
        case login = "LoginViewController"
        case register = "RegisterViewController"
        case forgotPassword = "ForgotPassViewController"
        case registerResult = "RegisterResultViewController"
        case updatePass = "UpdatePasswordViewController"

        // home
        case home = "HomeViewController"
        case homeNav = "HomeNavigationController"
        case slideMenu = "SlideMenuViewController"
        case notification = "NotificationViewController"
        case top = "TopViewController"
        case recruitment = "RecruitmentViewController"
        case youtube = "YoutubeViewController"
        case notificationDetail = "NotificationDetailViewController"

        // film
        case filmDetail = "FilmDetailViewController"
        case listFilm = "ListFilmViewController"
        case ticketBooking = "TicketBookingViewController"
        case filmBooking = "FilmBookingViewController"
        case filmChooseTime = "FilmChooseTimeViewController"

        // member
        case member = "MemberViewController"
        case changePass = "ChangePassViewController"
        case memberCard = "MemberCardViewController"
        case rewardPoints = "RewardPointsViewController"
        case accountInfo = "AccountInfoViewController"
        case confirmPass = "ConfirmPassViewController"
        case transactionHistory = "TranferHistoryViewController"
        case transactionDetail = "TransactionDetailViewController"
        case voucherCoupon = "VourcherCouponViewController"
        case voucher = "VoucherViewController"
        case coupon = "CouponViewController"

        // setting
        case setting = "SettingViewController"
        case profile = "ProfileViewController"
        case versionInfo = "VersionInfoViewController"
        case faq = "FAQViewController"
        case faqDetails = "FAQDetailViewController"
        case other = "OtherViewController"

        // payment
        case payment = "PaymentViewController"

        // news
        case newsDetail = "NewsDetailViewController"
        case newsAndDeals = "NewsAndDealsViewController"
        case listNews = "ListNewsViewController"

        // cenima
        case listAllCinema = "ListAllCinemasViewController"
        case cinemaDetail = "CinemaDetailViewController"
        case chooseCinema = "ChooseCinemasViewController"
        case price = "CinemaPriceViewController"
        case chooseSeat = "ChooseSeatViewController"
        case selectRegion = "SelectRegionViewController"
        case confirmBookAge = "ConfirmBookAgeViewController"
    }
}

class Utils{
    static let shared = Utils()
    
    func isEng() -> Bool{
        return LanguageManager.shared.getLanguage() == .English
    }
    
    func distanceFrom(latitude: String?, longitude: String?) -> Double{
        guard let latString = latitude,
            let lngString = longitude,
            let lat = Double(latString),
            let lng = Double(lngString) else { return 0 }
        let currentLat = UserDefaults.standard.double(forKey: Constant.Lat)
        let currentLng = UserDefaults.standard.double(forKey: Constant.Lng)
        
        let currentLocation: CLLocationCoordinate2D = CLLocationCoordinate2D(latitude: currentLat, longitude: currentLng)
        let goalLocation: CLLocationCoordinate2D = CLLocationCoordinate2D(latitude: lat, longitude: lng)
        return currentLocation.distanceTo(coordinate: goalLocation)/1000 //km
    }
    
    func timeFormatted(_ totalSeconds: Int) -> String {
        let seconds: Int = totalSeconds % 60
        let minutes: Int = (totalSeconds / 60) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }
    
}
