{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98388c402243acf11480a43689e549a4a0", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98519c0f1aabf34eacbf6f755b813f1496", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98047d3176a44ab96fc7c2448c294a1bf4", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ec62137b0ee28ca4265944856877be27", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98047d3176a44ab96fc7c2448c294a1bf4", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9832ebf16d88dad2729e444c32094aa46a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983155b84376e9fe6c82e73a59550b8360", "guid": "bfdfe7dc352907fc980b868725387e982ea0ca19c0771fd6d266f2b66446b37d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986678f39fe87f915a25a3668adfea4d26", "guid": "bfdfe7dc352907fc980b868725387e98f1887403300e04b839877af836998497", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e60f8fd4224fa426c8e435821a3c736d", "guid": "bfdfe7dc352907fc980b868725387e9899c4b45e473fef94c750c44c19300e3f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987088182dacb094e68f3179d0e29403fb", "guid": "bfdfe7dc352907fc980b868725387e9801e957e9f2cdc171382139f7af1a5681", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986092e90be77e647ce10f5a49f6985215", "guid": "bfdfe7dc352907fc980b868725387e98f9dcd7bfc2b70fc0334d5a2b029d2be4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac05925348cb8ff3e1c37317c3a568ef", "guid": "bfdfe7dc352907fc980b868725387e983c0142618f0c80e86891f682ede039d2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad24a290a26fcc6bc1c7f7c3d17af32d", "guid": "bfdfe7dc352907fc980b868725387e98a01fc3746b75fa29083dde20e4e057e1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c518292c8e1cb013f0841b76b7968077", "guid": "bfdfe7dc352907fc980b868725387e98cd73b22058278a9b4f651422cde8e6cb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98244b94d16226b680afa12171b9cf7a9f", "guid": "bfdfe7dc352907fc980b868725387e9858da164c6f05b97dc4f70eeaf0ab7f88", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e975d265cb3b1bff7518b625b4170ff", "guid": "bfdfe7dc352907fc980b868725387e9810ee0ac07ad1614d2a0f4f4692012057", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b65a8b3e8cb4fb15da2467597dd35a0", "guid": "bfdfe7dc352907fc980b868725387e9898f6f1d0225d5da11e073619b459ecbb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a134188a049870d9090716f93c8207ff", "guid": "bfdfe7dc352907fc980b868725387e982567c545f94782ef792faee70a9f28e6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98552b37e26ce79be816a6374e6e7a528e", "guid": "bfdfe7dc352907fc980b868725387e98dc02dbc796bf1a1c79ffc8c7bf3cea72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6e6b81e95dc7cd5d15508f96443eeaa", "guid": "bfdfe7dc352907fc980b868725387e98616c37b3aed90db7f9fdd15a498d9b27", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891adfdcc9b701e0c28d7e9fc0b62d80d", "guid": "bfdfe7dc352907fc980b868725387e989ec464f79f509c315509332bb727e800", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879f504e7cc005c23e81ed7db009d6516", "guid": "bfdfe7dc352907fc980b868725387e983249d575db8b5cbd53c0564d7472fd4b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c66cbac9d81a47beb48e0bdf5aca232c", "guid": "bfdfe7dc352907fc980b868725387e980d76b669df9576a70807ec449fb952b6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e890e67431d37de2606a1f64d7f5ca68", "guid": "bfdfe7dc352907fc980b868725387e98bc7c56555dd219b78ffdd88c372b232b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6a2f638b9f3d8d3161a84cd86b7ab77", "guid": "bfdfe7dc352907fc980b868725387e988e7018dc0c9e1f0c9f93b5994bcbc130", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8f57d60ee389ee94a91b9d6fe5c1e35", "guid": "bfdfe7dc352907fc980b868725387e98dcef7cb18e4d73ea8eef002919db5632", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98492990a47f2f87861a7c98d2d03ee975", "guid": "bfdfe7dc352907fc980b868725387e98955da4966904666f6adbe480ce93a36f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a911ced4535f216b3e27a39a327ea58d", "guid": "bfdfe7dc352907fc980b868725387e98232d0a9b2be5b70279bedd97f191826e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98928264cd3f2c0798b4f1277d215b1fe1", "guid": "bfdfe7dc352907fc980b868725387e987211db59fabb7296cf1568fb71a68255", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986a03fa67e33d48dce94be2b8eb2259ec", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984d3f87592a2705d5d8ddcbb38e20c8af", "guid": "bfdfe7dc352907fc980b868725387e98c8af4857ac32d849c3b447bcff03aa13"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847bccac320fe6f35739bfebeeaf13894", "guid": "bfdfe7dc352907fc980b868725387e98dc950c62ee46ede361cd98ae90dd27a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a03663eb90db1efee7000eaa1e65183b", "guid": "bfdfe7dc352907fc980b868725387e983f0af89b0b8bda8241febbe4723f8c88"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98569cbfe18611f3eff2e509b43e818d9f", "guid": "bfdfe7dc352907fc980b868725387e981a333127dad81374abf6fe8c83600eee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3d985101be5331feed63ec365eeae1a", "guid": "bfdfe7dc352907fc980b868725387e988f8e2ac76c48795c8610f17a754a59c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1c61e7b6649b74c090ec455edd33ef1", "guid": "bfdfe7dc352907fc980b868725387e984b1e4946f1823465931d4e8492eb19a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98137c70fbf294fd2973121b60797b487d", "guid": "bfdfe7dc352907fc980b868725387e98486c17b42526ba81b0e47a838401edd9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834c045e7ad144816460e57fd92ecd329", "guid": "bfdfe7dc352907fc980b868725387e98e24ce56a008cd8fb5a8cdae1e9811e54"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5d68197f2934d72d3feb403d8ff05b0", "guid": "bfdfe7dc352907fc980b868725387e98243ca78b2974c64cd01083596d8fe9a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6adca6eccd93878d4a2bffad550e1c6", "guid": "bfdfe7dc352907fc980b868725387e98c41a340685df4d1ce3783021dcf54fc1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98979ccebcd469efe932e1f89a6d292082", "guid": "bfdfe7dc352907fc980b868725387e98506a4bb5c9b087dedda179e379e974b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a439630ea375ccd1fcbdd012b4b7e94b", "guid": "bfdfe7dc352907fc980b868725387e984463890dd80178e8bcdc0b520010feee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e6038ee5f8e010bc8337f5a4437392e", "guid": "bfdfe7dc352907fc980b868725387e981850b344567e5543096f0a83d9eac0b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983201921198a1792889cc2042cb2b53d0", "guid": "bfdfe7dc352907fc980b868725387e9859e3b579695a572ae5f0cad67ec2876d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837db53afa870ed392402723f0e54b677", "guid": "bfdfe7dc352907fc980b868725387e9885aba67d2d135f9806c9a05487a1a98c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd051555a579817d0ca824c9c334b4d2", "guid": "bfdfe7dc352907fc980b868725387e985805bc10d6057738af5938ead2b69fee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5ee4433b5b25e28ad57f3f57dde4513", "guid": "bfdfe7dc352907fc980b868725387e985658d819a1ceda9c7580d1657159b11a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856899915e6c3d53d5462a9dd225ab752", "guid": "bfdfe7dc352907fc980b868725387e98c8c90bc417199af9668b9601526e858b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983157bcb170a635e6548dfa85ba5179f3", "guid": "bfdfe7dc352907fc980b868725387e98a79aa5b656ffdfb7930448cc9a744fc6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac854392d3fe2b4a49164f3e17136ce1", "guid": "bfdfe7dc352907fc980b868725387e9822c0d109ebdbc53ca874ef63b18251dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f625ccf59ff7b9eb5cd15e9ac964005", "guid": "bfdfe7dc352907fc980b868725387e983c3ab34f30d3bcea13309294f772a70c"}], "guid": "bfdfe7dc352907fc980b868725387e98be6229230a4715433df2f8e74fbafc5b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e980797c2152e50219ee4196549bb34f857"}], "guid": "bfdfe7dc352907fc980b868725387e984d290968aff9eafa4ed5b85c80a8c610", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98fa0d11ed0b4e1a85c13d68e37d1547e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802f35ab680609a626ebd2ddd692a3822", "name": "permission_handler_apple-permission_handler_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f8f53f8ba4165e76c7481b24262177ed", "name": "permission_handler_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}