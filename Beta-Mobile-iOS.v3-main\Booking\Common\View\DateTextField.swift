//
//  DateTextField.swift
//  Booking-dev
//
//  Created by <PERSON><PERSON> on 4/7/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit
import SwiftDate

@IBDesignable
class DateTextField: PickerTextField {
    fileprivate var datePickerView: UIDatePicker?

    @IBInspectable var dateFormatText: String = "dd/MM/yyyy"

    let serverFormatDate = "yyyy-MM-dd'T'HH:mm:ss"
    
    public var maxDate: Date? {
        didSet {
            self.datePickerView?.maximumDate = maxDate
        }
    }

    public var minDate: Date? {
        didSet {
            self.datePickerView?.minimumDate = minDate
        }
    }

    override var text: String? {
        didSet {
            self.datePickerView?.date = text?.toDate(dateFormatText) ?? Date()
        }
    }

    override func setup() {
        super.setup()
        self.contentVerticalAlignment = .center
        self.contentHorizontalAlignment = .center
        datePickerView = UIDatePicker()
        datePickerView?.datePickerMode = .date
        datePickerView?.maximumDate = Date()
        if #available(iOS 13.4, *) {
            datePickerView?.preferredDatePickerStyle = .wheels
        } else {
            // Fallback on earlier versions
        }
        datePickerView?.addTarget(self, action: #selector(dateDidChange(_:)), for: .valueChanged)
        self.inputView?.bounds = CGRect(x: 0, y: 0, width: UIScreen.main.bounds.width, height: 200)
        datePickerView?.center = inputView?.center ?? CGPoint.zero
        datePickerView?.frame = inputView?.frame ?? CGRectZero
        self.inputView = datePickerView
    }

    override func doneButtonPressed(_ sender: Any) {
        self.text = datePickerView?.date.toString(dateFormat: dateFormatText)

        resignFirstResponder()
    }
    
    func getDate() -> String?{
        return datePickerView?.date.toString(dateFormat: serverFormatDate)
    }
    
    func setText(text: String){
        let df = DateFormatter()
        df.dateFormat = serverFormatDate
        if let date = df.date(from: text){
            self.text = date.toString(dateFormat: dateFormatText)
        }
    }

    @objc func dateDidChange(_ picker: UIDatePicker) {
        self.text = picker.date.toString(dateFormat: dateFormatText)
    }
}
