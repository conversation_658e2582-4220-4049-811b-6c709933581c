//
//  AssetLibrary.swift
//  Booking-dev
//
//  Created by <PERSON><PERSON> on 4/19/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit
import Photos

class AssetLibrary: NSObject {
    static func loadImage(url: URL, targetSize: CGSize = CGSize(width: 1024, height: 1024), contentMode: PHImageContentMode = .aspectFit, completion: @escaping(_ image: UIImage?) -> Void) {
        // Fetch Image
        DispatchQueue.global().async {
            let option = PHFetchOptions()
            let result = PHAsset.fetchAssets(withALAssetURLs: [url], options: option)

            guard let asset = result.firstObject else {
                completion(nil)
                return
            }

            let requestOption = PHImageRequestOptions()
            requestOption.deliveryMode = .fastFormat
            requestOption.isSynchronous = true
            requestOption.isNetworkAccessAllowed = true
            PHImageManager.default().requestImage(for: asset, targetSize: targetSize, contentMode: contentMode, options: requestOption, resultHandler: { (image, info) in
                DispatchQueue.main.async {
                    completion(image)
                }
            })
        }
    }

    static func loadImageData(url: URL, targetSize: CGSize = CGSize(width: 1024, height: 1024), contentMode: PHImageContentMode = .aspectFit, completion: @escaping(_ data: Data?) -> Void) {
        // Fetch Image
        DispatchQueue.global().async {
            let option = PHFetchOptions()
            let result = PHAsset.fetchAssets(withALAssetURLs: [url], options: option)

            guard let asset = result.firstObject else {
                completion(nil)
                return
            }

            let requestOption = PHImageRequestOptions()
            requestOption.deliveryMode = .fastFormat
            requestOption.isSynchronous = true
            requestOption.isNetworkAccessAllowed = true
            PHImageManager.default().requestImageData(for: asset, options: requestOption, resultHandler: { (data, dataUTI, orientation, info) in
                DispatchQueue.main.async {
                    completion(data)
                }
            })
        }
    }
}
