//
//  Global.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 4/8/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation

func print(_ items: Any...) {
    #if DEBUG
    Swift.print(items[0])
    #endif
}

class Global {
    static let shared = Global()
    
    var user: UserModel?
    var selectedFilm: FilmModel?
    var appParams: [AppParams]?
    var appVersion: AppParams? {
        return appParams?.filter{ $0.paramsCode == "app-version" }.first
    }
    var appIntro: AppParams? {
        return appParams?.filter{ $0.paramsCode == "app-introduce" }.first
    }
    var isLogined: Bool {
        return user != nil
    }

    init() {
        loadData()
    }

    func saveUser(_ user: UserModel?) {
        self.user = user
        let json = user?.toJSON()

        UserDefaults.standard.set(json, forKey: "User")
    }

    func loadData() {
        if let json = UserDefaults.standard.value(forKey: "User") as? [String: Any] {
            self.user = UserModel(JSON: json)
        }
    }

    func logout() {
        self.saveUser(nil)
    }
    
    func headers() -> [String: String]{
        var sandbox = "0"
        #if DEBUG
        sandbox = "1"
        #endif
        let language = Utils.shared.isEng() ? "en" : "vi"
        var headers = ["channel": "mobile",
                       "Content-type": "application/json",
                       "device-type": "ios",
                       "language": language,
                       "sandbox_mode": sandbox]
        if let user = self.user{
            headers["Authorization"] = "Bearer \(user.Token ?? "")"
            headers["X-User"] = user.UserId
        }
        return headers
    }
}
