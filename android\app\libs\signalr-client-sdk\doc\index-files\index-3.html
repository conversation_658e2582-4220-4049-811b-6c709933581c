<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<title>C-Index</title>
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="C-Index";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../microsoft/aspnet/signalr/client/package-summary.html">Package</a></li>
<li>Class</li>
<li>Use</li>
<li><a href="../microsoft/aspnet/signalr/client/package-tree.html">Tree</a></li>
<li class="navBarCell1Rev">Index</li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-2.html">Prev Letter</a></li>
<li><a href="index-4.html">Next Letter</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-filesindex-3.html" target="_top">Frames</a></li>
<li><a href="index-3.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">L</a>&nbsp;<a href="index-11.html">M</a>&nbsp;<a href="index-12.html">N</a>&nbsp;<a href="index-13.html">O</a>&nbsp;<a href="index-14.html">P</a>&nbsp;<a href="index-15.html">R</a>&nbsp;<a href="index-16.html">S</a>&nbsp;<a href="index-17.html">T</a>&nbsp;<a href="index-18.html">U</a>&nbsp;<a href="index-19.html">V</a>&nbsp;<a name="_C_">
<!--   -->
</a>
<h2 class="title">C</h2>
<dl>
<dt><a href="../microsoft/aspnet/signalr/client/CalendarSerializer.html" title="class in microsoft.aspnet.signalr.client"><span class="strong">CalendarSerializer</span></a> - Class in <a href="../microsoft/aspnet/signalr/client/package-summary.html">microsoft.aspnet.signalr.client</a></dt>
<dd>&nbsp;</dd>
<dt><span class="strong"><a href="../microsoft/aspnet/signalr/client/CalendarSerializer.html#CalendarSerializer()">CalendarSerializer()</a></span> - Constructor for class microsoft.aspnet.signalr.client.<a href="../microsoft/aspnet/signalr/client/CalendarSerializer.html" title="class in microsoft.aspnet.signalr.client">CalendarSerializer</a></dt>
<dd>&nbsp;</dd>
<dt><span class="strong"><a href="../microsoft/aspnet/signalr/client/SignalRFuture.html#cancel()">cancel()</a></span> - Method in class microsoft.aspnet.signalr.client.<a href="../microsoft/aspnet/signalr/client/SignalRFuture.html" title="class in microsoft.aspnet.signalr.client">SignalRFuture</a></dt>
<dd>
<div class="block">Cancels the operation</div>
</dd>
<dt><span class="strong"><a href="../microsoft/aspnet/signalr/client/SignalRFuture.html#cancel(boolean)">cancel(boolean)</a></span> - Method in class microsoft.aspnet.signalr.client.<a href="../microsoft/aspnet/signalr/client/SignalRFuture.html" title="class in microsoft.aspnet.signalr.client">SignalRFuture</a></dt>
<dd>&nbsp;</dd>
<dt><span class="strong"><a href="../microsoft/aspnet/signalr/client/UpdateableCancellableFuture.html#cancel()">cancel()</a></span> - Method in class microsoft.aspnet.signalr.client.<a href="../microsoft/aspnet/signalr/client/UpdateableCancellableFuture.html" title="class in microsoft.aspnet.signalr.client">UpdateableCancellableFuture</a></dt>
<dd>&nbsp;</dd>
<dt><span class="strong"><a href="../microsoft/aspnet/signalr/client/Connection.html#closed(java.lang.Runnable)">closed(Runnable)</a></span> - Method in class microsoft.aspnet.signalr.client.<a href="../microsoft/aspnet/signalr/client/Connection.html" title="class in microsoft.aspnet.signalr.client">Connection</a></dt>
<dd>&nbsp;</dd>
<dt><span class="strong"><a href="../microsoft/aspnet/signalr/client/ConnectionBase.html#closed(java.lang.Runnable)">closed(Runnable)</a></span> - Method in interface microsoft.aspnet.signalr.client.<a href="../microsoft/aspnet/signalr/client/ConnectionBase.html" title="interface in microsoft.aspnet.signalr.client">ConnectionBase</a></dt>
<dd>
<div class="block">Sets the handler for the "Closed" event</div>
</dd>
<dt><span class="strong"><a href="../microsoft/aspnet/signalr/client/Connection.html#connected(java.lang.Runnable)">connected(Runnable)</a></span> - Method in class microsoft.aspnet.signalr.client.<a href="../microsoft/aspnet/signalr/client/Connection.html" title="class in microsoft.aspnet.signalr.client">Connection</a></dt>
<dd>&nbsp;</dd>
<dt><span class="strong"><a href="../microsoft/aspnet/signalr/client/ConnectionBase.html#connected(java.lang.Runnable)">connected(Runnable)</a></span> - Method in interface microsoft.aspnet.signalr.client.<a href="../microsoft/aspnet/signalr/client/ConnectionBase.html" title="interface in microsoft.aspnet.signalr.client">ConnectionBase</a></dt>
<dd>
<div class="block">Sets the handler for the "Connected" event</div>
</dd>
<dt><a href="../microsoft/aspnet/signalr/client/Connection.html" title="class in microsoft.aspnet.signalr.client"><span class="strong">Connection</span></a> - Class in <a href="../microsoft/aspnet/signalr/client/package-summary.html">microsoft.aspnet.signalr.client</a></dt>
<dd>
<div class="block">Represents a basic SingalR connection</div>
</dd>
<dt><span class="strong"><a href="../microsoft/aspnet/signalr/client/Connection.html#Connection(java.lang.String)">Connection(String)</a></span> - Constructor for class microsoft.aspnet.signalr.client.<a href="../microsoft/aspnet/signalr/client/Connection.html" title="class in microsoft.aspnet.signalr.client">Connection</a></dt>
<dd>
<div class="block">Initializes the connection with an URL</div>
</dd>
<dt><span class="strong"><a href="../microsoft/aspnet/signalr/client/Connection.html#Connection(java.lang.String, java.lang.String)">Connection(String, String)</a></span> - Constructor for class microsoft.aspnet.signalr.client.<a href="../microsoft/aspnet/signalr/client/Connection.html" title="class in microsoft.aspnet.signalr.client">Connection</a></dt>
<dd>
<div class="block">Initializes the connection with an URL and a query string</div>
</dd>
<dt><span class="strong"><a href="../microsoft/aspnet/signalr/client/Connection.html#Connection(java.lang.String, microsoft.aspnet.signalr.client.Logger)">Connection(String, Logger)</a></span> - Constructor for class microsoft.aspnet.signalr.client.<a href="../microsoft/aspnet/signalr/client/Connection.html" title="class in microsoft.aspnet.signalr.client">Connection</a></dt>
<dd>
<div class="block">Initializes the connection with an URL and a logger</div>
</dd>
<dt><span class="strong"><a href="../microsoft/aspnet/signalr/client/Connection.html#Connection(java.lang.String, java.lang.String, microsoft.aspnet.signalr.client.Logger)">Connection(String, String, Logger)</a></span> - Constructor for class microsoft.aspnet.signalr.client.<a href="../microsoft/aspnet/signalr/client/Connection.html" title="class in microsoft.aspnet.signalr.client">Connection</a></dt>
<dd>
<div class="block">Initializes the connection with an URL, a query string and a Logger</div>
</dd>
<dt><a href="../microsoft/aspnet/signalr/client/ConnectionBase.html" title="interface in microsoft.aspnet.signalr.client"><span class="strong">ConnectionBase</span></a> - Interface in <a href="../microsoft/aspnet/signalr/client/package-summary.html">microsoft.aspnet.signalr.client</a></dt>
<dd>&nbsp;</dd>
<dt><span class="strong"><a href="../microsoft/aspnet/signalr/client/Connection.html#connectionSlow(java.lang.Runnable)">connectionSlow(Runnable)</a></span> - Method in class microsoft.aspnet.signalr.client.<a href="../microsoft/aspnet/signalr/client/Connection.html" title="class in microsoft.aspnet.signalr.client">Connection</a></dt>
<dd>&nbsp;</dd>
<dt><span class="strong"><a href="../microsoft/aspnet/signalr/client/ConnectionBase.html#connectionSlow(java.lang.Runnable)">connectionSlow(Runnable)</a></span> - Method in interface microsoft.aspnet.signalr.client.<a href="../microsoft/aspnet/signalr/client/ConnectionBase.html" title="interface in microsoft.aspnet.signalr.client">ConnectionBase</a></dt>
<dd>
<div class="block">Sets the handler for the "ConnectionSlow" event</div>
</dd>
<dt><a href="../microsoft/aspnet/signalr/client/ConnectionState.html" title="enum in microsoft.aspnet.signalr.client"><span class="strong">ConnectionState</span></a> - Enum in <a href="../microsoft/aspnet/signalr/client/package-summary.html">microsoft.aspnet.signalr.client</a></dt>
<dd>
<div class="block">Represents the state of a connection</div>
</dd>
<dt><a href="../microsoft/aspnet/signalr/client/Constants.html" title="class in microsoft.aspnet.signalr.client"><span class="strong">Constants</span></a> - Class in <a href="../microsoft/aspnet/signalr/client/package-summary.html">microsoft.aspnet.signalr.client</a></dt>
<dd>
<div class="block">Constants used through the framework</div>
</dd>
<dt><span class="strong"><a href="../microsoft/aspnet/signalr/client/Constants.html#Constants()">Constants()</a></span> - Constructor for class microsoft.aspnet.signalr.client.<a href="../microsoft/aspnet/signalr/client/Constants.html" title="class in microsoft.aspnet.signalr.client">Constants</a></dt>
<dd>&nbsp;</dd>
<dt><span class="strong"><a href="../microsoft/aspnet/signalr/client/FutureHelper.html#copyHandlers(microsoft.aspnet.signalr.client.SignalRFuture, microsoft.aspnet.signalr.client.SignalRFuture)">copyHandlers(SignalRFuture&lt;?&gt;, SignalRFuture&lt;?&gt;)</a></span> - Static method in class microsoft.aspnet.signalr.client.<a href="../microsoft/aspnet/signalr/client/FutureHelper.html" title="class in microsoft.aspnet.signalr.client">FutureHelper</a></dt>
<dd>
<div class="block">Copy the Cancellation and Error handlers between two SignalRFuture
 instances</div>
</dd>
<dt><span class="strong"><a href="../microsoft/aspnet/signalr/client/FutureHelper.html#copyHandlers(microsoft.aspnet.signalr.client.http.HttpConnectionFuture, microsoft.aspnet.signalr.client.SignalRFuture)">copyHandlers(HttpConnectionFuture, SignalRFuture&lt;?&gt;)</a></span> - Static method in class microsoft.aspnet.signalr.client.<a href="../microsoft/aspnet/signalr/client/FutureHelper.html" title="class in microsoft.aspnet.signalr.client">FutureHelper</a></dt>
<dd>
<div class="block">Copy the Cancellation and Error handlers between two SignalRFuture
 instances, where the source is an HttpConnectionFuture</div>
</dd>
<dt><span class="strong"><a href="../microsoft/aspnet/signalr/client/Platform.html#createDefaultHttpConnection(microsoft.aspnet.signalr.client.Logger)">createDefaultHttpConnection(Logger)</a></span> - Static method in class microsoft.aspnet.signalr.client.<a href="../microsoft/aspnet/signalr/client/Platform.html" title="class in microsoft.aspnet.signalr.client">Platform</a></dt>
<dd>&nbsp;</dd>
<dt><span class="strong"><a href="../microsoft/aspnet/signalr/client/Platform.html#createHttpConnection(microsoft.aspnet.signalr.client.Logger)">createHttpConnection(Logger)</a></span> - Static method in class microsoft.aspnet.signalr.client.<a href="../microsoft/aspnet/signalr/client/Platform.html" title="class in microsoft.aspnet.signalr.client">Platform</a></dt>
<dd>
<div class="block">Creates an adequate HttpConnection for the current platform</div>
</dd>
<dt><span class="strong"><a href="../microsoft/aspnet/signalr/client/PlatformComponent.html#createHttpConnection(microsoft.aspnet.signalr.client.Logger)">createHttpConnection(Logger)</a></span> - Method in interface microsoft.aspnet.signalr.client.<a href="../microsoft/aspnet/signalr/client/PlatformComponent.html" title="interface in microsoft.aspnet.signalr.client">PlatformComponent</a></dt>
<dd>
<div class="block">Returns a platform-specific HttpConnection</div>
</dd>
<dt><a href="../microsoft/aspnet/signalr/client/Credentials.html" title="interface in microsoft.aspnet.signalr.client"><span class="strong">Credentials</span></a> - Interface in <a href="../microsoft/aspnet/signalr/client/package-summary.html">microsoft.aspnet.signalr.client</a></dt>
<dd>
<div class="block">Interface for credentials to be sent in a request</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">L</a>&nbsp;<a href="index-11.html">M</a>&nbsp;<a href="index-12.html">N</a>&nbsp;<a href="index-13.html">O</a>&nbsp;<a href="index-14.html">P</a>&nbsp;<a href="index-15.html">R</a>&nbsp;<a href="index-16.html">S</a>&nbsp;<a href="index-17.html">T</a>&nbsp;<a href="index-18.html">U</a>&nbsp;<a href="index-19.html">V</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../microsoft/aspnet/signalr/client/package-summary.html">Package</a></li>
<li>Class</li>
<li>Use</li>
<li><a href="../microsoft/aspnet/signalr/client/package-tree.html">Tree</a></li>
<li class="navBarCell1Rev">Index</li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-2.html">Prev Letter</a></li>
<li><a href="index-4.html">Next Letter</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-filesindex-3.html" target="_top">Frames</a></li>
<li><a href="index-3.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
