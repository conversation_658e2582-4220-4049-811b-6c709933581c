//
//  BaseNavigationViewController.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 3/10/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit

class BaseNavigationViewController: UINavigationController {
    override func viewDidLoad() {
        super.viewDidLoad()
        setTransparent(false)
        hideBottomLine()
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
    }
    override func didReceiveMemoryWarning() {
        super.didReceiveMemoryWarning()
        // Dispose of any resources that can be recreated.
    }

    override var preferredStatusBarStyle: UIStatusBarStyle {
        return .lightContent
    }

    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
    }

    /*
    override func pushViewController(_ viewController: UIViewController, animated: Bool) {
        let duration: TimeInterval = self.transitionCoordinator?.transitionDuration ?? 0.3
        self.navigationBar.alpha = 0.9
        if viewController.isBarTransparent == false {
            self.setTransparent(false)
        }
        UIView.animate(withDuration: duration / 2, animations: {
            self.navigationBar.alpha = 1
        }) { (finished) in
            if viewController.isBarTransparent == true {
                self.setTransparent(true)
            } else {
                self.setTransparent(false)
            }
        }
        super.pushViewController(viewController, animated: animated)
    }

    override func popViewController(animated: Bool) -> UIViewController? {
        let previousVC = previousViewController
        let popController = super.popViewController(animated: animated)

        var order: ComparisonResult = .orderedSame
        if previousVC?.isBarTransparent == true && popController?.isBarTransparent == false {
            order = .orderedDescending
        } else if previousVC?.isBarTransparent != true && popController?.isBarTransparent == true {
            order = .orderedAscending
//            self.setTransparent(false)
        }

        self.transitionCoordinator?.animate(alongsideTransition: { (transition) in
        }, completion: { (transition) in
            if transition.isCancelled {
                self.setTransparent(popController?.isBarTransparent == true, alpha: 1)
            } else {
                if order == .orderedAscending {
                    self.setTransparent(false)
                } else if order == .orderedDescending {
                    self.setTransparent(true)
                }
            }
        })
        return popController
    }

    override func popToRootViewController(animated: Bool) -> [UIViewController]? {
//        setTransparent(viewControllers.first?.isBarTransparent == true)
        let popControllers = super.popToRootViewController(animated: animated)
        return popControllers
    }
 */
}

// Background
extension UINavigationBar {
    func applyGradientBackground(_ colors: [UIColor]? = nil, vertical: Bool = false) {
        var updatedFrame = bounds
        updatedFrame.size.height += self.frame.origin.y
        let gradientLayer = CAGradientLayer.defaultGradient
        if colors?.count == 2 {
            gradientLayer.colors = colors!.map { $0.cgColor }
        }
        if vertical {
            gradientLayer.startPoint = CGPoint(x: 0.5, y: 0.0)
            gradientLayer.endPoint = CGPoint(x: 0.5, y: 1.0)
        }

        gradientLayer.frame = updatedFrame
        setBackgroundImage(gradientLayer.image, for: .default)
    }
}

extension UINavigationController {
    var previousViewController: UIViewController? {
        guard viewControllers.count > 1 else {
            return nil
        }
        return viewControllers[viewControllers.count - 2]
    }
    
    func hideBottomLine(_ hidden: Bool = true) {
        if hidden {
            self.navigationBar.shadowImage = UIImage()
        }
    }

    func setTransparent(_ transparent: Bool, alpha: CGFloat = 1) {
        self.navigationBar.isTranslucent = transparent
        if transparent {
            self.navigationBar.applyGradientBackground([.init(white: 0, alpha: 0.6), .clear], vertical: true)
        } else {
            self.navigationBar.applyGradientBackground([UIColor.gradientBg1.withAlphaComponent(alpha), UIColor.gradientBg2.withAlphaComponent(alpha)], vertical: false)
        }
    }
}

public extension UINavigationBar {

    func setBarColor(_ barColor: UIColor?) {

        if barColor != nil && barColor!.cgColor.alpha == 0 {
            // if transparent color then use transparent nav bar
            self.setBackgroundImage(UIImage(), for: .default)
            self.hideShadow(true)
        }
        else if barColor != nil {
            // use custom color
            self.setBackgroundImage(self.image(with: barColor!), for: .default)
            self.hideShadow(false)
        }
        else {
            // restore original nav bar color
            self.setBackgroundImage(nil, for: .default)
            self.hideShadow(false)
        }
    }

    func hideShadow(_ doHide: Bool) {
        self.shadowImage = doHide ? UIImage() : nil
    }

    func image(with color: UIColor) -> UIImage {
        let rect = CGRect(x: CGFloat(0.0), y: CGFloat(0.0), width: CGFloat(1.0), height: CGFloat(1.0))
        UIGraphicsBeginImageContext(rect.size)
        if let context = UIGraphicsGetCurrentContext() {
            context.setFillColor(color.cgColor)
            context.fill(rect)
        }
        let image: UIImage? = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        return image!
    }

}
