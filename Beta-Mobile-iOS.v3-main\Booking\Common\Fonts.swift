//
//  Fonts.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 4/4/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit

enum FontName: String {
    case Oswald, SourceSansPro
}

enum FontStyle: String {
    case Regular,
    Bold,
    SemiBold,
    Medium,
    Light,
    ExtraLight
}

extension UIFont {
    static func fontName(_ fontName: FontName, style: FontStyle) -> String {
        return fontName.rawValue + "-" + style.rawValue
    }

    convenience init(fontName: FontName, style: FontStyle = .Regular, size: CGFloat) {
        self.init(name: UIFont.fontName(fontName, style: style), size: size)!
    }
}
