//
//  AppParams.swift
//  Booking
//
//  Created by <PERSON><PERSON> Vu on 8/14/19.
//  Copyright © 2019 ddkc. All rights reserved.
//

import Foundation
import ObjectMapper

class AppParams : Mappable {
    var id : Int?
    var paramsCode : String?
    var paramsName : String?
    var paramsMessage : String?
    var paramsMessage_F : String?
    var paramsMessageHighLight : String?
    var paramsMessageHighLight_F : String?
    var appType : String?
    var canClose : Bool?
    var value : String?
    var status: Bool?

    required init?(map: Map) {

    }

    func mapping(map: Map) {

        id <- map["Id"]
        paramsCode <- map["ParamsCode"]
        paramsName <- map["ParamsName"]
        paramsMessage <- map["ParamsMessage"]
        paramsMessage_F <- map["ParamsMessage_F"]
        paramsMessageHighLight <- map["ParamsMessageHighLight"]
        paramsMessageHighLight_F <- map["ParamsMessageHighLight_F"]
        appType <- map["AppType"]
        canClose <- map["CanClose"]
        value <- map["Value"]
        status <- map["Status"]
    }

    var key: String {
        return value ?? "AppVersion"
    }

    var message: String? {
        return Utils.shared.isEng() ? paramsMessage_F : paramsMessage
    }

    var messageHighLight: String? {
        return Utils.shared.isEng() ? paramsMessageHighLight_F : paramsMessageHighLight
    }

}
