//
//  ListSeatModel.swift
//  Booking-dev
//
//  Created by <PERSON><PERSON> on 5/27/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation
import ObjectMapper

class ListSeatModel: Mappable {
    var FilmId: String?
    var FilmName: String?
    var TenRap: String?
    var PhongChieu: String?
    var NgayChieu: String?
    var OpeningDate: String?
    var GioChieu: String?
    var MainPosterUrl: String?
    var TrailerUrl: String?
    var Duration: Int?
    var FilmFormat: String?
    var ListShow: String?
    var FilmGenre: String?
    var Screen: ScreenModel?
    var SeatSolded: Int?
    var TotalSeat: Int?
    var ShowSeats: [SeatModel]?
    var TicketTypes: [TicketType]?
    var Description: String?
    var Director: String?
    var Actors: String?
    var MainLanguage: String?
    var FilmFormatCode: String?
    var FilmFormatName: String?
    var FilmFormatName_F: String?
    required init?(map: Map) {

    }

    func mapping(map: Map) {
        FilmId <- map["FilmId"]
        FilmName <- map["FilmName"]
        TenRap <- map["TenRap"]
        Phong<PERSON>hieu <- map["PhongChieu"]
        NgayChieu <- map["NgayChieu"]
        OpeningDate <- map["OpeningDate"]
        GioChieu <- map["GioChieu"]
        MainPosterUrl <- map["MainPosterUrl"]
        TrailerUrl <- map["TrailerUrl"]
        Duration <- map["Duration"]
        FilmFormat <- map["FilmFormat"]
        ListShow <- map["ListShow"]
        FilmGenre <- map["FilmGenre"]
        Screen <- map["Screen"]
        SeatSolded <- map["SeatSolded"]
        TotalSeat <- map["TotalSeat"]
        ShowSeats <- map["ShowSeats"]
        TicketTypes <- map["TicketTypes"]
        Description <- map["Description"]
        Director <- map["Director"]
        Actors <- map["Actors"]
        MainLanguage <- map["MainLanguage"]
        FilmFormatCode <- map["FilmFormatCode"]
        FilmFormatName <- map["FilmFormatName"]
        FilmFormatName_F <- map["FilmFormatName_F"]
    }

    func getFilmFormatName() -> String?{
        return Utils.shared.isEng() ? FilmFormatName_F : FilmFormatName
    }

    func getFullOptions() -> String? {
        var option = ""
        option += (getFilmFormatName() ?? "") + " | "
        option += (Date.dateFromServerSavis(GioChieu ?? "")).toString(dateFormat: "H:mm dd/MM/yyyy") + " | "
        option += "\(Duration ?? 0) " + "Home.Minute".localized

        return option
    }

  var formatName: String {
    return getFilmFormatName() ?? ""
  }
}
