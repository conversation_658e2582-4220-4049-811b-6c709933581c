# 💳 Complete Payment Deeplink Implementation Guide

## 🎯 **OVERVIEW**

<PERSON><PERSON> b<PERSON> sung đầy đủ **ZaloPay**, **AirPay**, và **ShopeePay** deeplink handling vào Flutter repo để match với Android repo.

## ✅ **PAYMENT METHODS SUPPORTED**

### **1. 💰 MoMo** ✅ (Đã có sẵn)
- **Deeplink:** `betacineplexx://momo`
- **Parameters:** orderId, resultCode, requestId, transId, message, etc.
- **Handler:** `_handleMoMoPaymentReturn()`

### **2. 💙 ZaloPay** ✅ (<PERSON><PERSON><PERSON> thêm)
- **Deeplink:** `betacineplexx://zalopay`
- **Parameters:** appTransId, status, amount
- **Handler:** `_handleZaloPayPaymentReturn()`

### **3. 🛩️ AirPay** ✅ (<PERSON><PERSON><PERSON> thêm)
- **Deeplink:** `betacineplexx://airpay`
- **Parameters:** orderId, status
- **Handler:** `_handleAirPayPaymentReturn()`

### **4. 🛒 ShopeePay** ✅ (<PERSON><PERSON><PERSON> thêm)
- **Deeplink:** `betacineplexx://shopeepay`
- **Parameters:** orderId, status, amount
- **Handler:** `_handleShopeePayPaymentReturn()`

## 🔧 **IMPLEMENTATION DETAILS**

### **1. Android Manifest Updates**

```xml
<!-- AndroidManifest.xml -->

<!-- ZaloPay deeplink -->
<intent-filter android:autoVerify="true">
  <action android:name="android.intent.action.VIEW" />
  <category android:name="android.intent.category.DEFAULT" />
  <category android:name="android.intent.category.BROWSABLE" />
  <data android:scheme="betacineplexx" android:host="zalopay" />
</intent-filter>

<!-- AirPay deeplink -->
<intent-filter android:autoVerify="true">
  <action android:name="android.intent.action.VIEW" />
  <category android:name="android.intent.category.DEFAULT" />
  <category android:name="android.intent.category.BROWSABLE" />
  <data android:scheme="betacineplexx" android:host="airpay" />
</intent-filter>

<!-- ShopeePay deeplink -->
<intent-filter android:autoVerify="true">
  <action android:name="android.intent.action.VIEW" />
  <category android:name="android.intent.category.DEFAULT" />
  <category android:name="android.intent.category.BROWSABLE" />
  <data android:scheme="betacineplexx" android:host="shopeepay" />
</intent-filter>
```

### **2. MainActivity Deeplink Handling**

```kotlin
// MainActivity.kt
when (uri.host) {
  "momo" -> {
    // Extract MoMo parameters
    deepLinkMethodChannel?.invokeMethod("onMoMoPaymentReturn", momoParams)
  }
  "zalopay" -> {
    // Extract ZaloPay parameters
    deepLinkMethodChannel?.invokeMethod("onZaloPayPaymentReturn", zaloPayParams)
  }
  "airpay" -> {
    // Extract AirPay parameters
    deepLinkMethodChannel?.invokeMethod("onAirPayPaymentReturn", airPayParams)
  }
  "shopeepay" -> {
    // Extract ShopeePay parameters
    deepLinkMethodChannel?.invokeMethod("onShopeePayPaymentReturn", shopeePayParams)
  }
}
```

### **3. Flutter Deeplink Listener**

```dart
// android_style_webview_payment.dart
void _setupDeepLinkListener() {
  _deepLinkChannel.setMethodCallHandler((call) async {
    switch (call.method) {
      case 'onMoMoPaymentReturn':
        await _handleMoMoPaymentReturn(call.arguments);
        break;
      case 'onZaloPayPaymentReturn':
        await _handleZaloPayPaymentReturn(call.arguments);
        break;
      case 'onAirPayPaymentReturn':
        await _handleAirPayPaymentReturn(call.arguments);
        break;
      case 'onShopeePayPaymentReturn':
        await _handleShopeePayPaymentReturn(call.arguments);
        break;
    }
  });
}
```

### **4. Payment Return Handlers**

```dart
// ZaloPay Handler
Future<void> _handleZaloPayPaymentReturn(Map<dynamic, dynamic> arguments) async {
  final appTransId = arguments['appTransId']?.toString() ?? '';
  final status = arguments['status']?.toString() ?? '';
  final amount = arguments['amount']?.toString() ?? '';
  
  // Process via JavaScript
  final jsCall = '''
    if (typeof processZaloPayReturn === 'function') {
      processZaloPayReturn('$appTransId', '$status', '$amount');
    }
  ''';
  await _loadJs(jsCall);
  
  // Timeout fallback
  Future.delayed(const Duration(seconds: 10), () {
    if (status == '1' || status == 'success') {
      widget.onPaymentSuccess('ZaloPay payment completed');
    } else {
      widget.onPaymentFailed('ZaloPay payment failed');
    }
  });
}

// Similar handlers for AirPay and ShopeePay...
```

### **5. JavaScript Interface Functions**

```javascript
// JavaScript functions injected into WebView
window.processZaloPayReturn = function(appTransId, status, amount) {
  if (status === '1' || status === 'success') {
    window.androidkit.postPaymentSuccess(appTransId);
  } else {
    window.androidkit.postMessage('payment_failed');
  }
};

window.processAirPayReturn = function(orderId, status) {
  if (status === '1' || status === 'success') {
    window.androidkit.postPaymentSuccess(orderId);
  } else {
    window.androidkit.postMessage('payment_failed');
  }
};

window.processShopeePayReturn = function(orderId, status, amount) {
  if (status === '1' || status === 'success') {
    window.androidkit.postPaymentSuccess(orderId);
  } else {
    window.androidkit.postMessage('payment_failed');
  }
};
```

### **6. URL Navigation Handling**

```dart
// Handle payment method redirects
Future<NavigationActionPolicy> _handleUrlNavigation(String url) async {
  // ZaloPay
  if (url.contains('zalopay.vn')) {
    widget.onPaymentMethodSelected('zalopay');
    final directUrl = _generateDirectZaloPayUrl(url);
    await launchUrl(Uri.parse(directUrl), mode: LaunchMode.externalApplication);
    return NavigationActionPolicy.CANCEL;
  }
  
  // ShopeePay
  if (url.contains('shopee.vn') || url.contains('shopeepay')) {
    widget.onPaymentMethodSelected('shopeepay');
    final directUrl = _generateDirectShopeePayUrl(url);
    await launchUrl(Uri.parse(directUrl), mode: LaunchMode.externalApplication);
    return NavigationActionPolicy.CANCEL;
  }
  
  // AirPay
  if (url.contains('airpay.vn')) {
    widget.onPaymentMethodSelected('airpay');
    await _launchExternalUrl(url);
    return NavigationActionPolicy.CANCEL;
  }
}
```

### **7. Direct App URL Generation**

```dart
// Generate direct app URLs for better UX
String _generateDirectZaloPayUrl(String webUrl) {
  final params = {
    'action': 'payment',
    'appTransId': extractedAppTransId,
    'amount': extractedAmount,
    'callbackScheme': 'betacineplexx',
    'callbackHost': 'zalopay',
  };
  return 'zalopay://payment?${buildQueryString(params)}';
}

String _generateDirectShopeePayUrl(String webUrl) {
  final params = {
    'action': 'payment',
    'orderId': extractedOrderId,
    'amount': extractedAmount,
    'callbackScheme': 'betacineplexx',
    'callbackHost': 'shopeepay',
  };
  return 'shopeepay://payment?${buildQueryString(params)}';
}
```

## 🔄 **PAYMENT FLOW**

### **Complete Payment Flow:**
1. **User selects payment method** → WebView detects payment URL
2. **App launches external payment app** → Using direct app URLs
3. **User completes payment** → In external app (MoMo/ZaloPay/etc.)
4. **Payment app returns to Beta Cinemas** → Via deeplink
5. **Android receives deeplink** → MainActivity processes parameters
6. **Flutter receives callback** → Via MethodChannel
7. **WebView processes result** → Via JavaScript injection
8. **Payment success/failure** → Callback to parent widget

## 🧪 **TESTING SCENARIOS**

### **Test Each Payment Method:**

#### **1. MoMo Payment:**
- Select MoMo → Launch MoMo app → Complete payment → Return via `betacineplexx://momo`

#### **2. ZaloPay Payment:**
- Select ZaloPay → Launch ZaloPay app → Complete payment → Return via `betacineplexx://zalopay`

#### **3. AirPay Payment:**
- Select AirPay → Launch AirPay app → Complete payment → Return via `betacineplexx://airpay`

#### **4. ShopeePay Payment:**
- Select ShopeePay → Launch ShopeePay app → Complete payment → Return via `betacineplexx://shopeepay`

### **Test Scenarios:**
- ✅ **Success payments** → Should trigger `onPaymentSuccess`
- ❌ **Failed payments** → Should trigger `onPaymentFailed`
- ⏰ **Timeout scenarios** → Should use fallback handling
- 🔄 **App not installed** → Should fallback to web payment

## 🎯 **BENEFITS**

### **✅ Complete Payment Coverage:**
- **All major payment methods** supported with deeplinks
- **Consistent handling** across all payment types
- **Robust fallback mechanisms** for each method

### **✅ Enhanced User Experience:**
- **Direct app launches** for better UX
- **Seamless return flow** back to Beta Cinemas
- **Proper error handling** for all scenarios

### **✅ Android Repo Parity:**
- **Exact same payment methods** as Android repo
- **Same deeplink schemes** and parameters
- **Consistent behavior** across platforms

## 🚀 **READY FOR PRODUCTION**

The Flutter repo now has **complete payment deeplink support** matching the Android repo:

- ✅ **MoMo** deeplink handling
- ✅ **ZaloPay** deeplink handling  
- ✅ **AirPay** deeplink handling
- ✅ **ShopeePay** deeplink handling
- ✅ **Robust error handling** for all methods
- ✅ **Timeout fallbacks** for reliability
- ✅ **Direct app URL generation** for better UX

**🎉 All payment methods now support deeplink returns exactly like the Android repo!**
