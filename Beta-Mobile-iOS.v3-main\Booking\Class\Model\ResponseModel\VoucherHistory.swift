//
//  VoucherHistory.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 8/14/19.
//  Copyright © 2019 ddkc. All rights reserved.
//

import Foundation
import ObjectMapper

enum VoucherStatusType: Int, CustomStringConvertible {
    case used = 1
    case expired = 2
    case donated = 3
    case received = 4

    var description: String {
        switch self {
        case .used:
            return "used"
        case .expired:
            return "expired"
        case .donated:
            return "donated"
        case .received:
            return "received"

        }
    }

    var color: UIColor {
        switch self {
        case .used:
            return 0x3fb7f9.toColor
        case .expired:
            return 0xfd2802.toColor
        case .donated:
            return 0xfd7c02.toColor
        case .received:
            return 0x7ed321.toColor
        }
    }

    var showName: Bool {
        switch self {
        case .received, .donated:
            return true
        default:
            return false
        }
    }
}

class VoucherHistory : Mappable {
    var voucherId : String?
    var code : String?
    var date : String?
    var description : String?
    var statusType : Int?
    var status : String?
    var accountName : String?

    var voucherStatus: VoucherStatusType {
        guard let _statusType = statusType, let type = VoucherStatusType(rawValue: _statusType) else {
            return .used
        }
        return type
    }

    var dateString: String {
        guard let _date = date else {
            return "-"
        }
        let dateBE = Date.dateFromServerSavis(_date)
        return dateBE.toString(dateFormat: "dd/MM/yyyy, HH:mm")
    }

    required init?(map: Map) {

    }

    func mapping(map: Map) {

        voucherId <- map["VoucherId"]
        code <- map["Code"]
        date <- map["Date"]
        description <- map["Description"]
        statusType <- map["StatusType"]
        status <- map["Status"]
        accountName <- map["AccountName"]
    }

}
