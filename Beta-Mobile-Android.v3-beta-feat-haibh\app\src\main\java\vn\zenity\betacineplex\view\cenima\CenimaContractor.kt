package vn.zenity.betacineplex.view.cenima

import android.location.Location
import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.model.CinemaModel
import vn.zenity.betacineplex.model.CinemaProvinceModel

/**
 * Created by Zenity.
 */

interface CenimaContractor {
    interface View : IBaseView {
        fun showListCinema(listNear: List<CinemaModel>, listArea: List<CinemaProvinceModel>)
    }

    interface Presenter : IBasePresenter<View> {
        fun getCinema(currentLocation: Location?)
    }
}
