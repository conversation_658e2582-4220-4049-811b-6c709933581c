<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14490.70" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14490.49"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="Oswald-Regular.ttf">
            <string>Oswald-Regular</string>
        </array>
        <array key="SourceSansPro-Regular.ttf">
            <string>SourceSansPro-Regular</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="UseVoucherViewController" customModule="Booking_dev" customModuleProvider="target">
            <connections>
                <outlet property="barCodeImageView" destination="1VA-Je-Nhv" id="aUF-sz-DDO"/>
                <outlet property="bottomView" destination="99y-uA-wHv" id="ZDD-vj-VxV"/>
                <outlet property="codeLabel" destination="flq-CZ-jaH" id="yMk-Jk-Y4h"/>
                <outlet property="noteLabel" destination="UHG-ID-0Ug" id="0oH-5V-i1v"/>
                <outlet property="topView" destination="BmX-pu-iRZ" id="su0-aw-idH"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Quét mã này tại của hàng để sử dụng voucher" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="UHG-ID-0Ug">
                    <rect key="frame" x="20" y="64" width="374" height="19"/>
                    <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                    <color key="textColor" red="0.28627450980392155" green="0.29803921568627451" blue="0.38431372549019605" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <nil key="highlightedColor"/>
                </label>
                <view contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="2rg-Gc-fi1">
                    <rect key="frame" x="8" y="104.5" width="398" height="400"/>
                    <subviews>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="BmX-pu-iRZ" customClass="GradientView" customModule="Booking_dev" customModuleProvider="target">
                            <rect key="frame" x="0.0" y="0.0" width="398" height="50"/>
                            <subviews>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="flq-CZ-jaH">
                                    <rect key="frame" x="174.5" y="13.5" width="49" height="23.5"/>
                                    <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="20"/>
                                    <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <nil key="highlightedColor"/>
                                </label>
                            </subviews>
                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="50" id="dpk-bN-0St"/>
                                <constraint firstItem="flq-CZ-jaH" firstAttribute="centerX" secondItem="BmX-pu-iRZ" secondAttribute="centerX" id="led-sr-sFi"/>
                                <constraint firstItem="flq-CZ-jaH" firstAttribute="centerY" secondItem="BmX-pu-iRZ" secondAttribute="centerY" id="zSd-8c-eye"/>
                            </constraints>
                            <userDefinedRuntimeAttributes>
                                <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                    <real key="value" value="5"/>
                                </userDefinedRuntimeAttribute>
                            </userDefinedRuntimeAttributes>
                        </view>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="5dg-b0-ir5" customClass="DashView" customModule="Booking_dev" customModuleProvider="target">
                            <rect key="frame" x="0.0" y="50" width="398" height="4"/>
                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="4" id="d7d-B5-sZC"/>
                            </constraints>
                            <userDefinedRuntimeAttributes>
                                <userDefinedRuntimeAttribute type="number" keyPath="dashNumber">
                                    <integer key="value" value="25"/>
                                </userDefinedRuntimeAttribute>
                                <userDefinedRuntimeAttribute type="number" keyPath="dashWidth">
                                    <real key="value" value="6"/>
                                </userDefinedRuntimeAttribute>
                                <userDefinedRuntimeAttribute type="color" keyPath="dashColor">
                                    <color key="value" red="0.011764705882352941" green="0.34901960784313724" blue="0.61568627450980395" alpha="1" colorSpace="calibratedRGB"/>
                                </userDefinedRuntimeAttribute>
                            </userDefinedRuntimeAttributes>
                        </view>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="99y-uA-wHv" customClass="GradientView" customModule="Booking_dev" customModuleProvider="target">
                            <rect key="frame" x="0.0" y="54" width="398" height="398"/>
                            <subviews>
                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="7Sv-gu-rDD" customClass="RoundView" customModule="Booking_dev" customModuleProvider="target">
                                    <rect key="frame" x="16" y="16" width="366" height="366"/>
                                    <subviews>
                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="KLx-4h-dsa" customClass="RoundView" customModule="Booking_dev" customModuleProvider="target">
                                            <rect key="frame" x="8" y="8" width="350" height="350"/>
                                            <subviews>
                                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="demo_barcode" translatesAutoresizingMaskIntoConstraints="NO" id="1VA-Je-Nhv">
                                                    <rect key="frame" x="16" y="16" width="318" height="318"/>
                                                </imageView>
                                            </subviews>
                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <constraints>
                                                <constraint firstAttribute="trailing" secondItem="1VA-Je-Nhv" secondAttribute="trailing" constant="16" id="2zu-tQ-GUk"/>
                                                <constraint firstAttribute="bottom" secondItem="1VA-Je-Nhv" secondAttribute="bottom" constant="16" id="F2w-aX-7AO"/>
                                                <constraint firstItem="1VA-Je-Nhv" firstAttribute="top" secondItem="KLx-4h-dsa" secondAttribute="top" constant="16" id="GpJ-zF-1E5"/>
                                                <constraint firstItem="1VA-Je-Nhv" firstAttribute="leading" secondItem="KLx-4h-dsa" secondAttribute="leading" constant="16" id="sWp-IK-hBp"/>
                                            </constraints>
                                            <userDefinedRuntimeAttributes>
                                                <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                    <real key="value" value="5"/>
                                                </userDefinedRuntimeAttribute>
                                                <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                                    <color key="value" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                </userDefinedRuntimeAttribute>
                                                <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                                    <real key="value" value="0.0"/>
                                                </userDefinedRuntimeAttribute>
                                            </userDefinedRuntimeAttributes>
                                        </view>
                                    </subviews>
                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <constraints>
                                        <constraint firstItem="KLx-4h-dsa" firstAttribute="top" secondItem="7Sv-gu-rDD" secondAttribute="top" constant="8" id="619-Kq-QHK"/>
                                        <constraint firstAttribute="bottom" secondItem="KLx-4h-dsa" secondAttribute="bottom" constant="8" id="PGw-vg-b27"/>
                                        <constraint firstItem="KLx-4h-dsa" firstAttribute="leading" secondItem="7Sv-gu-rDD" secondAttribute="leading" constant="8" id="VYK-25-esR"/>
                                        <constraint firstAttribute="trailing" secondItem="KLx-4h-dsa" secondAttribute="trailing" constant="8" id="cQY-1j-c2G"/>
                                    </constraints>
                                    <userDefinedRuntimeAttributes>
                                        <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                            <real key="value" value="5"/>
                                        </userDefinedRuntimeAttribute>
                                        <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                            <color key="value" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        </userDefinedRuntimeAttribute>
                                        <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                            <real key="value" value="1"/>
                                        </userDefinedRuntimeAttribute>
                                    </userDefinedRuntimeAttributes>
                                </view>
                            </subviews>
                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <constraints>
                                <constraint firstAttribute="width" secondItem="99y-uA-wHv" secondAttribute="height" id="IoA-D9-W3N"/>
                                <constraint firstAttribute="bottom" secondItem="7Sv-gu-rDD" secondAttribute="bottom" constant="16" id="eHZ-i9-2kw"/>
                                <constraint firstAttribute="trailing" secondItem="7Sv-gu-rDD" secondAttribute="trailing" constant="16" id="eSy-vz-xQC"/>
                                <constraint firstItem="7Sv-gu-rDD" firstAttribute="top" secondItem="99y-uA-wHv" secondAttribute="top" constant="16" id="peP-GE-U9a"/>
                                <constraint firstItem="7Sv-gu-rDD" firstAttribute="leading" secondItem="99y-uA-wHv" secondAttribute="leading" constant="16" id="y99-Hs-ewx"/>
                            </constraints>
                            <userDefinedRuntimeAttributes>
                                <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                    <real key="value" value="5"/>
                                </userDefinedRuntimeAttribute>
                            </userDefinedRuntimeAttributes>
                        </view>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstAttribute="trailing" secondItem="5dg-b0-ir5" secondAttribute="trailing" id="0oG-Dd-nAM"/>
                        <constraint firstAttribute="trailing" secondItem="BmX-pu-iRZ" secondAttribute="trailing" id="3US-0r-AS2"/>
                        <constraint firstItem="99y-uA-wHv" firstAttribute="leading" secondItem="2rg-Gc-fi1" secondAttribute="leading" id="HO6-ys-9dj"/>
                        <constraint firstItem="BmX-pu-iRZ" firstAttribute="leading" secondItem="2rg-Gc-fi1" secondAttribute="leading" id="Jvt-Pv-tmd"/>
                        <constraint firstItem="BmX-pu-iRZ" firstAttribute="top" secondItem="2rg-Gc-fi1" secondAttribute="top" id="Lq2-Jv-G6h"/>
                        <constraint firstItem="99y-uA-wHv" firstAttribute="top" secondItem="5dg-b0-ir5" secondAttribute="bottom" id="Ngk-Pq-ozy"/>
                        <constraint firstAttribute="trailing" secondItem="99y-uA-wHv" secondAttribute="trailing" id="Tbn-z8-XSW"/>
                        <constraint firstItem="5dg-b0-ir5" firstAttribute="leading" secondItem="2rg-Gc-fi1" secondAttribute="leading" id="XrN-uv-hL2"/>
                        <constraint firstItem="5dg-b0-ir5" firstAttribute="top" secondItem="BmX-pu-iRZ" secondAttribute="bottom" id="fmZ-g2-8iL"/>
                    </constraints>
                </view>
            </subviews>
            <color key="backgroundColor" red="0.95294117647058818" green="0.95294117647058818" blue="0.95294117647058818" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
            <constraints>
                <constraint firstItem="UHG-ID-0Ug" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" constant="20" id="Qqz-1m-81J"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="2rg-Gc-fi1" secondAttribute="trailing" constant="8" id="UlS-Fl-ljz"/>
                <constraint firstItem="2rg-Gc-fi1" firstAttribute="top" secondItem="UHG-ID-0Ug" secondAttribute="bottom" constant="20" id="Ymf-b7-XPn"/>
                <constraint firstItem="UHG-ID-0Ug" firstAttribute="top" secondItem="fnl-2z-Ty3" secondAttribute="top" constant="20" id="epf-10-Ae0"/>
                <constraint firstItem="2rg-Gc-fi1" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" constant="8" id="hP4-Lc-th0"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="UHG-ID-0Ug" secondAttribute="trailing" constant="20" id="wzc-cV-CIm"/>
            </constraints>
            <viewLayoutGuide key="safeArea" id="fnl-2z-Ty3"/>
            <point key="canvasLocation" x="131.8840579710145" y="152.67857142857142"/>
        </view>
    </objects>
    <resources>
        <image name="demo_barcode" width="824" height="274"/>
    </resources>
</document>
