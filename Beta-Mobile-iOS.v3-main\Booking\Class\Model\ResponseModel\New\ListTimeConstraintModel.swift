//
//  ListTimeConstraintModel.swift
//  Booking
//
//  Created by <PERSON><PERSON>u on 4/5/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation
import ObjectMapper

class ListTimeConstraintModel : Mappable {
     var StartTime : String?
     var EndTime : String?
     var ValidityType : Int?
     var IsEnabled : Bool?
     var TimeConstraint : TimeConstraintModel?
     var CreatedOnDate : String?
     var LastModifiedOnDate : String?
     var CreatedByUserId : String?
     var LastModifiedByUserId : String?
     var StorylineId : String?
     var TimeConstraintId : String?


    required init?(map: Map) {

    }

    func mapping(map: Map) {
        StartTime            <- map["StartTime"]
        EndTime              <- map["EndTime"]
        ValidityType         <- map["ValidityType"]
        IsEnabled            <- map["IsEnabled"]
        TimeConstraint       <- map["TimeConstraint"]
        CreatedOnDate        <- map["CreatedOnDate"]
        LastModifiedOnDate   <- map["LastModifiedOnDate"]
        CreatedByUserId      <- map["CreatedByUserId"]
        LastModifiedByUserId <- map["LastModifiedByUserId"]
        StorylineId          <- map["StorylineId"]
        TimeConstraintId     <- map["TimeConstraintId"]
    }
}
