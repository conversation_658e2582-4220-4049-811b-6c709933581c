<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<title>All Classes</title>
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
</head>
<body>
<h1 class="bar">All Classes</h1>
<div class="indexContainer">
<ul>
<li><a href="microsoft/aspnet/signalr/client/Action.html" title="interface in microsoft.aspnet.signalr.client" target="classFrame"><i>Action</i></a></li>
<li><a href="microsoft/aspnet/signalr/client/CalendarSerializer.html" title="class in microsoft.aspnet.signalr.client" target="classFrame">CalendarSerializer</a></li>
<li><a href="microsoft/aspnet/signalr/client/Connection.html" title="class in microsoft.aspnet.signalr.client" target="classFrame">Connection</a></li>
<li><a href="microsoft/aspnet/signalr/client/ConnectionBase.html" title="interface in microsoft.aspnet.signalr.client" target="classFrame"><i>ConnectionBase</i></a></li>
<li><a href="microsoft/aspnet/signalr/client/ConnectionState.html" title="enum in microsoft.aspnet.signalr.client" target="classFrame">ConnectionState</a></li>
<li><a href="microsoft/aspnet/signalr/client/Constants.html" title="class in microsoft.aspnet.signalr.client" target="classFrame">Constants</a></li>
<li><a href="microsoft/aspnet/signalr/client/Credentials.html" title="interface in microsoft.aspnet.signalr.client" target="classFrame"><i>Credentials</i></a></li>
<li><a href="microsoft/aspnet/signalr/client/DateSerializer.html" title="class in microsoft.aspnet.signalr.client" target="classFrame">DateSerializer</a></li>
<li><a href="microsoft/aspnet/signalr/client/ErrorCallback.html" title="interface in microsoft.aspnet.signalr.client" target="classFrame"><i>ErrorCallback</i></a></li>
<li><a href="microsoft/aspnet/signalr/client/FutureHelper.html" title="class in microsoft.aspnet.signalr.client" target="classFrame">FutureHelper</a></li>
<li><a href="microsoft/aspnet/signalr/client/HeartbeatMonitor.html" title="class in microsoft.aspnet.signalr.client" target="classFrame">HeartbeatMonitor</a></li>
<li><a href="microsoft/aspnet/signalr/client/InvalidProtocolVersionException.html" title="class in microsoft.aspnet.signalr.client" target="classFrame">InvalidProtocolVersionException</a></li>
<li><a href="microsoft/aspnet/signalr/client/InvalidStateException.html" title="class in microsoft.aspnet.signalr.client" target="classFrame">InvalidStateException</a></li>
<li><a href="microsoft/aspnet/signalr/client/Logger.html" title="interface in microsoft.aspnet.signalr.client" target="classFrame"><i>Logger</i></a></li>
<li><a href="microsoft/aspnet/signalr/client/LogLevel.html" title="enum in microsoft.aspnet.signalr.client" target="classFrame">LogLevel</a></li>
<li><a href="microsoft/aspnet/signalr/client/MessageReceivedHandler.html" title="interface in microsoft.aspnet.signalr.client" target="classFrame"><i>MessageReceivedHandler</i></a></li>
<li><a href="microsoft/aspnet/signalr/client/MessageResult.html" title="class in microsoft.aspnet.signalr.client" target="classFrame">MessageResult</a></li>
<li><a href="microsoft/aspnet/signalr/client/NullLogger.html" title="class in microsoft.aspnet.signalr.client" target="classFrame">NullLogger</a></li>
<li><a href="microsoft/aspnet/signalr/client/Platform.html" title="class in microsoft.aspnet.signalr.client" target="classFrame">Platform</a></li>
<li><a href="microsoft/aspnet/signalr/client/PlatformComponent.html" title="interface in microsoft.aspnet.signalr.client" target="classFrame"><i>PlatformComponent</i></a></li>
<li><a href="microsoft/aspnet/signalr/client/SignalRFuture.html" title="class in microsoft.aspnet.signalr.client" target="classFrame">SignalRFuture</a></li>
<li><a href="microsoft/aspnet/signalr/client/SimpleEntry.html" title="class in microsoft.aspnet.signalr.client" target="classFrame">SimpleEntry</a></li>
<li><a href="microsoft/aspnet/signalr/client/StateChangedCallback.html" title="interface in microsoft.aspnet.signalr.client" target="classFrame"><i>StateChangedCallback</i></a></li>
<li><a href="microsoft/aspnet/signalr/client/UpdateableCancellableFuture.html" title="class in microsoft.aspnet.signalr.client" target="classFrame">UpdateableCancellableFuture</a></li>
<li><a href="microsoft/aspnet/signalr/client/Version.html" title="class in microsoft.aspnet.signalr.client" target="classFrame">Version</a></li>
</ul>
</div>
</body>
</html>
