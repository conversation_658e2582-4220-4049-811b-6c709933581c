//
//  UseVoucherViewController.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 8/14/19.
//  Copyright © 2019 ddkc. All rights reserved.
//

import UIKit
import RSBarcodes_Swift
import AVFoundation

class UseVoucherViewController: BaseViewController {

    @IBOutlet weak var noteLabel: UILabel!
    @IBOutlet weak var topView: GradientView!
    @IBOutlet weak var bottomView: GradientView!
    @IBOutlet weak var barCodeImageView: UIImageView!
    @IBOutlet weak var codeLabel: UILabel!

    var voucher: VoucherModel?
    override func viewDidLoad() {
        super.viewDidLoad()
        localizableTitle = "use_voucher"
        noteLabel.text = "use_voucher_note".localized
        topView.colors = [UIColor.gradientBg1, UIColor.gradientBg2]
        bottomView.colors = [UIColor.gradientBg1, UIColor.gradientBg2]

        codeLabel.text = voucher?.VoucherCode

        barCodeImageView.image = generateQRCode(from: voucher?.VoucherCode ?? "")
    }

    private func generateQRCode(from string: String) -> UIImage? {
        let data = string.data(using: String.Encoding.ascii)

        if let filter = CIFilter(name: "CIQRCodeGenerator") {
            filter.setValue(data, forKey: "inputMessage")
            let transform = CGAffineTransform(scaleX: 12, y: 12)

            if let output = filter.outputImage?.transformed(by: transform) {
                return UIImage(ciImage: output)
            }
        }

        return nil
    }

}
