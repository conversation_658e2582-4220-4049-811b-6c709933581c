<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14490.70" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14490.49"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="Oswald-Light.ttf">
            <string>Oswald-Light</string>
        </array>
        <array key="SourceSansPro-Bold.ttf">
            <string>SourceSansPro-Bold</string>
        </array>
        <array key="SourceSansPro-Regular.ttf">
            <string>SourceSansPro-Regular</string>
        </array>
        <array key="SourceSansPro-SemiBold.ttf">
            <string>SourceSansPro-SemiBold</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" rowHeight="95" id="KGk-i7-Jjw" customClass="HistoryVoucherTableViewCell" customModule="Booking_dev" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="320" height="95"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="94.5"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="pzg-qY-Y4b">
                        <rect key="frame" x="8" y="8" width="304" height="78.5"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="sVw-4e-HgE" customClass="RoundView" customModule="Booking_dev" customModuleProvider="target">
                                <rect key="frame" x="0.0" y="0.0" width="114" height="78.5"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="05/07/2019, 14:00" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Psx-Lw-LVg">
                                        <rect key="frame" x="3.5" y="10.5" width="107" height="18"/>
                                        <fontDescription key="fontDescription" name="SourceSansPro-SemiBold" family="Source Sans Pro" pointSize="14"/>
                                        <color key="textColor" red="0.28627450980392155" green="0.29803921568627451" blue="0.38431372549019605" alpha="1" colorSpace="calibratedRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Xfh-7A-o1E">
                                        <rect key="frame" x="0.0" y="36.5" width="114" height="42"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="ĐÃ TẶNG" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ie7-ol-Mrm">
                                                <rect key="frame" x="0.0" y="0.0" width="114" height="18"/>
                                                <fontDescription key="fontDescription" name="SourceSansPro-Bold" family="Source Sans Pro" pointSize="14"/>
                                                <color key="textColor" red="0.99215686274509807" green="0.48627450980392156" blue="0.0078431372549019607" alpha="1" colorSpace="calibratedRGB"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="(Nguyen Kim Thi)" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="8nE-7m-vgk">
                                                <rect key="frame" x="0.0" y="20" width="114" height="15.5"/>
                                                <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="12"/>
                                                <color key="textColor" red="0.070588235294117646" green="0.12156862745098039" blue="0.15686274509803921" alpha="1" colorSpace="calibratedRGB"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="trailing" secondItem="ie7-ol-Mrm" secondAttribute="trailing" id="2Kq-ya-GsM"/>
                                            <constraint firstAttribute="trailing" secondItem="8nE-7m-vgk" secondAttribute="trailing" id="H5f-wR-Fjg"/>
                                            <constraint firstItem="8nE-7m-vgk" firstAttribute="leading" secondItem="Xfh-7A-o1E" secondAttribute="leading" id="Ltb-hx-lb1"/>
                                            <constraint firstItem="ie7-ol-Mrm" firstAttribute="leading" secondItem="Xfh-7A-o1E" secondAttribute="leading" id="U1k-31-nRv"/>
                                            <constraint firstItem="ie7-ol-Mrm" firstAttribute="top" secondItem="Xfh-7A-o1E" secondAttribute="top" id="VaT-aq-LnS"/>
                                            <constraint firstItem="8nE-7m-vgk" firstAttribute="top" secondItem="ie7-ol-Mrm" secondAttribute="bottom" constant="2" id="ekW-hd-Avl"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="bottom" secondItem="Xfh-7A-o1E" secondAttribute="bottom" id="4Th-Y0-cl8"/>
                                    <constraint firstItem="Psx-Lw-LVg" firstAttribute="centerY" secondItem="sVw-4e-HgE" secondAttribute="centerY" constant="-20" id="8vT-Bl-gGq"/>
                                    <constraint firstAttribute="trailing" secondItem="Xfh-7A-o1E" secondAttribute="trailing" id="ZEf-T0-Ru8"/>
                                    <constraint firstItem="Xfh-7A-o1E" firstAttribute="top" secondItem="Psx-Lw-LVg" secondAttribute="bottom" constant="8" id="esr-nh-cqh"/>
                                    <constraint firstItem="Psx-Lw-LVg" firstAttribute="centerX" secondItem="sVw-4e-HgE" secondAttribute="centerX" id="pBs-Kx-WsF"/>
                                    <constraint firstItem="Xfh-7A-o1E" firstAttribute="leading" secondItem="sVw-4e-HgE" secondAttribute="leading" id="xre-5l-pBC"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                        <real key="value" value="5"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="xYy-Jm-Pt1" customClass="RoundView" customModule="Booking_dev" customModuleProvider="target">
                                <rect key="frame" x="118" y="0.0" width="186" height="78.5"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="VC00156723511416" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="XwJ-Il-tA2">
                                        <rect key="frame" x="16" y="10.5" width="87.5" height="18"/>
                                        <fontDescription key="fontDescription" name="Oswald-Light" family="Oswald" pointSize="12"/>
                                        <color key="textColor" red="0.28627450980392155" green="0.29803921568627451" blue="0.38431372549019605" alpha="1" colorSpace="calibratedRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Giảm 5K cho mỗi Combo khi mua vé kèm Combo cho 2 ..." textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="kEh-XN-Lrr">
                                        <rect key="frame" x="16" y="30.5" width="154" height="44"/>
                                        <fontDescription key="fontDescription" name="SourceSansPro-Bold" family="Source Sans Pro" pointSize="14"/>
                                        <color key="textColor" red="0.070588235289999995" green="0.1215686275" blue="0.15686274510000001" alpha="1" colorSpace="calibratedRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="kEh-XN-Lrr" firstAttribute="leading" secondItem="xYy-Jm-Pt1" secondAttribute="leading" constant="16" id="4H5-Ba-dKm"/>
                                    <constraint firstAttribute="bottom" secondItem="kEh-XN-Lrr" secondAttribute="bottom" constant="4" id="A8H-NO-bKU"/>
                                    <constraint firstItem="XwJ-Il-tA2" firstAttribute="leading" secondItem="xYy-Jm-Pt1" secondAttribute="leading" constant="16" id="cmJ-83-yXW"/>
                                    <constraint firstItem="XwJ-Il-tA2" firstAttribute="centerY" secondItem="xYy-Jm-Pt1" secondAttribute="centerY" constant="-20" id="epB-Ms-3Jl"/>
                                    <constraint firstItem="kEh-XN-Lrr" firstAttribute="top" secondItem="XwJ-Il-tA2" secondAttribute="bottom" constant="2" id="qxJ-MG-41x"/>
                                    <constraint firstAttribute="trailing" secondItem="kEh-XN-Lrr" secondAttribute="trailing" constant="16" id="zLe-eJ-BDC"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                        <real key="value" value="5"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="cfz-V5-qfR" customClass="DashView" customModule="Booking_dev" customModuleProvider="target">
                                <rect key="frame" x="114" y="4" width="4" height="70.5"/>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="4" id="kA8-ew-flF"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="dashNumber">
                                        <integer key="value" value="8"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="boolean" keyPath="horizontal" value="NO"/>
                                    <userDefinedRuntimeAttribute type="color" keyPath="dashColor">
                                        <color key="value" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="number" keyPath="dashWidth">
                                        <real key="value" value="4"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </view>
                        </subviews>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="cfz-V5-qfR" firstAttribute="top" secondItem="pzg-qY-Y4b" secondAttribute="top" constant="4" id="6eE-mU-mkW"/>
                            <constraint firstItem="xYy-Jm-Pt1" firstAttribute="top" secondItem="pzg-qY-Y4b" secondAttribute="top" id="CU3-bf-V0t"/>
                            <constraint firstItem="sVw-4e-HgE" firstAttribute="width" secondItem="pzg-qY-Y4b" secondAttribute="width" multiplier="3:8" id="FEp-G0-gV8"/>
                            <constraint firstItem="xYy-Jm-Pt1" firstAttribute="leading" secondItem="cfz-V5-qfR" secondAttribute="trailing" id="LeQ-E4-54A"/>
                            <constraint firstItem="sVw-4e-HgE" firstAttribute="top" secondItem="pzg-qY-Y4b" secondAttribute="top" id="UVq-Be-FYn"/>
                            <constraint firstAttribute="bottom" secondItem="cfz-V5-qfR" secondAttribute="bottom" constant="4" id="WCr-W4-6qQ"/>
                            <constraint firstItem="xYy-Jm-Pt1" firstAttribute="leading" secondItem="cfz-V5-qfR" secondAttribute="trailing" id="igw-ZG-Y8r"/>
                            <constraint firstAttribute="bottom" secondItem="xYy-Jm-Pt1" secondAttribute="bottom" id="mcX-5t-qXI"/>
                            <constraint firstAttribute="trailing" secondItem="xYy-Jm-Pt1" secondAttribute="trailing" id="p0i-Fb-a4i"/>
                            <constraint firstItem="cfz-V5-qfR" firstAttribute="leading" secondItem="sVw-4e-HgE" secondAttribute="trailing" id="pTZ-EW-kb0"/>
                            <constraint firstAttribute="bottom" secondItem="sVw-4e-HgE" secondAttribute="bottom" id="q2U-io-tJy"/>
                            <constraint firstItem="sVw-4e-HgE" firstAttribute="leading" secondItem="pzg-qY-Y4b" secondAttribute="leading" id="xaU-A7-XRS"/>
                        </constraints>
                    </view>
                </subviews>
                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                <constraints>
                    <constraint firstItem="pzg-qY-Y4b" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" constant="8" id="7vU-cu-rU5"/>
                    <constraint firstAttribute="bottom" secondItem="pzg-qY-Y4b" secondAttribute="bottom" constant="8" id="Aua-ed-jAa"/>
                    <constraint firstAttribute="trailing" secondItem="pzg-qY-Y4b" secondAttribute="trailing" constant="8" id="Kct-hY-lbc"/>
                    <constraint firstItem="pzg-qY-Y4b" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="8" id="k0G-d9-8nb"/>
                </constraints>
            </tableViewCellContentView>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <viewLayoutGuide key="safeArea" id="njF-e1-oar"/>
            <connections>
                <outlet property="codeLabel" destination="XwJ-Il-tA2" id="8Mw-QI-QXe"/>
                <outlet property="dateLabel" destination="Psx-Lw-LVg" id="9cb-S0-pyI"/>
                <outlet property="nameLabel" destination="8nE-7m-vgk" id="iHe-Sv-r51"/>
                <outlet property="stateLabel" destination="ie7-ol-Mrm" id="OS9-BK-iym"/>
                <outlet property="titleLabel" destination="kEh-XN-Lrr" id="9FC-bR-a5y"/>
            </connections>
            <point key="canvasLocation" x="34.782608695652179" y="51.897321428571423"/>
        </tableViewCell>
    </objects>
</document>
