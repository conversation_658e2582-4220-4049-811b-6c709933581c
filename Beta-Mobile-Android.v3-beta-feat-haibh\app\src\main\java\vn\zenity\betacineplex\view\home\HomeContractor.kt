package vn.zenity.betacineplex.view.home

import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.model.*

/**
 * Created by eLifeTech.
 */

interface HomeContractor {
    interface View : IBaseView {
        fun openFilm(film: FilmModel)
        fun openEvent(event: NewsModel)
        fun openCinema(cinema: Cinema)
        fun openAllEvents()
        fun openAllCinema()
        fun showListFilm(listFilms: List<FilmModel>)
        fun showListEvent(listEvents: List<NewsModel>)
        fun showNearCinema(cinema: Cinema)
        fun showBanner(banners: List<BannerModel>)
        fun showAppParams(params: List<AppParamsModel>)
    }

    interface Presenter : IBasePresenter<View> {
        fun fetchListMovie(type: Int, isReselect: Boolean = false)
        fun fetchListEvent()
        fun getNearCinema()
        fun getBanner()
        fun getAppParams()
    }
}
