//
//  RegisterModel.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 4/4/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation

class RegisterModel: BaseRequestModel {
//    var firstName: String
//    var lastName: String
    var name: String
    var email: String?
    var password: String?
    var phone: String
    var passport: String?

    var birthDay: String?
    var gender: Int?
    var city: CityModel?
    var district: CityModel?
    var address: String?
    var isCreateCard: Bool?
    var token: String?
    
    init(name: String, email: String? = nil, password: String? = "", passport: String? = nil, phone: String, birthDay: String, gender: Int? = 0, city: CityModel? = nil, district: CityModel? = nil, address: String? = nil, isCreateCard: Bool? = nil) {
//        self.firstName = firstName
//        self.lastName = lastName
        self.name = name
        self.email = email
        self.password = password
        self.phone = phone
        self.passport = passport
        self.birthDay = birthDay
        
        self.gender = gender
        self.city = city
        self.district = district
        self.address = address
        self.isCreateCard = isCreateCard
    }
    
    override func toJSON() -> [String: Any] {
        var json: [String: Any] = [
//                "FirstName": firstName,
//                "LastName": lastName,
                "FullName": name,
//                "Email": email,
                "Password": password ?? "",
                "PhoneOffice": phone,
//                "PersonalId": passport ?? "",
                "Gender": gender ?? "0",
                "BirthDate": birthDay ?? "",
//                "AddressCity": city?.Name ?? "",
//                "AddressCityId": city?.Id ?? "",
//                "AddressDistrict": district?.Name ?? "",
//                "AddressDistrictId": district?.Id ?? "",
//                "AddressStreet": address ?? ""
        ]
        if let email = email {
            json.updateValue(email, forKey: "Email")
        }
        if let passport = passport {
            json.updateValue(passport, forKey: "PersonalId")
        }
        if let city = city {
            json.updateValue(city.Name ?? "", forKey: "AddressCity")
            json.updateValue(city.Id ?? "", forKey: "AddressCityId")
        }
        if let district = district {
            json.updateValue(district.Name ?? "", forKey: "AddressDistrict")
            json.updateValue(district.Id ?? "", forKey: "AddressDistrictId")
        }
        if let street = address {
            json.updateValue(street, forKey: "AddressStreet")
        }
        if let isCreateCard = self.isCreateCard {
            json.updateValue(isCreateCard, forKey: "IsCreateCard")
        }

        if let token = self.token {
            json.updateValue(token, forKey: "ReCaptchaToken")
        }
        return json
    }
}
