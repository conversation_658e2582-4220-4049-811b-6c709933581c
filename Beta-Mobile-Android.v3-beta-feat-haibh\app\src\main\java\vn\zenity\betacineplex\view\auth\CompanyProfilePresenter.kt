package vn.zenity.betacineplex.view.auth

import vn.zenity.betacineplex.Manager.Network.APIClient
import vn.zenity.betacineplex.app.App
import vn.zenity.betacineplex.helper.extension.applyOn
import java.lang.ref.WeakReference

/**
 * Created by Zenity.
 */

class CompanyProfilePresenter : CompanyProfileContractor.Presenter {
    override fun getCompanyProfile() {
        val lang = App.shared().getCurrentLang()
        APIClient.shared.ecmAPI.getCompanyInfoId("mobile:app:thongtin-congty:$lang").applyOn()
                .subscribe({
                    if (it.isSuccess) {
                        it.Data?.ParameterValue?.let {
                            APIClient.shared.ecmAPI.getNewWithId(it).applyOn()
                                    .subscribe({
                                        if (it.Data != null) {
                                            view?.get()?.showCompanyProfile(it.Data!!)
                                        }
                                    }, {
                                    })
                        }
                    }
                }, {

                })
    }

    private var view: WeakReference<CompanyProfileContractor.View?>? = null
    override fun attachView(view: CompanyProfileContractor.View) {
        this.view = WeakReference(view)
    }

    override fun detachView() {
        this.view?.clear()
        this.view = null
    }
}
