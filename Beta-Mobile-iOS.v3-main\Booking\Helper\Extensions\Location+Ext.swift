//
//  Location+Ext.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 4/12/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation
import CoreLocation

extension CLLocationCoordinate2D {
    func distanceTo(coordinate: CLLocationCoordinate2D) -> CLLocationDistance {
        let thisLocation = CLLocation(latitude: self.latitude, longitude: self.longitude)
        let otherLocation = CLLocation(latitude: coordinate.latitude, longitude: coordinate.longitude)
        
        return thisLocation.distance(from: otherLocation)
    }
}
