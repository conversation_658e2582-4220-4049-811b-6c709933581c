//
//  FilmItemTableCell.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 4/22/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit

class FilmSeperateView: UIView {
    override func draw(_ rect: CGRect) {
        super.draw(rect)

        let startX: CGFloat = 0
        let radius: CGFloat = 10
        let context = UIGraphicsGetCurrentContext()
        context?.beginPath()
        context?.setFillColor(UIColor.viewBg.cgColor)
        context?.move(to: CGPoint(x: startX, y: -radius))
        context?.fillEllipse(in: CGRect(x: startX, y: -radius, width: radius * 2, height: radius * 2))
        context?.move(to: CGPoint(x: startX, y: rect.maxY - radius))
        context?.fillEllipse(in: CGRect(x: startX, y: rect.maxY - radius, width: radius * 2, height: radius * 2))
        context?.fillPath()
//        context?.closePath()

//        context?.beginPath()
        context?.setLineWidth(4)
        context?.setLineDash(phase: 0, lengths: [5, 3])
        context?.setStrokeColor(UIColor.viewBg.cgColor)
        context?.move(to: CGPoint(x: startX + radius, y: radius))
        context?.addLine(to: CGPoint(x: startX + radius, y: rect.maxY - radius))
        context?.strokePath()
        if context?.isPathEmpty == false {
            context?.closePath()
        }
    }
}

class FilmItemTableCell: UITableViewCell {
    @IBOutlet weak var ivFilmLogo: RoundImageView!
    @IBOutlet weak var btPlay: UIButton!
    @IBOutlet weak var vLeft: UIView!
    @IBOutlet weak var lbFilmName: UILabel!
    @IBOutlet weak var lbFilmType: UILabel!
    @IBOutlet weak var lbFilmFormat: UILabel!
    @IBOutlet weak var lbFilmDuration: UILabel!
    @IBOutlet weak var ivAgeRate: UIImageView!
    @IBOutlet weak var ivTop: UIImageView!

    var onPlayFilmTrailer: (() -> Void)?

    override func awakeFromNib() {
        super.awakeFromNib()
        // Initialization code
    }

    override func setSelected(_ selected: Bool, animated: Bool) {
        super.setSelected(selected, animated: animated)

        // Configure the view for the selected state
    }

    override func updateViewWithItem(_ item: TableItem, indexPath: IndexPath) {
        guard let film = item.data as? FilmModel else {
            return
        }

        lbFilmName.text = film.getName()
        lbFilmFormat.text = film.getFormatName()
        lbFilmType.text = film.getFilmGenre()
        lbFilmDuration.text = "\(film.Duration ?? 0) " + "Home.Minute".localized

        let imageURL = Config.BaseURLResource + (film.MainPosterUrl ?? "")
        ivFilmLogo.af_setImage(withURL: URL(string: imageURL)!)

        if film.FilmRestrictAgeName == FilmModel.RestrictAge.c13 {
            ivAgeRate.image = #imageLiteral(resourceName: "ic_c13")
        } else if film.FilmRestrictAgeName == FilmModel.RestrictAge.c16 {
            ivAgeRate.image = #imageLiteral(resourceName: "ic_c16")
        } else if film.FilmRestrictAgeName == FilmModel.RestrictAge.c18 {
            ivAgeRate.image = #imageLiteral(resourceName: "ic_c18")
        } else if film.FilmRestrictAgeName == FilmModel.RestrictAge.p {
            ivAgeRate.image = #imageLiteral(resourceName: "ic_p")
        } else {
            ivAgeRate.image = nil
        }
        ivTop.isHidden = !item.isOpen
    }

    override func layoutSubviews() {
        super.layoutSubviews()
        setNeedsDisplay()
    }

    @IBAction func playButtonPressed(_ sender: Any) {
        onPlayFilmTrailer?()
    }
}
