//
//  ParagraphDataModel.swift
//  Booking
//
//  Created by <PERSON><PERSON>u on 4/5/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation
import ObjectMapper

class ParagraphDataModel : Mappable {
     var ParagraphID : String?
     var ParagraphContent : String?
     var ParagraphDraftContent : String?
     var URI : String?
     var ApplicationId : String?
     var CreatedByUserID : String?
     var CreatedOnDate : String?
     var LastModifiedByUserID : String?
     var LastModifiedOnDate : String?
     var StartDate : String?
     var EndDate : String?
     var HasAlarm : Bool?
     var DeleteSuccess : Bool?


    required init?(map: Map) {

    }

    func mapping(map: Map) {
        ParagraphID          <- map["ParagraphID"]
        ParagraphContent     <- map["ParagraphContent"]
        ParagraphDraftContent <- map["ParagraphDraftContent"]
        URI                  <- map["URI"]
        ApplicationId        <- map["ApplicationId"]
        CreatedByUserID      <- map["CreatedByUserID"]
        CreatedOnDate        <- map["CreatedOnDate"]
        LastModifiedByUserID <- map["LastModifiedByUserID"]
        LastModifiedOnDate   <- map["LastModifiedOnDate"]
        StartDate            <- map["StartDate"]
        EndDate              <- map["EndDate"]
        HasAlarm             <- map["HasAlarm"]
        DeleteSuccess        <- map["DeleteSuccess"]
    }
}
