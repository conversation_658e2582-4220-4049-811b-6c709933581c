//
//  LocalizableProtocol.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 4/14/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit

protocol Localizable: class {
    var localizableString: String? { get set }

    func updateLocalizable()
}

extension UIView {
    func updateLocalizableAllSubView() {
        subviews.forEach {
            if let v = $0 as? Localizable {
                v.updateLocalizable()
            } else {
                $0.updateLocalizableAllSubView()
            }
        }
    }
}
