//
//  MetadataListModel.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 4/5/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation
import ObjectMapper

class MetadataListModel : Mappable {
     var NodeMetadataID : String?
     var FileId : String?
     var NodeTypeID : String?
     var MetadataID : String?
     var MetadataName : String?
     var MetadataName_VI : String?
     var MetadataValue : String?


    required init?(map: Map) {

    }

    func mapping(map: Map) {
        NodeMetadataID       <- map["NodeMetadataID"]
        FileId               <- map["FileId"]
        NodeTypeID           <- map["NodeTypeID"]
        MetadataID           <- map["MetadataID"]
        MetadataName         <- map["MetadataName"]
        MetadataName_VI      <- map["MetadataName_VI"]
        MetadataValue        <- map["MetadataValue"]
    }
}
