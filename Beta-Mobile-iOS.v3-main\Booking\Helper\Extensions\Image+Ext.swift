//
//  Image+Ext.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 4/19/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit
import AlamofireImage

extension UIImage {
    public func resize(_ size: CGSize) -> UIImage {
//        let scale = min(size.width / self.size.width, size.height / self.size.height)
//        return af_imageScaled(to: CGSize(width: scale * self.size.width, height: scale * self.size.height))
        return af_imageAspectScaled(toFill: size)
    }

    func resize(_ scale: CGFloat) -> UIImage {
        return resize(CGSize(width: size.width * scale, height: size.height * scale))
    }
}
