//
//  PaymentViewController.swift
//  Booking
//
//  Created by <PERSON><PERSON> Vu on 3/14/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit
import WebKit
import SwiftDate
import PKHUD
import RxSwift

class PaymentViewController: BaseViewController {
    var show: ShowModel?
    var seat: [SeatModel]?
    var ticketType: [TicketType]?
    var film: FilmInformation?
    var cinemaId: String?
    var cinemaName: String?
    var listSeat: ListSeatModel?
    var filmFormat: String?
    fileprivate var timer: Timer?
    var timeStartBooking: TimeInterval = 0
    var seatsPrice: Int?
    
    //TRACKING
    private var movieId: String?
    private var movieName: String?
    private var date: Date?
    private var time: Date?
    private var screen: String?
    private var normalSeats: Int?
    private var vipSeats: Int?
    private var doubleSeats: Int?
    private var totalSeats: Int?
    private var totalAmount: Int?
    private var discountAmount: Int?
    private var paymentAmount: Int?
    private var totalCombos: Int?
    private var totalComboAmount: Int?
    private var redeemVouchers: Int?
    private var redeemPoints: Int?
    private var paymentMethod: String?
    
    
    var webView: WKWebView!
    let userContentController = WKUserContentController()
    fileprivate let webViewKeyPathsToObserve = ["loading", "estimatedProgress", "title"]
    fileprivate var webData: String = ""
    fileprivate var transparentButton: UIButton?

    
    private var airPayOrderId: String? // For airpay case

    // For zalopay
    private var zaloPayAppId: String?
    private var zaloPayTransId: String?
    private var zaloPayPmcId: String?
    private var zaloPayBankCode: String?
    private var zaloPayAmount: String?
    private var zaloPayDAmount: String?
    private var zaloPayStatus: String?
    private var zaloPayCheckSum: String?
    
    //For Momo payment method
    private var momoOrderId: String?
    private var resultCode: String?
    private var requestId: String?
    private var transId: String?
    private var message: String?
    private var responseTime: String?
    private var payType: String?
    private var extraData: String?
    private var partnerCode: String?
    
    
    override func viewDidLoad() {
        super.viewDidLoad()

        setTitle("Payment.Title".localized)

        let configuration = WKWebViewConfiguration()
        configuration.userContentController = userContentController
        userContentController.add(self, name: "scriptHandler")

//        addLeftButton(#imageLiteral(resourceName: "icBack"))
        transparentButton = UIButton()
        transparentButton?.frame = CGRect( x: 0, y: 0, width: 50, height: 40)
        self.navigationController?.navigationBar.addSubview(transparentButton!)

        let jscript = "var meta = document.createElement('meta'); meta.setAttribute('name', 'viewport'); meta.setAttribute('content', 'width=device-width'); document.getElementsByTagName('head')[0].appendChild(meta);"
        let userScript = WKUserScript(source: jscript, injectionTime: .atDocumentEnd, forMainFrameOnly: true)
        userContentController.addUserScript(userScript)
     
        webView = WKWebView(frame: view.bounds, configuration: configuration)
        webView.autoresizingMask = [.flexibleWidth, .flexibleHeight]
        webView.uiDelegate = self
        webView.navigationDelegate = self
        view.addSubview(webView)
        webView.scrollView.delegate = self

        for keyPath in webViewKeyPathsToObserve {
            webView.addObserver(self, forKeyPath: keyPath, options: .new, context: nil)
        }

        getPaymentWeb()

        NotificationCenter.default.addObserver(self,
                                               selector: #selector(applicationDidBecomeActive),
                                               name: .UIApplicationDidBecomeActive,
            object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(receivedDataFromMomo(_:)), name: NSNotification.Name.CheckMomoOrderStatus, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(receivedDataFromZaloPay(_:)), name: NSNotification.Name.CheckZaloPayOrderStatus, object: nil)
    }

    override func viewDidDisappear(_ animated: Bool) {
        super.viewDidDisappear(animated)
        transparentButton?.removeTarget(nil, action: #selector(leftButtonPressed(_:)), for: .touchUpInside)
        transparentButton?.isUserInteractionEnabled = false
        stopTimer()
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        transparentButton?.addTarget(self, action: #selector(leftButtonPressed(_:)), for: .touchUpInside)
        transparentButton?.isUserInteractionEnabled = true
        startTimer()
    }

    deinit {
        for keyPath in webViewKeyPathsToObserve {
            webView.removeObserver(self, forKeyPath: keyPath)
        }
        webView.scrollView.delegate = nil
        transparentButton?.removeFromSuperview()

        NotificationCenter.default.removeObserver(self,
                                                  name: .UIApplicationDidBecomeActive,
            object: nil)
    }

    @objc func applicationDidBecomeActive() {
        print("Application become active")
        self.dismissLoading()
        checkAirPayPurchaseStatus()
        checkMomoPurchaseStatus()
        checkZaloPayPurchaseStatus()
    }

    func getPaymentWeb() {
        let ticketType = self.listSeat?.TicketTypes
        guard let showId = show?.showId, let seats = seat, let normalTicket = ticketType?.first(where: { $0.isNormal }), let vipTicket = ticketType?.first(where: { $0.isVip }), let coupleTicket = ticketType?.first(where: { $0.isCouple }) else {
            return
        }

        let seatBookingList = seats.map {
            SeatBookingModel(seat: $0, ticketType: $0.seatType?.isNormal == true ? normalTicket : ($0.seatType?.isCouple == true ? coupleTicket : vipTicket) )
        }
        let bookingModel = CreateBookingModel(showId: showId, list: seatBookingList, expiredTime: Date.init(timeIntervalSince1970: timeStartBooking + Config.TimeExpired).toServerString())

       showLoading()

        FilmProvider.rx.request(.booking(bookingModel)).mapString().subscribe(onNext: { response in
            self.dismissLoading()

            self.saveWeb(response)
            self.webView.loadHTMLString(response, baseURL: URL(string: Config.BaseURL))
        }).disposed(by: disposeBag)
    }

    func saveWeb(_ data: String) {
        webData = data
    }

    override func leftButtonPressed(_ sender: UIBarButtonItem) {
//        print("\(webView.url)")
        if webView.canGoBack {
            webView.goBack()
        } else if webView.url?.absoluteString.contains(Config.BaseURL) == false {
            self.webView.loadHTMLString(webData, baseURL: URL(string: Config.BaseURL))
        } else {
            webView.evaluateJavaScript("screenType;") { data, error in
                let type = data as? String
                if type == "payment" || error != nil {
                    super.leftButtonPressed(sender)
                } else {
                    self.webView.evaluateJavaScript("backToMain();", completionHandler: nil)
                }
            }
        }
    }

    func updateBookingInfo() {
        guard let user = Global.shared.user else {
            return
        }
        let normalTicket = self.listSeat?.TicketTypes?.first(where: { $0.isNormal })
        let vipTicket = self.listSeat?.TicketTypes?.first(where: { $0.isVip })
        let coupleTicket = self.listSeat?.TicketTypes?.first(where: { $0.isCouple })

        let realSeats: [SeatModel] = seat?.compactMap{
//            if $0.seatType?.isCouple == true {
//                return $0.coupleSeat
//            }
            return $0
        } ?? []
        let totalPrice = realSeats.compactMap {
            if $0.seatType?.isVip == true {
                return vipTicket?.Price
            } else if $0.seatType?.isCouple == true {
                return coupleTicket?.Price
            } else if $0.seatType?.isNormal == true {
                return normalTicket?.Price
            } else {
                return nil
            }
            }.reduce(0, +)

        let combo: String = [realSeats.first{ $0.seatType?.isNormal == true }?.seatType?.Name,
                            realSeats.first{ $0.seatType?.isVip == true }?.seatType?.Name,
                            realSeats.first{ $0.seatType?.isCouple == true }?.seatType?.Name].compactMap{$0}.joined(separator: ", ")

        let date = listSeat?.NgayChieu?.toDate("yyyy-MM-dd'T'HH:mm:ss")?.toString(dateFormat: "dd/MM/yyyy")
        let time = listSeat?.GioChieu?.toDate("yyyy-MM-dd'T'HH:mm:ss")?.toString(dateFormat: "HH:mm")

        let method = "getBookingInfo({'FilmName': '\(film?.getName() ?? "")', 'FilmInfo': '\(film?.getFinalOptions(filmFormat) ?? "")', 'CinemaName': '\(listSeat?.TenRap ?? "")', 'DateShow': '\(date ?? "")', 'ShowTime': '\(time ?? "")', 'Combo': '\(combo)', 'TotalMoney': '\(totalPrice.toCurrency())', 'Screen': '\(listSeat?.PhongChieu ?? "")', 'FilmPoster': '\(film?.getFilmPoster() ?? "")', 'FilmFormatCode': '\(listSeat?.FilmFormatCode ?? "")'});"
        webView.evaluateJavaScript(method) { (data, error) in
//            print("\(#function) data: \(data) error: \(error)")
        }

        webView.evaluateJavaScript("getCustomerInfo({'customerId': '\(user.AccountId ?? "")', 'customerCard': '\(user.CardNumber ?? "")'});") { data, error in
//            print("\(#function) data: \(data) error: \(error)")
        }
    }

    override func observeValue(forKeyPath keyPath: String?, of object: Any?, change: [NSKeyValueChangeKey : Any]?, context: UnsafeMutableRawPointer?) {
        guard let keyPath = keyPath else { return }

        switch keyPath {
        case "loading":
            UIApplication.shared.isNetworkActivityIndicatorVisible = webView.isLoading
            if !HUD.isVisible && webView.isLoading {
                self.showLoading()
            } else if !webView.isLoading {
                self.dismissLoading()
            }
            break
        case "title":
            setTitle(webView.title)
        default:
            break
        }
    } 

    func checkAirPayPurchaseStatus() {
        guard let orderId = self.airPayOrderId else {
            return
        }
        let jsfuntion = String(format: "checkShopeePayTransactionStatus('%@');", orderId)
        webView.evaluateJavaScript(jsfuntion) { result, error in
            print("Result: \(String(describing: result))")
        }
    }
    
    func checkMomoPurchaseStatus() {
        guard let orderId = self.momoOrderId,
              let resultcode = self.resultCode,
              let requestId = self.requestId,
              let transId = self.transId,
              let message = self.message,
              let responseTime = self.responseTime,
              let payType = self.payType,
              let extraData = self.extraData,
              let partnerCode = self.partnerCode
              else {
                return
              }
        let jsfunction = String(format: "checkMomoTransactionStatus('%@', '%@', '%@', '%@', '%@', '%@', '%@', '%@', '%@');", orderId, resultcode, requestId, transId, message, responseTime, payType, extraData, partnerCode)
        webView.evaluateJavaScript(jsfunction) {result, error in
            print("Result: \(String(describing: result))")
        }
    }
    
    func checkZaloPayPurchaseStatus() {
        guard let appId = self.zaloPayAppId,
              let transId = self.zaloPayTransId,
              let pmcId = self.zaloPayPmcId,
              let bankCode = self.zaloPayBankCode,
              let amount = self.zaloPayAmount,
              let dAmount = self.zaloPayDAmount,
              let status = self.zaloPayStatus,
              let checkSum = self.zaloPayCheckSum
              else {
                return
              }
        let jsfuntion = String(format: "checkZaloPayTransactionStatus('%@', '%@', '%@', '%@', '%@', '%@', '%@', '%@');", appId, transId, pmcId, bankCode, amount, dAmount, status, checkSum)
        webView.evaluateJavaScript(jsfuntion) { result, error in
            print("Result: \(String(describing: result))")
        }
    }
    
    @objc func receivedDataFromMomo(_ notification: Notification) {
        guard let object = notification.object as? (String, String, String, String, String, String, String, String, String) else {
            return
        }
        self.momoOrderId = object.0;
        self.resultCode = object.1
        self.requestId = object.2
        self.transId = object.3
        self.message = object.4
        self.responseTime = object.5
        self.payType = object.6
        self.extraData = object.7
        self.partnerCode = object.8
    }
    
    @objc func receivedDataFromZaloPay(_ notification: Notification) {
        guard let object = notification.object as? (String, String, String, String, String, String, String, String) else {
            return
        }
        self.zaloPayAppId = object.0
        self.zaloPayTransId = object.1
        self.zaloPayPmcId = object.2
        self.zaloPayBankCode = object.3
        self.zaloPayAmount = object.4
        self.zaloPayDAmount = object.5
        self.zaloPayStatus = object.6
        self.zaloPayCheckSum = object.7
    }

    func getTransactionDetail() {
        webView.evaluateJavaScript("getTransactionId();") { result, error in
            if let id = result as? String {
                let transactionModel = TransactionHistoryModel(JSON: [:])
                transactionModel?.Invoice_Id = id
                let vc = UIStoryboard.member[.transactionDetail] as! TransactionDetailViewController
                vc.item = transactionModel
                vc.backToHome = true
                self.navigationController?.pushViewController(vc, animated: true)
            } else {
                self.showAlert(title: "Error".localized) { _ in
                    self.navigationController?.popToRootViewController(animated: false)
                    AppDelegate.shared.gotoHome()
                }
            }
        }
    }        
    
    func startTimer() {
        stopTimer()
        timer = Timer.scheduledTimer(timeInterval: 1, target: self, selector: #selector(updateTimer), userInfo: nil, repeats: true)
        timer?.fire()
    }

    func stopTimer() {
        if (timer != nil) {
            timer?.invalidate()
            timer = nil;
        }
    }

    @objc func updateTimer() {
        let currentTime = NSDate().timeIntervalSince1970
        let timeLeft = Int(Config.TimeExpired - (currentTime - timeStartBooking));
        if (timeLeft <= 0) {
            stopTimer()
            self.showAlert(message: "SeatsTimeOut".localized) { _ in
                self.navigationController?.popToRootViewController(animated: true)
            }
        } 
    }
    
    func tracking( type: String,_ errorCode: String?, _ errorMsg: String?){

        self.webView.evaluateJavaScript("document.documentElement.innerHTML.toString()",
                                   completionHandler: { (html: Any?, error: Error?) in
            if let html = try? String(html as! String) {
//                print(html)
                print("screen \(String(describing: self.screen))")
                print("normalSeats \(String(describing: self.normalSeats))")
                print("vipSeats \(String(describing: self.vipSeats))")
                print("doubleSeats \(String(describing: self.doubleSeats))")
                print("totalSeats \(String(describing: self.totalSeats))")
                print("totalAmount \(String(describing: self.totalAmount))")
                print("discountAmount \(String(describing: self.discountAmount))")
                print("paymentAmount \(String(describing:self.paymentAmount ))")
                print("redeemVouchers \(String(describing: self.redeemVouchers))")
                print("redeemPoints \(String(describing: self.redeemPoints))")
                print("totalCombos \(String(describing: self.totalCombos))")
                print("totalComboAmount \(String(describing: self.totalComboAmount))")

                var orderId: String?
                if self.paymentMethod == "momo" {
                    orderId = self.momoOrderId
                } else if  self.paymentMethod == "airpay" {
                    orderId = self.airPayOrderId
                } else if  self.paymentMethod == "zalopay" {
                    orderId = self.zaloPayTransId
                }
                
                if type == "confirm" {
                    self.movieId =  self.listSeat?.FilmId
                    self.movieName = self.listSeat?.FilmName
                    self.date = self.show?.getStartDate()
                    self.time = self.show?.getStartDate()
                    self.screen = self.listSeat?.PhongChieu
                    self.normalSeats = (self.seat?.filter{$0.seatType?.isNormal == true} ?? []).count
                    self.vipSeats =  (self.seat?.filter{ $0.seatType?.isVip == true} ?? []).count
                    self.doubleSeats = (self.seat?.filter{ $0.seatType?.isCouple == true} ?? []).count
                    self.totalSeats = (self.normalSeats ?? 0) + (self.vipSeats ?? 0) + (self.doubleSeats ?? 0)
                    var totalAmount = self.getElementByClassName(content: html ,name: "total-money-name")
                    totalAmount = totalAmount?.replacingOccurrences(of: ",", with: "")
                    totalAmount = totalAmount?.replacingOccurrences(of: "đ", with: "")
                    self.totalAmount = Int(totalAmount ?? "")
                    var discountAmount = self.getElementByClassName(content: html ,name: "coupon-discount")
                    discountAmount = discountAmount?.replacingOccurrences(of: ",", with: "")
                    discountAmount = discountAmount?.replacingOccurrences(of: "đ", with: "")
                    self.discountAmount = Int(discountAmount ?? "")
                    var paymentAmount = self.getElementByClassName(content: html ,name: "money-need-pay")
                    paymentAmount = paymentAmount?.replacingOccurrences(of: ",", with: "")
                    paymentAmount = paymentAmount?.replacingOccurrences(of: "đ", with: "")
                    self.paymentAmount = Int(paymentAmount ?? "")
                    var redeemVouchers = self.getElementByClassName(content: html ,name: "beta-voucher-value")
                    redeemVouchers = redeemVouchers?.replacingOccurrences(of: ",", with: "")
                    redeemVouchers = redeemVouchers?.replacingOccurrences(of: "đ", with: "")
                    self.redeemVouchers = Int(redeemVouchers ?? "")
                    var redeemPoints = self.getElementByClassName(content: html ,name: "beta-point-value")
                    redeemPoints = redeemPoints?.replacingOccurrences(of: ",", with: "")
                    redeemPoints = redeemPoints?.replacingOccurrences(of: "đ", with: "")
                    self.redeemPoints = Int(redeemPoints ?? "")
                    let comboInfo = self.getComboByHtml(content: html)
                    self.totalCombos = comboInfo.first ?? nil
                    self.totalComboAmount = comboInfo.last ?? nil

                    Tracking.shared.confirmPayment(
                        cinemaId: self.cinemaId,
                        cinemaName: self.cinemaName,
                        movieId: self.movieId,
                        movieName: self.movieName,
                        date: self.date,
                        time: self.time,
                        screen: self.screen,
                        normalSeats: self.normalSeats,
                        vipSeats: self.vipSeats,
                        doubleSeats: self.doubleSeats,
                        totalSeats: self.totalSeats,
                        totalAmount:  self.totalAmount,
                        discountAmount: self.discountAmount,
                        paymentAmount: self.paymentAmount,
                        totalCombos: self.totalCombos,
                        totalComboAmount: self.totalComboAmount ,
                        redeemVouchers:  self.redeemVouchers,
                        redeemPoints: self.redeemVouchers,
                        paymentMethod:  self.paymentMethod,
                        orderId: orderId,
                        channel: "Mobile App")
                } else if type == "success" {
                    Tracking.shared.paySuccess(
                        cinemaId: self.cinemaId,
                        cinemaName: self.cinemaName,
                        movieId:self.movieId,
                        movieName:self.movieName,
                        date:self.date,
                        time: self.time,
                        screen: self.screen,
                        normalSeats: self.normalSeats,
                        vipSeats: self.vipSeats,
                        doubleSeats: self.doubleSeats,
                        totalSeats: self.totalSeats,
                        totalAmount:  self.totalAmount,
                        discountAmount: self.discountAmount,
                        paymentAmount: self.paymentAmount,
                        totalCombos: self.totalCombos,
                        totalComboAmount: self.totalComboAmount ,
                        redeemVouchers:  self.redeemVouchers,
                        redeemPoints: self.redeemVouchers,
                        paymentMethod:  self.paymentMethod,
                        orderId: orderId,
                        channel: "Mobile App")
                }else if type == "fail" {
                    Tracking.shared.payFail(
                        cinemaId: self.cinemaId,
                        cinemaName: self.cinemaName,
                        movieId:self.movieId,
                        movieName:self.movieName,
                        date:self.date,
                        time: self.time,
                        screen: self.screen,
                        normalSeats: self.normalSeats,
                        vipSeats: self.vipSeats,
                        doubleSeats: self.doubleSeats,
                        totalSeats: self.totalSeats,
                        totalAmount:  self.totalAmount,
                        discountAmount: self.discountAmount,
                        paymentAmount: self.paymentAmount,
                        totalCombos: self.totalCombos,
                        totalComboAmount: self.totalComboAmount ,
                        redeemVouchers:  self.redeemVouchers,
                        redeemPoints: self.redeemVouchers,
                        paymentMethod:  self.paymentMethod,
                        orderId: orderId,
                        channel: "Mobile App",
                        errorCode: errorCode,
                        errorMsg: errorMsg)
                }
                
            }
            
        })
    }
    
    func getElementByClassName(content: String, name: String)->String? {
        var result: String?
        if let range: Range<String.Index> = content.range(of: name) {
            var text =  String(content[range.upperBound...])
            if let range : Range<String.Index> = text.range(of: ">") {
                text =  String(text[range.upperBound...])
                if let range: Range<String.Index> = text.range(of: "<") {
                    result = String(text[..<range.lowerBound])
                    print(text)
                }
            }
        }
        return result
    }
    
    func getComboByHtml(content: String)->[Int?] {
        var comboList: ComboListModel?
        if let range: Range<String.Index> = self.webData.range(of: "var listCombo = JSON.parse(") {
            var text =  String(self.webData[range.upperBound...])
            if let range: Range<String.Index> = text.range(of: ");") {
                    text = String(text[..<range.lowerBound])
                    text = text.replacingOccurrences(of: "'", with: "")
                    if let data = try? JSONDecoder().decode(ComboListModel.self, from: text.data(using: .utf8)!){
                        comboList = data
                }
            }
        }
        var comboTotal: Int?
        var comboAmount: Int?
        if let list: [ComboListModelElement] = comboList {
            for cb in list {
                if let comboName = cb.combo?.name, let comboPrice = cb.combo?.priceAfterVAT {
                    if let range: Range<String.Index> = content.range(of: comboName) {
                        var text =  String(content[range.upperBound...])
                        if let range: Range<String.Index> = text.range(of: "combo-quantity") {
                            text =  String(text[range.lowerBound...])
                            if let range: Range<String.Index> = text.range(of: ">") {
                                text =  String(text[range.upperBound...])
                                    if let range: Range<String.Index> = text.range(of: "<") {
                                    text =  String(text[..<range.lowerBound])
                                    if let quantity = Int(text){
                                        comboTotal = (comboTotal ?? 0) + quantity
                                        comboAmount = (comboAmount ?? 0) + (quantity * comboPrice)
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        return [comboTotal,comboAmount]
    }
}

extension PaymentViewController: WKNavigationDelegate {
    func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
        print("\(#function)")
        setTitle(webView.title)
        webView.evaluateJavaScript("screenType;") { data, error in
            let type = data as? String
            if type == "payment" {
                print("update payment info")
                self.updateBookingInfo()
            }
        }
    }

    func webView(_ webView: WKWebView, decidePolicyFor navigationAction: WKNavigationAction, decisionHandler: @escaping (WKNavigationActionPolicy) -> Void) {
        if let url = navigationAction.request.url,
            url.absoluteString.contains("airpay.vn"),
            UIApplication.shared.canOpenURL(url) {
            self.paymentMethod = "airpay"
            tracking(type: "confirm", nil, nil)
            UIApplication.shared.open(url)
            let orderId = url.valueOf("order_id")
            self.airPayOrderId = orderId
            print(url)
            print("Redirected to browser. No need to open it locally")
            decisionHandler(.cancel)
        } else if let url = navigationAction.request.url,
            url.absoluteString.contains("payment.momo"),
            UIApplication.shared.canOpenURL(url) {
            self.paymentMethod = "momo"
            tracking(type: "confirm", nil, nil)
            UIApplication.shared.open(url)
            print(url)
            print("Redirected to browser. No need to open it locally")
            decisionHandler(.cancel)
        } else if let url = navigationAction.request.url,
            url.absoluteString.contains("gateway.zalopay.vn"),
            UIApplication.shared.canOpenURL(url) {
            self.paymentMethod = "zalopay"
            tracking(type: "confirm", nil, nil)
            UIApplication.shared.open(url)
            print(url)
            print("Redirected to browser. No need to open it locally")
            decisionHandler(.cancel)
        } else if let url = navigationAction.request.url,
            url.absoluteString.contains("mtf.onepay.vn/onecomm-pay"){
            self.paymentMethod = "noidia"
            tracking(type: "confirm", nil, nil)
            print(url)
            decisionHandler(.allow)
        }  else if let url = navigationAction.request.url,
           url.absoluteString.contains("mtf.onepay.vn/promotion/vpcpr.op"){
            self.paymentMethod = "quocte"
           tracking( type: "confirm", nil, nil)
           print(url)
           decisionHandler(.allow)
       } else {
            print("Open it locally")
            decisionHandler(.allow)
        }
    }
    


//    func webView(_ webView: WKWebView, didStartProvisionalNavigation navigation: WKNavigation!) {
//        print("Start \(webView.url?.absoluteString ?? "---")")
//        if webView.url?. == true, let orderId = webView.url?.valueOf("order_id") {
//
//            UIApplication.shared.open(webView.url!)
//            DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
//                if webView.url?.absoluteString.contains(Config.BaseURL) == false {
//                    self.webView.loadHTMLString(self.webData, baseURL: URL(string: Config.BaseURL))
//                }
//            }
//            self.orderId = orderId
//        }
//    }
}

extension PaymentViewController: WKUIDelegate {
    func webView(_ webView: WKWebView, runJavaScriptAlertPanelWithMessage message: String, initiatedByFrame frame: WKFrameInfo, completionHandler: @escaping () -> Void) {
        showAlert(message: message) { _ in
            completionHandler()
        }
    }
}

extension PaymentViewController: WKScriptMessageHandler {
    func userContentController(_ userContentController: WKUserContentController, didReceive message: WKScriptMessage) {
        print("\(#function) message: \(message.name) body: \(message.body)")

        let msg =  message.body as? String
        if message.name == "scriptHandler" {
            if msg == "payment_susccess" {
                tracking(type: "success", nil, nil)
                showAlert(message: "Alert.PaymentSuccess".localized) { _ in
                    self.getTransactionDetail()
                }
            } else if msg == "policy" {
                let vc = UIStoryboard.setting[.other] as! OtherViewController
                vc.type = .Term
                show(vc)
            } else if msg == "payment_failed" {
                tracking(type: "fail", msg, "Alert.PaymentFailed".localized)
                showAlert(message: "Alert.PaymentFailed".localized) { _ in
                    AppDelegate.shared.gotoHome()
                }
            } else if msg == "booking_seat_failed" {
                showAlert(message: "Alert.BookingSeatFailed".localized) { _ in
                    AppDelegate.shared.gotoHome()
                }
            } else if msg == "awaiting_payment" {
                if AppDelegate.shared.topVC?.isKind(of: PaymentViewController.self) == false {
                    return
                }
                showAlert(message: "Alert.BookingWaiting".localized) { _ in
                    let vc = UIStoryboard.member[.transactionHistory]
                    self.show(vc, sender: nil)
                    self.navigationController?.viewControllers.removeAll(where: { (vc) -> Bool in
                        return !vc.isKind(of: TranferHistoryViewController.self) && !vc.isKind(of: NewHomeViewController.self)
                    })
                }
            }
        }
    }
}

extension PaymentViewController: UIScrollViewDelegate {
    func scrollViewWillBeginZooming(_ scrollView: UIScrollView, with view: UIView?) {

    }
}

