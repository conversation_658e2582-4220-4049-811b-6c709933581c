<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="19529" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="19519"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="Oswald-Bold.ttf">
            <string>Oswald-Bold</string>
        </array>
        <array key="Oswald-Regular.ttf">
            <string>Oswald-Regular</string>
        </array>
        <array key="SourceSansPro-Bold.ttf">
            <string>SourceSansPro-Bold</string>
        </array>
        <array key="SourceSansPro-Regular.ttf">
            <string>SourceSansPro-Regular</string>
        </array>
    </customFonts>
    <scenes>
        <!--List Film View Controller-->
        <scene sceneID="O9D-s9-TxA">
            <objects>
                <viewController storyboardIdentifier="ListFilmViewController" id="JPR-Dc-uZe" customClass="ListFilmViewController" customModule="Beta_Cinemas" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="t3D-Ub-tpR">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="none" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="2dt-ve-tSM">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                                <color key="backgroundColor" red="0.95294117649999999" green="0.95294117649999999" blue="0.95294117649999999" alpha="1" colorSpace="calibratedRGB"/>
                                <connections>
                                    <outlet property="delegate" destination="JPR-Dc-uZe" id="iq1-7J-9es"/>
                                </connections>
                            </tableView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="F0o-Ti-ELs"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="F0o-Ti-ELs" firstAttribute="trailing" secondItem="2dt-ve-tSM" secondAttribute="trailing" id="AbE-mD-mLA"/>
                            <constraint firstItem="2dt-ve-tSM" firstAttribute="top" secondItem="F0o-Ti-ELs" secondAttribute="top" id="S7a-2v-JO5"/>
                            <constraint firstItem="F0o-Ti-ELs" firstAttribute="bottom" secondItem="2dt-ve-tSM" secondAttribute="bottom" id="eD0-Ye-jen"/>
                            <constraint firstItem="2dt-ve-tSM" firstAttribute="leading" secondItem="F0o-Ti-ELs" secondAttribute="leading" id="kWV-dK-cAM"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="tableView" destination="2dt-ve-tSM" id="blX-L4-INp"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="TJQ-aA-rHn" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-330" y="-251"/>
        </scene>
        <!--Page View Controller-->
        <scene sceneID="Boh-xp-qv3">
            <objects>
                <pageViewController autoresizesArchivedViewToFullSize="NO" transitionStyle="scroll" navigationOrientation="horizontal" spineLocation="none" id="U0F-Wm-9js" sceneMemberID="viewController"/>
                <placeholder placeholderIdentifier="IBFirstResponder" id="G6C-s0-acf" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-1659" y="-253"/>
        </scene>
        <!--FilmBookingViewController-->
        <scene sceneID="r3W-NW-QCN">
            <objects>
                <viewController storyboardIdentifier="FilmBookingViewController" id="FoS-jK-54N" userLabel="FilmBookingViewController" customClass="FilmBookingViewController" customModule="Beta_Cinemas" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="fCr-W5-v8U">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="93J-yV-KrT" customClass="ScrollPager" customModule="Beta_Cinemas" customModuleProvider="target">
                                <rect key="frame" x="0.0" y="10" width="375" height="50"/>
                                <color key="backgroundColor" red="0.95294117649999999" green="0.95294117649999999" blue="0.95294117649999999" alpha="1" colorSpace="calibratedRGB"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="50" id="AQK-rp-TuW"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="color" keyPath="textColor">
                                        <color key="value" red="0.72549019609999998" green="0.72549019609999998" blue="0.72549019609999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="color" keyPath="selectedTextColor">
                                        <color key="value" red="0.1176470588" green="0.1215686275" blue="0.15686274510000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="color" keyPath="indicatorColor">
                                        <color key="value" red="0.1176470588" green="0.1215686275" blue="0.15686274510000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="boolean" keyPath="indicatorIsAtBottom" value="YES"/>
                                    <userDefinedRuntimeAttribute type="boolean" keyPath="indicatorSizeMatchesTitle" value="NO"/>
                                    <userDefinedRuntimeAttribute type="number" keyPath="indicatorWidth">
                                        <real key="value" value="21"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="number" keyPath="indicatorHeight">
                                        <real key="value" value="2"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                                <connections>
                                    <outlet property="delegate" destination="FoS-jK-54N" id="Ro8-SR-RAu"/>
                                </connections>
                            </view>
                            <containerView opaque="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="2v8-tq-cNv">
                                <rect key="frame" x="0.0" y="60" width="375" height="607"/>
                                <connections>
                                    <segue destination="U0F-Wm-9js" kind="embed" id="rB4-eW-VYe"/>
                                </connections>
                            </containerView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="Yks-oi-C0R"/>
                        <color key="backgroundColor" red="0.95294117649999999" green="0.95294117649999999" blue="0.95294117649999999" alpha="1" colorSpace="calibratedRGB"/>
                        <constraints>
                            <constraint firstItem="Yks-oi-C0R" firstAttribute="bottom" secondItem="2v8-tq-cNv" secondAttribute="bottom" id="4oY-4h-nhD"/>
                            <constraint firstItem="2v8-tq-cNv" firstAttribute="top" secondItem="93J-yV-KrT" secondAttribute="bottom" id="Jqx-oz-Rcs"/>
                            <constraint firstItem="2v8-tq-cNv" firstAttribute="leading" secondItem="Yks-oi-C0R" secondAttribute="leading" id="O3R-jq-Jnh"/>
                            <constraint firstItem="93J-yV-KrT" firstAttribute="leading" secondItem="Yks-oi-C0R" secondAttribute="leading" id="OAE-5f-PEY"/>
                            <constraint firstItem="Yks-oi-C0R" firstAttribute="trailing" secondItem="2v8-tq-cNv" secondAttribute="trailing" id="UaV-e5-wMT"/>
                            <constraint firstItem="Yks-oi-C0R" firstAttribute="trailing" secondItem="93J-yV-KrT" secondAttribute="trailing" id="amS-Q3-j0H"/>
                            <constraint firstItem="93J-yV-KrT" firstAttribute="top" secondItem="Yks-oi-C0R" secondAttribute="top" constant="10" id="x9X-pN-cn5"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="tabPager" destination="93J-yV-KrT" id="i0M-sb-zPH"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="1QA-zI-nbB" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-998" y="-251"/>
        </scene>
        <!--Graph Shiting View Controller-->
        <scene sceneID="eex-RS-FQu">
            <objects>
                <viewController storyboardIdentifier="GraphShitingViewController" id="pnt-Jl-0dC" customClass="GraphShitingViewController" customModule="Beta_Cinemas" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="9fI-8W-yX7">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <viewLayoutGuide key="safeArea" id="SqR-rB-WcX"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="4Ds-md-tAR" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="338" y="-251"/>
        </scene>
        <!--Ticket Booking View Controller-->
        <scene sceneID="VAE-EO-kRY">
            <objects>
                <viewController storyboardIdentifier="TicketBookingViewController" id="Thl-Od-gJG" customClass="TicketBookingViewController" customModule="Beta_Cinemas" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="MCK-YO-Mdj">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <viewLayoutGuide key="safeArea" id="lem-II-hP4"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="bee-7F-AaI" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="969" y="-251"/>
        </scene>
        <!--Film Detail View Controller-->
        <scene sceneID="wzm-TH-Cb5">
            <objects>
                <viewController storyboardIdentifier="FilmDetailViewController" extendedLayoutIncludesOpaqueBars="YES" id="cE4-lS-Hx5" customClass="FilmDetailViewController" customModule="Beta_Cinemas" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="8xx-LR-dhF">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="none" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="1" sectionFooterHeight="1" translatesAutoresizingMaskIntoConstraints="NO" id="0XR-Zz-Zct">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="603"/>
                                <color key="backgroundColor" red="0.95294117649999999" green="0.95294117649999999" blue="0.95294117649999999" alpha="1" colorSpace="calibratedRGB"/>
                                <view key="tableHeaderView" contentMode="scaleToFill" id="g1w-1i-rMh">
                                    <rect key="frame" x="0.0" y="0.0" width="375" height="437"/>
                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                    <subviews>
                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="bg4.png" translatesAutoresizingMaskIntoConstraints="NO" id="TTX-fL-CDp">
                                            <rect key="frame" x="0.0" y="0.0" width="375" height="292"/>
                                            <constraints>
                                                <constraint firstAttribute="width" secondItem="TTX-fL-CDp" secondAttribute="height" multiplier="375:292" id="gJa-uN-wuJ"/>
                                            </constraints>
                                        </imageView>
                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="3v2-6P-zyN">
                                            <rect key="frame" x="157.5" y="116" width="60" height="60"/>
                                            <state key="normal" image="ic_play"/>
                                            <connections>
                                                <action selector="playTrailerButtonPressed:" destination="cE4-lS-Hx5" eventType="touchUpInside" id="IVH-Ph-WKC"/>
                                            </connections>
                                        </button>
                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="kTL-wT-xpx" customClass="RoundView" customModule="Beta_Cinemas" customModuleProvider="target">
                                            <rect key="frame" x="8" y="242" width="114" height="150"/>
                                            <subviews>
                                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="bg3.png" translatesAutoresizingMaskIntoConstraints="NO" id="bnt-zp-rT7" customClass="RoundImageView" customModule="Beta_Cinemas" customModuleProvider="target">
                                                    <rect key="frame" x="0.0" y="0.0" width="114" height="150"/>
                                                    <constraints>
                                                        <constraint firstAttribute="width" constant="114" id="AHD-1V-YKa"/>
                                                        <constraint firstAttribute="height" constant="150" id="Xed-lS-a7i"/>
                                                        <constraint firstAttribute="width" secondItem="bnt-zp-rT7" secondAttribute="height" multiplier="19:25" id="tgg-Ie-Icc"/>
                                                    </constraints>
                                                    <userDefinedRuntimeAttributes>
                                                        <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                            <real key="value" value="4"/>
                                                        </userDefinedRuntimeAttribute>
                                                    </userDefinedRuntimeAttributes>
                                                </imageView>
                                            </subviews>
                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <constraints>
                                                <constraint firstItem="bnt-zp-rT7" firstAttribute="leading" secondItem="kTL-wT-xpx" secondAttribute="leading" id="8AV-Li-kFf"/>
                                                <constraint firstAttribute="height" constant="150" id="KzO-R9-eAr"/>
                                                <constraint firstAttribute="width" constant="114" id="UO1-qM-t2a"/>
                                                <constraint firstAttribute="bottom" secondItem="bnt-zp-rT7" secondAttribute="bottom" id="i1W-Ah-H8w"/>
                                                <constraint firstAttribute="trailing" secondItem="bnt-zp-rT7" secondAttribute="trailing" id="sJE-Wv-vQV"/>
                                                <constraint firstItem="bnt-zp-rT7" firstAttribute="top" secondItem="kTL-wT-xpx" secondAttribute="top" id="utN-Lb-CtR"/>
                                            </constraints>
                                            <userDefinedRuntimeAttributes>
                                                <userDefinedRuntimeAttribute type="number" keyPath="shadowOpacity">
                                                    <real key="value" value="0.25"/>
                                                </userDefinedRuntimeAttribute>
                                                <userDefinedRuntimeAttribute type="color" keyPath="shadowColor">
                                                    <color key="value" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                </userDefinedRuntimeAttribute>
                                                <userDefinedRuntimeAttribute type="point" keyPath="shadowOffset">
                                                    <point key="value" x="0.0" y="4"/>
                                                </userDefinedRuntimeAttribute>
                                                <userDefinedRuntimeAttribute type="number" keyPath="shadowRadius">
                                                    <real key="value" value="6"/>
                                                </userDefinedRuntimeAttribute>
                                                <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                    <real key="value" value="4"/>
                                                </userDefinedRuntimeAttribute>
                                            </userDefinedRuntimeAttributes>
                                        </view>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" ambiguous="YES" text="Pacific Rim: Troi day cua quai vat X" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="aRz-eO-b8V">
                                            <rect key="frame" x="138" y="308" width="221" height="59.5"/>
                                            <fontDescription key="fontDescription" name="Oswald-Bold" family="Oswald" pointSize="20"/>
                                            <color key="textColor" red="0.28627450980000002" green="0.**********0000003" blue="0.38431372549999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                            <nil key="highlightedColor"/>
                                        </label>
                                        <view hidden="YES" contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="ha1-nF-siA" customClass="RoundView" customModule="Beta_Cinemas" customModuleProvider="target">
                                            <rect key="frame" x="138" y="379.5" width="190.5" height="22"/>
                                            <subviews>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" ambiguous="YES" text="Chir danh cho nguoi tren 13" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="9Uj-7I-xRE">
                                                    <rect key="frame" x="8" y="2" width="174.5" height="18"/>
                                                    <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="14"/>
                                                    <color key="textColor" red="0.28627450980000002" green="0.**********0000003" blue="0.38431372549999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                            </subviews>
                                            <color key="backgroundColor" red="0.95294117649999999" green="0.95294117649999999" blue="0.95294117649999999" alpha="1" colorSpace="calibratedRGB"/>
                                            <constraints>
                                                <constraint firstItem="9Uj-7I-xRE" firstAttribute="leading" secondItem="ha1-nF-siA" secondAttribute="leading" constant="8" id="3OD-1h-f1O"/>
                                                <constraint firstAttribute="trailing" secondItem="9Uj-7I-xRE" secondAttribute="trailing" constant="8" id="5uV-5D-kEi"/>
                                                <constraint firstAttribute="bottom" secondItem="9Uj-7I-xRE" secondAttribute="bottom" constant="2" id="Egn-e0-z8b"/>
                                                <constraint firstItem="9Uj-7I-xRE" firstAttribute="top" secondItem="ha1-nF-siA" secondAttribute="top" constant="2" id="Fc9-gb-yhD"/>
                                            </constraints>
                                            <userDefinedRuntimeAttributes>
                                                <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                                    <real key="value" value="1"/>
                                                </userDefinedRuntimeAttribute>
                                                <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                                    <color key="value" red="0.72549019609999998" green="0.72549019609999998" blue="0.72549019609999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                </userDefinedRuntimeAttribute>
                                                <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                    <real key="value" value="12"/>
                                                </userDefinedRuntimeAttribute>
                                            </userDefinedRuntimeAttributes>
                                        </view>
                                    </subviews>
                                    <color key="backgroundColor" red="0.95294117649999999" green="0.95294117649999999" blue="0.95294117649999999" alpha="1" colorSpace="calibratedRGB"/>
                                    <constraints>
                                        <constraint firstItem="kTL-wT-xpx" firstAttribute="top" secondItem="TTX-fL-CDp" secondAttribute="bottom" constant="-50" id="3sq-6t-ncw"/>
                                        <constraint firstItem="3v2-6P-zyN" firstAttribute="centerX" secondItem="TTX-fL-CDp" secondAttribute="centerX" id="IyU-h4-nTA"/>
                                        <constraint firstItem="TTX-fL-CDp" firstAttribute="top" secondItem="g1w-1i-rMh" secondAttribute="top" id="Kve-02-GnY"/>
                                        <constraint firstItem="ha1-nF-siA" firstAttribute="leading" secondItem="aRz-eO-b8V" secondAttribute="leading" id="Msq-ao-86h"/>
                                        <constraint firstItem="TTX-fL-CDp" firstAttribute="leading" secondItem="g1w-1i-rMh" secondAttribute="leading" id="ST6-TO-dyR"/>
                                        <constraint firstItem="ha1-nF-siA" firstAttribute="top" secondItem="aRz-eO-b8V" secondAttribute="bottom" constant="12" id="WGO-sT-muf"/>
                                        <constraint firstItem="3v2-6P-zyN" firstAttribute="centerY" secondItem="TTX-fL-CDp" secondAttribute="centerY" id="hDn-ih-f5m"/>
                                        <constraint firstAttribute="bottom" secondItem="ha1-nF-siA" secondAttribute="bottom" constant="35.5" id="hIa-ap-B76"/>
                                        <constraint firstItem="aRz-eO-b8V" firstAttribute="top" secondItem="TTX-fL-CDp" secondAttribute="bottom" constant="16" id="mYw-Ku-2sI"/>
                                        <constraint firstItem="aRz-eO-b8V" firstAttribute="leading" secondItem="kTL-wT-xpx" secondAttribute="trailing" constant="16" id="qLJ-rH-uAE"/>
                                        <constraint firstAttribute="trailing" secondItem="aRz-eO-b8V" secondAttribute="trailing" constant="16" id="r3F-VA-8Xv"/>
                                        <constraint firstItem="kTL-wT-xpx" firstAttribute="leading" secondItem="g1w-1i-rMh" secondAttribute="leading" constant="8" id="v89-zJ-wa6"/>
                                        <constraint firstAttribute="trailing" secondItem="TTX-fL-CDp" secondAttribute="trailing" id="y7f-E5-hu5"/>
                                    </constraints>
                                </view>
                                <prototypes>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="default" indentationWidth="10" reuseIdentifier="FilmDetailCell" rowHeight="92" id="ded-yO-hNk" customClass="FilmDetailCell" customModule="Beta_Cinemas" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="481.5" width="375" height="92"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="ded-yO-hNk" id="mH8-8C-B7X">
                                            <rect key="frame" x="0.0" y="0.0" width="375" height="92"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="02k-oH-muD">
                                                    <rect key="frame" x="0.0" y="0.0" width="375" height="92"/>
                                                    <subviews>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="DAO DIEN" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Avu-PK-Ylf">
                                                            <rect key="frame" x="16" y="4" width="143" height="19"/>
                                                            <fontDescription key="fontDescription" name="SourceSansPro-Bold" family="Source Sans Pro" pointSize="16"/>
                                                            <color key="textColor" red="0.28627450980000002" green="0.**********0000003" blue="0.38431372549999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                            <nil key="highlightedColor"/>
                                                        </label>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Steven DKing" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="RDl-hV-hcT">
                                                            <rect key="frame" x="159" y="4" width="200" height="84"/>
                                                            <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                                            <color key="textColor" red="0.11764705882352941" green="0.12156862745098039" blue="0.15686274509803921" alpha="1" colorSpace="calibratedRGB"/>
                                                            <nil key="highlightedColor"/>
                                                        </label>
                                                    </subviews>
                                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    <constraints>
                                                        <constraint firstItem="RDl-hV-hcT" firstAttribute="leading" secondItem="Avu-PK-Ylf" secondAttribute="trailing" id="1Ln-Kb-kkK"/>
                                                        <constraint firstItem="RDl-hV-hcT" firstAttribute="width" secondItem="Avu-PK-Ylf" secondAttribute="width" multiplier="1.4" id="3Bn-Jc-8ta"/>
                                                        <constraint firstAttribute="bottom" secondItem="RDl-hV-hcT" secondAttribute="bottom" constant="4" id="NYe-iT-JAb"/>
                                                        <constraint firstItem="Avu-PK-Ylf" firstAttribute="top" secondItem="02k-oH-muD" secondAttribute="top" constant="4" id="ZSb-ad-Uhh"/>
                                                        <constraint firstAttribute="bottom" relation="greaterThanOrEqual" secondItem="Avu-PK-Ylf" secondAttribute="bottom" constant="4" id="aWK-Qc-b1E"/>
                                                        <constraint firstItem="RDl-hV-hcT" firstAttribute="top" secondItem="02k-oH-muD" secondAttribute="top" constant="4" id="krx-qb-wiO"/>
                                                        <constraint firstItem="Avu-PK-Ylf" firstAttribute="leading" secondItem="02k-oH-muD" secondAttribute="leading" constant="16" id="qXH-dX-cl6"/>
                                                        <constraint firstAttribute="trailing" secondItem="RDl-hV-hcT" secondAttribute="trailing" constant="16" id="vPw-uD-Ldo"/>
                                                    </constraints>
                                                </view>
                                            </subviews>
                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <constraints>
                                                <constraint firstItem="02k-oH-muD" firstAttribute="top" secondItem="mH8-8C-B7X" secondAttribute="top" id="MT2-H2-YW9"/>
                                                <constraint firstItem="02k-oH-muD" firstAttribute="leading" secondItem="mH8-8C-B7X" secondAttribute="leading" id="MyG-3Z-1Gj"/>
                                                <constraint firstAttribute="trailing" secondItem="02k-oH-muD" secondAttribute="trailing" id="b7Y-b0-ZL0"/>
                                                <constraint firstAttribute="bottom" secondItem="02k-oH-muD" secondAttribute="bottom" id="xjS-r7-ecP"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <connections>
                                            <outlet property="lbContent" destination="RDl-hV-hcT" id="lNS-Bb-H6a"/>
                                            <outlet property="lbTitle" destination="Avu-PK-Ylf" id="bsV-Qz-60z"/>
                                        </connections>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="default" indentationWidth="10" reuseIdentifier="FilmDescriptionTableCell" rowHeight="128" id="hKJ-3r-2hG" customClass="FilmDescriptionTableCell" customModule="Beta_Cinemas" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="573.5" width="375" height="128"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="hKJ-3r-2hG" id="tPR-t5-P1B">
                                            <rect key="frame" x="0.0" y="0.0" width="375" height="128"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="r4G-4M-q7S">
                                                    <rect key="frame" x="0.0" y="0.0" width="375" height="128"/>
                                                    <subviews>
                                                        <view alpha="0.10000000000000001" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ewj-SA-o77">
                                                            <rect key="frame" x="20" y="14" width="335" height="1"/>
                                                            <color key="backgroundColor" red="0.28627450980000002" green="0.**********0000003" blue="0.38431372549999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                            <constraints>
                                                                <constraint firstAttribute="height" constant="1" id="zbl-fL-Inx"/>
                                                            </constraints>
                                                        </view>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="justified" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="3gM-Lt-EGc">
                                                            <rect key="frame" x="20" y="29" width="335" height="85"/>
                                                            <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                                            <color key="textColor" red="0.1176470588" green="0.1215686275" blue="0.15686274510000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                            <nil key="highlightedColor"/>
                                                        </label>
                                                    </subviews>
                                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    <constraints>
                                                        <constraint firstItem="ewj-SA-o77" firstAttribute="top" secondItem="r4G-4M-q7S" secondAttribute="top" constant="14" id="22p-YD-jjB"/>
                                                        <constraint firstAttribute="bottom" secondItem="3gM-Lt-EGc" secondAttribute="bottom" constant="14" id="AiF-UN-Tac"/>
                                                        <constraint firstAttribute="trailing" secondItem="3gM-Lt-EGc" secondAttribute="trailing" constant="20" id="JIa-lQ-uxp"/>
                                                        <constraint firstItem="ewj-SA-o77" firstAttribute="leading" secondItem="r4G-4M-q7S" secondAttribute="leading" constant="20" id="JfR-Hw-eQu"/>
                                                        <constraint firstAttribute="trailing" secondItem="ewj-SA-o77" secondAttribute="trailing" constant="20" id="OWv-oI-6Sf"/>
                                                        <constraint firstItem="3gM-Lt-EGc" firstAttribute="top" secondItem="ewj-SA-o77" secondAttribute="bottom" constant="14" id="TAV-jA-qtr"/>
                                                        <constraint firstItem="3gM-Lt-EGc" firstAttribute="leading" secondItem="r4G-4M-q7S" secondAttribute="leading" constant="20" id="hjf-uY-SB1"/>
                                                    </constraints>
                                                </view>
                                            </subviews>
                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <constraints>
                                                <constraint firstAttribute="trailing" secondItem="r4G-4M-q7S" secondAttribute="trailing" id="MGI-L1-5Fi"/>
                                                <constraint firstAttribute="bottom" secondItem="r4G-4M-q7S" secondAttribute="bottom" id="f1t-En-u46"/>
                                                <constraint firstItem="r4G-4M-q7S" firstAttribute="leading" secondItem="tPR-t5-P1B" secondAttribute="leading" id="idS-xb-pfN"/>
                                                <constraint firstItem="r4G-4M-q7S" firstAttribute="top" secondItem="tPR-t5-P1B" secondAttribute="top" id="tLk-IU-0tR"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <connections>
                                            <outlet property="lbTitle" destination="3gM-Lt-EGc" id="nw7-gX-pKN"/>
                                        </connections>
                                    </tableViewCell>
                                </prototypes>
                                <connections>
                                    <outlet property="dataSource" destination="cE4-lS-Hx5" id="Ql3-gO-ZaE"/>
                                    <outlet property="delegate" destination="cE4-lS-Hx5" id="J3G-Cs-3Sd"/>
                                </connections>
                            </tableView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="8Hd-Oe-yp3">
                                <rect key="frame" x="0.0" y="603" width="375" height="64"/>
                                <subviews>
                                    <stackView opaque="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="gvN-kA-Z7x">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="64"/>
                                        <subviews>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="IF5-gv-Kgo" customClass="LocalizableButton" customModule="Beta_Cinemas" customModuleProvider="target">
                                                <rect key="frame" x="0.0" y="0.0" width="375" height="64"/>
                                                <color key="backgroundColor" red="0.01176470588" green="0.34901960780000002" blue="0.61568627450000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="24"/>
                                                <state key="normal" title="CHIA SẺ"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="FilmDetail.Share"/>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <action selector="shareButtonPressed:" destination="cE4-lS-Hx5" eventType="touchUpInside" id="sdk-Vr-2Fb"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                        <constraints>
                                            <constraint firstAttribute="trailing" secondItem="IF5-gv-Kgo" secondAttribute="trailing" id="sXH-eS-hcj"/>
                                        </constraints>
                                    </stackView>
                                </subviews>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="gvN-kA-Z7x" firstAttribute="top" secondItem="8Hd-Oe-yp3" secondAttribute="top" id="RI3-AG-vKA"/>
                                    <constraint firstItem="gvN-kA-Z7x" firstAttribute="leading" secondItem="8Hd-Oe-yp3" secondAttribute="leading" id="bAY-UE-1WV"/>
                                    <constraint firstAttribute="height" constant="64" id="dhL-MO-fwE"/>
                                    <constraint firstAttribute="bottom" secondItem="gvN-kA-Z7x" secondAttribute="bottom" id="iRf-Pn-BWS"/>
                                    <constraint firstAttribute="trailing" secondItem="gvN-kA-Z7x" secondAttribute="trailing" id="yRT-CJ-lHx"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="61p-76-u5F"/>
                        <color key="backgroundColor" red="0.95294117649999999" green="0.95294117649999999" blue="0.95294117649999999" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <constraint firstItem="61p-76-u5F" firstAttribute="trailing" secondItem="0XR-Zz-Zct" secondAttribute="trailing" id="1SJ-Lg-2mG"/>
                            <constraint firstItem="8Hd-Oe-yp3" firstAttribute="top" secondItem="0XR-Zz-Zct" secondAttribute="bottom" id="5wg-MP-CBJ"/>
                            <constraint firstItem="0XR-Zz-Zct" firstAttribute="leading" secondItem="61p-76-u5F" secondAttribute="leading" id="Id1-Rh-eFU"/>
                            <constraint firstItem="8Hd-Oe-yp3" firstAttribute="leading" secondItem="61p-76-u5F" secondAttribute="leading" id="PYV-QQ-2ll"/>
                            <constraint firstItem="0XR-Zz-Zct" firstAttribute="top" secondItem="61p-76-u5F" secondAttribute="top" id="am5-cV-giq"/>
                            <constraint firstItem="8Hd-Oe-yp3" firstAttribute="trailing" secondItem="61p-76-u5F" secondAttribute="trailing" id="ePa-Au-9FJ"/>
                            <constraint firstItem="61p-76-u5F" firstAttribute="bottom" secondItem="8Hd-Oe-yp3" secondAttribute="bottom" id="rvW-6D-QS7"/>
                        </constraints>
                    </view>
                    <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
                    <size key="freeformSize" width="375" height="667"/>
                    <connections>
                        <outlet property="headerView" destination="g1w-1i-rMh" id="S5Y-RS-68r"/>
                        <outlet property="ivFilmBanner" destination="TTX-fL-CDp" id="Iz8-Sa-3bB"/>
                        <outlet property="ivFilmLogo" destination="bnt-zp-rT7" id="psU-GT-0HB"/>
                        <outlet property="lbName" destination="aRz-eO-b8V" id="lCG-gj-we0"/>
                        <outlet property="lbWarning" destination="9Uj-7I-xRE" id="HXA-gI-GFz"/>
                        <outlet property="shareButton" destination="IF5-gv-Kgo" id="Apb-26-g9o"/>
                        <outlet property="tableView" destination="0XR-Zz-Zct" id="6eg-pW-cW6"/>
                        <outlet property="vAgeRestrict" destination="ha1-nF-siA" id="mto-9C-6kV"/>
                        <outlet property="vBottom" destination="8Hd-Oe-yp3" id="IvO-wo-kWk"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Kq9-oN-pZ1" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1664.8" y="-252.32383808095955"/>
        </scene>
        <!--Film Choose Time View Controller-->
        <scene sceneID="gDY-6n-ZoY">
            <objects>
                <viewController storyboardIdentifier="FilmChooseTimeViewController" id="twf-hl-jWp" customClass="FilmChooseTimeViewController" customModule="Beta_Cinemas" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="442-5O-Rw4">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="none" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="QUd-gD-GYO">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                                <color key="backgroundColor" red="0.95294117649999999" green="0.95294117649999999" blue="0.95294117649999999" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <view key="tableHeaderView" contentMode="scaleToFill" id="Qb0-5J-udq">
                                    <rect key="frame" x="0.0" y="0.0" width="375" height="386"/>
                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                    <subviews>
                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="ngE-yT-VUU">
                                            <rect key="frame" x="0.0" y="0.0" width="375" height="150"/>
                                            <constraints>
                                                <constraint firstAttribute="height" constant="150" id="HYK-SC-VUA"/>
                                            </constraints>
                                        </imageView>
                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="rwR-rC-pe1" customClass="GradientView" customModule="Beta_Cinemas" customModuleProvider="target">
                                            <rect key="frame" x="0.0" y="0.0" width="375" height="150"/>
                                            <subviews>
                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Laz-Vj-z9d">
                                                    <rect key="frame" x="20" y="35.5" width="335" height="79"/>
                                                    <subviews>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Pacific Rim: Trỗi Dậy" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="BVc-CN-8ga">
                                                            <rect key="frame" x="0.0" y="0.0" width="335" height="28"/>
                                                            <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="24"/>
                                                            <color key="textColor" red="0.1176470588" green="0.1215686275" blue="0.15686274510000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                            <nil key="highlightedColor"/>
                                                        </label>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="2D - LT  |  Võ thuật, Viễn Tưởng  |  135 phút " textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="xSq-zL-xNb">
                                                            <rect key="frame" x="0.0" y="28" width="335" height="19"/>
                                                            <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                                            <color key="textColor" red="0.1176470588" green="0.1215686275" blue="0.15686274510000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                            <nil key="highlightedColor"/>
                                                        </label>
                                                        <button opaque="NO" contentMode="scaleToFill" misplaced="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="bI1-ZA-qU7" customClass="RoundButton" customModule="Beta_Cinemas" customModuleProvider="target">
                                                            <rect key="frame" x="144" y="58" width="46" height="30"/>
                                                            <constraints>
                                                                <constraint firstAttribute="width" constant="104" id="Otp-ii-Z7A"/>
                                                                <constraint firstAttribute="height" constant="24" id="ycU-f4-PCy"/>
                                                            </constraints>
                                                            <fontDescription key="fontDescription" name="SourceSansPro-Bold" family="Source Sans Pro" pointSize="12"/>
                                                            <state key="normal">
                                                                <color key="titleColor" red="0.011764705882352941" green="0.34901960784313724" blue="0.61568627450980395" alpha="1" colorSpace="calibratedRGB"/>
                                                            </state>
                                                            <userDefinedRuntimeAttributes>
                                                                <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="film_detail"/>
                                                                <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                                    <real key="value" value="12"/>
                                                                </userDefinedRuntimeAttribute>
                                                                <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                                                    <real key="value" value="1"/>
                                                                </userDefinedRuntimeAttribute>
                                                                <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                                                    <color key="value" red="0.01176470588" green="0.34901960780000002" blue="0.61568627450000002" alpha="1" colorSpace="calibratedRGB"/>
                                                                </userDefinedRuntimeAttribute>
                                                            </userDefinedRuntimeAttributes>
                                                            <connections>
                                                                <action selector="detailTapped:" destination="twf-hl-jWp" eventType="touchUpInside" id="2kk-ht-cr6"/>
                                                            </connections>
                                                        </button>
                                                    </subviews>
                                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    <constraints>
                                                        <constraint firstAttribute="bottom" secondItem="bI1-ZA-qU7" secondAttribute="bottom" id="5e6-MO-tdf"/>
                                                        <constraint firstItem="bI1-ZA-qU7" firstAttribute="top" secondItem="xSq-zL-xNb" secondAttribute="bottom" constant="8" id="Kq0-6H-I56"/>
                                                        <constraint firstAttribute="trailing" secondItem="xSq-zL-xNb" secondAttribute="trailing" id="Q1C-kd-C8J"/>
                                                        <constraint firstItem="BVc-CN-8ga" firstAttribute="leading" secondItem="Laz-Vj-z9d" secondAttribute="leading" id="R38-Jc-WaG"/>
                                                        <constraint firstItem="xSq-zL-xNb" firstAttribute="top" secondItem="BVc-CN-8ga" secondAttribute="bottom" id="eb5-D3-K2p"/>
                                                        <constraint firstAttribute="trailing" secondItem="BVc-CN-8ga" secondAttribute="trailing" id="gis-MU-m9c"/>
                                                        <constraint firstItem="bI1-ZA-qU7" firstAttribute="centerX" secondItem="Laz-Vj-z9d" secondAttribute="centerX" id="gxk-N8-FUY"/>
                                                        <constraint firstItem="BVc-CN-8ga" firstAttribute="top" secondItem="Laz-Vj-z9d" secondAttribute="top" id="gyl-ns-c0J"/>
                                                        <constraint firstItem="xSq-zL-xNb" firstAttribute="leading" secondItem="Laz-Vj-z9d" secondAttribute="leading" id="sNC-Tz-bpo"/>
                                                    </constraints>
                                                </view>
                                            </subviews>
                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <constraints>
                                                <constraint firstItem="Laz-Vj-z9d" firstAttribute="centerY" secondItem="rwR-rC-pe1" secondAttribute="centerY" id="68l-1h-SP1"/>
                                                <constraint firstItem="Laz-Vj-z9d" firstAttribute="centerX" secondItem="rwR-rC-pe1" secondAttribute="centerX" id="8Qw-Rp-OFY"/>
                                                <constraint firstAttribute="height" constant="150" id="kjB-zT-yK1"/>
                                                <constraint firstItem="Laz-Vj-z9d" firstAttribute="leading" secondItem="rwR-rC-pe1" secondAttribute="leading" constant="20" id="q9m-xz-tgF"/>
                                            </constraints>
                                            <userDefinedRuntimeAttributes>
                                                <userDefinedRuntimeAttribute type="point" keyPath="startPoint">
                                                    <point key="value" x="0.5" y="1"/>
                                                </userDefinedRuntimeAttribute>
                                                <userDefinedRuntimeAttribute type="point" keyPath="endPoint">
                                                    <point key="value" x="0.5" y="0.0"/>
                                                </userDefinedRuntimeAttribute>
                                            </userDefinedRuntimeAttributes>
                                        </view>
                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="GAU-5f-rmJ" customClass="CalendarHeaderView" customModule="Beta_Cinemas" customModuleProvider="target">
                                            <rect key="frame" x="0.0" y="150" width="375" height="80"/>
                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <constraints>
                                                <constraint firstAttribute="height" constant="80" id="QGK-4f-a05"/>
                                            </constraints>
                                        </view>
                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="kOr-of-Kxu">
                                            <rect key="frame" x="0.0" y="230" width="375" height="156"/>
                                            <subviews>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Chọn khu vực" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="v9w-D1-ErG" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                                    <rect key="frame" x="20" y="20" width="101" height="116"/>
                                                    <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                                    <color key="textColor" red="0.28627450980000002" green="0.**********0000003" blue="0.38431372549999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <nil key="highlightedColor"/>
                                                    <userDefinedRuntimeAttributes>
                                                        <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Film.SelectRegion"/>
                                                    </userDefinedRuntimeAttributes>
                                                </label>
                                                <imageView userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_arrow" translatesAutoresizingMaskIntoConstraints="NO" id="Xm8-89-bwI">
                                                    <rect key="frame" x="343" y="66" width="24" height="24"/>
                                                    <constraints>
                                                        <constraint firstAttribute="width" constant="24" id="7Pw-uA-ecm"/>
                                                        <constraint firstAttribute="height" constant="24" id="jQU-2D-BOz"/>
                                                    </constraints>
                                                </imageView>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Tất cả" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="5WB-64-gid">
                                                    <rect key="frame" x="289.5" y="68.5" width="45.5" height="19"/>
                                                    <fontDescription key="fontDescription" name="SourceSansPro-Bold" family="Source Sans Pro" pointSize="16"/>
                                                    <nil key="textColor"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="sHj-nR-3TK">
                                                    <rect key="frame" x="0.0" y="0.0" width="375" height="156"/>
                                                    <connections>
                                                        <action selector="selectRegionButtonPressed:" destination="twf-hl-jWp" eventType="touchUpInside" id="ofu-jY-xZa"/>
                                                    </connections>
                                                </button>
                                            </subviews>
                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <constraints>
                                                <constraint firstItem="sHj-nR-3TK" firstAttribute="top" secondItem="kOr-of-Kxu" secondAttribute="top" id="JNL-Q8-Ciy"/>
                                                <constraint firstItem="Xm8-89-bwI" firstAttribute="centerY" secondItem="kOr-of-Kxu" secondAttribute="centerY" id="Lvc-sO-IkT"/>
                                                <constraint firstItem="v9w-D1-ErG" firstAttribute="top" secondItem="kOr-of-Kxu" secondAttribute="top" constant="20" id="NiZ-eS-A5I"/>
                                                <constraint firstAttribute="trailing" secondItem="Xm8-89-bwI" secondAttribute="trailing" constant="8" id="Svh-9z-deK"/>
                                                <constraint firstAttribute="trailing" secondItem="sHj-nR-3TK" secondAttribute="trailing" id="cKL-4b-vbk"/>
                                                <constraint firstAttribute="bottom" secondItem="sHj-nR-3TK" secondAttribute="bottom" id="dU6-eu-N3P"/>
                                                <constraint firstItem="v9w-D1-ErG" firstAttribute="leading" secondItem="kOr-of-Kxu" secondAttribute="leading" constant="20" id="fC0-qg-67t"/>
                                                <constraint firstItem="5WB-64-gid" firstAttribute="centerY" secondItem="kOr-of-Kxu" secondAttribute="centerY" id="hDc-mp-teA"/>
                                                <constraint firstItem="Xm8-89-bwI" firstAttribute="leading" secondItem="5WB-64-gid" secondAttribute="trailing" constant="8" id="sHg-vZ-bww"/>
                                                <constraint firstItem="sHj-nR-3TK" firstAttribute="leading" secondItem="kOr-of-Kxu" secondAttribute="leading" id="u6O-3e-mkc"/>
                                                <constraint firstItem="v9w-D1-ErG" firstAttribute="centerY" secondItem="kOr-of-Kxu" secondAttribute="centerY" id="ucq-uY-tIA"/>
                                            </constraints>
                                        </view>
                                    </subviews>
                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <constraints>
                                        <constraint firstItem="GAU-5f-rmJ" firstAttribute="leading" secondItem="Qb0-5J-udq" secondAttribute="leading" id="3BZ-eI-Wym"/>
                                        <constraint firstItem="rwR-rC-pe1" firstAttribute="top" secondItem="Qb0-5J-udq" secondAttribute="top" id="3h9-M0-VI8"/>
                                        <constraint firstAttribute="trailing" secondItem="rwR-rC-pe1" secondAttribute="trailing" id="6jg-co-Itp"/>
                                        <constraint firstItem="ngE-yT-VUU" firstAttribute="top" secondItem="Qb0-5J-udq" secondAttribute="top" id="AyD-Qd-K1h"/>
                                        <constraint firstItem="kOr-of-Kxu" firstAttribute="leading" secondItem="Qb0-5J-udq" secondAttribute="leading" id="CbF-th-dAb"/>
                                        <constraint firstItem="kOr-of-Kxu" firstAttribute="top" secondItem="GAU-5f-rmJ" secondAttribute="bottom" id="E79-Rx-C8w"/>
                                        <constraint firstItem="rwR-rC-pe1" firstAttribute="leading" secondItem="Qb0-5J-udq" secondAttribute="leading" id="O6E-zx-GDc"/>
                                        <constraint firstAttribute="trailing" secondItem="ngE-yT-VUU" secondAttribute="trailing" id="SXd-tr-HcL"/>
                                        <constraint firstAttribute="bottom" secondItem="kOr-of-Kxu" secondAttribute="bottom" id="Xc0-A6-Shb"/>
                                        <constraint firstAttribute="trailing" secondItem="kOr-of-Kxu" secondAttribute="trailing" id="YDT-I7-YAq"/>
                                        <constraint firstItem="GAU-5f-rmJ" firstAttribute="top" secondItem="rwR-rC-pe1" secondAttribute="bottom" id="e7z-QB-5zl"/>
                                        <constraint firstAttribute="trailing" secondItem="GAU-5f-rmJ" secondAttribute="trailing" id="lCH-xJ-2O5"/>
                                        <constraint firstItem="ngE-yT-VUU" firstAttribute="leading" secondItem="Qb0-5J-udq" secondAttribute="leading" id="tiB-3z-enq"/>
                                    </constraints>
                                </view>
                                <connections>
                                    <outlet property="delegate" destination="twf-hl-jWp" id="T78-0a-WoB"/>
                                </connections>
                            </tableView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="y6l-qZ-lMR"/>
                        <color key="backgroundColor" red="0.95294117649999999" green="0.95294117649999999" blue="0.95294117649999999" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <constraint firstItem="y6l-qZ-lMR" firstAttribute="bottom" secondItem="QUd-gD-GYO" secondAttribute="bottom" id="5aG-rZ-Ufy"/>
                            <constraint firstItem="QUd-gD-GYO" firstAttribute="leading" secondItem="y6l-qZ-lMR" secondAttribute="leading" id="YLC-FU-wuf"/>
                            <constraint firstItem="y6l-qZ-lMR" firstAttribute="trailing" secondItem="QUd-gD-GYO" secondAttribute="trailing" id="cpX-oe-AsV"/>
                            <constraint firstItem="QUd-gD-GYO" firstAttribute="top" secondItem="y6l-qZ-lMR" secondAttribute="top" id="gZ4-nv-vRo"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="calendarView" destination="GAU-5f-rmJ" id="rmp-ZO-aeY"/>
                        <outlet property="detailButton" destination="bI1-ZA-qU7" id="sxv-yb-PZ0"/>
                        <outlet property="headerView" destination="Qb0-5J-udq" id="QjW-CE-ZFt"/>
                        <outlet property="ivFilmBanner" destination="ngE-yT-VUU" id="qtb-f8-3t0"/>
                        <outlet property="lbFilmType" destination="xSq-zL-xNb" id="wuI-Jz-Crf"/>
                        <outlet property="lbFimName" destination="BVc-CN-8ga" id="OpW-Go-u55"/>
                        <outlet property="lbRegion" destination="5WB-64-gid" id="Iog-09-viA"/>
                        <outlet property="tableView" destination="QUd-gD-GYO" id="JnF-EH-Fbq"/>
                        <outlet property="vGradientBanner" destination="rwR-rC-pe1" id="SE8-2b-9Pu"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="cal-d7-9cE" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="2300" y="-258.62068965517244"/>
        </scene>
    </scenes>
    <designables>
        <designable name="IF5-gv-Kgo">
            <size key="intrinsicContentSize" width="95" height="40"/>
        </designable>
        <designable name="bI1-ZA-qU7">
            <size key="intrinsicContentSize" width="30" height="26"/>
        </designable>
        <designable name="bnt-zp-rT7">
            <size key="intrinsicContentSize" width="320" height="568"/>
        </designable>
        <designable name="v9w-D1-ErG">
            <size key="intrinsicContentSize" width="101" height="19"/>
        </designable>
    </designables>
    <resources>
        <image name="bg3.png" width="320" height="568"/>
        <image name="bg4.png" width="320" height="568"/>
        <image name="ic_arrow" width="25" height="49"/>
        <image name="ic_play" width="60" height="60"/>
    </resources>
</document>
