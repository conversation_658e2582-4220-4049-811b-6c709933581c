//
//  ListPosterUrlModel.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 4/3/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation
import ObjectMapper

class ListPosterUrlModel : Mappable {
    var AbsolutePath : String?
    var MainPoster : Bool?

    required init?(map: Map) {

    }

    func mapping(map: Map) {
        AbsolutePath         <- map["AbsolutePath"]
        MainPoster           <- map["MainPoster"]
    }
}
