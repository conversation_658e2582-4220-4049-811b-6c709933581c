<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<title>Uses of Package microsoft.aspnet.signalr.client</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="Uses of Package microsoft.aspnet.signalr.client";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../microsoft/aspnet/signalr/client/package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Use</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?microsoft/aspnet/signalr/client/package-use.html" target="_top">Frames</a></li>
<li><a href="package-use.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Uses of Package microsoft.aspnet.signalr.client" class="title">Uses of Package<br>microsoft.aspnet.signalr.client</h1>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList"><a name="microsoft.aspnet.signalr.client">
<!--   -->
</a>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../microsoft/aspnet/signalr/client/package-summary.html">microsoft.aspnet.signalr.client</a> used by <a href="../../../../microsoft/aspnet/signalr/client/package-summary.html">microsoft.aspnet.signalr.client</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../microsoft/aspnet/signalr/client/class-use/Action.html#microsoft.aspnet.signalr.client">Action</a>
<div class="block">Represents a generic executable action</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../microsoft/aspnet/signalr/client/class-use/ConnectionBase.html#microsoft.aspnet.signalr.client">ConnectionBase</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../microsoft/aspnet/signalr/client/class-use/ConnectionState.html#microsoft.aspnet.signalr.client">ConnectionState</a>
<div class="block">Represents the state of a connection</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../microsoft/aspnet/signalr/client/class-use/Credentials.html#microsoft.aspnet.signalr.client">Credentials</a>
<div class="block">Interface for credentials to be sent in a request</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../microsoft/aspnet/signalr/client/class-use/ErrorCallback.html#microsoft.aspnet.signalr.client">ErrorCallback</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../microsoft/aspnet/signalr/client/class-use/Logger.html#microsoft.aspnet.signalr.client">Logger</a>
<div class="block">Interface to define a Logger</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../microsoft/aspnet/signalr/client/class-use/LogLevel.html#microsoft.aspnet.signalr.client">LogLevel</a>
<div class="block">Represents a logging level</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../microsoft/aspnet/signalr/client/class-use/MessageReceivedHandler.html#microsoft.aspnet.signalr.client">MessageReceivedHandler</a>
<div class="block">Interface to define a handler for a "Message received" event</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../microsoft/aspnet/signalr/client/class-use/PlatformComponent.html#microsoft.aspnet.signalr.client">PlatformComponent</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../microsoft/aspnet/signalr/client/class-use/SignalRFuture.html#microsoft.aspnet.signalr.client">SignalRFuture</a>
<div class="block">Represents long running SignalR operations</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../microsoft/aspnet/signalr/client/class-use/StateChangedCallback.html#microsoft.aspnet.signalr.client">StateChangedCallback</a>
<div class="block">Callback invoked when a connection changes its state</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../microsoft/aspnet/signalr/client/class-use/Version.html#microsoft.aspnet.signalr.client">Version</a>
<div class="block">Represents a Version of a Product or Library</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../microsoft/aspnet/signalr/client/package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Use</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?microsoft/aspnet/signalr/client/package-use.html" target="_top">Frames</a></li>
<li><a href="package-use.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
