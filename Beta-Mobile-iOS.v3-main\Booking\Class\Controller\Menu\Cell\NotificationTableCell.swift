//
//  NotificationTableCell.swift
//  Booking-dev
//
//  Created by <PERSON><PERSON> on 4/8/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit

class NotificationTableCell: UITableViewCell {
    @IBOutlet weak var titleLabel: UILabel!
    @IBOutlet weak var detailView: UIView!
    @IBOutlet weak var dateLabel: UILabel!
    
    @IBOutlet weak var arrowDownImageView: UIImageView!
    @IBOutlet weak var detailLabel: UILabel!

    var isOpen: Bool = false {
        didSet {
            detailView.isHidden = !isOpen
            UIView.animate(withDuration: 0.3) {
                self.arrowDownImageView.transform = CGAffineTransform(rotationAngle: self.isOpen ? .pi : 0)
            }
        }
    }
    
    override func awakeFromNib() {
        super.awakeFromNib()
    }

    override func setSelected(_ selected: <PERSON><PERSON>, animated: <PERSON><PERSON>) {

    }

    override func setHighlighted(_ highlighted: <PERSON><PERSON>, animated: <PERSON><PERSON>) {
        
    }
}
