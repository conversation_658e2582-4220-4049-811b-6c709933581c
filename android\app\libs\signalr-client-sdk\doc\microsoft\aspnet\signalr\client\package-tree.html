<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<title>microsoft.aspnet.signalr.client Class Hierarchy</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="microsoft.aspnet.signalr.client Class Hierarchy";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../microsoft/aspnet/signalr/client/package-summary.html">Package</a></li>
<li>Class</li>
<li>Use</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?microsoft/aspnet/signalr/client/package-tree.html" target="_top">Frames</a></li>
<li><a href="package-tree.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 class="title">Hierarchy For Package microsoft.aspnet.signalr.client</h1>
</div>
<div class="contentContainer">
<h2 title="Class Hierarchy">Class Hierarchy</h2>
<ul>
<li type="circle">java.lang.Object
<ul>
<li type="circle">microsoft.aspnet.signalr.client.<a href="../../../../microsoft/aspnet/signalr/client/CalendarSerializer.html" title="class in microsoft.aspnet.signalr.client"><span class="strong">CalendarSerializer</span></a> (implements com.google.gson.JsonDeserializer&lt;T&gt;, com.google.gson.JsonSerializer&lt;T&gt;)</li>
<li type="circle">microsoft.aspnet.signalr.client.<a href="../../../../microsoft/aspnet/signalr/client/Connection.html" title="class in microsoft.aspnet.signalr.client"><span class="strong">Connection</span></a> (implements microsoft.aspnet.signalr.client.<a href="../../../../microsoft/aspnet/signalr/client/ConnectionBase.html" title="interface in microsoft.aspnet.signalr.client">ConnectionBase</a>)</li>
<li type="circle">microsoft.aspnet.signalr.client.<a href="../../../../microsoft/aspnet/signalr/client/Constants.html" title="class in microsoft.aspnet.signalr.client"><span class="strong">Constants</span></a></li>
<li type="circle">microsoft.aspnet.signalr.client.<a href="../../../../microsoft/aspnet/signalr/client/DateSerializer.html" title="class in microsoft.aspnet.signalr.client"><span class="strong">DateSerializer</span></a> (implements com.google.gson.JsonDeserializer&lt;T&gt;, com.google.gson.JsonSerializer&lt;T&gt;)</li>
<li type="circle">microsoft.aspnet.signalr.client.<a href="../../../../microsoft/aspnet/signalr/client/FutureHelper.html" title="class in microsoft.aspnet.signalr.client"><span class="strong">FutureHelper</span></a></li>
<li type="circle">microsoft.aspnet.signalr.client.<a href="../../../../microsoft/aspnet/signalr/client/HeartbeatMonitor.html" title="class in microsoft.aspnet.signalr.client"><span class="strong">HeartbeatMonitor</span></a></li>
<li type="circle">microsoft.aspnet.signalr.client.<a href="../../../../microsoft/aspnet/signalr/client/MessageResult.html" title="class in microsoft.aspnet.signalr.client"><span class="strong">MessageResult</span></a></li>
<li type="circle">microsoft.aspnet.signalr.client.<a href="../../../../microsoft/aspnet/signalr/client/NullLogger.html" title="class in microsoft.aspnet.signalr.client"><span class="strong">NullLogger</span></a> (implements microsoft.aspnet.signalr.client.<a href="../../../../microsoft/aspnet/signalr/client/Logger.html" title="interface in microsoft.aspnet.signalr.client">Logger</a>)</li>
<li type="circle">microsoft.aspnet.signalr.client.<a href="../../../../microsoft/aspnet/signalr/client/Platform.html" title="class in microsoft.aspnet.signalr.client"><span class="strong">Platform</span></a></li>
<li type="circle">microsoft.aspnet.signalr.client.<a href="../../../../microsoft/aspnet/signalr/client/SignalRFuture.html" title="class in microsoft.aspnet.signalr.client"><span class="strong">SignalRFuture</span></a>&lt;V&gt; (implements java.util.concurrent.Future&lt;V&gt;)
<ul>
<li type="circle">microsoft.aspnet.signalr.client.<a href="../../../../microsoft/aspnet/signalr/client/UpdateableCancellableFuture.html" title="class in microsoft.aspnet.signalr.client"><span class="strong">UpdateableCancellableFuture</span></a>&lt;V&gt;</li>
</ul>
</li>
<li type="circle">microsoft.aspnet.signalr.client.<a href="../../../../microsoft/aspnet/signalr/client/SimpleEntry.html" title="class in microsoft.aspnet.signalr.client"><span class="strong">SimpleEntry</span></a>&lt;K,V&gt; (implements java.util.Map.Entry&lt;K,V&gt;)</li>
<li type="circle">java.lang.Throwable (implements java.io.Serializable)
<ul>
<li type="circle">java.lang.Exception
<ul>
<li type="circle">microsoft.aspnet.signalr.client.<a href="../../../../microsoft/aspnet/signalr/client/InvalidProtocolVersionException.html" title="class in microsoft.aspnet.signalr.client"><span class="strong">InvalidProtocolVersionException</span></a></li>
<li type="circle">java.lang.RuntimeException
<ul>
<li type="circle">microsoft.aspnet.signalr.client.<a href="../../../../microsoft/aspnet/signalr/client/InvalidStateException.html" title="class in microsoft.aspnet.signalr.client"><span class="strong">InvalidStateException</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li type="circle">microsoft.aspnet.signalr.client.<a href="../../../../microsoft/aspnet/signalr/client/Version.html" title="class in microsoft.aspnet.signalr.client"><span class="strong">Version</span></a></li>
</ul>
</li>
</ul>
<h2 title="Interface Hierarchy">Interface Hierarchy</h2>
<ul>
<li type="circle">microsoft.aspnet.signalr.client.<a href="../../../../microsoft/aspnet/signalr/client/Action.html" title="interface in microsoft.aspnet.signalr.client"><span class="strong">Action</span></a>&lt;E&gt;</li>
<li type="circle">microsoft.aspnet.signalr.client.<a href="../../../../microsoft/aspnet/signalr/client/ConnectionBase.html" title="interface in microsoft.aspnet.signalr.client"><span class="strong">ConnectionBase</span></a></li>
<li type="circle">microsoft.aspnet.signalr.client.<a href="../../../../microsoft/aspnet/signalr/client/Credentials.html" title="interface in microsoft.aspnet.signalr.client"><span class="strong">Credentials</span></a></li>
<li type="circle">microsoft.aspnet.signalr.client.<a href="../../../../microsoft/aspnet/signalr/client/ErrorCallback.html" title="interface in microsoft.aspnet.signalr.client"><span class="strong">ErrorCallback</span></a></li>
<li type="circle">microsoft.aspnet.signalr.client.<a href="../../../../microsoft/aspnet/signalr/client/Logger.html" title="interface in microsoft.aspnet.signalr.client"><span class="strong">Logger</span></a></li>
<li type="circle">microsoft.aspnet.signalr.client.<a href="../../../../microsoft/aspnet/signalr/client/MessageReceivedHandler.html" title="interface in microsoft.aspnet.signalr.client"><span class="strong">MessageReceivedHandler</span></a></li>
<li type="circle">microsoft.aspnet.signalr.client.<a href="../../../../microsoft/aspnet/signalr/client/PlatformComponent.html" title="interface in microsoft.aspnet.signalr.client"><span class="strong">PlatformComponent</span></a></li>
<li type="circle">microsoft.aspnet.signalr.client.<a href="../../../../microsoft/aspnet/signalr/client/StateChangedCallback.html" title="interface in microsoft.aspnet.signalr.client"><span class="strong">StateChangedCallback</span></a></li>
</ul>
<h2 title="Enum Hierarchy">Enum Hierarchy</h2>
<ul>
<li type="circle">java.lang.Object
<ul>
<li type="circle">java.lang.Enum&lt;E&gt; (implements java.lang.Comparable&lt;T&gt;, java.io.Serializable)
<ul>
<li type="circle">microsoft.aspnet.signalr.client.<a href="../../../../microsoft/aspnet/signalr/client/LogLevel.html" title="enum in microsoft.aspnet.signalr.client"><span class="strong">LogLevel</span></a></li>
<li type="circle">microsoft.aspnet.signalr.client.<a href="../../../../microsoft/aspnet/signalr/client/ConnectionState.html" title="enum in microsoft.aspnet.signalr.client"><span class="strong">ConnectionState</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../microsoft/aspnet/signalr/client/package-summary.html">Package</a></li>
<li>Class</li>
<li>Use</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?microsoft/aspnet/signalr/client/package-tree.html" target="_top">Frames</a></li>
<li><a href="package-tree.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
