package vn.zenity.betacineplex.view.setting

import io.reactivex.disposables.Disposable
import vn.zenity.betacineplex.Manager.Network.APIClient
import vn.zenity.betacineplex.app.App
import vn.zenity.betacineplex.helper.extension.applyOn
import vn.zenity.betacineplex.model.QuestionCategory
import java.lang.ref.WeakReference

/**
 * Created by Zenity.
 */

class QuestionAnswerPresenter : QuestionAnswerContractor.Presenter {

    private var disposable: Disposable? = null

    override fun getCategories() {


        view?.get()?.showLoading()
        val lang = App.shared().getCurrentLang()
        disposable = APIClient.shared.ecmAPI.getTopic(lang).applyOn()
                .subscribe({
                    if (it.isSuccess) {
                        it.Data?.let {
                            view?.get()?.showListCategories(it)
                        }
                    }
                    view?.get()?.hideLoading()
                }, {
                    view?.get()?.hideLoading()
                })
//        view?.get()?.showListCategories(listOf(QuestionCategory("Phim")
//                , QuestionCategory("Bắp nước và thức ăn tại rạp")
//                , QuestionCategory("Voucher")
//                , QuestionCategory("Chương trình thành viên")
//                , QuestionCategory("Tích lũy điểm thưởng")
//                , QuestionCategory("Tổng chi tiêu và hạng thẻ")
//                , QuestionCategory("Ưu đãi sinh nhật")
//                , QuestionCategory("Mua vé trực tuyến")))
    }

    private var view: WeakReference<QuestionAnswerContractor.View?>? = null
    override fun attachView(view: QuestionAnswerContractor.View) {
        this.view = WeakReference(view)
    }

    override fun detachView() {
        disposable?.dispose()
        this.view?.clear()
        this.view = null
    }
}
