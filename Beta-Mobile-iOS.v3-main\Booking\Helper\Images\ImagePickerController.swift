//
//  ImagePickerController.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 4/19/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit
import PKHUD
import AlamofireImage
import Photos

typealias ImagePickerHandler = (_ image: UIImage?) -> Void

class ImagePickerController: NSObject {
    var completionHandler: ImagePickerHandler?
    static var listPickerController: [ImagePickerController] = []

    deinit {
    }

    static func showImagePicker(allowEdit: Bool = false, from viewController: UIViewController, completionHandler: ImagePickerHandler?) {
        UIAlertController.showActionSheet(in: viewController, title: nil, list: ["Bt.CaptureImage".localized, "Bt.ChooseFromLibrary".localized]) { (selectedIndex) in
            if selectedIndex == 0 {
                showImagePickerImmediate(type: .camera, allowEdit: allowEdit, from: viewController, completionHandler: completionHandler)
            } else if selectedIndex == 1 {
                showImagePickerImmediate(type: .photoLibrary, allowEdit: allowEdit, from: viewController, completionHandler: completionHandler)
            }
        }
    }

    static func showImagePickerWithRetry(allowEdit: Bool = false, from viewController: UIViewController, onRetry: (()-> Void)?, completionHandler: ImagePickerHandler?) {
        UIAlertController.showActionSheet(in: viewController, title: nil, list: ["Bt.Retry".localized, "Bt.CaptureImage".localized, "Bt.ChooseFromLibrary".localized]) { (selectedIndex) in
            if selectedIndex == 0 {
                onRetry?()
            } else if selectedIndex == 1 {
                showImagePickerImmediate(type: .camera, allowEdit: allowEdit, from: viewController, completionHandler: completionHandler)
            } else if selectedIndex == 2 {
                showImagePickerImmediate(type: .photoLibrary, allowEdit: allowEdit, from: viewController, completionHandler: completionHandler)
            }
        }
    }

    static func showImagePickerImmediate(type: UIImagePickerControllerSourceType, allowEdit: Bool, from viewController: UIViewController, completionHandler: ImagePickerHandler?) {
        if type == .photoLibrary {
            PHPhotoLibrary.requestAuthorization({ (status) in
                if status == .authorized {
                    DispatchQueue.main.async {
                        let picker = ImagePickerController()
                        picker.showImagePicker(type: type, allowEdit: allowEdit, from: viewController, completionHandler: completionHandler)
                        listPickerController.append(picker)
                    }
                } else {
                    UIAlertController.showAlert(viewController, title: "NoPhotoPermissionTitle".localized, message: "NoPhotoPermissionMsg".localized)
                }
            })
        } else {
            DispatchQueue.main.async {
                let picker = ImagePickerController()
                picker.showImagePicker(type: type, allowEdit: allowEdit, from: viewController, completionHandler: completionHandler)
                listPickerController.append(picker)
            }
        }
    }

    fileprivate func showImagePicker(type: UIImagePickerControllerSourceType, allowEdit: Bool, from viewController: UIViewController, completionHandler: ImagePickerHandler?) {
        self.completionHandler = completionHandler

        //        DispatchQueue.main.async {
        HUD.show(.progress)
        let picker = UIImagePickerController()
        picker.sourceType = type
        picker.delegate = self
        picker.allowsEditing = allowEdit
        picker.navigationBar.tintColor = .black
        viewController.present(picker, animated: true) {
            HUD.hide()
        }
        //        }
    }

    fileprivate func closePicker() {
        if let index = ImagePickerController.listPickerController.index(where: { $0 == self }) {
            ImagePickerController.listPickerController.remove(at: index)
        }
    }
}

extension ImagePickerController: UIImagePickerControllerDelegate, UINavigationControllerDelegate {
    func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
        picker.dismiss(animated: true, completion: nil)
        closePicker()
    }

    func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [String : Any]) {
        picker.dismiss(animated: true, completion: nil)
        if picker.allowsEditing == true, let image = info[UIImagePickerControllerEditedImage] as? UIImage {
            let resizedImage = image.resize(CGSize(width: 1024, height: 1024))
            completionHandler?(resizedImage)
        } else if let url = info[UIImagePickerControllerReferenceURL] as? URL {
            AssetLibrary.loadImage(url: url, completion: { (image) in
                self.completionHandler?(image)
            })
        } else if let image = info[UIImagePickerControllerOriginalImage] as? UIImage {
            let resizedImage = image.resize(CGSize(width: 1024, height: 1024))
            completionHandler?(resizedImage)
        } else {
            completionHandler?(nil)
        }
        closePicker()
    }
}

