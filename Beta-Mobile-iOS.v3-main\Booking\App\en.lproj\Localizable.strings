/* 
  Localizable.strings
  Booking
  Created by <PERSON><PERSON> on 4/4/18.
  Copyright © 2018 ddkc. All rights reserved.
*/

"HelloWorld" = "Hello World";

"Bt.OK" = "OK";
"Bt.Yes" = "Yes";
"Bt.SEND" = "SEND";
"Bt.Confirm" = "CONFIRM";
"Bt.Update" = "UPDATE";
"Bt.Cancel" = "Cancel";
"Bt.Retry" = "Retry";
"Bt.CaptureImage" = "Capture new photo";
"Bt.ChooseFromLibrary" = "From photo library";
"Bt.REGISTER" = "REGISTER";
"Bt.LocationSetting" = "Open Settings";
"Bt.Close" = "Close";
"add_new" = "ADD";

"Menu.Home" = "Home";
"Menu.Member" = "BETA Membership";
"Menu.Cenima" = "BETA Theaters";
"Menu.NewsAndHotDeals" = "News and Offers";
"Menu.Barcode" = "Barcode";
"Menu.Recruitment" = "Recruitment Information";
"Menu.Notification" = "Notifications";
"Menu.Setting" = "Setting";
"Menu.Price" = "Ticket Prices";
"Menu.BookByMovie" = "BOOKING BY MOVIE";
"Menu.BookByTheater" = "BOOKING BY THEATER";

"Morning" = "AM";
"Afternoon" = "PM";

"Register.ConfirmTermAndPolicyText" = "I am committed to complying with the Betacineplex privacy policy and terms of use.";
"Register.Term" = "privacy policy";
"Register.Policy" = "terms of use";
"Register.Title" = "Register";
"Register.RequiredInfo" = "REQUIRED INFORMATION";
"Register.Name" = "Full name";
"Register.FirstName" = "First name";
"Register.LastName" = "Last name";
"Register.Email" = "Email";
"Register.Password" = "Password";
"Register.RePassword" = "Confirm password";
"Register.Phone" = "Phone number";
"Register.CMND" = "Personal ID/Passport ";
"Register.Birthday" = "Birthday";
"Register.AdditionalInfo" = "ADDITIONAL INFORMATION";
"Register.Gender" = "Gender";
"Register.City" = "Province/ City";
"Register.District" = "State/ District";
"Register.Address" = "Address";
"Register.Register" = "REGISTER";
"RegisterSuccess" = "Register successfully!";
"Register.Success" = "REGISTER SUCCESS";
"Register.YouReceivedCardNumber" = "You received the card number is";
"Register.ReceiveMemberCard" = "Receive online member card";

"Login.Title" = "Login";
"Login.Email" = "Email or Username";
"Login.Password" = "Password";
"Login.ForgotPassword" = "Forgot Password?";
"Login.Login" = "LOGIN";
"Login.LoginWithFacebook" = "SIGN IN WITH FACEBOOK";
"Login.LoginWithApple" = "SIGN IN WITH APPLE";
"Or" = "Or";
"Login.RegisterBETAAccount" = "Register Beta Cineplex Account";

"ForgotPass.Title" = "Forgot Password";
"ForgotPass.GuideTitle" = "New password will be sent to your Email inbox!";
"ForgotPass.Email" = "Email";

"Home.Minute" = "Minutes";
"Home.UpComing" = "COMING SOON";
"Home.NowShowing" = "NOW SHOWING";
"Home.Special" = "SPECIAL";
"Home.BuyTicket" = "GET TICKETS";
"Home.SearchAroundYou" = "Search theater near you...";
"Home.BigDeals" = "BIG DEALS";
"Home.All" = "All";
"Home.Hi" = "Hi";

"FilmDetail.Director" = "Director";
"FilmDetail.Actor" = "Cast";
"FilmDetail.Type" = "Genre";
"FilmDetail.Duration" = "Run time";
"FilmDetail.Language" = "Language";
"FilmDetail.DateShow" = "Release date";
"FilmDetail.Title" = "Movie detail";
"FilmDetail.Share" = "SHARE";
"FilmDetail.BuyTicket" = "GET TICKETS";
"Film.SelectRegion" = "Choose zone";

"Alert.EmailNotEmpty" = "Email is not empty";
"Alert.EmailInvalid" = "Email is invalid";
"Alert.PasswordNotEmpty" = "Password is not empty";
"Alert.NewPasswordNotEmpty" = "New Password is not empty";
"Alert.ConfirmPasswordNotEmpty" = "Confirm Password is not empty";
"Alert.ConfirmPasswordInvalid" = "Confirm Password invalid";
"Alert.NameNotEmpty" = "Name is not empty";
"Alert.FirstNameNotEmpty" = "First name is not empty";
"Alert.LastNameNotEmpty" = "Last name is not empty";
"Alert.PhoneNotEmpty" = "Phone is not empty";
"Alert.PhoneInvalid" = "Phone is invalid";
"Alert.IDNotEmpty" = "ID is not empty";
"Alert.IDInvalid" = "ID is invalid";
"Alert.BithdayNotEmpty" = "Birthday is not empty";
"Alert.BirthdayInvalid" = "Birthday is invalid";
"Alert.PasswordNotMatch" = "Password is not match";
"Alert.PasswordInvalid" = "Password must be longer than or equal to 6 characters";
"Alert.VoucherCodeNotEmpty" = "Voucher Code is not empty";
"Alert.PinCodeNotEmpty" = "Pin Code is not empty";
"Alert.CouponCodeNotEmpty" = "Coupon Code is not empty";
"Alert.NoFilmTrailerURL" = "Cannot load the film trailer!";
"Alert.FilmTrailerLoadError" = "Cannot load the film trailer!";
"Alert.UserNotAcceptTerm" = "You must accept our term and policy";
"Alert.ChangePassFailed" = "Change password failed!";
"Alert.Success" = "Success";
"Alert.Error" = "Error";
"Alert.ErrorServer" = "Some errors occurred. Please try again later.";
"Alert.LoginFailed" = "Login failed";
"Alert.PaymentFailed" = "Your payment has failed! Please try again later.";
"Alert.BookingSeatFailed" = "Cannot book these seats! Please choose another seat!";
"Alert.PaymentSuccess" = "Booking success!";
"Alert.LoginFBWithNoEmail" = "Cannot get your email from your Facebook.";
"Alert.LoginFBFailed" = "Login with Facebook failed!";
"Alert.LoginAppleFailed" = "Login with Apple failed!";
"Alert.LoginAppleUnsupported" = "Unsupported login with Apple !";
"Alert.RegisterCouponSuccess" = "Register new coupon successfully";
"Alert.RegisterVoucherSuccess" = "Register new voucher successfully";
"Alert.UpdatePassFailed" = "Update password failed!";
"Alert.BookingWaiting" = "The transaction is processing. Return to the Transaction history to view transaction details and continue to payment.";
"Alert.DeleteAccountTitle" = "Delete Account";
"Alert.DeleteAccountMessage" = "All account information, transaction history and accumulated points will be deleted. Do you want to delete your account?";
"Alert.LoginDeleteAccount" = "This account has been deleted";

"FILE_UPLOAD_TOO_LARGE_1MB" = "File size is too large. Maximum upload file size: 1MB";
"PASSWORD_CHANGED_SUCCESSFULLY" = "Password changed successfully";
"OLD_PASSWORD_INCORRECT" = "Old password is incorrect";
"PASSWORD_INCORRECT" = "Password is incorrect";
"ACCOUNT_NOT_EXISTED" = "Account is not existed";
"ACCOUNT_LOCKED" = "Account is locked";
"LOGIN_SUCCESSFULLY" = "Login successfully";
"LOGIN_FAILED" = "The username or password is incorrect";
"EMAIL_EXISTED" = "Email is exist";
"EMAIL_EXIST" = "Email is exist";
"PHONE_NUMBER_EXISTED" = "Phone number is exist";
"PERSONAL_ID_EXISTED" = "Personal ID is exist";
"REGISTRATION_SUCCESSFULLY" = "Register successfully";
"REGISTRATION_FAIL" = "Register failed";
"UPDATE_SUCCESSFULLY" = "Update profile successfully";

"CinemaList.ChooseByArea" = "Choose by area";
"CinemaList.NearCinema" = "Theater near you";

// setting
"Language" = "Language";
"Other" = "Other";
"Setting.Notify" = "Notifications";
"Setting.Location" = "Location";
"Setting.FAQ" = "F.A.Q";
"Setting.Version" = "Version";
"Setting.Policy" = "Term of use";
"Setting.PaymentPolicy" = "Payment Policy";
"Setting.SecurePolicy" = "Secure Policy";
"Setting.CompanyInfo" = "Company Profile";
"Setting.VietNam" = "Vietnamese";
"Setting.English" = "English";
"Setting.Title" = "Setting";

"ListCinema.Title" = "BETA Theater";
"Cinema.Screen" = "SCREEN";

"Notification.Title" = "Notifications";

"FAQ.Title" = "F.A.Q";

"AppVersion.Title" = "App version";
"App.NewVersionAvailable" = "New version available";
"App.UsingLatestVersion" = "You are using the latest version";
"Alert.CheckNewVersionFailed" = "Check app version failed!";
"App.VersionSupport" = "Support iOS XXX and above";
"App.CurrentVersion" = "Current version";

"NewsAndDeals.Title" = "News and Offers";
"NewsAndDeals.Promotion" = "OFFERS";
"NewsAndDeals.News" = "NEWS";

"NewsDetail.Title" = "News and Offers";
"NewsDetail.Share" = "SHARE";

"Member.Title" = "Beta Membership";
"Member.AccountInfo" = "Account information";
"Member.ChangePass" = "Change password";
"Member.MemberCard" = "Member card";
"Member.BetaPoint" = "BETA Points";
"Member.PreferentialCard" = "Preferential Cards";
"Member.TransactionHistory" = "Transaction history";
"Member.Intro" = "Introduce your friends";
"Member.DeleteAccount" = "Delete Account";
"Member.ChooseImage" = "Choose";
"Member.MemberCard" = "Member Card";
"Member.TotalSpent" = "Total spending";
"Member.TotalPoint" = "Accumulating Point";
"Member.Logout" = "Log out";
"Member.SpentToVip" = "You need to accumulate XXX more to upgrade VIP";
"Member.ReachMaxVip" = "Congratulations on being a super cosmic, VIP Pro-level member of Beta Cinemas";
"Member.RewardPoint" = "You have %d reward points expiring on %@";
"Member.NextVip" = "Yayy, Your account is eligible for a VIP upgrade and will be updated after %@ today.";

"Confirm.Password" = "Password";
"Confirm.GuideText" = "Please enter your password for secure";

"ChangePass.Title" = "Change password";
"ChangePass.EnterCurrentPass" = "Enter current password";
"ChangePass.CurrentPass" = "Current password";
"ChangePass.EnterNewPass" = "Enter new password";
"ChangePass.NewPass" = "New password";
"ChangePass.ReNewPass" = "Confirm new password";

"AccountInfo.Title" = "Account Information";
"AccountInfo.BasicInfo" = "BASIC INFORMATION";
"AccountInfo.ContactInfo" = "CONTACT INFORMATION";

"BetaPoint.Title" = "BETA Points";
"BetaPoint.TotalPoint" = "Accumulating Point";
"BetaPoint.UsedPoint" = "Used Point";
"BetaPoint.CurrentPoint" = "Current Point";

"TransactionHistory.Title" = "Transaction History";
"TransactionHistory.GuideText" = "You can see your transactions during the last 3 months. Please visit website for all transaction history.";
"TransactionHistory.Point" = "Point";
"TransactionHistory.Point" = "Points expiry date";

"TransactionDetail.Cinema" = "Theater";
"TransactionDetail.ShowDate" = "Date";
"TransactionDetail.ShowTime" = "Time";
"TransactionDetail.ShowRoom" = "Room";
"TransactionDetail.Seat" = "Seat ({SEAT_COUNT})";
"TransactionDetail.Combo" = "Combo ({COMBO_COUNT})";
"TransactionDetail.Cash" = "Cash";
"TransactionDetail.Voucher" = "Voucher";
"TransactionDetail.Point" = "BetaID";
"TransactionDetail.Card" = "Card";
"TransactionDetail.Airpay" = "ShopeePay";
"TransactionDetail.Momo" = "Momo";
"TransactionDetail.ZaloPay" = "ZaloPay";
"TransactionDetail.TotalMoney" = "Total";
"TransactionDetail.Title" = "Transaction Detail";
"TransactionDetail.GuideText" = "You should present your booking number to cinema staff to get your tickets ";
"TransactionDetail.NoticeText" = "Notice: Beta does not accept refunds or exchange tickets successfully paid on the Website and Beta Applications";
"TransactionDetail.Notice" = "Notice:";

"NoPhotoPermissionTitle" = "Photo Access Permission Denied!";
"NoPhotoPermissionMsg" = "Please allow app to access the Photo Library!";

"VoucherCoupon.Title" = "Preferential Card";
"BetaVoucher" = "BETA Voucher";
"BetaCoupon" = "BETA Coupon";

"Voucher.Title" = "BETA Voucher";
"Voucher.Info" = "VOUCHER INFORMATION";
"Voucher.Code" = "Voucher Code";
"Voucher.PIN" = "PIN Code";
"YourVoucher" = "YOUR VOUCHER";
"Voucher.CardType" = "Card type";
"Voucher.CardCode" = "Card number";
"Voucher.ExpireDate" = "Expire Date";

"Coupon.Title" = "BETA Coupon";
"Coupon.Info" = "COUPON INFORMATION";
"Coupon.Code" = "Coupon Code";
"Coupon.PIN" = "PIN Code";
"YourCoupon" = "YOUR COUPON";

"Subtitles" = "Subtitles";
"Dubbing" = "Dubbing";
"Sub" = "SUB";
"Dub" = "DUB";
"AgeRestrict.C13" = "No childrent under 13 years old";
"AgeRestrict.C16" = "No childrent under 16 years old";
"AgeRestrict.C18" = "No childrent under 18 years old";

"CompanyInfo.Title" = "Company Profile";
"SecurePolicy.Title" = "Secure Policy";
"PaymentPolicy.Title" = "Payment Policy";
"TermOfUse.Title" = "Terms of use";

"BookingByFilm.Title" = "Booking by Movie";
"BookingByTheater.Title" = "Booking by Theater";
"Film.NowShowing" = "NOW SHOWING";
"Film.Special" = "SPECIAL";

"Today" = "Today";

"Address" = "Address";
"Bt.Guide" = "Guide";

"CinemaDetail.Title" = "Beta Cinema";
"NoRoutesFound" = "Cannot find any available routes!";
"LocationNoPermission" = "Permission is not allowed! Please open Location Service settings and allow app using location access!";
"RouteInMap.Title" = "Select your app";
"OpenInAppleMap" = "Open in Apple Map";
"OpenInGoogleMap" = "Open in Google Map";

"MemberCard.Title" = "Member Card";
"MemberCard.CardName" = "Card name";
"MemberCard.CardNumber" = "Card number";
"MemberCard.CardDate" = "Date";
"MemberCard.UsingCard" = "(Using)";

"empty" = "empty";

"Recruitment.Title" = "Recruitment";
"Seat.Empty" = "Empty seat";
"Seat.Selecting" = "Holding seat";
"Seat.Selected" = "Selecting seat";
"Seat.Sold" = "Sold seat";
"Seat.Booked" = "Booked seat";
"Seat.Normal" = "Normal seat";
"Seat.Vip" = "VIP seat";
"Seat.Couple" = "Couple seat";

"Seat.Continue" = "Continue";
"Seat.SelectedSeats" = "Selected seats";
"Seat.TotalBill" = "Total";
"Seat.MaxSelectedSeats" = "You can choose maximum 8 seats";

"ConfirmAge.Message" = "I comfirm buying tickets for audience from XXX & above years old, and understand that no refund is applied if falling to present proof of age. Read here for Ministry of Culture, Sports and Tourism's rule ";
"AgeAbove13" = "13";
"AgeAbove16" = "16";
"AgeAbove18" = "18";
"ConfirmAge.Rule" = "here";
"Payment.Title" = "Payment";

"ShowFilm.TimeOut" = "The selected show is time out! Please select another show!";
"CouponCode" = "Coupon Code";

"AlertWarningLogin" = "You must login to see notifications";
"DontEmptySeat" = "Don't leave the seat {seat_name} empty";
"DontEmptyLeftRightSeat" = "Please do not leave 1 seat empty at left or right of selected seats";
"DontEmptyBetweenSeat" = "Please do not leave 1 seat empty between selected seats";
"SeatsTimeOut" = "Time for selecting seats is out!";

"UpdatePassword.Title" = "Update Password";

"Tab1" = "Booking by\n Movies";
"Tab2" = "Booking by\n Cinema";
"Tab3" = "Voucher";
"Tab4" = "Deals";
"Tab5" = "Other";

"free_voucher" = "Free Voucher";

"your_voucher" = "Your Voucher";
"add_new_voucher" = "Add new voucher";
"voucher_history" = "Voucher history used";
"point_history" = "Points History";
"donate_voucher" = "Donate Voucher";
"Message_401" = "This session expired, please login again!";

"out_of_voucher" = "Out of Voucher";
"free_voucher" = "Free Voucher";
"hsd" = "ED: ";
"add_success" = "Add new success";
"add_voucher_success" = "You have added this voucher to your storehouse";
"donate_voucher_success" = "donate voucher success";
"donate_voucher_success_alert" = "You have donated voucher for account \n #%@# \n (%@) \n success!";

"donate_point_success" = "donate point success";
"donate_point_success_alert" = "You have donated point for account \n #%@# \n (%@) \n success!";

"get_voucher" = "Get Code";
"donate_point" = "Donate Point";

"donate_point_alert_title" = "You are donating points to the account \n #%@# \n (%@)";
"donate_point_alert_content" = "Enter points that you want to donate:";

"voucher_history_note" = "Displays the voucher history of the# last 3 months.# Please visit the website to view the entire history.";
"point_history_note" = "Displays the point history of the# last 3 months.# Please visit the website to view the entire history.";

"used" = "Used";
"expired" = "Expired";
"donated" = "Donated";
"received" = "Recieved";

"point_save" = "Point Save";
"point_expense" = "Point Expense";
"point_cancel" = "Transaction Cancel";
"point_donate" = "Point Donate";
"point_receive" = "Point Receive";

"scan_qrcode" = "Scan QRCode";
"scan_note" = "Move the camera to an area containing QR Code to scan";

"use_voucher" = "Use voucher";
"use_voucher_note" = "Scan this code at POS to use voucher";

"share_code_note" = "Share your referral code to your friends to get a gift right away!";
"enter_code_note" = "Do you have referral code from friends? \nPlease enter the code below.";

"enter_intro_code" = "Please enter introduce code";

"coming_soon" = "Coming Soon";
"now_showing" = "Now Showing";
"early_show" = "Sneak Show";

"film_detail" = "Film Detail";

"buy" = "Buy";
"free" = "Free";
"confirm" = "Confirm";

"use" = "USE";
"donate" = "DONATE";

"confirm_donate_point" = "Confirm to donate #\(%d) points# to the account \n #%@# \n (%@)";
"confirm_donate_voucher" = "Confirm to donate Voucher #%@ # to the account \n #%@# \n (%@)";
"enter_other" = "Enter other number...";
"search_friend" = "Search friends...";
"referral_code" = "Referral code...";
"recruiment_info" = "Recruitment Information";

"voucher_empty_title" = "You haven't anyvoucher!";
"voucher_empty_detail" = "You can search to add voucher public or add new a voucher in your voucher";

"ticket_price" = "Ticket price";
"show" = "Show";
"call_now" = "Call";

"pull_to_refresh" = "Pull to refresh";
"release_to_refresh" = "Release to refresh";
"success" = "Success";
"refreshing" = "Refreshing...";
"failed" = "Failed";
"waiting_process" = "Waiting";

"not_login_yet" = "You're not logged yet";
"must_login_to_get_voucher" = "You have to login to get voucher list";
