# 🛠️ Direct Payment Implementation Guide

## 📋 **Implementation Checklist**

### ✅ **Phase 1: Core Infrastructure**
- [ ] Create `DirectPaymentService`
- [ ] Update WebView URL interception
- [ ] Implement payment URL parsing
- [ ] Add direct payment URL generation

### ✅ **Phase 2: Platform Integration**
- [ ] Update Android MainActivity deep link handling
- [ ] Update iOS URL scheme handling
- [ ] Implement payment result processing
- [ ] Add error handling and fallbacks

### ✅ **Phase 3: Testing & Rollout**
- [ ] Unit tests for URL parsing
- [ ] Integration tests with payment apps
- [ ] A/B testing implementation
- [ ] Production rollout

## 🔧 **Code Implementation**

### **1. Direct Payment Service**

```dart
// lib/services/direct_payment_service.dart

class DirectPaymentService {
  static const _channel = MethodChannel('direct_payment');
  
  /// Launch direct payment with bypass web layer
  static Future<bool> launchDirectPayment({
    required PaymentProvider provider,
    required PaymentInfo paymentInfo,
  }) async {
    try {
      final directUrl = await _generateDirectUrl(provider, paymentInfo);
      
      // Check if payment app is installed
      if (await canLaunchUrl(Uri.parse(directUrl))) {
        await launchUrl(
          Uri.parse(directUrl),
          mode: LaunchMode.externalApplication,
        );
        return true;
      } else {
        // Fallback to web-based payment
        print('⚠️ Payment app not installed, falling back to web');
        return false;
      }
    } catch (e) {
      print('❌ Direct payment launch failed: $e');
      return false;
    }
  }
  
  /// Generate direct payment URL for each provider
  static Future<String> _generateDirectUrl(
    PaymentProvider provider,
    PaymentInfo paymentInfo,
  ) async {
    switch (provider) {
      case PaymentProvider.momo:
        return _generateMomoUrl(paymentInfo);
      case PaymentProvider.zaloPay:
        return _generateZaloPayUrl(paymentInfo);
      case PaymentProvider.airPay:
        return _generateAirPayUrl(paymentInfo);
      default:
        throw UnsupportedError('Unsupported payment provider: $provider');
    }
  }
  
  /// Generate Momo direct payment URL
  static String _generateMomoUrl(PaymentInfo info) {
    final params = {
      'action': 'payment',
      'orderId': info.orderId,
      'amount': info.amount,
      'description': info.description,
      'callbackScheme': 'betacineplexx',
      'callbackHost': 'momo',
      'partnerCode': 'BETACINEMA',
    };
    
    final queryString = params.entries
        .map((e) => '${e.key}=${Uri.encodeComponent(e.value)}')
        .join('&');
    
    return 'momo://payment?$queryString';
  }
  
  /// Generate ZaloPay direct payment URL
  static String _generateZaloPayUrl(PaymentInfo info) {
    final params = {
      'action': 'payment',
      'appTransId': info.orderId,
      'amount': info.amount,
      'description': info.description,
      'callbackScheme': 'betacineplexx',
      'callbackHost': 'zalopay',
    };
    
    final queryString = params.entries
        .map((e) => '${e.key}=${Uri.encodeComponent(e.value)}')
        .join('&');
    
    return 'zalopay://payment?$queryString';
  }
  
  /// Generate AirPay direct payment URL
  static String _generateAirPayUrl(PaymentInfo info) {
    final params = {
      'action': 'payment',
      'order_id': info.orderId,
      'amount': info.amount,
      'description': info.description,
      'callbackScheme': 'betacineplexx',
      'callbackHost': 'airpay',
    };
    
    final queryString = params.entries
        .map((e) => '${e.key}=${Uri.encodeComponent(e.value)}')
        .join('&');
    
    return 'airpay://payment?$queryString';
  }
}
```

### **2. Enhanced WebView URL Interception**

```dart
// lib/pages/cinema/payment/direct_payment_webview.dart

class DirectPaymentWebView extends StatefulWidget {
  final String htmlData;
  final Function(PaymentResult) onPaymentResult;
  
  const DirectPaymentWebView({
    Key? key,
    required this.htmlData,
    required this.onPaymentResult,
  }) : super(key: key);
  
  @override
  State<DirectPaymentWebView> createState() => _DirectPaymentWebViewState();
}

class _DirectPaymentWebViewState extends State<DirectPaymentWebView> {
  late WebViewController _controller;
  
  @override
  void initState() {
    super.initState();
    _setupWebView();
    _setupDeepLinkListener();
  }
  
  void _setupWebView() {
    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onNavigationRequest: _handleNavigationRequest,
        ),
      )
      ..loadHtmlString(widget.htmlData);
  }
  
  void _setupDeepLinkListener() {
    // Listen for payment app returns
    DirectPaymentService.setPaymentResultCallback(_handlePaymentResult);
  }
  
  /// Handle URL navigation with direct payment logic
  Future<NavigationDecision> _handleNavigationRequest(
    NavigationRequest request,
  ) async {
    final url = request.url;
    
    // Check for payment URLs
    final paymentInfo = PaymentUrlParser.tryParse(url);
    if (paymentInfo != null) {
      print('💳 Payment URL detected: ${paymentInfo.provider}');
      
      // Try direct payment first
      final success = await DirectPaymentService.launchDirectPayment(
        provider: paymentInfo.provider,
        paymentInfo: paymentInfo,
      );
      
      if (success) {
        print('✅ Direct payment launched successfully');
        return NavigationDecision.prevent;
      } else {
        print('⚠️ Direct payment failed, falling back to web');
        return NavigationDecision.navigate; // Fallback to web
      }
    }
    
    return NavigationDecision.navigate;
  }
  
  /// Handle payment result from direct payment
  void _handlePaymentResult(PaymentResult result) {
    print('💳 Direct payment result: ${result.status}');
    widget.onPaymentResult(result);
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Payment')),
      body: WebViewWidget(controller: _controller),
    );
  }
}
```

### **3. Payment URL Parser**

```dart
// lib/services/payment_url_parser.dart

class PaymentUrlParser {
  /// Try to parse payment URL and extract info
  static PaymentInfo? tryParse(String url) {
    try {
      if (url.contains('momo.vn')) {
        return _parseMomoUrl(url);
      } else if (url.contains('gateway.zalopay.vn')) {
        return _parseZaloPayUrl(url);
      } else if (url.contains('airpay.vn')) {
        return _parseAirPayUrl(url);
      }
      return null;
    } catch (e) {
      print('❌ Failed to parse payment URL: $e');
      return null;
    }
  }
  
  static PaymentInfo _parseMomoUrl(String url) {
    final uri = Uri.parse(url);
    return PaymentInfo(
      provider: PaymentProvider.momo,
      orderId: uri.queryParameters['orderId'] ?? '',
      amount: uri.queryParameters['amount'] ?? '',
      description: uri.queryParameters['description'] ?? '',
      additionalData: {
        'partnerCode': uri.queryParameters['partnerCode'] ?? '',
        'requestId': uri.queryParameters['requestId'] ?? '',
      },
    );
  }
  
  static PaymentInfo _parseZaloPayUrl(String url) {
    final uri = Uri.parse(url);
    return PaymentInfo(
      provider: PaymentProvider.zaloPay,
      orderId: uri.queryParameters['appTransId'] ?? '',
      amount: uri.queryParameters['amount'] ?? '',
      description: uri.queryParameters['description'] ?? '',
      additionalData: {
        'appId': uri.queryParameters['appId'] ?? '',
        'pmcId': uri.queryParameters['pmcId'] ?? '',
      },
    );
  }
  
  static PaymentInfo _parseAirPayUrl(String url) {
    final uri = Uri.parse(url);
    return PaymentInfo(
      provider: PaymentProvider.airPay,
      orderId: uri.queryParameters['order_id'] ?? '',
      amount: uri.queryParameters['amount'] ?? '',
      description: uri.queryParameters['description'] ?? '',
      additionalData: {},
    );
  }
}

class PaymentInfo {
  final PaymentProvider provider;
  final String orderId;
  final String amount;
  final String description;
  final Map<String, String> additionalData;
  
  PaymentInfo({
    required this.provider,
    required this.orderId,
    required this.amount,
    required this.description,
    required this.additionalData,
  });
}

enum PaymentProvider {
  momo,
  zaloPay,
  airPay,
}
```

### **4. Payment Result Models**

```dart
// lib/models/payment_result.dart

class PaymentResult {
  final PaymentStatus status;
  final PaymentProvider provider;
  final String orderId;
  final String? transactionId;
  final String? errorMessage;
  final Map<String, dynamic> rawData;
  
  PaymentResult({
    required this.status,
    required this.provider,
    required this.orderId,
    this.transactionId,
    this.errorMessage,
    this.rawData = const {},
  });
  
  factory PaymentResult.success({
    required PaymentProvider provider,
    required String orderId,
    required String transactionId,
    Map<String, dynamic> rawData = const {},
  }) {
    return PaymentResult(
      status: PaymentStatus.success,
      provider: provider,
      orderId: orderId,
      transactionId: transactionId,
      rawData: rawData,
    );
  }
  
  factory PaymentResult.failed({
    required PaymentProvider provider,
    required String orderId,
    required String errorMessage,
    Map<String, dynamic> rawData = const {},
  }) {
    return PaymentResult(
      status: PaymentStatus.failed,
      provider: provider,
      orderId: orderId,
      errorMessage: errorMessage,
      rawData: rawData,
    );
  }
  
  factory PaymentResult.cancelled({
    required PaymentProvider provider,
    required String orderId,
  }) {
    return PaymentResult(
      status: PaymentStatus.cancelled,
      provider: provider,
      orderId: orderId,
      errorMessage: 'Payment cancelled by user',
    );
  }
}

enum PaymentStatus {
  success,
  failed,
  cancelled,
  pending,
}
```

## 🧪 **Testing Implementation**

### **Unit Tests**

```dart
// test/services/payment_url_parser_test.dart

void main() {
  group('PaymentUrlParser', () {
    test('should parse Momo URL correctly', () {
      const url = 'https://payment.momo.vn/pay?orderId=123&amount=50000';
      final result = PaymentUrlParser.tryParse(url);
      
      expect(result, isNotNull);
      expect(result!.provider, PaymentProvider.momo);
      expect(result.orderId, '123');
      expect(result.amount, '50000');
    });
    
    test('should parse ZaloPay URL correctly', () {
      const url = 'https://gateway.zalopay.vn/pay?appTransId=456&amount=75000';
      final result = PaymentUrlParser.tryParse(url);
      
      expect(result, isNotNull);
      expect(result!.provider, PaymentProvider.zaloPay);
      expect(result.orderId, '456');
      expect(result.amount, '75000');
    });
    
    test('should return null for non-payment URLs', () {
      const url = 'https://google.com';
      final result = PaymentUrlParser.tryParse(url);
      
      expect(result, isNull);
    });
  });
}
```

### **Integration Tests**

```dart
// integration_test/direct_payment_test.dart

void main() {
  group('Direct Payment Integration', () {
    testWidgets('should launch Momo app when available', (tester) async {
      // Mock Momo app availability
      when(mockUrlLauncher.canLaunchUrl(any)).thenAnswer((_) async => true);
      
      final paymentInfo = PaymentInfo(
        provider: PaymentProvider.momo,
        orderId: 'test123',
        amount: '50000',
        description: 'Test payment',
        additionalData: {},
      );
      
      final result = await DirectPaymentService.launchDirectPayment(
        provider: PaymentProvider.momo,
        paymentInfo: paymentInfo,
      );
      
      expect(result, isTrue);
      verify(mockUrlLauncher.launchUrl(any)).called(1);
    });
    
    testWidgets('should fallback to web when app not available', (tester) async {
      // Mock Momo app not available
      when(mockUrlLauncher.canLaunchUrl(any)).thenAnswer((_) async => false);
      
      final paymentInfo = PaymentInfo(
        provider: PaymentProvider.momo,
        orderId: 'test123',
        amount: '50000',
        description: 'Test payment',
        additionalData: {},
      );
      
      final result = await DirectPaymentService.launchDirectPayment(
        provider: PaymentProvider.momo,
        paymentInfo: paymentInfo,
      );
      
      expect(result, isFalse);
      verifyNever(mockUrlLauncher.launchUrl(any));
    });
  });
}
```

## 🚀 **Deployment Strategy**

### **Feature Flag Configuration**

```dart
// lib/config/feature_flags.dart

class FeatureFlags {
  static bool get directPaymentEnabled => 
      const bool.fromEnvironment('DIRECT_PAYMENT', defaultValue: false);
  
  static bool get directPaymentMomo => 
      const bool.fromEnvironment('DIRECT_PAYMENT_MOMO', defaultValue: false);
  
  static bool get directPaymentZaloPay => 
      const bool.fromEnvironment('DIRECT_PAYMENT_ZALOPAY', defaultValue: false);
  
  static double get directPaymentRolloutPercentage => 
      const double.fromEnvironment('DIRECT_PAYMENT_ROLLOUT', defaultValue: 0.0);
}
```

### **Gradual Rollout**

```bash
# Development
flutter run --dart-define=DIRECT_PAYMENT=true --dart-define=DIRECT_PAYMENT_ROLLOUT=100.0

# Staging (10% rollout)
flutter build apk --dart-define=DIRECT_PAYMENT=true --dart-define=DIRECT_PAYMENT_ROLLOUT=10.0

# Production (50% rollout)
flutter build apk --dart-define=DIRECT_PAYMENT=true --dart-define=DIRECT_PAYMENT_ROLLOUT=50.0
```

## 📊 **Monitoring & Analytics**

```dart
// lib/services/payment_analytics.dart

class PaymentAnalytics {
  static void trackDirectPaymentAttempt(PaymentProvider provider) {
    Analytics.track('direct_payment_attempt', {
      'provider': provider.name,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }
  
  static void trackDirectPaymentSuccess(PaymentProvider provider, Duration duration) {
    Analytics.track('direct_payment_success', {
      'provider': provider.name,
      'duration_ms': duration.inMilliseconds,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }
  
  static void trackDirectPaymentFallback(PaymentProvider provider, String reason) {
    Analytics.track('direct_payment_fallback', {
      'provider': provider.name,
      'reason': reason,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }
}
```

**Kết luận: Implementation plan hoàn chỉnh và ready để thực hiện!** 🎯
