//
//  JSONMappable.swift
//  Booking
//
//  Created by  baoth<PERSON> on 10/08/2022.
//  Copyright © 2022 ddkc. All rights reserved.
//
import Foundation
import RxSwift
import Moya
import ObjectMapper

// MARK: - Json -> Model
extension Response {

    public func mapObject<T: BaseMappable>(_ type: T.Type) throws -> T {

        guard let object = Mapper<T>().map(JSONObject: try mapJSON()) else {
            throw MoyaError.jsonMapping(self)
        }
        

        return object
    }
    
    

    public func mapObjectArray<T: BaseMappable>(_ type: T.Type) throws -> [T] {

        guard let array = try mapJSON() as? [String: Any] else {
            throw MoyaError.jsonMapping(self)
        }
        
  
        guard let anchors = array["message"] as? [String: Any] else {
            throw MoyaError.jsonMapping(self)
        }
        
        guard let dictArr = anchors["anchors"] as? [[String: Any]] else {
            throw MoyaError.jsonMapping(self)
        }
        
        return Mapper<T>().mapArray(JSONArray: dictArr)
    }
}
extension ObservableType where E == Response {
    public func mapObject<T: BaseMappable>(_ type: T.Type) -> Observable<T> {
        return flatMap({ (response) -> Observable<T> in
            return Observable.just(try response.mapObject(T.self))
        })
    }
    
    public func mapObjectArray<T: BaseMappable>(_ type: T.Type) -> Observable<[T]> {
        return flatMap({ (response) -> Observable<[T]> in
            return Observable.just(try response.mapObjectArray(T.self))
        })
    }
}
