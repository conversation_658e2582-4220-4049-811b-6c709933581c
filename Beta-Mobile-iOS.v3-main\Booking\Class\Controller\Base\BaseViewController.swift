//
//  BaseViewController.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 3/10/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit
import RxSwift
import PKHUD
import IQ<PERSON>eyboardManagerSwift
import AlamofireImage
import PopupDialog

class BaseViewController: UIViewController, NavigationItemConfigurable {
    let disposeBag = DisposeBag()

    var localizableTitle: String? {
        didSet {
            if let text = localizableTitle {
                titleLabel?.text = text.localized
            }
        }
    }
    internal var titleLabel: UILabel?

    var isFromRouter: Bool = false

    override func viewDidLoad() {
        super.viewDidLoad()

        if isFromRouter {
            self.navigationController?.isNavigationBarHidden = false
        }

        self.navigationController?.setTransparent(false)
        self.navigationController?.navigationBar.topItem?.title = ""

        createTitleLabel()

        NotificationCenter.default.addObserver(self, selector: #selector(localizationDidChange), name: .ChangeLocalization, object: nil)
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)

        var frame = self.titleLabel!.frame
        if UIDevice.current.systemVersion.compare("11", options: String.CompareOptions.numeric) == .orderedAscending {
            if navigationController?.viewControllers.first == self {
                frame.origin.x = 0
            } else {
                frame.origin.x = -30
            }
            self.titleLabel?.frame = frame
        }

        if isShowTabbar() {
            guard let tabbarVC = AppDelegate.shared.tabbarVC else {
                return
            }
            tabbarVC.My_tabBar.isHidden = false
        }
        AppDelegate.shared.tabbarVC?.tabBar.isHidden = true
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        self.navigationController?.navigationBar.isTranslucent = isBarTransparent
        IQKeyboardManager.shared.enable = true
    }

    override func viewDidDisappear(_ animated: Bool) {
        super.viewDidDisappear(animated)
        IQKeyboardManager.shared.enable = true
    }

    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        IQKeyboardManager.shared.enable = false
        if isShowTabbar() {
            guard let tabbarVC = AppDelegate.shared.tabbarVC else {
                return
            }
            tabbarVC.My_tabBar.isHidden = true
        }
    }

    override var preferredStatusBarStyle: UIStatusBarStyle {
        return .lightContent
    }

    override func willMove(toParentViewController parent: UIViewController?) {
        super.willMove(toParentViewController: parent)
        if parent == nil {
            NotificationCenter.default.removeObserver(self)
        }
    }

    override func didRotate(from fromInterfaceOrientation: UIInterfaceOrientation) {
        self.navigationController?.setTransparent(isBarTransparent)
    }

    private func isShowTabbar() -> Bool {
        let parentViewControllersClass = [
            NewHomeViewController.self,
            CinemasViewController.self,
            MyVoucherViewController.self,
            NewsAndDealsViewController.self,
            TabOtherViewController.self
        ]
        return parentViewControllersClass.contains(where: { type(of: self) == $0 })
    }

    func createTitleLabel() {
        var frame = CGRect(x: 0, y: 0, width: self.view.bounds.width, height: 30)
        let view = UIView(frame: frame)
        if UIDevice.current.systemVersion.compare("11", options: String.CompareOptions.numeric) == .orderedAscending {
            frame.origin.x = -30
        }
        let label = UILabel(frame: frame)
        label.text = title
        label.font = UIFont(fontName: .SourceSansPro, style: .Bold, size: 20)
        label.textColor = .white
        label.textAlignment = .left
        label.autoresizingMask = [.flexibleWidth, .flexibleHeight]
        view.addSubview(label)
        self.navigationItem.titleView = view
        label.frame = frame
        self.titleLabel = label
    }

    func setTitle(_ title: String?) {
        self.titleLabel?.text = title
    }
    
    func showLoading(){
        DispatchQueue.main.async {
            HUD.show(.rotatingImage(UIImage(named: "progress")))
        }
    }
    
    func dismissLoading(){
        DispatchQueue.main.async {
            PKHUD.sharedHUD.hide()
        }
    }
    
    func flashSuccess(){
        DispatchQueue.main.async {
            HUD.show(.labeledSuccess(title: "Alert.Success".localized, subtitle: nil))
        }
        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
            HUD.hide()
        }
    }
    
    func flashError(title: String = "", message: String = ""){
        DispatchQueue.main.async {
            HUD.flash(.labeledError(title: title, subtitle: message), delay: 3.0)
        }
    }

    func showCustomizeAlert(type: MyAlertType, okAction: @escaping () -> Void) {
        let customAlert = CustomizeAlert(type: type)
        customAlert.providesPresentationContextTransitionStyle = true
        customAlert.definesPresentationContext = true
        customAlert.modalPresentationStyle = UIModalPresentationStyle.overCurrentContext
        customAlert.modalTransitionStyle = UIModalTransitionStyle.crossDissolve
        customAlert.okHandler = okAction
        self.present(customAlert, animated: true, completion: nil)
    }
    
    func handlerResponse<T>(_ response: DDKCResponse<T>, success: @escaping()->Void, error: (() -> Void)? = nil){
        if response.isSuccess() {
            success()
        }else{
            error?()
            UIAlertController.showAlert(self, message: response.Message ?? "")
        }
    }
    
    func handlerNetworkResponse<T>(_ response: DDKCResult<T>?, error: Error?, success: @escaping(DDKCResult<T>)->Void){
        if let error = error{
            UIAlertController.showAlert(self, message: error.localizedDescription)
            return
        }
        guard let response = response else {
            UIAlertController.showAlert(self, message: "Request timeout")
            return
        }
        if response.isSuccess {
            success(response)
        }else{
            UIAlertController.showAlert(self, message: response.Message ?? "")
        }
    }

    func showRightMenuButton() {
        addRightButton(#imageLiteral(resourceName: "icMenubuger"))
    }

    func rightButtonPressed(_ sender: UIBarButtonItem) {
        presentRightMenuViewController()
        let slideMenu = sideMenuViewController?.rightMenuViewController as? SlideMenuViewController
        slideMenu?.getUnreadNotification()
    }

    func leftButtonPressed(_ sender: UIBarButtonItem) {
        navigationController?.popViewController(animated: true)
    }

    func showAlert(title: String? = nil, message: String? = nil, action: ((UIAlertAction)->Void)? = nil) {
        UIAlertController.showAlert(self, message: message ?? "", handler: action)
    }

    func share(data: [Any]? = nil) {
        guard let data = data else {
            return
        }
        let vc = UIActivityViewController(activityItems: data, applicationActivities: nil)
        present(vc, animated: true, completion: nil)
    }

    func show(_ vc: UIViewController) {
        show(vc, sender: nil)
    }

    @objc func localizationDidChange(){
        // Todo change text
        view.updateLocalizableAllSubView()
        if let text = localizableTitle {
            titleLabel?.text = text.localized
        }
    }

    func updateNotifications(_ needReset: Bool = false) {
        let slideMenu = sideMenuViewController?.rightMenuViewController as? SlideMenuViewController
        if needReset {
            slideMenu?.notificationCount = 0
        }
        let notifcationCount = slideMenu?.notificationCount ?? 0
        addRightButton(notifcationCount > 0 ? #imageLiteral(resourceName: "icMenubugerRed").withRenderingMode(.alwaysOriginal) : #imageLiteral(resourceName: "icMenubuger"))
    }
}

@objc protocol NavigationItemConfigurable: class {
    func rightButtonPressed(_ sender: UIBarButtonItem)
    func leftButtonPressed(_ sender: UIBarButtonItem)
}

extension NavigationItemConfigurable {
    func rightButtonPressed(_ sender: UIBarButtonItem) {}
    func leftButtonPressed(_ sender: UIBarButtonItem) {}
}

extension NavigationItemConfigurable where Self: UIViewController {
    func addRightButton(_ image: UIImage, action: Selector? = nil) {
        let button = UIBarButtonItem(image: image, style: .plain, target: self, action: action ?? #selector(self.rightButtonPressed(_:)))
        navigationItem.rightBarButtonItem = button
    }

    func addLeftButton(_ image: UIImage, action: Selector? = nil) {
        let button = UIBarButtonItem(image: image, style: .plain, target: self, action: action ?? #selector(self.leftButtonPressed(_:)))
        navigationItem.leftBarButtonItem = button
    }
    
    func changeLeftButton(_ image: UIImage, title: String, action: Selector? = nil){
        let button = UIButton()
        button.setImage(image, for: .normal)
        button.setTitle(title, for: .normal)
        button.titleLabel?.font = UIFont(fontName: .SourceSansPro, size: 16)
        button.setTitleColor(.white, for: .normal)
        button.contentHorizontalAlignment = .left
        button.addTarget(self, action: action ?? #selector(self.leftButtonPressed(_:)), for: .touchUpInside)
        button.sizeToFit()
        let navButton = UIBarButtonItem(customView: button)
        navigationItem.leftBarButtonItem = navButton
    }

    func changeLeftButton(_ imageUrl: String, placeholderImage: UIImage? = nil, title: String, action: Selector? = nil){
        let button = UIButton()
        if let url = URL(string: imageUrl) {
            button.af_setImage(for: .normal, url: url, placeholderImage: placeholderImage, filter: nil, progress: nil, progressQueue: DispatchQueue.main) { result in
                if let image = result.value?.resize(CGSize(width: 24, height: 24)).af_imageRoundedIntoCircle() {
                    button.setImage(image, for: .normal)
//                    button.imageView?.layer.cornerRadius = 12
//                    button.imageView?.layer.masksToBounds = true
                }
            }
        }
        button.setTitle(title, for: .normal)
        button.titleLabel?.font = UIFont(fontName: .SourceSansPro, size: 16)
        button.setTitleColor(.white, for: .normal)
        button.contentHorizontalAlignment = .left
        button.addTarget(self, action: action ?? #selector(self.leftButtonPressed(_:)), for: .touchUpInside)
        button.sizeToFit()
        let navButton = UIBarButtonItem(customView: button)
        navigationItem.leftBarButtonItem = navButton
    }
}


// popup
extension UIViewController {

    @discardableResult func createPopup(_ viewController: UIViewController, width: CGFloat? = nil, height: CGFloat? = nil, gestureDismissal: Bool = true) -> PopupDialog {
        let w = width ?? self.view.frame.width - 16
        let popup = PopupDialog(viewController: viewController, preferredWidth: w, tapGestureDismissal: gestureDismissal)
        if let height = height {
            let constraint = NSLayoutConstraint(item: viewController.view, attribute: .height, relatedBy: .equal, toItem: nil, attribute: .height, multiplier: 1, constant: height)
            constraint.isActive = true
        }

//        self.present(popup, animated: true, completion: nil)
        return popup
    }

    func showPopup(_ viewController: UIViewController, width: CGFloat? = nil, height: CGFloat? = nil) {
        let popup = createPopup(viewController, width: width, height: height)
        self.present(popup, animated: true, completion: nil)
    }
}
