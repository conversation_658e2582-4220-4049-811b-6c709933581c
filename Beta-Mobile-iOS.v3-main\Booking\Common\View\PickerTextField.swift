//
//  PickerTextField.swift
//  Booking-dev
//
//  Created by <PERSON><PERSON> on 4/7/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit
import IQKeyboardManagerSwift

class PickerTextField: InputTextField {
    fileprivate var pickerView: UIPickerView?
    
    var listItem: [String]? {
        didSet {
            if let index = listItem?.index(where: {$0 == self.text}), pickerView?.selectedRow(inComponent: 0) != index {
                pickerView?.selectRow(index, inComponent: 0, animated: false)
            }
        }
    }
    var didSelectItemAtIndex: ((Int) -> ())?
    override var text: String? {
        didSet {
            if let index = listItem?.index(where: { $0 == text }) {
                pickerView?.selectRow(index, inComponent: 0, animated: false)
            }
        }
    }

    override func setup() {
        super.setup()

        let rightImageView = UIImageView(frame: CGRect(x: 0, y: 0, width: 12, height: 6))
        rightImageView.image = #imageLiteral(resourceName: "ic_arrow_down")
        rightImageView.contentMode = .scaleAspectFit
        rightImageView.autoresizingMask = [.flexibleTopMargin, .flexibleBottomMargin, .flexibleRightMargin]
        let righView = UIView(frame: CGRect(x: 0, y: 0, width: 32, height: 6))
        righView.addSubview(rightImageView)
        self.rightView = righView
        self.rightViewMode = .always

        pickerView = UIPickerView()
        pickerView?.dataSource = self
        pickerView?.delegate = self
        self.inputView = pickerView

//        disable paste, cut
        isShowCursor = false
        isEnableMenu = false

        addDoneOnKeyboardWithTarget(nil, action: #selector(self.doneButtonPressed(_:)))
        addPreviousNextDoneOnKeyboardWithTarget(self, previousAction: #selector(previousButtonPressed(_:)), nextAction: #selector(nextButtonPressed(_:)), doneAction: #selector(doneButtonPressed(_:)), shouldShowPlaceholder: true)

        let toolbar = keyboardToolbar
        switch self.keyboardAppearance {
        case UIKeyboardAppearance.dark:
            toolbar.barStyle = UIBarStyle.black
            toolbar.tintColor = UIColor.white
            toolbar.barTintColor = nil
        default:
            toolbar.barStyle = UIBarStyle.default
            toolbar.barTintColor = IQKeyboardManager.shared.toolbarBarTintColor

            //Setting toolbar tintColor //  (Enhancement ID: #30)
            if IQKeyboardManager.shared.shouldToolbarUsesTextFieldTintColor {
                toolbar.tintColor = self.tintColor
            } else if let tintColor = IQKeyboardManager.shared.toolbarBarTintColor {
                toolbar.tintColor = tintColor
            } else {
                toolbar.tintColor = UIColor.black
            }
        }

        NotificationCenter.default.addObserver(self, selector: #selector(textFieldDidBeginEditing(_:)), name: Notification.Name.UITextFieldTextDidBeginEditing, object: nil)
    }

    @objc func doneButtonPressed(_ sender: Any) {
        guard let index = pickerView?.selectedRow(inComponent: 0), let items = self.listItem else {
            resignFirstResponder()
            return
        }
        if !items.isEmpty {
            self.text = listItem?[index]
            didSelectItemAtIndex?(index)
        }
        resignFirstResponder()
    }

    @objc func previousButtonPressed(_ sender: Any) {
        IQKeyboardManager.shared.goPrevious()
    }

    @objc func nextButtonPressed(_ sender: Any) {
        IQKeyboardManager.shared.goNext()
    }

    @objc func textFieldDidBeginEditing(_ aNoti: Notification) {
        self.toolbarPlaceholder = self.placeholder
    }
}

extension PickerTextField: UIPickerViewDataSource {
    func numberOfComponents(in pickerView: UIPickerView) -> Int {
        return 1
    }

    func pickerView(_ pickerView: UIPickerView, numberOfRowsInComponent component: Int) -> Int {
        return listItem?.count ?? 0
    }

    func pickerView(_ pickerView: UIPickerView, titleForRow row: Int, forComponent component: Int) -> String? {
        return listItem?[row]
    }
}

extension PickerTextField: UIPickerViewDelegate {
    func pickerView(_ pickerView: UIPickerView, didSelectRow row: Int, inComponent component: Int) {
        guard let items = self.listItem, items.count > 0 else {return}
        self.text = items[row]
        if let didSelectItem = self.didSelectItemAtIndex{
            didSelectItem(row)
        }
    }
}
