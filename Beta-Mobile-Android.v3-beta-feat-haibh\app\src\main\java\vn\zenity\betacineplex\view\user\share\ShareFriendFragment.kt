package vn.zenity.betacineplex.view.user.share

import android.os.Bundle
import androidx.core.content.res.ResourcesCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import kotlinx.android.synthetic.main.fragment_coupon.*
import kotlinx.android.synthetic.main.item_share_friend_header.view.*
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.base.BaseFragment
import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.global.Constant
import vn.zenity.betacineplex.global.Global
import vn.zenity.betacineplex.helper.extension.*
import vn.zenity.betacineplex.view.HomeActivity

/**
 * Created by Zenity.
 */

class ShareFriendFragment : BaseFragment(), ShareFriendContractor.View {

    private var requestCode: String? = null

    override fun registerCodeSuccess(message: String) {
        if (!TextUtils.isEmpty(message)) {
            showAlert(message)
        }
        Global.share().user?.ReferenceAccountRefCode = requestCode ?: "123"
        Global.share().user = Global.share().user
        (activity as? HomeActivity)?.getAppParamsViaApi()
        recyclerView?.adapter?.notifyDataSetChanged()
    }

    private val presenter = ShareFriendPresenter()

    override fun getPresenter(): IBasePresenter<IBaseView>? {
        return presenter as? IBasePresenter<IBaseView>
    }

    override fun isShowToolbar(): Boolean {
        return false
    }

    override fun getLayoutRes(): Int {
        return R.layout.fragment_coupon
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        recyclerView?.layoutManager = LinearLayoutManager(context)
        recyclerView?.adapter = Adapter()
        tvActionBarTitle.text = getString(R.string.share_friends)
        btnMenu.gone()
    }

    private inner class Adapter : RecyclerView.Adapter<Holder>() {

        override fun getItemCount(): Int {
            return 1
        }

        override fun onBindViewHolder(holder: Holder, position: Int) {
            holder.itemView.apply {
                tvShareCode.text = Global.share().user?.ReferenceCode
                btnShare.click {
                    activity?.let {
                        Global.share().user?.ReferenceCode?.share("${R.string.app_name.getString()} - ${R.string.share_friends.getString()} ", it)
                    }
                }
                btnConfirm.click {
                    val code = edtInputCode.text.toString()
                    requestCode = code
                    if (TextUtils.isEmpty(code)) {
                        showNotice(context.getString(R.string.you_need_enter_code_first))
                        return@click
                    }
                    presenter.registerCode(code)
                }
                val refCode = Global.share().user?.ReferenceAccountRefCode
                val params = (activity as? HomeActivity)?.appParams?.firstOrNull { it.ParamsCode == Constant.AppParamCode.intro }
                var isEnable = TextUtils.isEmpty(refCode)
                params?.let {
                    tvNoticeVoucherPercent.setTextWithSpecialText(it.ParamsMessage, it.ParamsMessageHighLight) {
                        it.isUnderlineText = false
                        it.typeface = context.let { it1 -> ResourcesCompat.getFont(it1, R.font.sanspro_bold) }
                        it.color = R.color.textBlack.getColor()
                        it.linkColor = R.color.textBlack.getColor()
                    }
                    isEnable = it.Value == "1" && TextUtils.isEmpty(refCode)
                }
                edtInputCode.isEnabled = isEnable
                edtInputCode.setText(refCode)
                btnConfirm.isEnabled = isEnable
                llInputReferenceCode.alpha = if (isEnable) 1f else 0.6f
            }
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): Holder {
            val itemView = LayoutInflater.from(parent.context).inflate(R.layout.item_share_friend_header, parent, false)
            return Holder(itemView)
        }
    }

    private inner class Holder(itemView: View) : RecyclerView.ViewHolder(itemView)
}
