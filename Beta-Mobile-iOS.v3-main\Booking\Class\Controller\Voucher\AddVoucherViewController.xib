<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14490.70" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14490.49"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="Oswald-Regular.ttf">
            <string>Oswald-Regular</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="AddVoucherViewController" customModule="Booking_dev" customModuleProvider="target">
            <connections>
                <outlet property="codeTextField" destination="YTE-Ie-abL" id="cgB-Lj-Hq8"/>
                <outlet property="pinCodeTextfield" destination="yPZ-Nd-bRv" id="uWi-GM-jis"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="OhG-Le-ns4">
                    <rect key="frame" x="0.0" y="44" width="414" height="364"/>
                    <subviews>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Mã Voucher" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="HTP-DM-1ZV" customClass="LocalizableLabel" customModule="Booking_dev" customModuleProvider="target">
                            <rect key="frame" x="20" y="32" width="68.5" height="24"/>
                            <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="16"/>
                            <color key="textColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            <nil key="highlightedColor"/>
                            <userDefinedRuntimeAttributes>
                                <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Voucher.Code"/>
                            </userDefinedRuntimeAttributes>
                        </label>
                        <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="YTE-Ie-abL" customClass="RoundTextField" customModule="Booking_dev" customModuleProvider="target">
                            <rect key="frame" x="20" y="64" width="316" height="50"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="50" id="Ff4-QC-uRn"/>
                            </constraints>
                            <nil key="textColor"/>
                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                            <textInputTraits key="textInputTraits"/>
                            <userDefinedRuntimeAttributes>
                                <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                    <real key="value" value="2"/>
                                </userDefinedRuntimeAttribute>
                                <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                    <color key="value" red="0.58431372550000005" green="0.58431372550000005" blue="0.58431372550000005" alpha="0.59999999999999998" colorSpace="custom" customColorSpace="sRGB"/>
                                </userDefinedRuntimeAttribute>
                                <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                    <real key="value" value="1"/>
                                </userDefinedRuntimeAttribute>
                                <userDefinedRuntimeAttribute type="number" keyPath="horizontalSpace">
                                    <real key="value" value="10"/>
                                </userDefinedRuntimeAttribute>
                            </userDefinedRuntimeAttributes>
                        </textField>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="252" text="Mã PIN" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="sQo-Xa-Flx" customClass="LocalizableLabel" customModule="Booking_dev" customModuleProvider="target">
                            <rect key="frame" x="20" y="130" width="374" height="24"/>
                            <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="16"/>
                            <color key="textColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            <nil key="highlightedColor"/>
                            <userDefinedRuntimeAttributes>
                                <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Voucher.PIN"/>
                            </userDefinedRuntimeAttributes>
                        </label>
                        <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="yPZ-Nd-bRv" customClass="RoundTextField" customModule="Booking_dev" customModuleProvider="target">
                            <rect key="frame" x="20" y="162" width="374" height="50"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="50" id="gLZ-0v-eg8"/>
                            </constraints>
                            <nil key="textColor"/>
                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                            <textInputTraits key="textInputTraits"/>
                            <userDefinedRuntimeAttributes>
                                <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                    <real key="value" value="2"/>
                                </userDefinedRuntimeAttribute>
                                <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                    <color key="value" red="0.58431372550000005" green="0.58431372550000005" blue="0.58431372550000005" alpha="0.59999999999999998" colorSpace="custom" customColorSpace="sRGB"/>
                                </userDefinedRuntimeAttribute>
                                <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                    <real key="value" value="1"/>
                                </userDefinedRuntimeAttribute>
                                <userDefinedRuntimeAttribute type="number" keyPath="horizontalSpace">
                                    <real key="value" value="10"/>
                                </userDefinedRuntimeAttribute>
                            </userDefinedRuntimeAttributes>
                        </textField>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="HtL-FK-Ezx" customClass="RoundView" customModule="Booking_dev" customModuleProvider="target">
                            <rect key="frame" x="20" y="236" width="374" height="56"/>
                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <userDefinedRuntimeAttributes>
                                <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                    <real key="value" value="4"/>
                                </userDefinedRuntimeAttribute>
                                <userDefinedRuntimeAttribute type="color" keyPath="shadowColor">
                                    <color key="value" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                </userDefinedRuntimeAttribute>
                                <userDefinedRuntimeAttribute type="number" keyPath="shadowOpacity">
                                    <real key="value" value="0.20000000000000001"/>
                                </userDefinedRuntimeAttribute>
                                <userDefinedRuntimeAttribute type="point" keyPath="shadowOffset">
                                    <point key="value" x="0.0" y="6"/>
                                </userDefinedRuntimeAttribute>
                                <userDefinedRuntimeAttribute type="number" keyPath="shadowRadius">
                                    <real key="value" value="4"/>
                                </userDefinedRuntimeAttribute>
                            </userDefinedRuntimeAttributes>
                        </view>
                        <button opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="sEy-Fa-5IN" customClass="GradientButton" customModule="Booking_dev" customModuleProvider="target">
                            <rect key="frame" x="20" y="236" width="374" height="56"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="56" id="nhw-rg-3Sj"/>
                            </constraints>
                            <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="20"/>
                            <state key="normal" title="ĐĂNG KÝ">
                                <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </state>
                            <userDefinedRuntimeAttributes>
                                <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                    <real key="value" value="4"/>
                                </userDefinedRuntimeAttribute>
                                <userDefinedRuntimeAttribute type="color" keyPath="firstColor">
                                    <color key="value" red="0.99215686270000003" green="0.15686274510000001" blue="0.0078431372550000003" alpha="0.97999999999999998" colorSpace="custom" customColorSpace="sRGB"/>
                                </userDefinedRuntimeAttribute>
                                <userDefinedRuntimeAttribute type="color" keyPath="secondColor">
                                    <color key="value" red="0.99215686270000003" green="0.48627450979999998" blue="0.0078431372550000003" alpha="0.98999999999999999" colorSpace="custom" customColorSpace="sRGB"/>
                                </userDefinedRuntimeAttribute>
                                <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="add_new"/>
                            </userDefinedRuntimeAttributes>
                            <connections>
                                <action selector="addTapped:" destination="-1" eventType="touchUpInside" id="7rU-cD-un5"/>
                            </connections>
                        </button>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="IAV-4w-JCj" customClass="RoundView" customModule="Booking_dev" customModuleProvider="target">
                            <rect key="frame" x="344" y="64" width="50" height="50"/>
                            <subviews>
                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="BZY-2j-zx8">
                                    <rect key="frame" x="8" y="8" width="34" height="34"/>
                                    <state key="normal" image="ic_scan"/>
                                    <connections>
                                        <action selector="scanTapped:" destination="-1" eventType="touchUpInside" id="8jI-L8-513"/>
                                    </connections>
                                </button>
                            </subviews>
                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <constraints>
                                <constraint firstAttribute="width" secondItem="IAV-4w-JCj" secondAttribute="height" id="BEx-MI-bcu"/>
                                <constraint firstAttribute="bottom" secondItem="BZY-2j-zx8" secondAttribute="bottom" constant="8" id="EKr-PE-Z7k"/>
                                <constraint firstAttribute="trailing" secondItem="BZY-2j-zx8" secondAttribute="trailing" constant="8" id="I0E-bg-yvb"/>
                                <constraint firstItem="BZY-2j-zx8" firstAttribute="leading" secondItem="IAV-4w-JCj" secondAttribute="leading" constant="8" id="dwA-AI-bxH"/>
                                <constraint firstItem="BZY-2j-zx8" firstAttribute="top" secondItem="IAV-4w-JCj" secondAttribute="top" constant="8" id="lep-q5-rux"/>
                            </constraints>
                            <userDefinedRuntimeAttributes>
                                <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                    <real key="value" value="1"/>
                                </userDefinedRuntimeAttribute>
                                <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                    <real key="value" value="3"/>
                                </userDefinedRuntimeAttribute>
                                <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                    <color key="value" red="0.01176470588" green="0.34901960780000002" blue="0.61568627450000002" alpha="1" colorSpace="calibratedRGB"/>
                                </userDefinedRuntimeAttribute>
                            </userDefinedRuntimeAttributes>
                        </view>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="364" id="2E4-kB-lbL"/>
                        <constraint firstItem="sEy-Fa-5IN" firstAttribute="trailing" secondItem="HtL-FK-Ezx" secondAttribute="trailing" id="9pQ-Ho-nVB"/>
                        <constraint firstItem="sQo-Xa-Flx" firstAttribute="leading" secondItem="OhG-Le-ns4" secondAttribute="leading" constant="20" id="Ad9-qO-vRA"/>
                        <constraint firstItem="sEy-Fa-5IN" firstAttribute="top" secondItem="yPZ-Nd-bRv" secondAttribute="bottom" constant="24" id="BLx-Hc-HT0"/>
                        <constraint firstItem="HTP-DM-1ZV" firstAttribute="top" secondItem="OhG-Le-ns4" secondAttribute="top" constant="32" id="Ceg-2q-qWR"/>
                        <constraint firstItem="sEy-Fa-5IN" firstAttribute="top" secondItem="HtL-FK-Ezx" secondAttribute="top" id="Ge7-xd-f5a"/>
                        <constraint firstItem="YTE-Ie-abL" firstAttribute="leading" secondItem="OhG-Le-ns4" secondAttribute="leading" constant="20" id="Ht9-Ni-yoC"/>
                        <constraint firstAttribute="trailing" secondItem="sEy-Fa-5IN" secondAttribute="trailing" constant="20" id="KGg-vs-ihe"/>
                        <constraint firstAttribute="trailing" secondItem="yPZ-Nd-bRv" secondAttribute="trailing" constant="20" id="Ozh-9x-Fef"/>
                        <constraint firstItem="sEy-Fa-5IN" firstAttribute="leading" secondItem="OhG-Le-ns4" secondAttribute="leading" constant="20" id="Wo6-UJ-S3c"/>
                        <constraint firstItem="sQo-Xa-Flx" firstAttribute="top" secondItem="YTE-Ie-abL" secondAttribute="bottom" constant="16" id="Zvp-Af-Fs1"/>
                        <constraint firstItem="HTP-DM-1ZV" firstAttribute="leading" secondItem="OhG-Le-ns4" secondAttribute="leading" constant="20" id="bW7-gR-FUt"/>
                        <constraint firstItem="YTE-Ie-abL" firstAttribute="top" secondItem="HTP-DM-1ZV" secondAttribute="bottom" constant="8" id="dBH-Xo-wZo"/>
                        <constraint firstItem="yPZ-Nd-bRv" firstAttribute="top" secondItem="sQo-Xa-Flx" secondAttribute="bottom" constant="8" id="eEm-th-sMz"/>
                        <constraint firstItem="yPZ-Nd-bRv" firstAttribute="top" secondItem="sQo-Xa-Flx" secondAttribute="bottom" constant="8" id="eUW-YL-9lH"/>
                        <constraint firstItem="IAV-4w-JCj" firstAttribute="leading" secondItem="YTE-Ie-abL" secondAttribute="trailing" constant="8" id="gj6-jM-QFG"/>
                        <constraint firstItem="sEy-Fa-5IN" firstAttribute="bottom" secondItem="HtL-FK-Ezx" secondAttribute="bottom" id="gsy-JB-rcg"/>
                        <constraint firstItem="sEy-Fa-5IN" firstAttribute="leading" secondItem="HtL-FK-Ezx" secondAttribute="leading" id="hLP-nY-8Yg"/>
                        <constraint firstAttribute="trailing" secondItem="IAV-4w-JCj" secondAttribute="trailing" constant="20" id="iwC-Xw-o1Y"/>
                        <constraint firstItem="yPZ-Nd-bRv" firstAttribute="leading" secondItem="OhG-Le-ns4" secondAttribute="leading" constant="20" id="qF7-uB-QQD"/>
                        <constraint firstAttribute="trailing" secondItem="sQo-Xa-Flx" secondAttribute="trailing" constant="20" id="ql9-bH-cJg"/>
                        <constraint firstItem="IAV-4w-JCj" firstAttribute="centerY" secondItem="YTE-Ie-abL" secondAttribute="centerY" id="uOy-92-S3g"/>
                        <constraint firstItem="IAV-4w-JCj" firstAttribute="height" secondItem="YTE-Ie-abL" secondAttribute="height" id="whs-z6-FyQ"/>
                    </constraints>
                </view>
            </subviews>
            <color key="backgroundColor" red="0.95294117649999999" green="0.95294117649999999" blue="0.95294117649999999" alpha="1" colorSpace="calibratedRGB"/>
            <constraints>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="OhG-Le-ns4" secondAttribute="trailing" id="Klr-dF-daF"/>
                <constraint firstItem="OhG-Le-ns4" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" id="fMv-Oj-UNJ"/>
                <constraint firstItem="OhG-Le-ns4" firstAttribute="top" secondItem="fnl-2z-Ty3" secondAttribute="top" id="j6W-Go-RuR"/>
            </constraints>
            <viewLayoutGuide key="safeArea" id="fnl-2z-Ty3"/>
            <point key="canvasLocation" x="34.782608695652179" y="34.821428571428569"/>
        </view>
    </objects>
    <resources>
        <image name="ic_scan" width="26" height="26"/>
    </resources>
</document>
