<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14490.70" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14490.49"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="Oswald-Bold.ttf">
            <string>Oswald-Bold</string>
        </array>
        <array key="SourceSansPro-Bold.ttf">
            <string>SourceSansPro-Bold</string>
        </array>
        <array key="SourceSansPro-Regular.ttf">
            <string>SourceSansPro-Regular</string>
        </array>
        <array key="SourceSansPro-SemiBold.ttf">
            <string>SourceSansPro-SemiBold</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" rowHeight="90" id="KGk-i7-Jjw" customClass="HistoryPointTableViewCell" customModule="Booking_dev" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="320" height="90"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="89.5"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="wGF-8U-U6d">
                        <rect key="frame" x="8" y="8" width="304" height="73.5"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="47C-LP-bXP" customClass="RoundView" customModule="Booking_dev" customModuleProvider="target">
                                <rect key="frame" x="0.0" y="0.0" width="114" height="73.5"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="05/07/2019, 14:00" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="QEv-33-aBT">
                                        <rect key="frame" x="3.5" y="8" width="107" height="18"/>
                                        <fontDescription key="fontDescription" name="SourceSansPro-SemiBold" family="Source Sans Pro" pointSize="14"/>
                                        <color key="textColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="calibratedRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="7d7-Tw-1wf">
                                        <rect key="frame" x="0.0" y="34" width="114" height="39.5"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="ĐÃ TẶNG" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="e1n-ib-HGd">
                                                <rect key="frame" x="0.0" y="0.0" width="114" height="18"/>
                                                <fontDescription key="fontDescription" name="SourceSansPro-Bold" family="Source Sans Pro" pointSize="14"/>
                                                <color key="textColor" red="0.99215686270000003" green="0.48627450979999998" blue="0.0078431372550000003" alpha="1" colorSpace="calibratedRGB"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="(Nguyen Kim Thi)" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="IKC-sf-dFT">
                                                <rect key="frame" x="0.0" y="20" width="114" height="15.5"/>
                                                <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="12"/>
                                                <color key="textColor" red="0.070588235289999995" green="0.1215686275" blue="0.15686274510000001" alpha="1" colorSpace="calibratedRGB"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="trailing" secondItem="e1n-ib-HGd" secondAttribute="trailing" id="8OL-jE-Mht"/>
                                            <constraint firstItem="IKC-sf-dFT" firstAttribute="top" secondItem="e1n-ib-HGd" secondAttribute="bottom" constant="2" id="DJl-1c-yKB"/>
                                            <constraint firstItem="e1n-ib-HGd" firstAttribute="leading" secondItem="7d7-Tw-1wf" secondAttribute="leading" id="Mkd-cY-EBJ"/>
                                            <constraint firstItem="e1n-ib-HGd" firstAttribute="top" secondItem="7d7-Tw-1wf" secondAttribute="top" id="RdU-aM-6ZD"/>
                                            <constraint firstAttribute="trailing" secondItem="IKC-sf-dFT" secondAttribute="trailing" id="W7E-s4-801"/>
                                            <constraint firstItem="IKC-sf-dFT" firstAttribute="leading" secondItem="7d7-Tw-1wf" secondAttribute="leading" id="bha-tP-omS"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="trailing" secondItem="7d7-Tw-1wf" secondAttribute="trailing" id="QMF-XH-gHz"/>
                                    <constraint firstItem="7d7-Tw-1wf" firstAttribute="leading" secondItem="47C-LP-bXP" secondAttribute="leading" id="SHk-Xg-izW"/>
                                    <constraint firstItem="QEv-33-aBT" firstAttribute="centerY" secondItem="47C-LP-bXP" secondAttribute="centerY" constant="-20" id="VsJ-bP-NXP"/>
                                    <constraint firstAttribute="bottom" secondItem="7d7-Tw-1wf" secondAttribute="bottom" id="fEa-If-V6d"/>
                                    <constraint firstItem="QEv-33-aBT" firstAttribute="centerX" secondItem="47C-LP-bXP" secondAttribute="centerX" id="geb-Ch-8GF"/>
                                    <constraint firstItem="7d7-Tw-1wf" firstAttribute="top" secondItem="QEv-33-aBT" secondAttribute="bottom" constant="8" id="iOx-XR-sN3"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                        <real key="value" value="5"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="lXF-Sy-k7Y" customClass="RoundView" customModule="Booking_dev" customModuleProvider="target">
                                <rect key="frame" x="118" y="0.0" width="186" height="73.5"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="+50" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="AwG-zb-brS">
                                        <rect key="frame" x="16" y="19" width="36.5" height="36"/>
                                        <fontDescription key="fontDescription" name="Oswald-Bold" family="Oswald" pointSize="24"/>
                                        <color key="textColor" red="0.11764705882352941" green="0.12156862745098039" blue="0.15686274509803921" alpha="1" colorSpace="calibratedRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="AwG-zb-brS" firstAttribute="centerY" secondItem="lXF-Sy-k7Y" secondAttribute="centerY" id="0EH-YF-6Fu"/>
                                    <constraint firstItem="AwG-zb-brS" firstAttribute="leading" secondItem="lXF-Sy-k7Y" secondAttribute="leading" constant="16" id="Vrj-C2-Bbd"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                        <real key="value" value="5"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="QRE-2l-Fkg" customClass="DashView" customModule="Booking_dev" customModuleProvider="target">
                                <rect key="frame" x="114" y="4" width="4" height="65.5"/>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="4" id="Bdq-DI-Un9"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="dashNumber">
                                        <integer key="value" value="8"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="boolean" keyPath="horizontal" value="NO"/>
                                    <userDefinedRuntimeAttribute type="color" keyPath="dashColor">
                                        <color key="value" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="number" keyPath="dashWidth">
                                        <real key="value" value="4"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </view>
                        </subviews>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="47C-LP-bXP" firstAttribute="width" secondItem="wGF-8U-U6d" secondAttribute="width" multiplier="3:8" id="EYH-Qy-aiW"/>
                            <constraint firstItem="47C-LP-bXP" firstAttribute="leading" secondItem="wGF-8U-U6d" secondAttribute="leading" id="HQj-kk-rvN"/>
                            <constraint firstAttribute="bottom" secondItem="47C-LP-bXP" secondAttribute="bottom" id="Jer-GE-sVC"/>
                            <constraint firstItem="QRE-2l-Fkg" firstAttribute="top" secondItem="wGF-8U-U6d" secondAttribute="top" constant="4" id="Nf5-OP-AMD"/>
                            <constraint firstAttribute="bottom" secondItem="lXF-Sy-k7Y" secondAttribute="bottom" id="Q6a-dl-jFb"/>
                            <constraint firstItem="QRE-2l-Fkg" firstAttribute="leading" secondItem="47C-LP-bXP" secondAttribute="trailing" id="Ro7-H3-wEj"/>
                            <constraint firstAttribute="bottom" secondItem="QRE-2l-Fkg" secondAttribute="bottom" constant="4" id="byX-5o-ReJ"/>
                            <constraint firstAttribute="trailing" secondItem="lXF-Sy-k7Y" secondAttribute="trailing" id="cec-EB-LcX"/>
                            <constraint firstItem="lXF-Sy-k7Y" firstAttribute="leading" secondItem="QRE-2l-Fkg" secondAttribute="trailing" id="kJe-hg-S0a"/>
                            <constraint firstItem="47C-LP-bXP" firstAttribute="top" secondItem="wGF-8U-U6d" secondAttribute="top" id="mII-Ua-r1L"/>
                            <constraint firstItem="lXF-Sy-k7Y" firstAttribute="top" secondItem="wGF-8U-U6d" secondAttribute="top" id="vwA-Eu-OMp"/>
                            <constraint firstItem="lXF-Sy-k7Y" firstAttribute="leading" secondItem="QRE-2l-Fkg" secondAttribute="trailing" id="w17-On-GKq"/>
                        </constraints>
                    </view>
                </subviews>
                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                <constraints>
                    <constraint firstItem="wGF-8U-U6d" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" constant="8" id="f0K-GL-Zk9"/>
                    <constraint firstAttribute="bottom" secondItem="wGF-8U-U6d" secondAttribute="bottom" constant="8" id="g3G-wT-EzC"/>
                    <constraint firstAttribute="trailing" secondItem="wGF-8U-U6d" secondAttribute="trailing" constant="8" id="jAM-LW-vQY"/>
                    <constraint firstItem="wGF-8U-U6d" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="8" id="jOJ-3j-pTu"/>
                </constraints>
            </tableViewCellContentView>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <viewLayoutGuide key="safeArea" id="njF-e1-oar"/>
            <connections>
                <outlet property="dateLabel" destination="QEv-33-aBT" id="qhw-OR-NJN"/>
                <outlet property="nameLabel" destination="IKC-sf-dFT" id="Cc8-tx-XdK"/>
                <outlet property="pointLabel" destination="AwG-zb-brS" id="662-FL-aUT"/>
                <outlet property="stateLabel" destination="e1n-ib-HGd" id="45j-B8-ldh"/>
            </connections>
            <point key="canvasLocation" x="131.8840579710145" y="168.08035714285714"/>
        </tableViewCell>
    </objects>
</document>
