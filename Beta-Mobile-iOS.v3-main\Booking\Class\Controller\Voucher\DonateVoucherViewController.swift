//
//  DonateVoucherViewController.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 8/3/19.
//  Copyright © 2019 ddkc. All rights reserved.
//

import UIKit
import RxSwift

enum DonateType {
    case voucher, point
}

class DonateVoucherViewController: BaseViewController {

    @IBOutlet weak var searchTextField: UITextField!
    @IBOutlet weak var tableView: UITableView!

    private let throttleIntervalInMilliseconds = 100
    private var users: [ShortUser] = []
    var voucher: VoucherModel?
    var type: DonateType = .voucher
    override func viewDidLoad() {
        super.viewDidLoad()

        localizableTitle = type == .voucher ? "donate_voucher" : "donate_point"
        tableView.separatorStyle = .none
        tableView.registerCell(id: DonateVoucherTableViewCell.id)
        searchTextField.placeholder = "search_friend".localized
    }

    @objc private func getUsers() {
        guard let text = searchTextField.text else {
            return
        }
        AccountProvider.rx.request(.searchUser(text)).mapObject(DDKCResponse<ShortUser>.self)
            .subscribeOn(MainScheduler.instance)
            .subscribe(onNext:{ response in
                self.handlerResponse(response, success: {
                    guard let objects = response.ListObject else{
                        print("Data wrong")
                        self.dismissLoading()
                        return
                    }
                    self.users = objects
                    self.tableView.reloadData()
                }, error: {
                })

            }, onError:{[weak self] error in
                guard let `self` = self else { return }
                UIAlertController.showAlert(self, message: error.localizedDescription)
            }).disposed(by: disposeBag)
    }

    private func donate(indexPath: IndexPath) {
        if type == .voucher {
            showConfirmDonateVoucher(voucher?.VoucherPackageName ?? "null", indexPath: indexPath)
        } else {
            donatePoint(indexPath: indexPath)
        }
    }

    private func donatePoint(indexPath: IndexPath) {
        let user = users[indexPath.row]
        guard let name = user.name, let email = user.email else {
            return
        }
        let customAlert = DonatePointAlert()
        customAlert.providesPresentationContextTransitionStyle = true
        customAlert.definesPresentationContext = true
        customAlert.modalPresentationStyle = UIModalPresentationStyle.overCurrentContext
        customAlert.modalTransitionStyle = UIModalTransitionStyle.crossDissolve
        customAlert.delegate = self
        customAlert.alertTitle = String(format: "donate_point_alert_title".localized, name, email)
        customAlert.alertContent = "donate_point_alert_content".localized
        customAlert.index = indexPath.row
        self.present(customAlert, animated: true, completion: nil)
    }

    private func donatePoint(point: Int, indexPath: IndexPath) {
        let user = users[indexPath.row]
        guard let email = user.email else {
            return
        }
        self.showLoading()
        VoucherProvider.rx.request(.donatePoint(email, point)).mapObject(DDKCResponse<EmptyModel>.self)
            .subscribeOn(MainScheduler.instance)
            .subscribe(onNext:{ response in
                self.handlerResponse(response, success: {
                    if response.isSuccess() {
                        let type = MyAlertType.successAttribute("donate_point_success", String(format: "donate_point_success_alert".localized, (user.name ?? ""), email))
                        self.showCustomizeAlert(type: type, okAction: {
                            self.navigationController?.popViewController(animated: true)
                        })
                    }
                    self.dismissLoading()
                }, error: {
                    self.dismissLoading()
                })

            }, onError:{[weak self] error in
                guard let `self` = self else { return }
                self.dismissLoading()
                UIAlertController.showAlert(self, message: error.localizedDescription)
            }).disposed(by: disposeBag)
    }

    private func donateVoucher(indexPath: IndexPath) {
        let user = users[indexPath.row]
        guard let id = voucher?.VoucherId, let email = user.email else {
            return
        }
        self.showLoading()
        VoucherProvider.rx.request(.donate(email, id)).mapObject(DDKCResponse<EmptyModel>.self)
            .subscribeOn(MainScheduler.instance)
            .subscribe(onNext:{ response in
                self.handlerResponse(response, success: {
                    if response.isSuccess() {
                        let type = MyAlertType.successAttribute("donate_voucher_success", String(format: "donate_voucher_success_alert".localized, (user.name ?? ""), email))
                        self.showCustomizeAlert(type: type, okAction: {
                            self.navigationController?.popViewController(animated: true)
                        })
                    }
                    self.dismissLoading()
                }, error: {
                    self.dismissLoading()
                })

            }, onError:{[weak self] error in
                guard let `self` = self else { return }
                self.dismissLoading()
                UIAlertController.showAlert(self, message: error.localizedDescription)
            }).disposed(by: disposeBag)
    }

    private func showConfirmDonateVoucher(_ voucherName: String, indexPath: IndexPath) {
        let user = users[indexPath.row]
        guard let name = user.name, let email = user.email else {
            return
        }

        let alert = UIAlertController(title: nil, message: "", preferredStyle: .alert)

        let contents = String(format: "confirm_donate_voucher".localized, voucherName, name, email)

        let strings = contents.components(separatedBy: "#")
        let attributedString = NSMutableAttributedString()

        attributedString.string(strings[0], font: UIFont(fontName: .SourceSansPro, style: .Regular, size: 16))
        attributedString.string(strings[1], font: UIFont(fontName: .SourceSansPro, style: .Bold, size: 16))
        attributedString.string(strings[2], font: UIFont(fontName: .SourceSansPro, style: .Regular, size: 16))
        attributedString.string(strings[3], font: UIFont(fontName: .SourceSansPro, style: .Bold, size: 16))
        attributedString.string(strings[4], font: UIFont(fontName: .SourceSansPro, style: .Regular, size: 16))

        alert.setValue(attributedString, forKey: "attributedMessage")

        let cancel = UIAlertAction(title: "Bt.Cancel".localized, style: .cancel)
        let confirm = UIAlertAction(title: "confirm".localized, style: .default) { (_) in
            self.donateVoucher(indexPath: indexPath)
        }
        alert.addAction(cancel)
        alert.addAction(confirm)
        present(alert, animated: true, completion: nil)
    }

    private func showConfirmDonatePoint(point: Int, indexPath: IndexPath) {
        let user = users[indexPath.row]
        guard let name = user.name, let email = user.email else {
            return
        }

        let alert = UIAlertController(title: nil, message: "", preferredStyle: .alert)

        let contents = String(format: "confirm_donate_point".localized, point, name, email)

        let strings = contents.components(separatedBy: "#")
        let attributedString = NSMutableAttributedString()

        attributedString.string(strings[0], font: UIFont(fontName: .SourceSansPro, style: .Regular, size: 16))
        attributedString.string(strings[1], font: UIFont(fontName: .SourceSansPro, style: .Bold, size: 16))
        attributedString.string(strings[2], font: UIFont(fontName: .SourceSansPro, style: .Regular, size: 16))
        attributedString.string(strings[3], font: UIFont(fontName: .SourceSansPro, style: .Bold, size: 16))
        attributedString.string(strings[4], font: UIFont(fontName: .SourceSansPro, style: .Regular, size: 16))

        alert.setValue(attributedString, forKey: "attributedMessage")

        let cancel = UIAlertAction(title: "Bt.Cancel".localized, style: .cancel)
        let confirm = UIAlertAction(title: "confirm".localized, style: .default) { (_) in
            self.donatePoint(point: point, indexPath: indexPath)
        }
        alert.addAction(cancel)
        alert.addAction(confirm)
        present(alert, animated: true, completion: nil)
    }

    @IBAction func searchChanging(_ sender: UITextField) {
        guard let text = sender.text, !text.isEmpty else {
            return
        }
        NSObject.cancelPreviousPerformRequests(withTarget: self, selector: #selector(self.getUsers), object: nil)
        self.perform(#selector(self.getUsers), with: nil, afterDelay: 0.5)
    }

}

extension DonateVoucherViewController: UITableViewDelegate, UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return users.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let cell = tableView.dequeueReusableCell(withIdentifier: DonateVoucherTableViewCell.id) as? DonateVoucherTableViewCell else {
            return DonateVoucherTableViewCell()
        }

        cell.configure(users[indexPath.row], index: indexPath)

        cell.donateHandler = { [weak self] index in
            self?.donate(indexPath: index)
        }

        return cell
    }

    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 82
    }
}

extension DonateVoucherViewController: CustomAlertViewDelegate {
    func okButtonTapped(point: Int, index: Int?) {
        guard let index = index else {
            return
        }
        showConfirmDonatePoint(point: point, indexPath: IndexPath(row: index, section: 0))
    }

    func cancelButtonTapped() {

    }


}
