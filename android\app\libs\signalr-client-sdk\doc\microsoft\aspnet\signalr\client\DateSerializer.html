<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<title>DateSerializer</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="DateSerializer";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../microsoft/aspnet/signalr/client/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/DateSerializer.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../microsoft/aspnet/signalr/client/Credentials.html" title="interface in microsoft.aspnet.signalr.client"><span class="strong">Prev Class</span></a></li>
<li><a href="../../../../microsoft/aspnet/signalr/client/ErrorCallback.html" title="interface in microsoft.aspnet.signalr.client"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?microsoft/aspnet/signalr/client/DateSerializer.html" target="_top">Frames</a></li>
<li><a href="DateSerializer.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">microsoft.aspnet.signalr.client</div>
<h2 title="Class DateSerializer" class="title">Class DateSerializer</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>microsoft.aspnet.signalr.client.DateSerializer</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>com.google.gson.JsonDeserializer&lt;java.util.Date&gt;, com.google.gson.JsonSerializer&lt;java.util.Date&gt;</dd>
</dl>
<hr>
<br>
<pre>public class <span class="strong">DateSerializer</span>
extends java.lang.Object
implements com.google.gson.JsonSerializer&lt;java.util.Date&gt;, com.google.gson.JsonDeserializer&lt;java.util.Date&gt;</pre>
<div class="block">Date Serializer/Deserializer to make .NET and Java dates compatible</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><strong><a href="../../../../microsoft/aspnet/signalr/client/DateSerializer.html#DateSerializer()">DateSerializer</a></strong>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method_summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span>Methods</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>java.util.Date</code></td>
<td class="colLast"><code><strong><a href="../../../../microsoft/aspnet/signalr/client/DateSerializer.html#deserialize(com.google.gson.JsonElement, java.lang.reflect.Type, com.google.gson.JsonDeserializationContext)">deserialize</a></strong>(com.google.gson.JsonElement&nbsp;element,
           java.lang.reflect.Type&nbsp;type,
           com.google.gson.JsonDeserializationContext&nbsp;ctx)</code>
<div class="block">Deserializes a JsonElement containing an ISO-8601 formatted date</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.util.Date</code></td>
<td class="colLast"><code><strong><a href="../../../../microsoft/aspnet/signalr/client/DateSerializer.html#deserialize(java.lang.String)">deserialize</a></strong>(java.lang.String&nbsp;strVal)</code>
<div class="block">Deserializes an ISO-8601 formatted date</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><strong><a href="../../../../microsoft/aspnet/signalr/client/DateSerializer.html#serialize(java.util.Date)">serialize</a></strong>(java.util.Date&nbsp;date)</code>
<div class="block">Serializes a Date object to an ISO-8601 formatted date string</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>com.google.gson.JsonElement</code></td>
<td class="colLast"><code><strong><a href="../../../../microsoft/aspnet/signalr/client/DateSerializer.html#serialize(java.util.Date, java.lang.reflect.Type, com.google.gson.JsonSerializationContext)">serialize</a></strong>(java.util.Date&nbsp;date,
         java.lang.reflect.Type&nbsp;type,
         com.google.gson.JsonSerializationContext&nbsp;ctx)</code>
<div class="block">Serializes a Date to a JsonElement containing a ISO-8601 formatted date</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="DateSerializer()">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>DateSerializer</h4>
<pre>public&nbsp;DateSerializer()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method_detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="deserialize(com.google.gson.JsonElement, java.lang.reflect.Type, com.google.gson.JsonDeserializationContext)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deserialize</h4>
<pre>public&nbsp;java.util.Date&nbsp;deserialize(com.google.gson.JsonElement&nbsp;element,
                         java.lang.reflect.Type&nbsp;type,
                         com.google.gson.JsonDeserializationContext&nbsp;ctx)
                           throws com.google.gson.JsonParseException</pre>
<div class="block">Deserializes a JsonElement containing an ISO-8601 formatted date</div>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>deserialize</code>&nbsp;in interface&nbsp;<code>com.google.gson.JsonDeserializer&lt;java.util.Date&gt;</code></dd>
<dt><span class="strong">Throws:</span></dt>
<dd><code>com.google.gson.JsonParseException</code></dd></dl>
</li>
</ul>
<a name="deserialize(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deserialize</h4>
<pre>public static&nbsp;java.util.Date&nbsp;deserialize(java.lang.String&nbsp;strVal)
                                  throws java.text.ParseException</pre>
<div class="block">Deserializes an ISO-8601 formatted date</div>
<dl><dt><span class="strong">Throws:</span></dt>
<dd><code>java.text.ParseException</code></dd></dl>
</li>
</ul>
<a name="serialize(java.util.Date)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>serialize</h4>
<pre>public static&nbsp;java.lang.String&nbsp;serialize(java.util.Date&nbsp;date)</pre>
<div class="block">Serializes a Date object to an ISO-8601 formatted date string</div>
</li>
</ul>
<a name="serialize(java.util.Date, java.lang.reflect.Type, com.google.gson.JsonSerializationContext)">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>serialize</h4>
<pre>public&nbsp;com.google.gson.JsonElement&nbsp;serialize(java.util.Date&nbsp;date,
                                    java.lang.reflect.Type&nbsp;type,
                                    com.google.gson.JsonSerializationContext&nbsp;ctx)</pre>
<div class="block">Serializes a Date to a JsonElement containing a ISO-8601 formatted date</div>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>serialize</code>&nbsp;in interface&nbsp;<code>com.google.gson.JsonSerializer&lt;java.util.Date&gt;</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../microsoft/aspnet/signalr/client/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/DateSerializer.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../microsoft/aspnet/signalr/client/Credentials.html" title="interface in microsoft.aspnet.signalr.client"><span class="strong">Prev Class</span></a></li>
<li><a href="../../../../microsoft/aspnet/signalr/client/ErrorCallback.html" title="interface in microsoft.aspnet.signalr.client"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?microsoft/aspnet/signalr/client/DateSerializer.html" target="_top">Frames</a></li>
<li><a href="DateSerializer.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
