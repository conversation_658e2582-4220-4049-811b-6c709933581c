//
//  TransactionHistoryCell.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 4/18/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit

class TransactionHistoryCell: UITableViewCell {
    @IBOutlet weak var lbFilmName: UILabel!
    @IBOutlet weak var lbFilmDate: UILabel!
    @IBOutlet weak var lbFilmCinema: UILabel!
    @IBOutlet weak var lbMoney: UILabel!
    @IBOutlet weak var lbPoint: UILabel!
    @IBOutlet weak var lbDatePoint: UILabel!
    @IBOutlet weak var waitingView: UIView!
    @IBOutlet weak var waitingLabel: UILabel!
    @IBOutlet weak var lbPointTitle: LocalizableLabel!
    @IBOutlet weak var lbDatePointTitle: LocalizableLabel!
    
    override func awakeFromNib() {
        super.awakeFromNib()
    }
    
    func fillData(_ item: TransactionHistoryModel){
        lbFilmName.text = item.FilmName
        lbFilmCinema.text = item.CinemaName
        waitingLabel.text = "waiting_process".localized.uppercased()
        if let dateString = item.ShowTime{
            let date = Date.dateFromServerSavis(dateString)
            lbFilmDate.text = Date.dateFormatter("dd/MM/yyyy | HH:mm").string(from: date)
        }
        
        if let currency = item.TotalPayment{
            lbMoney.text = currency.toCurrency()
        }

        if item.Invoice_Id == "00000000-0000-0000-0000-000000000000" {
            waitingView.isHidden = false
            waitingLabel.isHidden = false
            lbPoint.isHidden = true
            lbPointTitle.isHidden = true
            lbDatePointTitle.isHidden = true
            lbDatePoint.isHidden = true
        } else {
            lbPoint.text = "\(item.QuantityPoint ?? 0)"
            lbDatePoint.text = Date.dateFromServerSavis(item.DateExpiredPoint ?? "").toStringStandard()
            waitingView.isHidden = true
            waitingLabel.isHidden = true
            lbPoint.isHidden = false
            lbPointTitle.isHidden = false
            lbDatePoint.isHidden = false
            lbDatePointTitle.isHidden = false
        }
    }
    
    

    
}
