{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98869b51186bc821cb6ac8ff727347ee4d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f3061f34e81014b8815bc96d5b6f09c0", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980f362a276f4286a9d84b7a8df431f46c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987d2406fa585cc32db45ddb6ed8200aac", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980f362a276f4286a9d84b7a8df431f46c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f41409066042d724a152e804bbc44362", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984ece0fefacdd71790a3ba22d884bbcc8", "guid": "bfdfe7dc352907fc980b868725387e98ed5dd22ef28993cf94181aff6f66b1c3", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98531bdabac93b4e034b724d8751281946", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981a07f362cfe2cfbe4b647ddb9859f00c", "guid": "bfdfe7dc352907fc980b868725387e98b83e1f80099e63c0667f3b286ae34f6e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983526ea6185d8a3d8a2e906b98a1e1fff", "guid": "bfdfe7dc352907fc980b868725387e9853f48244a76c8f34ac75331d8792a6e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6374a40a1da37e4d5b9bb8f71bc2691", "guid": "bfdfe7dc352907fc980b868725387e98e69f5a1494d8abc9cd6a12e86efffc1c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c136bf542c09e76c2e92edc4dc8bd74", "guid": "bfdfe7dc352907fc980b868725387e985ae2d9162cb1499dcf97d841fbafc2dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841410bd7355521b5531af09a774d294f", "guid": "bfdfe7dc352907fc980b868725387e98e11efb57d19214b2b1a690da8224a36c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98164149537905cea4387467a6b98162f1", "guid": "bfdfe7dc352907fc980b868725387e98da6aae4c9773a3c3e20ce5cef6ea8e35"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9871d479fa4f265a610a957d981a4d47a4", "guid": "bfdfe7dc352907fc980b868725387e988b5da8701ae8359436138c21e33918f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f047dab61b5bc0b2edaac698c8c16947", "guid": "bfdfe7dc352907fc980b868725387e98f80db56d9d37f044ed6914ab1929d540"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98257e23c93d2d753db87bc9cd46235e25", "guid": "bfdfe7dc352907fc980b868725387e98cdc5dc05c39194ca16afdcc2c044e650"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855c6cc83357ac0056102f907dedf40e3", "guid": "bfdfe7dc352907fc980b868725387e988dfa642b89fb8d7ee25ffe642a7b5336"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812262985f6665364b00d503ccdd045ed", "guid": "bfdfe7dc352907fc980b868725387e98eee7753c85b682a9e687f790988c3c1d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884e4aa5970fc06008718b0e661e9f6ad", "guid": "bfdfe7dc352907fc980b868725387e988b2b2175baca981852d104fe57b8cae4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb0fd5c433e4a25d8065d072f26ee7e8", "guid": "bfdfe7dc352907fc980b868725387e988db6b4600aacc04ffc9db0789e140e93"}], "guid": "bfdfe7dc352907fc980b868725387e9834b54de6ebce75f26d1a53c16e292847", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e98dd0d27ef6697d35e95c925e55c72d8dd"}], "guid": "bfdfe7dc352907fc980b868725387e9878c2b786d533da62a0e23ed6ed3300be", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98cae4725f3376b1ae917db79aa10047b4", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e987e59c4511c288592071e0553e2830e4a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}