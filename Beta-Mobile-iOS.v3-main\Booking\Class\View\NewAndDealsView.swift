//
//  NewAndDealsView.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 3/14/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit

protocol NewsAndDealsViewDelegate: class {
    func newsView(_ view: NewAndDealsView, didSelected item: NewsModel)
    func newsViewDidShowAll()
    func didChangeHeight(_ height: CGFloat)
}

class NewAndDealsView: UIView {
    @IBOutlet weak var tableView: UITableView!

    var dataList: [NewsModel] = [] {
        didSet {
            self.tableView.reloadData()
        }
    }

    let cellId = "NewsTableViewCell"
    weak var delegate: NewsAndDealsViewDelegate?

    fileprivate var oldHeight: CGFloat = 0

    override func awakeFromNib() {
        super.awakeFromNib()

        guard let view = Bundle(for: NewAndDealsView.self).loadNibNamed(self.className, owner: self, options: nil)?.first as? UIView else {
            return
        }
        view.frame = self.bounds
        view.autoresizingMask = [.flexibleWidth, .flexibleHeight]
        view.translatesAutoresizingMaskIntoConstraints = true
        self.addSubview(view)

        tableView.register(UINib(nibName: cellId, bundle: nil), forCellReuseIdentifier: cellId)
        tableView.addObserver(self, forKeyPath: "contentSize", options: .new, context: nil)
    }

    override func willMove(toSuperview newSuperview: UIView?) {
        super.willMove(toSuperview: newSuperview)
        if newSuperview == nil {
            tableView.removeObserver(self, forKeyPath: "contentSize")
        }
    }

    override func observeValue(forKeyPath keyPath: String?, of object: Any?, change: [NSKeyValueChangeKey : Any]?, context: UnsafeMutableRawPointer?) {
        if oldHeight != tableView.contentSize.height {
            oldHeight = tableView.contentSize.height
            delegate?.didChangeHeight(oldHeight)
            var frame = tableView.frame
            frame.size.height = oldHeight
            tableView.frame = frame
        }
    }

    @IBAction func showAllButtonPress(_ sender: UIButton) {
        delegate?.newsViewDidShowAll()
    }

    override var intrinsicContentSize: CGSize {
        get {
            var size = tableView.contentSize
            size.height += 50
            return size
        }
    }
}

extension NewAndDealsView: UITableViewDataSource, UITableViewDelegate {
    func numberOfSections(in tableView: UITableView) -> Int {
        return 1
    }

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return dataList.count // test
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: cellId, for: indexPath) as! NewsTableViewCell
        cell.fillData(dataList[indexPath.row])
        return cell
    }

    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: false)

        delegate?.newsView(self, didSelected: dataList[indexPath.row])
    }
}
