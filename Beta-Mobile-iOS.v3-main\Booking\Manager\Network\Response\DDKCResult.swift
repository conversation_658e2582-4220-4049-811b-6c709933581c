//
//  DDKCResult.swift
//  Booking
//
//  Created by <PERSON><PERSON>u on 4/10/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation
import ObjectMapper

class DDKCResult<T: BaseMappable>: NSObject {
    
    var Object: T?
    var ListObject: [T]?
    var TotalCount: Int?
    var DataCount: Int?
    var Status: UInt?
    var Message: String?
    
    init(dict: [String: Any]) {
        if let object = dict["Data"] as? [String: Any]{
            self.Object =  object as? T
        }else if let listObject = dict["Data"] as? [[String: Any]]{
            self.ListObject = listObject.map{ $0 as? T}.compactMap{$0}
        }else if let _ = dict["Data"] as? [Any]{
            self.ListObject = []
        }
        
        if let totalCount = dict["TotalCount"] as? Int{
            self.TotalCount = totalCount
        }
        
        if let dataCount = dict["DataCount"] as? Int{
            self.DataCount = dataCount
        }
        
        if let status = dict["Status"] as? UInt{
            self.Status = status
        }
        
        if let message = dict["Message"] as? String{
            self.Message = message
        }
    }
    var isSuccess: Bool {
        return Status == 1
    }
}
