//
//  View+Size.swift
//  Booking-dev
//
//  Created by <PERSON><PERSON> on 4/7/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit

extension UIView {
    func autoFitSize() {
        var frame = self.frame
        let widthConstraint = NSLayoutConstraint(item: self, attribute: .width, relatedBy: .equal, toItem: nil, attribute: .width, multiplier: 1, constant: frame.width)
        widthConstraint.isActive = true
        frame.size = self.systemLayoutSizeFitting(CGSize(width: frame.width, height: 10000))
        widthConstraint.isActive = false
        self.frame = frame
    }
}
