<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14490.70" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14490.49"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="Oswald-Regular.ttf">
            <string>Oswald-Regular</string>
        </array>
        <array key="SourceSansPro-Bold.ttf">
            <string>SourceSansPro-Bold</string>
        </array>
        <array key="SourceSansPro-Regular.ttf">
            <string>SourceSansPro-Regular</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="DonatePointAlert" customModule="Booking_dev" customModuleProvider="target">
            <connections>
                <outlet property="alertView" destination="eMI-Mu-ihX" id="zM4-tJ-HXT"/>
                <outlet property="cancelButton" destination="anM-Wo-yI2" id="59S-zP-eOn"/>
                <outlet property="messageLabel" destination="Ys8-tk-ztP" id="s7c-Q6-Kd4"/>
                <outlet property="okButton" destination="S18-17-Ypq" id="eSq-OK-63k"/>
                <outlet property="otherValueTextField" destination="Dnu-U0-Sm2" id="xgf-wK-jz2"/>
                <outlet property="stackView" destination="3WP-0E-2WN" id="zI8-9e-WuK"/>
                <outlet property="titleLabel" destination="akz-LA-pR0" id="W9Z-Ao-8jP"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="eMI-Mu-ihX">
                    <rect key="frame" x="72" y="298" width="270" height="300"/>
                    <subviews>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Title" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="akz-LA-pR0">
                            <rect key="frame" x="8" y="16" width="254" height="20.5"/>
                            <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                            <color key="textColor" red="0.**********" green="0.**********" blue="0.15686274510000001" alpha="1" colorSpace="calibratedRGB"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Nhập số điểm muốn tặng:" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Ys8-tk-ztP">
                            <rect key="frame" x="45.5" y="44.5" width="179.5" height="20.5"/>
                            <fontDescription key="fontDescription" name="SourceSansPro-Bold" family="Source Sans Pro" pointSize="16"/>
                            <color key="textColor" red="0.**********" green="0.**********" blue="0.15686274510000001" alpha="1" colorSpace="calibratedRGB"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="anM-Wo-yI2">
                            <rect key="frame" x="0.0" y="254" width="135" height="46"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="46" id="pyJ-9l-ZQs"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                            <state key="normal" title="Cancel"/>
                            <connections>
                                <action selector="didTapCancel:" destination="-1" eventType="touchUpInside" id="1Jt-Br-spB"/>
                            </connections>
                        </button>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="S18-17-Ypq">
                            <rect key="frame" x="135" y="254" width="135" height="46"/>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <state key="normal" title="Ok"/>
                            <connections>
                                <action selector="okTapped:" destination="-1" eventType="touchUpInside" id="Qoc-oK-YDJ"/>
                            </connections>
                        </button>
                        <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" alignment="top" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="3WP-0E-2WN">
                            <rect key="frame" x="16" y="158" width="238" height="40"/>
                            <subviews>
                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="CC8-tS-4rt" customClass="RoundButton" customModule="Booking_dev" customModuleProvider="target">
                                    <rect key="frame" x="0.0" y="0.0" width="41" height="36"/>
                                    <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="16"/>
                                    <state key="normal" title="5">
                                        <color key="titleColor" red="0.**********" green="0.**********" blue="0.15686274510000001" alpha="1" colorSpace="calibratedRGB"/>
                                    </state>
                                    <userDefinedRuntimeAttributes>
                                        <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                            <color key="value" red="0.58431372549019611" green="0.58431372549019611" blue="0.58431372549019611" alpha="1" colorSpace="calibratedRGB"/>
                                        </userDefinedRuntimeAttribute>
                                        <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                            <real key="value" value="3"/>
                                        </userDefinedRuntimeAttribute>
                                        <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                            <real key="value" value="1"/>
                                        </userDefinedRuntimeAttribute>
                                    </userDefinedRuntimeAttributes>
                                    <connections>
                                        <action selector="numberTapped:" destination="-1" eventType="touchUpInside" id="tdW-pU-fQg"/>
                                    </connections>
                                </button>
                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="vKV-fP-kZJ" customClass="RoundButton" customModule="Booking_dev" customModuleProvider="target">
                                    <rect key="frame" x="49" y="0.0" width="41.5" height="36"/>
                                    <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="16"/>
                                    <state key="normal" title="10">
                                        <color key="titleColor" red="0.**********" green="0.**********" blue="0.15686274510000001" alpha="1" colorSpace="calibratedRGB"/>
                                    </state>
                                    <userDefinedRuntimeAttributes>
                                        <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                            <color key="value" red="0.58431372550000005" green="0.58431372550000005" blue="0.58431372550000005" alpha="1" colorSpace="calibratedRGB"/>
                                        </userDefinedRuntimeAttribute>
                                        <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                            <real key="value" value="3"/>
                                        </userDefinedRuntimeAttribute>
                                        <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                            <real key="value" value="1"/>
                                        </userDefinedRuntimeAttribute>
                                    </userDefinedRuntimeAttributes>
                                    <connections>
                                        <action selector="numberTapped:" destination="-1" eventType="touchUpInside" id="2NA-yD-pqz"/>
                                    </connections>
                                </button>
                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="DqX-xp-ceP" customClass="RoundButton" customModule="Booking_dev" customModuleProvider="target">
                                    <rect key="frame" x="98.5" y="0.0" width="41" height="36"/>
                                    <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="16"/>
                                    <state key="normal" title="20">
                                        <color key="titleColor" red="0.**********" green="0.**********" blue="0.15686274510000001" alpha="1" colorSpace="calibratedRGB"/>
                                    </state>
                                    <userDefinedRuntimeAttributes>
                                        <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                            <color key="value" red="0.58431372550000005" green="0.58431372550000005" blue="0.58431372550000005" alpha="1" colorSpace="calibratedRGB"/>
                                        </userDefinedRuntimeAttribute>
                                        <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                            <real key="value" value="3"/>
                                        </userDefinedRuntimeAttribute>
                                        <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                            <real key="value" value="1"/>
                                        </userDefinedRuntimeAttribute>
                                    </userDefinedRuntimeAttributes>
                                    <connections>
                                        <action selector="numberTapped:" destination="-1" eventType="touchUpInside" id="xnl-Ky-96f"/>
                                    </connections>
                                </button>
                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="epr-A7-K1L" customClass="RoundButton" customModule="Booking_dev" customModuleProvider="target">
                                    <rect key="frame" x="147.5" y="0.0" width="41.5" height="36"/>
                                    <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="16"/>
                                    <state key="normal" title="30">
                                        <color key="titleColor" red="0.**********" green="0.**********" blue="0.15686274510000001" alpha="1" colorSpace="calibratedRGB"/>
                                    </state>
                                    <userDefinedRuntimeAttributes>
                                        <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                            <color key="value" red="0.58431372550000005" green="0.58431372550000005" blue="0.58431372550000005" alpha="1" colorSpace="calibratedRGB"/>
                                        </userDefinedRuntimeAttribute>
                                        <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                            <real key="value" value="3"/>
                                        </userDefinedRuntimeAttribute>
                                        <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                            <real key="value" value="1"/>
                                        </userDefinedRuntimeAttribute>
                                    </userDefinedRuntimeAttributes>
                                    <connections>
                                        <action selector="numberTapped:" destination="-1" eventType="touchUpInside" id="Tei-G3-3S6"/>
                                    </connections>
                                </button>
                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="NOi-5N-ZoN" customClass="RoundButton" customModule="Booking_dev" customModuleProvider="target">
                                    <rect key="frame" x="197" y="0.0" width="41" height="36"/>
                                    <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="16"/>
                                    <state key="normal" title="100">
                                        <color key="titleColor" red="0.**********" green="0.**********" blue="0.15686274510000001" alpha="1" colorSpace="calibratedRGB"/>
                                    </state>
                                    <userDefinedRuntimeAttributes>
                                        <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                            <color key="value" red="0.58431372550000005" green="0.58431372550000005" blue="0.58431372550000005" alpha="1" colorSpace="calibratedRGB"/>
                                        </userDefinedRuntimeAttribute>
                                        <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                            <real key="value" value="3"/>
                                        </userDefinedRuntimeAttribute>
                                        <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                            <real key="value" value="1"/>
                                        </userDefinedRuntimeAttribute>
                                    </userDefinedRuntimeAttributes>
                                    <connections>
                                        <action selector="numberTapped:" destination="-1" eventType="touchUpInside" id="aqX-K4-3Fz"/>
                                    </connections>
                                </button>
                            </subviews>
                            <constraints>
                                <constraint firstAttribute="height" constant="40" id="yGF-r9-Tnu"/>
                            </constraints>
                        </stackView>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="kgC-9E-Sfa" customClass="RoundView" customModule="Booking_dev" customModuleProvider="target">
                            <rect key="frame" x="16" y="206" width="238" height="40"/>
                            <subviews>
                                <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Nhập số khác ..." textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="Dnu-U0-Sm2">
                                    <rect key="frame" x="8" y="0.0" width="222" height="40"/>
                                    <color key="textColor" red="0.070588235289999995" green="0.**********" blue="0.15686274510000001" alpha="1" colorSpace="calibratedRGB"/>
                                    <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="14"/>
                                    <textInputTraits key="textInputTraits"/>
                                </textField>
                            </subviews>
                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <constraints>
                                <constraint firstAttribute="trailing" secondItem="Dnu-U0-Sm2" secondAttribute="trailing" constant="8" id="0l7-DR-3A2"/>
                                <constraint firstItem="Dnu-U0-Sm2" firstAttribute="leading" secondItem="kgC-9E-Sfa" secondAttribute="leading" constant="8" id="NEx-mT-cdH"/>
                                <constraint firstAttribute="height" constant="40" id="fHJ-86-LaR"/>
                                <constraint firstAttribute="bottom" secondItem="Dnu-U0-Sm2" secondAttribute="bottom" id="mhx-80-OxH"/>
                                <constraint firstItem="Dnu-U0-Sm2" firstAttribute="top" secondItem="kgC-9E-Sfa" secondAttribute="top" id="xpp-uA-Q91"/>
                            </constraints>
                            <userDefinedRuntimeAttributes>
                                <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                    <real key="value" value="1"/>
                                </userDefinedRuntimeAttribute>
                                <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                    <color key="value" red="0.58431372549019611" green="0.58431372549019611" blue="0.58431372549019611" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                </userDefinedRuntimeAttribute>
                                <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                    <real key="value" value="5"/>
                                </userDefinedRuntimeAttribute>
                            </userDefinedRuntimeAttributes>
                        </view>
                    </subviews>
                    <color key="backgroundColor" red="0.97647058819999999" green="0.97647058819999999" blue="0.97647058819999999" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <constraints>
                        <constraint firstItem="S18-17-Ypq" firstAttribute="height" secondItem="anM-Wo-yI2" secondAttribute="height" id="0hc-jc-fg2"/>
                        <constraint firstItem="anM-Wo-yI2" firstAttribute="top" secondItem="kgC-9E-Sfa" secondAttribute="bottom" constant="8" id="26z-pA-O2j"/>
                        <constraint firstAttribute="trailing" secondItem="kgC-9E-Sfa" secondAttribute="trailing" constant="16" id="6Hx-n8-H1U"/>
                        <constraint firstItem="akz-LA-pR0" firstAttribute="centerX" secondItem="Ys8-tk-ztP" secondAttribute="centerX" id="7SP-yc-T6r"/>
                        <constraint firstItem="anM-Wo-yI2" firstAttribute="width" secondItem="eMI-Mu-ihX" secondAttribute="width" multiplier="0.5" id="8D4-ni-1IE"/>
                        <constraint firstAttribute="trailing" secondItem="akz-LA-pR0" secondAttribute="trailing" constant="8" id="FvT-2o-hLf"/>
                        <constraint firstAttribute="bottom" secondItem="anM-Wo-yI2" secondAttribute="bottom" id="O96-Tm-XgU"/>
                        <constraint firstItem="Ys8-tk-ztP" firstAttribute="centerX" secondItem="eMI-Mu-ihX" secondAttribute="centerX" id="RtR-JX-Nz4"/>
                        <constraint firstAttribute="height" constant="300" id="UDq-6n-cCG"/>
                        <constraint firstItem="kgC-9E-Sfa" firstAttribute="top" secondItem="3WP-0E-2WN" secondAttribute="bottom" constant="8" id="YW5-PN-Jio"/>
                        <constraint firstItem="akz-LA-pR0" firstAttribute="top" secondItem="eMI-Mu-ihX" secondAttribute="top" constant="16" id="bRJ-2x-QOw"/>
                        <constraint firstItem="anM-Wo-yI2" firstAttribute="leading" secondItem="eMI-Mu-ihX" secondAttribute="leading" id="cHr-OX-ex2"/>
                        <constraint firstItem="Ys8-tk-ztP" firstAttribute="top" secondItem="akz-LA-pR0" secondAttribute="bottom" constant="8" id="hAq-r1-wb7"/>
                        <constraint firstItem="S18-17-Ypq" firstAttribute="width" secondItem="anM-Wo-yI2" secondAttribute="width" id="heJ-cQ-vRO"/>
                        <constraint firstAttribute="trailing" secondItem="3WP-0E-2WN" secondAttribute="trailing" constant="16" id="hnp-kD-xvX"/>
                        <constraint firstAttribute="bottom" secondItem="S18-17-Ypq" secondAttribute="bottom" id="k07-az-yCz"/>
                        <constraint firstAttribute="trailing" secondItem="S18-17-Ypq" secondAttribute="trailing" id="lRe-QH-uEd"/>
                        <constraint firstItem="akz-LA-pR0" firstAttribute="leading" secondItem="eMI-Mu-ihX" secondAttribute="leading" constant="8" id="p6H-kN-deR"/>
                        <constraint firstItem="3WP-0E-2WN" firstAttribute="leading" secondItem="eMI-Mu-ihX" secondAttribute="leading" constant="16" id="rrR-6t-cvd"/>
                        <constraint firstItem="kgC-9E-Sfa" firstAttribute="leading" secondItem="eMI-Mu-ihX" secondAttribute="leading" constant="16" id="sQu-HI-t2j"/>
                    </constraints>
                </view>
            </subviews>
            <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
            <constraints>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="eMI-Mu-ihX" secondAttribute="trailing" constant="32" id="ABS-FK-nmG"/>
                <constraint firstItem="eMI-Mu-ihX" firstAttribute="centerX" secondItem="i5M-Pr-FkT" secondAttribute="centerX" id="UpO-NE-hD9"/>
                <constraint firstItem="eMI-Mu-ihX" firstAttribute="centerY" secondItem="i5M-Pr-FkT" secondAttribute="centerY" id="aDY-z5-obC"/>
                <constraint firstItem="eMI-Mu-ihX" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" constant="32" id="fSD-Gn-NOl"/>
            </constraints>
            <viewLayoutGuide key="safeArea" id="fnl-2z-Ty3"/>
            <point key="canvasLocation" x="131.8840579710145" y="152.67857142857142"/>
        </view>
    </objects>
</document>
