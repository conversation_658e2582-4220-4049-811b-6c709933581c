//
//  TransactionHistoryDetailModel.swift
//  Booking-dev
//
//  Created by <PERSON><PERSON> Vu on 5/31/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation
import ObjectMapper

enum PaymentType: String {
    case CARD = "CARD"
    case CASH = "CASH"
    case VOUCHER = "VOUCHER"
    case BETAID = "BETAID"
    case ONEPAY = "ONEPAY"
    case MOMO = "MOMO"
    case ZALOPAY = "ZALOPAY"
    case SHOPEEPAY_ONLINE = "SHOPEEPAY_ONLINE"
}

class TransactionHistoryDetailModel : Mappable {
     var ListTicketType : [TicketTypeCopy]?
     var ListCombo : [Combo]?
     var FilmModel : FilmModel?
     var PaymentModel : [Payment]?
     var Invoice_Id : String?
     var FilmName : String?
     var CinemaName : String?
     var ScreenName : String?
     var CinemaId : String?
     var ShowId : String?
     var FilmId : String?
     var DateShow : String?
     var ShowTime : String?
     var DateEntered : String?
     var SeatName : String?
     var TransactionType : String?
     var QuantityPoint : Int?
     var DateExpiredPoint : String?
     var SpendingPoint : Int?
     var QuantityCombo : Int?
     var QuantitySeat : Int?
     var TicketTypeId : String?
     var TicketTypeName : String?
     var AccountId : String?
     var ApplicationId : String?
     var SalesChannelId : String?
     var SalesChannelCode : String?
     var CardId : String?
     var CardNumber : Int?
     var TotalPayment : Int?
     var No : String?


    required init?(map: Map) {

    }

    func mapping(map: Map) {
        ListTicketType       <- map["ListTicketType"]
        ListCombo            <- map["ListCombo"]
        FilmModel            <- map["FilmModel"]
        PaymentModel         <- map["PaymentModel"]
        Invoice_Id           <- map["Invoice_Id"]
        FilmName             <- map["FilmName"]
        CinemaName           <- map["CinemaName"]
        ScreenName           <- map["ScreenName"]
        CinemaId             <- map["CinemaId"]
        ShowId               <- map["ShowId"]
        FilmId               <- map["FilmId"]
        DateShow             <- map["DateShow"]
        ShowTime             <- map["ShowTime"]
        DateEntered          <- map["DateEntered"]
        SeatName             <- map["SeatName"]
        TransactionType      <- map["TransactionType"]
        QuantityPoint        <- map["QuantityPoint"]
        DateExpiredPoint     <- map["DateExpiredPoint"]
        SpendingPoint        <- map["SpendingPoint"]
        QuantityCombo        <- map["QuantityCombo"]
        QuantitySeat         <- map["QuantitySeat"]
        TicketTypeId         <- map["TicketTypeId"]
        TicketTypeName       <- map["TicketTypeName"]
        AccountId            <- map["AccountId"]
        ApplicationId        <- map["ApplicationId"]
        SalesChannelId       <- map["SalesChannelId"]
        SalesChannelCode     <- map["SalesChannelCode"]
        CardId               <- map["CardId"]
        CardNumber           <- map["CardNumber"]
        TotalPayment         <- map["TotalPayment"]
        No                   <- map["No"]
    }
    
    var showTime: (String, String){
        guard let dateString = ShowTime else {
            return ("-", "-")
        }
        let date = Date.dateFromServerSavis(dateString)
        return (date.toStringStandard(), date.timeFromDate())
    }
    
    var seatName: (Int, String){
        guard let ticketTypes = ListTicketType else {
            return (0, "")
        }
        var seats: [String] = []
        var total: Int = 0
        let _ = ticketTypes.map{ ticket in
            if let listSeat = ticket.ListSeatName{
                total += listSeat.count
                seats.append("\(ticket.Name ?? "") \(listSeat.joined(separator: ", "))")
            }
        }
        return (total, seats.joined(separator: "\n"))
    }
    
    var combos: (Int, String){
        guard let listCombo = ListCombo else {
            return (0, "")
        }
        
        return (listCombo.compactMap{$0.Quantity}.reduce(0, +), listCombo.map{ ($0.Name ?? "") + "(\($0.Quantity ?? 0))" }.joined(separator: ", "))
    }
    
    func paymentValue(type: PaymentType) -> Int {
        guard let paymentModel = PaymentModel else {
            return 0
        }
        
        let model = paymentModel.filter {
            return $0.PaymentTypeCode == type.rawValue
            }.compactMap { $0.Values }.reduce(0, +)
        return model
    }

    func paymentValue(types: [PaymentType]) -> Int {
        guard let paymentModel = PaymentModel else {
            return 0
        }

        let model = paymentModel.filter { payment in
            return types.first { $0.rawValue == payment.PaymentTypeCode } != nil
            }.compactMap { $0.Values }.reduce(0, +)
        return model
    }
}

class TicketTypeCopy : Mappable {
     var TicketTypeId : String?
     var Name : String?
     var ListSeatName : [String]?


    required init?(map: Map) {

    }

    func mapping(map: Map) {
        TicketTypeId         <- map["TicketTypeId"]
        Name                 <- map["Name"]
        ListSeatName         <- map["ListSeatName"]
    }
}

class Payment : Mappable {
     var PaymentTypeName : String?
     var PaymentTypeCode : String?
     var Values : Int?


    required init?(map: Map) {

    }

    func mapping(map: Map) {
        PaymentTypeName      <- map["PaymentTypeName"]
        PaymentTypeCode      <- map["PaymentTypeCode"]
        Values               <- map["Values"]
    }
}

class Combo: Mappable {
    required init?(map: Map) {
        
    }
    
    func mapping(map: Map) {
        ComboId <- map["ComboId"]
        Name <- map["Name"]
        Quantity <- map["Quantity"]
    }
    
    var ComboId: String?
    var Name: String?
    var Quantity: Int?
    
    
}
