//
//  NotificationViewController.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 3/14/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit
import UITableView_FDTemplateLayoutCell
import RxSwift

class NotificationViewController: BaseViewController {
    @IBOutlet weak var tableView: UITableView!

    let cellId = "NotificationCell"
    private var items: [NewsModel] = []
    private var categories: [NewModel] = []
    private var notifications: [NewNotification] = []
    fileprivate let dataSource = SimpleTableViewDataSource()
//    fileprivate var listDownload: [UITableViewCell: Disposable] = [:]
    

    override func viewDidLoad() {
        super.viewDidLoad()
        self.tableView.dataSource = dataSource
        self.tableView.register(UINib.init(nibName: cellId, bundle: nil), forCellReuseIdentifier: cellId)
//        self.getCategories()
        // Do any additional setup after loading the view.

        localizableTitle = "Notification.Title"

    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        self.navigationController?.setTransparent(false)
        self.getNotifications()
    }

    override func didReceiveMemoryWarning() {
        super.didReceiveMemoryWarning()
        // Dispose of any resources that can be recreated.
    }
    
    private func getNotifications() {
        guard let userId = Global.shared.user?.UserId else {
            return
        }
        self.showLoading()
        EcmProvider.rx.request(.getNotificationByUserID(userId)).mapObject(DDKCResponse<NewNotification>.self)
        
            .subscribe(onNext:{[weak self] response in
                self?.dismissLoading()
                guard let `self` = self else { return }
                
                guard let items = response.ListObject else{
                    print("Data wrong")
                    return
                }
                self.notifications = items
                let rows = items.map{ TableItem(data: $0, cellId: self.cellId, isOpen: false) }
                self.dataSource.removeAll()
                self.dataSource.addRows(rows)
                self.tableView.reloadData()
            }).disposed(by: disposeBag)
    }
    
    private func getCategories(){
        self.showLoading()
        EcmProvider.rx.request(.getNotification).mapObject(DDKCResponse<NewModel>.self)
            
            .subscribe(onNext:{[weak self] response in
                guard let items = response.ListObject else{
                    self?.dismissLoading()
                    print("Data wrong")
                    self?.dismissLoading()
                    return
                }
                self?.categories = items
                self?.getNotification()
            }).disposed(by: disposeBag)
    }
    
    private func getNotification(){
        guard !categories.isEmpty else {
            self.dismissLoading()
            return
        }
        let requestGroup = DispatchGroup()
        let _ = DispatchQueue.global(qos: .userInitiated)
        DispatchQueue.concurrentPerform(iterations: categories.count) { (i) in
            let categoryId = categories[i].CategoryId
            requestGroup.enter()
            self.getListNotification(categoryId, completionHander: {
                requestGroup.leave()
            })
        }
        
        requestGroup.notify(queue: DispatchQueue.main){
            print("Get data completion")
            self.dismissLoading()
            let items = self.items.sorted(by: {$0.getStartDate() > $1.getStartDate()}).map { TableItem(data: $0, cellId: self.cellId, isOpen: false) }
            self.dataSource.removeAll()
            self.dataSource.addRows(items)
            self.tableView.reloadData()

            UserDefaults.standard.set(Date().toServerString(), forKey: DefaultKey.lastNotificationDate.rawValue)
        }
    }
    
    private func getListNotification(_ categoryId: String?, completionHander: @escaping()->Void){
        guard let id = categoryId else {
            return
        }
        EcmProvider.rx.request(.getNewForCategory(id, nil, nil)).mapObject(DDKCResponse<NewsModel>.self)
            
            .subscribe(onNext:{[weak self] response in
                guard response.isSuccess(), let items = response.ListObject else {return}
                self?.items += items
                completionHander()
            }).disposed(by: disposeBag)
    }

    private func updateReadNotification(_ newNotification: NewNotification) {
        guard let id = newNotification.Id, let code = newNotification.ScreenCode else {
            return
        }
        EcmProvider.rx.request(.updateRead(id, code)).subscribe(onNext: {[weak self] (response) in
            self?.getNotifications()
        }).disposed(by: disposeBag)
    }
}

extension NotificationViewController: UITableViewDelegate {

    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return min(60, UITableViewAutomaticDimension)
    }

    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        let item = dataSource[indexPath]
        guard let notification = item.data as? NewNotification else {
            return
        }

        updateReadNotification(notification)

        if notification.ScreenCode == 0 {
            let vc = UIStoryboard.home[.newsDetail] as! NewsDetailViewController
            vc.type = NewType.notification(notification)
            show(vc)
        } else {
            guard let code = notification.ScreenCode,
                let type = RouteType(rawValue: code),
                let id = notification.RefId else {
                return
            }

            RouteManager(vc: self, type: type, params: [id]).route()
        }
    }
}
