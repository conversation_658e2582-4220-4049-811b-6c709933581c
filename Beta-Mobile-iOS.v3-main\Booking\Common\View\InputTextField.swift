//
//  InputTextField.swift
//  Booking-dev
//
//  Created by <PERSON><PERSON> on 4/6/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit

@IBDesignable
class InputTextField: RoundTextField {
    @IBInspectable var leftIcon: UIImage? {
        didSet {
            leftImageView.image = leftIcon
        }
    }
    
    var isShowCursor: Bool = true
    var isEnableMenu: Bool = true

    fileprivate lazy var _leftView: UIView = UIView()
    fileprivate var leftImageView: UIImageView!

    override init(frame: CGRect) {
        super.init(frame: frame)
        setup()
    }

    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        setup()
    }

    func setup() {
        let width = self.frame.height
        _leftView.frame = CGRect(x: 0, y: 0, width: width + 5, height: width)
        let lineView = UIView(frame: CGRect(x: width - 5, y: 11, width: 1, height: width - 22))
        lineView.autoresizingMask = [.flexibleHeight, .flexibleLeftMargin]
        lineView.translatesAutoresizingMaskIntoConstraints = true
        lineView.backgroundColor = .border
        _leftView.addSubview(lineView)

        leftImageView = UIImageView(frame: CGRect(x: (width - 26) / 2, y: (width - 26) / 2, width: 26, height: 26))
        leftImageView.contentMode = .scaleAspectFit
        leftImageView.autoresizingMask = [.flexibleTopMargin, .flexibleLeftMargin, .flexibleRightMargin, .flexibleBottomMargin]
        leftImageView.translatesAutoresizingMaskIntoConstraints = true
        _leftView.addSubview(leftImageView)

        _leftView.autoresizingMask = [.flexibleHeight, .flexibleRightMargin]
        _leftView.translatesAutoresizingMaskIntoConstraints = true
        self.leftView = _leftView
        self.leftViewMode = .always

        self.borderColor = .border
        self.borderWidth = 1
        self.cornerRadius = 4
        self.font = UIFont(fontName: .SourceSansPro, size: 16)
        self.textColor = .inputText
        self.tintColor = .inputText
        self.clearButtonMode = .whileEditing
    }

    override func layoutSubviews() {
        super.layoutSubviews()
        let width = self.frame.height
        _leftView.frame = CGRect(x: 0, y: 0, width: width + 5, height: width)
    }

    override func caretRect(for position: UITextPosition) -> CGRect {
        if !isShowCursor {
            return .zero
        }
        return super.caretRect(for: position)
    }

    override func canPerformAction(_ action: Selector, withSender sender: Any?) -> Bool {
//        UIMenuController.shared.isMenuVisible     = isEnableMenu
        return isEnableMenu
    }
}
