//
//  Response.swift
//  Booking-dev
//
//  Created by <PERSON>h Vu on 4/3/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation
import ObjectMapper

struct DDKCResponse<T:BaseMappable>: Mappable {
    var Object: T?
    var ListObject: [T]?
    var TotalCount: Int?
    var DataCount: Int?
    var Status: UInt?
    var Message: String?
    
    init?(map: Map) { }
    
    mutating func mapping(map: Map) {
        ListObject <- map["Data"]
        Object <- map["Data"]
        TotalCount <- map["TotalCount"]
        DataCount <- map["DataCount"]
        Status <- map["Status"]
        Message <- map["Message"]

        Message = Message?.localized
    }
    
    func isSuccess() -> Bool{
        return Status == 1
    }
}

struct MyResponse<T>: Mappable {
    var Object: T?
    var ListObject: [T]?
    var TotalCount: Int?
    var DataCount: Int?
    var Status: UInt?
    var Message: String?

    init?(map: Map) { }

    mutating func mapping(map: Map) {
        ListObject <- map["Data"]
        Object <- map["Data"]
        TotalCount <- map["TotalCount"]
        DataCount <- map["DataCount"]
        Status <- map["Status"]
        Message <- map["Message"]

        Message = Message?.localized
    }

    func isSuccess() -> Bool{
        return Status == 1
    }
}

class EmptyModel: Mappable {
    required init?(map: Map) {

    }

    func mapping(map: Map) {

    }
}

class ConfirmModel: Mappable {
    required init?(map: Map) {
        
    }
    
    func mapping(map: Map) {
        Result <- map["Result"]
    }
    
    var Result: Bool?
    
    
}

class NotificationCount: Mappable {
    required init?(map: Map) {
        
    }
    
    func mapping(map: Map) {
        NumberUnread <- map["NumberUnread"]
        Total <- map["Total"]
        TotalRead <- map["TotalRead"]
        TotalUnread <- map["TotalUnRead"]
    }
    
    var NumberUnread: Int?
    var Total: Int?
    var TotalRead: Int?
    var TotalUnread: Int?
}

class RegisterDeviceToken: Mappable {
    var DeviceToken: String?
    var DeviceId: String?
    var AccountId: String?
    
    required init?(map: Map) {
        
    }
    
    func mapping(map: Map) {
        DeviceToken <- map["DeviceToken"]
        DeviceId <- map["DeviceId"]
        AccountId <- map["AccountId"]
    }
}


