import 'package:barcode/barcode.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_app/core/index.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:qr_flutter/qr_flutter.dart';

import '../../constants/index.dart';
import '../../models/transaction_history_model.dart';
import '../../service/index.dart';
import '../../utils/index.dart';

/// Transaction Detail Screen - tương tự iOS TransactionDetailViewController
class TransactionDetailScreenIOSStyle extends StatefulWidget {
  final String transactionId;
  final String userId;
  final bool backToHome;
  final TransactionHistoryModel? item; // tương tự iOS item property

  const TransactionDetailScreenIOSStyle({
    super.key,
    required this.transactionId,
    required this.userId,
    this.backToHome = false,
    this.item,
  });

  @override
  State<TransactionDetailScreenIOSStyle> createState() => _TransactionDetailScreenIOSStyleState();
}

class _TransactionDetailScreenIOSStyleState extends State<TransactionDetailScreenIOSStyle> {
  TransactionHistoryDetailModel? _transaction;
  bool _isLoading = true;
  String? _error;
  final List<KeyValueEntry> _paymentItems = []; // tương tự iOS items array

  @override
  void initState() {
    super.initState();
    _loadTransactionDetail();
  }

  /// Get data like iOS getData() method
  Future<void> _loadTransactionDetail() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Use same API endpoint as iOS: AccountProvider.rx.request(.getTransactionDetails(userId, tranId))
      final api = RepositoryProvider.of<Api>(context).auth;
      final response = await api.getTransactionDetail(widget.userId, widget.transactionId);

      if (response != null) {
        final transactionData = TransactionHistoryDetailModel.fromJson(response.data);
        setState(() {
          _transaction = transactionData;
          _isLoading = false;
        });
        _fillData(transactionData); // tương tự iOS fillData method
      } else {
        setState(() {
          _isLoading = false;
          _error = response?.message ?? "Không thể tải thông tin giao dịch";
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _error = "Lỗi: $e";
      });
    }
  }

  /// Fill data like iOS fillData method
  void _fillData(TransactionHistoryDetailModel model) {
    _paymentItems.clear();

    // Build payment items exactly like iOS
    if (model.paymentModel != null) {
      for (var payment in model.paymentModel!) {
        if (payment.values != null && payment.values! > 0) {
          String key = _getPaymentTypeLabel(payment.paymentTypeCode ?? '');
          String value = Convert.price(payment.values!);

          // Special handling for BETAID like iOS
          if (payment.paymentTypeCode == 'BETAID') {
            value = "${model.spendingPoint ?? 0} - $value";
          }

          _paymentItems.add(KeyValueEntry(key: key, value: value));
        }
      }
    }
  }

  /// Get payment type label like iOS
  String _getPaymentTypeLabel(String paymentTypeCode) {
    switch (paymentTypeCode) {
      case 'CASH':
        return 'Tiền mặt';
      case 'VOUCHER':
        return 'Voucher';
      case 'CARD':
      case 'ONEPAY':
        return 'Thẻ';
      case 'BETAID':
        return 'Điểm';
      case 'SHOPEEPAY_ONLINE':
        return 'Airpay';
      case 'MOMO':
        return 'MoMo';
      case 'ZALOPAY':
        return 'ZaloPay';
      default:
        return paymentTypeCode;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: appBar(
        title:
          'Chi tiết giao dịch',
        titleColor: Colors.white,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
          onPressed: () {
            if (widget.backToHome) {
              // tương tự iOS backToHome logic
              Navigator.of(context).popUntil((route) => route.isFirst);
            } else {
              Navigator.of(context).pop();
            }
          },
        ),
      ),
      backgroundColor: Colors.grey[300],

      body: _isLoading
          ? Center(child: CircularProgressIndicator(color: CColor.primary))
          : _error != null
              ? _buildErrorState()
              : _buildTransactionDetail(),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            _error ?? 'Có lỗi xảy ra',
            style: TextStyle(fontSize: 16, color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadTransactionDetail,
            child: const Text('Thử lại'),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionDetail() {
    if (_transaction == null) {
      return const Center(child: Text('Không có thông tin giao dịch'));
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: 8,
        children: [
          // Main transaction info - tương tự iOS stackView
          _buildMainInfoCard(),
          // Payment details table - tương tự iOS tableView
          if (_paymentItems.isNotEmpty) _buildPaymentTable(),

          // QR Code/Barcode - tương tự iOS ivBarcode
          if (_transaction!.no != null && _transaction!.no!.isNotEmpty) _buildBarcodeCard(),

          // Notice - tương tự iOS lbNotice
          _buildNoticeCard(),
        ],
      ),
    );
  }

  Widget _buildMainInfoCard() {
    return Padding(
      padding: const EdgeInsets.all(4),
      child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(0),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            spacing: 8,
            children: [
              // Film name - tương tự iOS lbFilmName
              Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    _transaction!.filmModel?.Name ?? _transaction!.filmName ?? 'N/A',
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      fontFamily: 'Oswald',
                    ),
                  ),

                  const SizedBox(height: 8),

                  // Film details - tương tự iOS lbFilmDetail
                  Text(
                    _buildFilmDetails(),
                    style: TextStyle(fontSize: 18, color: Colors.grey[600]),
                  ),
                ],
              ),
              const Divider(),
              const SizedBox(height: 8),

              // Cinema - tương tự iOS lbCinema
              _buildInfoRow('Rạp:', _transaction!.cinemaName ?? 'N/A'),

              // Date and Time - tương tự iOS lbDate, lbTime
              _buildInfoRow('Ngày:', _formatShowDate()),
              _buildInfoRow('Giờ:', _formatShowTime()),

              // Room - tương tự iOS lbRoom
              _buildInfoRow('Phòng:', _transaction!.screenName ?? 'N/A'),

              // Seats - tương tự iOS lbSeat
              if (_getSeatInfo().isNotEmpty) _buildInfoRow('Ghế (${_getSeatCount()}):', _getSeatInfo()),

              // Combos - tương tự iOS lbCombo
              if (_getComboInfo().isNotEmpty) _buildInfoRow('Combo (${_getComboCount()}):', _getComboInfo()),

              const SizedBox(height: 12),

              // Total money - tương tự iOS lbTotalMoney
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Tổng tiền:',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  Text(
                    Convert.price(_transaction!.totalPayment ?? 0),
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.red,
                      fontFamily: 'Oswald',
                    ),
                  ),
                ],
              ),

              // Points info - tương tự iOS titlePoint, titleDatePoint
              if (_transaction!.quantityPoint != null && _transaction!.quantityPoint! > 0) ...[
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text('Điểm tích lũy:',
                      style: TextStyle(fontSize: 16),
                    ),
                    Text(
                      '${_transaction!.quantityPoint?.toInt()}',
                      style: const TextStyle(color: Colors.indigo,fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
                if (_transaction!.dateExpiredPoint != null)
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text('Hạn điểm:',
                        style: const TextStyle(fontSize: 16),),
                      Text(
                        _formatDate(_transaction!.dateExpiredPoint!),
                        style: const TextStyle(color: Colors.orange,fontSize: 16),
                      ),
                    ],
                  ),
              ],
            ],
          )),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: TextStyle(color: Colors.grey[600], fontSize: CFontSize.lg, fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontWeight: FontWeight.bold, fontSize: CFontSize.xl),
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods for data formatting - tương tự iOS computed properties
  String _buildFilmDetails() {
    final film = _transaction!.filmModel;
    if (film == null) return '';

    List<String> details = [];
    if (film.RestrictAgeString?.isNotEmpty == true) details.add(film.RestrictAgeString!);
    if (film.FilmFormatName?.isNotEmpty == true) details.add(film.FilmFormatName!);
    if (film.Duration != null) details.add('${film.Duration} phút');

    return details.join(' | ');
  }

  String _formatShowDate() {
    if (_transaction!.showTime == null) return 'N/A';
    try {
      final date = DateTime.parse(_transaction!.showTime!);
      return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
    } catch (e) {
      return 'N/A';
    }
  }

  String _formatShowTime() {
    if (_transaction!.showTime == null) return 'N/A';
    try {
      final date = DateTime.parse(_transaction!.showTime!);
      return '${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return 'N/A';
    }
  }

  int _getSeatCount() {
    return _transaction!.quantitySeat ?? 0;
  }

  String _getSeatInfo() {
    if (_transaction!.listTicketType == null) return '';

    List<String> seatInfo = [];
    for (var ticket in _transaction!.listTicketType!) {
      if (ticket.listSeatName?.isNotEmpty == true) {
        seatInfo.add(ticket.listSeatName!.join(', '));
      }
    }
    return seatInfo.join('\n');
  }

  int _getComboCount() {
    return _transaction!.listCombo?.length ?? 0;
  }

  String _getComboInfo() {
    if (_transaction!.listCombo == null) return '';

    return _transaction!.listCombo!.map((combo) => '${combo.name ?? ''}(${combo.quantity ?? 0})').join(', ');
  }

  String _formatDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
    } catch (e) {
      return dateString;
    }
  }

  /// Build payment table - tương tự iOS tableView with TransactionDetailCell
  Widget _buildPaymentTable() {
    return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(0),
          border: Border.all(color: Colors.grey[300]!),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Padding(
              padding: EdgeInsets.all(16),
              child: Text(
                'Chi tiết thanh toán',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const Divider(height: 1),
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _paymentItems.length,
              separatorBuilder: (context, index) => const Divider(height: 1),
              itemBuilder: (context, index) {
                final item = _paymentItems[index];
                return ListTile(
                  title: Text(item.key),
                  trailing: Text(
                    item.value,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                );
              },
            ),
          ],
        ));
  }

  /// Build barcode card - tương tự iOS ivBarcode và lbCodeNumber
  Widget _buildBarcodeCard() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(0),
          border: Border.all(color: Colors.grey[300]!),
        ),
        child: Column(
          children: [
            SizedBox(
              width: CSpace.width - 50,
              child: FutureBuilder<String>(
                future: _generateBarcode(_transaction!.no!),
                builder: (context, snapshot) {
                  if (snapshot.hasData) {
                    return SvgPicture.string(
                      snapshot.data!,
                      height: 70,
                      fit: BoxFit.fitWidth,
                    );
                  }
                  return const SizedBox(
                    height: 50,
                    child: Center(child: CircularProgressIndicator()),
                  );
                },
              ),
            ),
            const SizedBox(height: 8),

            // Code number - tương tự iOS lbCodeNumber
            Text(
              _transaction!.no ?? '',
              style: const TextStyle(fontSize: CFontSize.lg),
            ),
          ],
        ),
      ),
    );
  }

  Future<String> _generateBarcode(String data) async {
    final bc = Barcode.code128();
    return bc.toSvg(
      data,
      width: 300,
      height: 80,
      fontHeight: 0,
    );
  }

  /// Build notice card - tương tự iOS lbNotice
  Widget _buildNoticeCard() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Vui lòng đưa mã số này đến quầy vé Beta để nhận vé',
            style: TextStyle(fontSize: CFontSize.sm),
          ),
          const SizedBox(height: 8),
          Text(
            'Lưu ý: Beta không chấp nhận hoàn tiền hoặc đổi vé đã thanh toán thành công trên Website và Ứng dụng Beta ',
            style: TextStyle(fontSize: CFontSize.sm, color: Colors.grey[600]),
          ),
        ],
      ),
    );
  }
}

/// Key-Value entry for payment table - tương tự iOS KeyValueEntry
class KeyValueEntry {
  final String key;
  final String value;

  KeyValueEntry({required this.key, required this.value});
}
