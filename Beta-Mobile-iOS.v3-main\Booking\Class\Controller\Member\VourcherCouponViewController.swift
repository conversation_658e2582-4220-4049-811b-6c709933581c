//
//  VourcherCouponViewController.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 3/14/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit

class VourcherCouponViewController: BaseViewController {
    @IBOutlet weak var tableView: UITableView!

    let dataSource = SimpleTableViewDataSource()
    let cellId = "SettingTableCell"

    override func viewDidLoad() {
        super.viewDidLoad()

        localizableTitle = "VoucherCoupon.Title"
        // Do any additional setup after loading the view.
        tableView.dataSource = dataSource
        tableView.register(UINib(nibName: cellId, bundle: nil), forCellReuseIdentifier: cellId)

        loadMenu()
    }

    override func didReceiveMemoryWarning() {
        super.didReceiveMemoryWarning()
        // Dispose of any resources that can be recreated.
    }

    func loadMenu() {
        var items: [TableItem] = []
        items.append(TableItem(title: "BetaVoucher".localized, cellId: cellId, tag: .voucher))
        items.append(TableItem(title: "BetaCoupon".localized, cellId: cellId, tag: .coupon))
        dataSource.addRows(items)
    }
}

extension VourcherCouponViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: false)

        let item = dataSource[indexPath]
        if item.tag == .voucher {
            let vc = UIStoryboard.member[.voucher]
            show(vc, sender: nil)
        } else if item.tag == .coupon {
            let vc = UIStoryboard.member[.coupon]
            show(vc, sender: nil)
        }
    }
}

extension TableItemTag {
    static let voucher = TableItemTag(value: 1)
    static let coupon = TableItemTag(value: 2)
}
