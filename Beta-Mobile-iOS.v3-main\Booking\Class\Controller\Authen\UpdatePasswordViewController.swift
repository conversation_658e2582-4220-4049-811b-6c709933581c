//
//  UpdatePasswordViewController.swift
//  Booking-dev
//
//  Created by <PERSON><PERSON> on 7/31/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit
import IQKeyboardManagerSwift

class UpdatePasswordViewController: BaseViewController {

//    @IBOutlet weak var tfCurrentPass: InputTextField!
    @IBOutlet weak var tfNewPass: InputTextField!
    @IBOutlet weak var tfNewPass2: InputTextField!

    var openAccountInfo: Bool = false

    override func viewDidLoad() {
        super.viewDidLoad()

        localizableTitle = "UpdatePassword.Title"

        // remove login VC from navigation controller
        if var listVC = self.navigationController?.viewControllers {
            if let confirmIndex = listVC.index(where: { $0 is LoginViewController }){
                listVC.remove(at: confirmIndex)
                self.navigationController?.viewControllers = listVC
            }
        }
    }
    @IBAction func updateBtPressed(_ sender: Any) {
        view.endEditing(true)
        if validate() {
            self.updatePassword()
        }
    }
}

extension UpdatePasswordViewController {
    
    private func updatePwObs(email: String) -> Account {
        if (Global.shared.user?.IsUpdatedApplePassword == false) {
            return .updateApplePassword(email, tfNewPass.text!);
        }
        return .updatePassword(email, tfNewPass.text!);
    }
    
    private func updatePassword() {
        guard let email = Global.shared.user?.Email else {
            print("Can't get email from logged User")
            self.flashError(title: "Alert.Error".localized, message: "Alert.ErrorServer".localized)
            return
        }

        self.showLoading()
        AccountProvider.rx.request(updatePwObs(email: email)).mapObject(DDKCResponse<ConfirmModel>.self)
            
            .subscribe(onNext: {[weak self] (response) in
                guard let `self` = self else {return}
                self.dismissLoading()
                guard let confirm = response.Object else{
                    print("Data wrong")
                    self.flashError(title: "Alert.UpdatePassFailed".localized)
                    return
                }

                if let confirm = confirm.Result, confirm{
                    self.flashSuccess()
//                    self.getProfile(id: Global.shared.user?.UserId)
                    if self.openAccountInfo {
                        let vc = UIStoryboard.member[.accountInfo]
                        self.show(vc, sender: nil)
                    } else {
                        self.navigationController?.popToRootViewController(animated: true)
                    }
                }else{
                    self.flashError(title: "Alert.Error".localized, message: response.Message ?? "Alert.ErrorServer".localized)
                }

            }).disposed(by: disposeBag)
    }

    private func validate() -> Bool{

        if let text = tfNewPass.text, text.isEmpty{
            UIAlertController.showAlert(self, message: "Alert.NewPasswordNotEmpty".localized)
            return false
        }

        if let text = tfNewPass2.text, text.isEmpty{
            UIAlertController.showAlert(self, message: "Alert.ConfirmPasswordNotEmpty".localized)
            return false
        }

        if let text = tfNewPass.text, let text2 = tfNewPass2.text, text != text2{
            UIAlertController.showAlert(self, message: "Alert.ConfirmPasswordInvalid".localized)
            return false
        }

        if tfNewPass.text?.isValidPassword() != true {
            UIAlertController.showAlert(self, message: "Alert.PasswordInvalid".localized, handler: { _ in
                //                self.passwordTextField.becomeFirstResponder()
            })
            return false
        }

        return true
    }

    private func getProfile(id: String?, email: String? = nil){
        guard let userId = id else {
            self.dismissLoading()
            self.flashError(title: "Alert.Error".localized, message: "Alert.LoginFailed".localized)
            return
        }
        self.registerDeviceToken(userId)
        AccountProvider.rx.request(.getProfileById(userId)).mapObject(DDKCResponse<UserModel>.self)
            
            .subscribe(onNext:{ [weak self] response in
                self?.dismissLoading()
                guard let object = response.Object else{
                    print("Data wrong")
                    self?.showAlert(message: "Alert.LoginFailed".localized)
                    return
                }
                if response.isSuccess(){
                    print("Name: \(object.FullName ?? "")")
                    if let user = Global.shared.user{
                        let fullUser = object
                        fullUser.Token = user.Token
                        fullUser.UserId = user.UserId
                        Global.shared.saveUser(fullUser)
                    }
                    self?.navigationController?.popToRootViewController(animated: true)
                } else {
                    self?.showAlert(message: "Alert.LoginFailed".localized)
                }

                }, onError: { [weak self] _ in
                    self?.showAlert(message: "Alert.LoginFailed".localized)
                    self?.dismissLoading()
            }).disposed(by: disposeBag)
    }

    private func registerDeviceToken(_ accountId: String) {
        guard let token: String = UserDefaults.standard.object(forKey: DefaultKey.deviceToken.rawValue) as? String,
            let deviceId = UIDevice.current.identifierForVendor?.uuidString else {
                return
        }
        AccountProvider.rx.request(.registerDeviceToken(deviceId, accountId, token)).mapObject(DDKCResponse<RegisterDeviceToken>.self)
            .subscribe().disposed(by: disposeBag)
    }
}

extension UpdatePasswordViewController: UITextFieldDelegate {
    func textFieldShouldReturn(_ textField: UITextField) -> Bool {
        if textField == tfNewPass2 {
            updateBtPressed(textField)
        } else {
            IQKeyboardManager.shared.goNext()
        }
        return true
    }
}

