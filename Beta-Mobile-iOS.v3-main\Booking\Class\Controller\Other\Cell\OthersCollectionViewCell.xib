<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14490.70" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14490.49"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="Oswald-Regular.ttf">
            <string>Oswald-Regular</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" id="gTV-IL-0wX" customClass="OthersCollectionViewCell" customModule="Booking_dev" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="200" height="198"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="200" height="198"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="yQt-AH-kBo" customClass="RoundView" customModule="Booking_dev" customModuleProvider="target">
                        <rect key="frame" x="12" y="12" width="176" height="174"/>
                        <subviews>
                            <view alpha="0.10000000000000001" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="oQR-a6-Hu9" customClass="RoundView" customModule="Booking_dev" customModuleProvider="target">
                                <rect key="frame" x="60" y="39" width="56" height="56"/>
                                <color key="backgroundColor" red="0.**********" green="0.87843137250000003" blue="0.38823529410000002" alpha="1" colorSpace="calibratedRGB"/>
                                <constraints>
                                    <constraint firstAttribute="width" secondItem="oQR-a6-Hu9" secondAttribute="height" id="10B-bZ-XHR"/>
                                    <constraint firstAttribute="width" constant="56" id="WpC-gv-l3y"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                        <real key="value" value="3"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </view>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="66C-lC-1dW">
                                <rect key="frame" x="76" y="55" width="24" height="24"/>
                                <constraints>
                                    <constraint firstAttribute="width" secondItem="66C-lC-1dW" secondAttribute="height" id="GJu-Lg-VPD"/>
                                    <constraint firstAttribute="width" constant="24" id="gfZ-Q2-WGo"/>
                                </constraints>
                            </imageView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Ttf-CK-cnB">
                                <rect key="frame" x="8" y="111" width="160" height="25.5"/>
                                <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="17"/>
                                <color key="textColor" red="0.13333333333333333" green="0.2196078431372549" blue="0.28627450980392155" alpha="0.84705882352941175" colorSpace="calibratedRGB"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="Ttf-CK-cnB" firstAttribute="top" secondItem="oQR-a6-Hu9" secondAttribute="bottom" constant="16" id="5HS-a9-5jp"/>
                            <constraint firstItem="oQR-a6-Hu9" firstAttribute="centerX" secondItem="yQt-AH-kBo" secondAttribute="centerX" id="C2l-Mx-vcQ"/>
                            <constraint firstItem="66C-lC-1dW" firstAttribute="centerY" secondItem="oQR-a6-Hu9" secondAttribute="centerY" id="Gsg-pT-Us4"/>
                            <constraint firstItem="66C-lC-1dW" firstAttribute="centerX" secondItem="oQR-a6-Hu9" secondAttribute="centerX" id="Kob-Pk-u1v"/>
                            <constraint firstItem="oQR-a6-Hu9" firstAttribute="centerY" secondItem="yQt-AH-kBo" secondAttribute="centerY" constant="-20" id="ST8-eO-O9C"/>
                            <constraint firstItem="Ttf-CK-cnB" firstAttribute="leading" secondItem="yQt-AH-kBo" secondAttribute="leading" constant="8" id="bCv-92-6Wf"/>
                            <constraint firstAttribute="trailing" secondItem="Ttf-CK-cnB" secondAttribute="trailing" constant="8" id="oGx-tW-cIE"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="color" keyPath="shadowColor">
                                <color key="value" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </userDefinedRuntimeAttribute>
                            <userDefinedRuntimeAttribute type="point" keyPath="shadowOffset">
                                <point key="value" x="1" y="8"/>
                            </userDefinedRuntimeAttribute>
                            <userDefinedRuntimeAttribute type="number" keyPath="shadowRadius">
                                <real key="value" value="4"/>
                            </userDefinedRuntimeAttribute>
                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                <real key="value" value="8"/>
                            </userDefinedRuntimeAttribute>
                            <userDefinedRuntimeAttribute type="number" keyPath="shadowOpacity">
                                <real key="value" value="0.10000000000000001"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </view>
                </subviews>
            </view>
            <constraints>
                <constraint firstItem="yQt-AH-kBo" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" constant="12" id="8F3-az-L8c"/>
                <constraint firstAttribute="trailing" secondItem="yQt-AH-kBo" secondAttribute="trailing" constant="12" id="GOW-Hs-VVp"/>
                <constraint firstAttribute="bottom" secondItem="yQt-AH-kBo" secondAttribute="bottom" constant="12" id="UvS-7h-rHL"/>
                <constraint firstItem="yQt-AH-kBo" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" constant="12" id="VYR-of-7bf"/>
            </constraints>
            <viewLayoutGuide key="safeArea" id="ZTg-uK-7eu"/>
            <size key="customSize" width="200" height="198"/>
            <connections>
                <outlet property="iconImageView" destination="66C-lC-1dW" id="UGH-TL-Qyb"/>
                <outlet property="iconView" destination="oQR-a6-Hu9" id="0MA-2C-pkJ"/>
                <outlet property="titleLabel" destination="Ttf-CK-cnB" id="tl4-A5-bFR"/>
            </connections>
            <point key="canvasLocation" x="240.57971014492756" y="186.16071428571428"/>
        </collectionViewCell>
    </objects>
</document>
