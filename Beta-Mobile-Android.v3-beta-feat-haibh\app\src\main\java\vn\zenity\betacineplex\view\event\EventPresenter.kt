package vn.zenity.betacineplex.view.event

import vn.zenity.betacineplex.Manager.Network.APIClient
import vn.zenity.betacineplex.app.App
import vn.zenity.betacineplex.helper.extension.applyOn
import vn.zenity.betacineplex.helper.extension.logD
import vn.zenity.betacineplex.model.NewModel
import java.lang.ref.WeakReference

/**
 * Created by Zenity.
 */

class EventPresenter : EventContractor.Presenter {
    override fun getListNewsCategories() {
        view?.get()?.showLoading()
        val lang = App.shared().getCurrentLang()
        APIClient.shared.ecmAPI.getNewEvent(if (lang == "en") lang else "")
                .applyOn()
                .subscribe({
                    view?.get()?.hideLoading()
                    val data = it.Data?.filter { it != null } as? ArrayList<NewModel>
                    if (data?.size ?: 0 > 0) {
                        view?.get()?.showListNewsCategories(data!!)
                    }
                }, {
                    view?.get()?.hideLoading()
                })
    }

    private var view: WeakReference<EventContractor.View?>? = null
    override fun attachView(view: EventContractor.View) {
        this.view = WeakReference(view)
    }

    override fun detachView() {
        this.view?.clear()
        this.view = null
    }
}
