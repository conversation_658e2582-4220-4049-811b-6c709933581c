//
//  ChangePassViewController.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 3/14/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit
import IQKeyboardManagerSwift

class ChangePassViewController: BaseViewController {
    @IBOutlet weak var tfCurrentPass: InputTextField!
    @IBOutlet weak var tfNewPass: InputTextField!
    @IBOutlet weak var tfNewPass2: InputTextField!

    override func viewDidLoad() {
        super.viewDidLoad()

       localizableTitle = "ChangePass.Title"
    }
    @IBAction func updateBtPressed(_ sender: Any) {
        view.endEditing(true)
        if validate() {
            self.changePass()
        }
    }
}

extension ChangePassViewController{
    private func changePass(){
        guard let email = Global.shared.user?.Email else {
            print("Can't get email from logged User")
            self.flashError(title: "Alert.Error".localized, message: "Alert.ErrorServer".localized)
            return
        }
        self.showLoading()
        AccountProvider.rx.request(.changePass(email, tfNewPass.text!, tfCurrentPass.text!)).mapObject(DDKCResponse<ConfirmModel>.self)
            
            .subscribe(onNext: {[weak self] (response) in
                guard let `self` = self else {return}
                self.dismissLoading()
                guard let confirm = response.Object else{
                    print("Data wrong")
                    self.flashError(title: "Alert.ChangePassFailed".localized)
                    return
                }
                
                if let confirm = confirm.Result, confirm{
                    self.flashSuccess()
                    self.navigationController?.popViewController(animated: true)
                }else{
                    self.flashError(title: "Alert.Error".localized, message: response.Message ?? "Alert.ErrorServer".localized)
                }
                
            }).disposed(by: disposeBag)
    }
    
    private func validate() -> Bool{
        if let text = tfCurrentPass.text, text.isEmpty{
            UIAlertController.showAlert(self, message: "Alert.PasswordNotEmpty".localized)
            return false
        }
        
        if let text = tfNewPass.text, text.isEmpty{
            UIAlertController.showAlert(self, message: "Alert.NewPasswordNotEmpty".localized)
            return false
        }
        
        if let text = tfNewPass2.text, text.isEmpty{
            UIAlertController.showAlert(self, message: "Alert.ConfirmPasswordNotEmpty".localized)
            return false
        }
        
        if let text = tfNewPass.text, let text2 = tfNewPass2.text, text != text2{
            UIAlertController.showAlert(self, message: "Alert.ConfirmPasswordInvalid".localized)
            return false
        }

        if tfNewPass.text?.isValidPassword() != true {
            UIAlertController.showAlert(self, message: "Alert.PasswordInvalid".localized, handler: { _ in
                //                self.passwordTextField.becomeFirstResponder()
            })
            return false
        }
        
        return true
    }
}

extension ChangePassViewController: UITextFieldDelegate {
    func textFieldShouldReturn(_ textField: UITextField) -> Bool {
        if textField == tfNewPass2 {
            updateBtPressed(textField)
        } else {
            IQKeyboardManager.shared.goNext()
        }
        return true
    }
}
