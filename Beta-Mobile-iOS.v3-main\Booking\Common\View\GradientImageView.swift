//
//  GradientImageView.swift
//  Booking-dev
//
//  Created by <PERSON><PERSON> on 4/17/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit

@IBDesignable
class GradientImageView: RoundImageView {
//    fileprivate var gradientLayer: CAGradientLayer {
//        return self.layer as! CAGradientLayer
//    }

    @IBInspectable var firstColor: UIColor = .gradientBg1 {
        didSet {
            setNeedsLayout()
        }
    }

    @IBInspectable var secondColor: UIColor = .gradientBg2 {
        didSet {
            setNeedsLayout()
        }
    }

    @IBInspectable var startPoint: CGPoint = CGPoint(x: 0, y: 0.5) {
        didSet {
            setNeedsLayout()
        }
    }

    @IBInspectable var endPoint: CGPoint = CGPoint(x: 1, y: 0.5) {
        didSet {
            setNeedsLayout()
        }
    }

//    override public class var layerClass: Swift.AnyClass {
//        return CAGradientLayer.self
//    }

    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
    }

    override func layoutSubviews() {
        super.layoutSubviews()
//        self.gradientLayer.colors = [firstColor.cgColor, secondColor.cgColor]
//        self.gradientLayer.startPoint = startPoint
//        self.gradientLayer.endPoint = endPoint
        let layer = CAGradientLayer.defaultGradient
        layer.colors = [firstColor, secondColor].map { $0.cgColor }
        layer.startPoint = self.startPoint
        layer.endPoint = self.endPoint
        layer.frame = bounds
        self.image = layer.image
    }
}

