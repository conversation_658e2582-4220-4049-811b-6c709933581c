//
//  RoundTextField.swift
//  Booking-dev
//
//  Created by <PERSON><PERSON> on 4/5/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit

class RoundTextField: UITextField, Localizable {
    @IBInspectable var localizableString: String? { didSet { updateLocalizable() } }

    func updateLocalizable() {
        if let text = localizableString {
            self.placeholder = text.localized
        }
    }
    
    /* The color of the shadow. Defaults to opaque black. Colors created
     * from patterns are currently NOT supported. Animatable. */
    @IBInspectable public var shadowColor: UIColor? {
        set {
            layer.shadowColor = newValue!.cgColor
        }
        get {
            if let color = layer.shadowColor {
                return UIColor(cgColor: color)
            } else {
                return nil
            }
        }
    }

    /* The opacity of the shadow. Defaults to 0. Specifying a value outside the
     * [0,1] range will give undefined results. Animatable. */
    @IBInspectable public var shadowOpacity: Float {
        set {
            layer.shadowOpacity = newValue
        }
        get {
            return layer.shadowOpacity
        }
    }

    /* The shadow offset. Defaults to (0, -3). Animatable. */
    @IBInspectable public var shadowOffset: CGPoint {
        set {
            layer.shadowOffset = CGSize(width: newValue.x, height: newValue.y)
        }
        get {
            return CGPoint(x: layer.shadowOffset.width, y:layer.shadowOffset.height)
        }
    }

    /* The blur radius used to create the shadow. Defaults to 3. Animatable. */
    @IBInspectable public var shadowRadius: CGFloat {
        set {
            layer.shadowRadius = newValue
        }
        get {
            return layer.shadowRadius
        }
    }

    /* The corner radius of the view. */
    @IBInspectable public var cornerRadius: CGFloat {
        set {
            layer.cornerRadius = newValue
        }
        get {
            return layer.cornerRadius
        }
    }

    @IBInspectable public var borderWidth: CGFloat {
        set {
            layer.borderWidth = newValue
        }
        get {
            return layer.borderWidth
        }
    }

    @IBInspectable public var borderColor:UIColor = UIColor.lightGray {
        didSet {
            layer.borderColor = borderColor.cgColor
        }
    }

    @IBInspectable public var horizontalSpace: CGFloat = 0 {
        didSet {
            setNeedsLayout()
        }
    }

    @IBInspectable public var verticalSpace: CGFloat = 0 {
        didSet {
            setNeedsLayout()
        }
    }


    override func textRect(forBounds bounds: CGRect) -> CGRect {
        let leftWidth: CGFloat = leftView?.frame.width ?? 0
        let rightWidth: CGFloat = rightView?.frame.width ?? 0
        return CGRect(x: bounds.origin.x + horizontalSpace + leftWidth, y: bounds.origin.y + verticalSpace, width: bounds.width - horizontalSpace * 2 - leftWidth - rightWidth, height: bounds.height - verticalSpace * 2)
    }

    override func editingRect(forBounds bounds: CGRect) -> CGRect {
        let leftWidth: CGFloat = leftView?.frame.width ?? 0
        let rightWidth: CGFloat = rightView?.frame.width ?? 0
        return CGRect(x: bounds.origin.x + horizontalSpace + leftWidth, y: bounds.origin.y + verticalSpace, width: bounds.width - horizontalSpace * 2 - leftWidth - rightWidth, height: bounds.height - verticalSpace * 2)
    }

    override func rightViewRect(forBounds bounds: CGRect) -> CGRect {
        guard let frame = rightView?.frame else {
            return .zero
        }
        return CGRect(x: bounds.width - frame.width - horizontalSpace, y: (bounds.height - frame.height) / 2, width: frame.width, height: frame.height)
    }

    override func leftViewRect(forBounds bounds: CGRect) -> CGRect {
        guard let frame = leftView?.frame else {
            return .zero
        }
        return CGRect(x: frame.origin.x + horizontalSpace, y: (bounds.height - frame.height) / 2, width: frame.width, height: frame.height)
    }
}
