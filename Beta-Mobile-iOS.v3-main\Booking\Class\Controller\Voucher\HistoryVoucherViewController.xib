<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14490.70" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14490.49"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="HistoryVoucherViewController" customModule="Booking_dev" customModuleProvider="target">
            <connections>
                <outlet property="descriptionLabel" destination="K9f-sm-T11" id="Qmv-Pd-0ai"/>
                <outlet property="tableView" destination="jkD-S9-IQr" id="sOg-5Z-Ock"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" style="plain" separatorStyle="default" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="jkD-S9-IQr">
                    <rect key="frame" x="0.0" y="124" width="414" height="738"/>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <connections>
                        <outlet property="dataSource" destination="-1" id="Xje-xl-jFS"/>
                        <outlet property="delegate" destination="-1" id="41R-3J-Trd"/>
                    </connections>
                </tableView>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="hwc-38-I68">
                    <rect key="frame" x="0.0" y="44" width="414" height="80"/>
                    <subviews>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="K9f-sm-T11">
                            <rect key="frame" x="24" y="30" width="366" height="20.5"/>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <color key="textColor" red="0.070588235289999995" green="0.1215686275" blue="0.15686274510000001" alpha="1" colorSpace="calibratedRGB"/>
                            <nil key="highlightedColor"/>
                        </label>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstItem="K9f-sm-T11" firstAttribute="leading" secondItem="hwc-38-I68" secondAttribute="leading" constant="24" id="I4r-bn-Qgq"/>
                        <constraint firstAttribute="height" constant="80" id="JRA-gW-n63"/>
                        <constraint firstItem="K9f-sm-T11" firstAttribute="centerY" secondItem="hwc-38-I68" secondAttribute="centerY" id="aDK-Mn-aU5"/>
                        <constraint firstAttribute="trailing" secondItem="K9f-sm-T11" secondAttribute="trailing" constant="24" id="eYC-mJ-Dob"/>
                    </constraints>
                </view>
            </subviews>
            <color key="backgroundColor" red="0.95294117649999999" green="0.95294117649999999" blue="0.95294117649999999" alpha="1" colorSpace="calibratedRGB"/>
            <constraints>
                <constraint firstItem="hwc-38-I68" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" id="4Gk-bQ-drd"/>
                <constraint firstItem="jkD-S9-IQr" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" id="AxL-iX-2Ws"/>
                <constraint firstItem="hwc-38-I68" firstAttribute="top" secondItem="fnl-2z-Ty3" secondAttribute="top" id="eBs-8x-Bnw"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="bottom" secondItem="jkD-S9-IQr" secondAttribute="bottom" id="gZX-2e-Dsw"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="hwc-38-I68" secondAttribute="trailing" id="m0h-e6-MdB"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="jkD-S9-IQr" secondAttribute="trailing" id="pXD-eC-18W"/>
                <constraint firstItem="jkD-S9-IQr" firstAttribute="top" secondItem="hwc-38-I68" secondAttribute="bottom" id="z0u-n5-6BK"/>
            </constraints>
            <viewLayoutGuide key="safeArea" id="fnl-2z-Ty3"/>
        </view>
    </objects>
</document>
