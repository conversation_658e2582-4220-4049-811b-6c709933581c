//
//  PointHistory.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 8/14/19.
//  Copyright © 2019 ddkc. All rights reserved.
//

import Foundation
import ObjectMapper

/*
StatusType => 1: <PERSON><PERSON><PERSON> điể<PERSON>, 2: <PERSON><PERSON><PERSON><PERSON> đ<PERSON>, 3: <PERSON><PERSON><PERSON>, 4: <PERSON><PERSON><PERSON> d<PERSON><PERSON> h<PERSON>, 5: Tặng điểm, 6: <PERSON><PERSON><PERSON><PERSON> điểm
 */
enum PointStatusType: Int, CustomStringConvertible {
    case save = 1
    case expense = 2
    case cancel = 3
    case cancel2 = 4
    case donate = 5
    case receive = 6

    var description: String {
        switch self {
        case .save:
            return "point_save"
        case .expense:
            return "point_expense"
        case .cancel, .cancel2:
            return "point_cancel"
        case .donate:
            return "point_donate"
        case .receive:
            return "point_receive"

        }
    }

    var color: UIColor {
        switch self {
        case .save:
            return 0x3fb7f9.toColor
        case .expense:
            return 0xfd2802.toColor
        case .donate:
            return 0xfd7c02.toColor
        case .receive:
            return 0x7ed321.toColor
        case .cancel, .cancel2:
            return 0x494c62.toColor
        }
    }

    var showName: Bool {
        switch self {
        case .receive, .donate:
            return true
        default:
            return false
        }
    }
}

class PointHistory : Mappable {
    var date : String?
    var statusType : Int?
    var statusName : String?
    var point : Int?
    var accountName : String?

    var pointStatus: PointStatusType {
        guard let _statusType = statusType, let type = PointStatusType(rawValue: _statusType) else {
            return .save
        }
        return type
    }

    var dateString: String {
        guard let _date = date else {
            return "-"
        }
        let dateBE = Date.dateFromServerSavis(_date)
        return dateBE.toString(dateFormat: "dd/MM/yyyy, HH:mm")
    }

    required init?(map: Map) {

    }

    func mapping(map: Map) {

        date <- map["Date"]
        statusType <- map["StatusType"]
        statusName <- map["StatusName"]
        point <- map["Point"]
        accountName <- map["AccountName"]
    }

}
