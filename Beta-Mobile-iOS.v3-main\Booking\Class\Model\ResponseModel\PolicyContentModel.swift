//
//  PolicyContentModel.swift
//  Booking
//
//  Created by <PERSON><PERSON>u on 4/15/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation
import ObjectMapper

class PolicyContentModel : Mappable {
    var Noi_dung_chi_tiet: [Content]?
    var PublishOnDate: String?
    var Tieu_de: String?

    var contents: String {
        return (Noi_dung_chi_tiet?.map({ (content) -> String in
            return content.ParagraphData?.ParagraphContent ?? ""
        }) ?? []).joined(separator: "\n")
    }

    required init?(map: Map) {

    }

    func mapping(map: Map) {
        Noi_dung_chi_tiet     <- map["Noi_dung_chi_tiet"]
        PublishOnDate        <- map["PublishOnDate"]
        Tieu_de <- map["Tieu_de"]
    }
    
    func getStartDateString() -> String?{
        guard let dateString = PublishOnDate, let date = Date.dateFromServer(dateString) else {
            return ""
        }
        return date.toStringStandard()
    }
    
    func getStartDate() -> Date{
        guard let dateString = PublishOnDate else {return Date()}
        return Date.dateFromServer(dateString) ?? Date()
    }
}

class Content: Mappable {
    var ParagraphData: Paragraph?
    required init?(map: Map) {
        
    }
    
    func mapping(map: Map) {
        ParagraphData     <- map["ParagraphData"]
    }
}

class Paragraph: Mappable {
    var ParagraphContent: String?
    required init?(map: Map) {
        
    }
    
    func mapping(map: Map) {
        ParagraphContent     <- map["ParagraphContent"]
    }
}
