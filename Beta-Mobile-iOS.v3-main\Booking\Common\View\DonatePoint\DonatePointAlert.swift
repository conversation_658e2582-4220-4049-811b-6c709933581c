//
//  DonatePointAlert.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 8/4/19.
//  Copyright © 2019 ddkc. All rights reserved.
//

import UIKit

protocol CustomAlertViewDelegate: class {
    func okButtonTapped(point: Int, index: Int?)
    func cancelButtonTapped()
}


class DonatePointAlert: UIViewController {

    @IBOutlet weak var titleLabel: UILabel!
    @IBOutlet weak var messageLabel: UILabel!

    @IBOutlet weak var alertView: UIView!

    @IBOutlet weak var stackView: UIStackView!
    @IBOutlet weak var otherValueTextField: UITextField!

    @IBOutlet weak var cancelButton: UIButton!
    @IBOutlet weak var okButton: UIButton!
    
    let alertViewGrayColor = UIColor(red: 224.0/255.0, green: 224.0/255.0, blue: 224.0/255.0, alpha: 1)
    var alertTitle: String?
    var alertContent: String?
    var index: Int?
    weak var delegate: CustomAlertViewDelegate?
    private var point = 30
    override func viewDidLoad() {
        super.viewDidLoad()
        otherValueTextField.keyboardType = .numberPad
        selectPoint(point)
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        setupView()
        animateView()
        cancelButton.addBorder(side: .Top, color: alertViewGrayColor, width: 1)
        cancelButton.addBorder(side: .Right, color: alertViewGrayColor, width: 1)
        okButton.addBorder(side: .Top, color: alertViewGrayColor, width: 1)
    }

    func setupView() {
        alertView.layer.cornerRadius = 15
        self.view.backgroundColor = UIColor.black.withAlphaComponent(0.4)

        messageLabel.text = alertContent
        let strings = (alertTitle ?? "").components(separatedBy: "#")
        let attributedString = NSMutableAttributedString()

        attributedString.string(strings[0], font: UIFont(fontName: .SourceSansPro, style: .Regular, size: 16))
        attributedString.string(strings[1], font: UIFont(fontName: .SourceSansPro, style: .Bold, size: 16))
        attributedString.string(strings[2], font: UIFont(fontName: .SourceSansPro, style: .Regular, size: 16))
        titleLabel.attributedText = attributedString
        otherValueTextField.placeholder = "enter_other".localized
    }

    func animateView() {
        alertView.alpha = 0;
        self.alertView.frame.origin.y = self.alertView.frame.origin.y + 50
        UIView.animate(withDuration: 0.2, animations: { () -> Void in
            self.alertView.alpha = 1.0;
            self.alertView.frame.origin.y = self.alertView.frame.origin.y - 50
        })
    }

    private func resetSelected() {
        for view in stackView.arrangedSubviews {
            guard let button = view as? UIButton else {
                continue
            }
            button.backgroundColor = .white
            button.setTitleColor(0x1e1f28.toColor, for: .normal)
        }
    }

    private func selectPoint(_ point: Int) {
        self.point = point
        for view in stackView.arrangedSubviews {
            guard let button = view as? UIButton,
                let titleString = button.title(for: .normal),
                let title = Int(titleString),
                title == point else {
                continue
            }

            button.backgroundColor = 0x03599d.toColor
            button.setTitleColor(.white, for: .normal)
        }
    }

    @IBAction func didTapCancel(_ sender: Any) {
        otherValueTextField.resignFirstResponder()
        delegate?.cancelButtonTapped()
        self.dismiss(animated: true, completion: nil)
    }

    @IBAction func okTapped(_ sender: Any) {
        otherValueTextField.resignFirstResponder()
        if let text = otherValueTextField.text, let point = Int(text), point > 0 {
            self.point = point
        }
        delegate?.okButtonTapped(point: point, index: index)
        self.dismiss(animated: true, completion: nil)
    }

    @IBAction func numberTapped(_ sender: UIButton) {
        resetSelected()
        selectPoint(Int(sender.title(for: .normal)!)!)
    }

}
