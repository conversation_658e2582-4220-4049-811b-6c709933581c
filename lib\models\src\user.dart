class MUser {
  MUser({
    String? id,
    String? userName,
    String? name,
    String? token,
    String? phoneNumber,
    String? countryCode,
    int? gender,
    String? genderString,
    String? email,
    String? avatarUrl,
    String? bankAccountNo,
    String? bankName,
    String? bankUsername,
    String? birthdate,
    String? lastActivityDate,
    bool? isLockedOut,
    bool? isActive,
    String? activeDate,
    num? level,
    String? facebookUserId,
    String? googleUserId,
    String? emailVerifyToken,
    bool? isEmailVerified,
    List<String>? roleListCode,
    List<String>? rights,
    String? profileType,
    String? createdOnDate,
    String? approvalStatus,
    String? createdByUserName,
    String? lastModifiedByUserId,
    String? lastModifiedOnDate,
    String? lastModifiedByUserName,
    String? bio,
    String? cardNumber,
    double? totalBillPayment,
    int? totalPoint,
    int? almostExpiredPoint,
    String? almostExpiredPointDate,
    String? classId,
    double? totalRemainingBillsToUpgradeClass,
    String? picture,
    String? accountId,
    bool? isUpdatedFacebookPassword,
    bool? isUpdatedApplePassword,
    String? createdDate,
    // Address fields - tương tự iOS/Android UserModel
    String? personalId,
    String? addressStreet,
    String? addressDistrict,
    String? addressCity,
    String? addressCityId,
    String? addressDistrictId,
  }) {
    _id = id;
    _userName = userName;
    _name = name;
    _phoneNumber = phoneNumber;
    _countryCode = countryCode;
    _token = token;
    _gender = gender;
    _genderString = genderString;
    _email = email;
    _avatarUrl = avatarUrl;
    _bankAccountNo = bankAccountNo;
    _bankName = bankName;
    _bankUsername = bankUsername;
    _birthdate = birthdate;
    _lastActivityDate = lastActivityDate;
    _isLockedOut = isLockedOut;
    _isActive = isActive;
    _activeDate = activeDate;
    _level = level;
    _facebookUserId = facebookUserId;
    _googleUserId = googleUserId;
    _emailVerifyToken = emailVerifyToken;
    _isEmailVerified = isEmailVerified;
    _roleListCode = roleListCode;
    _rights = rights;
    _profileType = profileType;
    _createdOnDate = createdOnDate;
    _approvalStatus = approvalStatus;
    _createdByUserName = createdByUserName;
    _lastModifiedByUserId = lastModifiedByUserId;
    _lastModifiedOnDate = lastModifiedOnDate;
    _lastModifiedByUserName = lastModifiedByUserName;
    _bio = bio;
    _cardNumber = cardNumber;
    _totalBillPayment = totalBillPayment;
    _totalPoint = totalPoint;
    _almostExpiredPoint = almostExpiredPoint;
    _almostExpiredPointDate = almostExpiredPointDate;
    _classId = classId;
    _totalRemainingBillsToUpgradeClass = totalRemainingBillsToUpgradeClass;
    _picture = picture;
    _accountId = accountId;
    _isUpdatedFacebookPassword = isUpdatedFacebookPassword;
    _isUpdatedApplePassword = isUpdatedApplePassword;
    _createdDate = createdDate;
    _personalId = personalId;
    _addressStreet = addressStreet;
    _addressDistrict = addressDistrict;
    _addressCity = addressCity;
    _addressCityId = addressCityId;
    _addressDistrictId = addressDistrictId;
  }

  MUser.fromJson(dynamic json) {
    _id = json['UserId'] as String?;
    _userName = json['userName'];
    _name = json['FullName'];
    _phoneNumber = json['PhoneNumber'] ?? json['PhoneOffice'];
    _token = json['token'];
    _countryCode = json['countryCode'];
    _gender = json['Gender'];
    _genderString = json['GenderString'];
    _email = json['Email'];
    _avatarUrl = json['avatarUrl'] ?? json['Picture'];
    _bankAccountNo = json['bankAccountNo'];
    _bankName = json['bankName'];
    _bankUsername = json['bankUsername'];
    _birthdate = json['BirthDate'];
    _lastActivityDate = json['lastActivityDate'];
    _isLockedOut = json['isLockedOut'];
    _isActive = json['isActive'];
    _activeDate = json['activeDate'];
    _level = json['level'];
    _facebookUserId = json['facebookUserId'];
    _googleUserId = json['googleUserId'];
    _emailVerifyToken = json['emailVerifyToken'];
    _isEmailVerified = json['isEmailVerified'];
    _roleListCode = json['roleListCode'] != null ? json['roleListCode'].cast<String>() : [];
    _rights = json['rights'] != null ? json['rights'].cast<String>() : [];
    _profileType = json['profileType'];
    _createdOnDate = json['createdOnDate'];
    _approvalStatus = json['approvalStatus'];
    _createdByUserName = json['createdByUserName'];
    _lastModifiedByUserId = json['lastModifiedByUserId'];
    _lastModifiedOnDate = json['lastModifiedOnDate'];
    _lastModifiedByUserName = json['lastModifiedByUserName'];
    _bio = json['bio'];
    _cardNumber = json['CardNumber'] as String?;
    _totalBillPayment = json['TotalBillPayment'] as double?;
    _totalPoint = json['TotalPoint'] as int?;
    _almostExpiredPoint = json['AlmostExpiredPoint'] as int?;
    _almostExpiredPointDate = json['AlmostExpiredPointDate'] as String?;
    _classId = json['ClassId'] as String?;
    _totalRemainingBillsToUpgradeClass = json['TotalRemainingBillsToUpgradeClass'] as double?;
    _picture = json['Picture'];
    _accountId = json['AccountId'];
    _isUpdatedFacebookPassword = json['IsUpdatedFacebookPassword'];
    _isUpdatedApplePassword = json['IsUpdatedApplePassword'];
    _createdDate = json['CreatedDate'];
    _personalId = json['PersonalId'];
    _addressStreet = json['AddressStreet'];
    _addressDistrict = json['AddressDistrict'];
    _addressCity = json['AddressCity'];
    _addressCityId = json['AddressCityId'];
    _addressDistrictId = json['AddressDistrictId'];
  }

  String? _id;
  String? _userName;
  String? _name;
  String? _phoneNumber;
  String? _countryCode;
  int? _gender;
  String? _genderString;
  String? _email;
  String? _token;
  String? _avatarUrl;
  String? _bankAccountNo;
  String? _bankName;
  String? _bankUsername;
  String? _birthdate;
  String? _lastActivityDate;
  bool? _isLockedOut;
  bool? _isActive;
  String? _activeDate;
  num? _level;
  String? _facebookUserId;
  String? _googleUserId;
  String? _emailVerifyToken;
  bool? _isEmailVerified;
  List<String>? _roleListCode;
  List<String>? _rights;
  String? _profileType;
  String? _createdOnDate;
  String? _approvalStatus;
  String? _createdByUserName;
  String? _lastModifiedByUserId;
  String? _lastModifiedOnDate;
  String? _lastModifiedByUserName;
  String? _bio;
  String? _cardNumber;
  double? _totalBillPayment;
  int? _totalPoint;
  int? _almostExpiredPoint;
  String? _almostExpiredPointDate;
  String? _classId;
  double? _totalRemainingBillsToUpgradeClass;
  String? _picture;
  String? _accountId;
  bool? _isUpdatedFacebookPassword;
  bool? _isUpdatedApplePassword;
  String? _createdDate;
  // Address fields - tương tự iOS/Android UserModel
  String? _personalId;
  String? _addressStreet;
  String? _addressDistrict;
  String? _addressCity;
  String? _addressCityId;
  String? _addressDistrictId;

  MUser copyWith({
    String? id,
    String? userName,
    String? name,
    String? token,
    String? phoneNumber,
    String? countryCode,
    int? gender,
    String? genderString,
    String? email,
    String? avatarUrl,
    String? bankAccountNo,
    String? bankName,
    String? bankUsername,
    String? birthdate,
    String? lastActivityDate,
    bool? isLockedOut,
    bool? isActive,
    String? activeDate,
    num? level,
    String? facebookUserId,
    String? googleUserId,
    String? emailVerifyToken,
    bool? isEmailVerified,
    List<String>? roleListCode,
    List<String>? rights,
    String? profileType,
    String? createdOnDate,
    String? approvalStatus,
    String? createdByUserName,
    String? lastModifiedByUserId,
    String? lastModifiedOnDate,
    String? lastModifiedByUserName,
    String? bio,
    String? cardNumber,
    double? totalBillPayment,
    int? totalPoint,
    int? almostExpiredPoint,
    String? almostExpiredPointDate,
    String? classId,
    double? totalRemainingBillsToUpgradeClass,
    String? picture,
    String? accountId,
    bool? isUpdatedFacebookPassword,
    bool? isUpdatedApplePassword,
    String? createdDate,
    String? personalId,
    String? addressStreet,
    String? addressDistrict,
    String? addressCity,
    String? addressCityId,
    String? addressDistrictId,
  }) =>
      MUser(
        id: id ?? _id,
        userName: userName ?? _userName,
        name: name ?? _name,
        token: token ?? _token,
        phoneNumber: phoneNumber ?? _phoneNumber,
        countryCode: countryCode ?? _countryCode,
        gender: gender ?? _gender,
        genderString: genderString ?? _genderString,
        email: email ?? _email,
        avatarUrl: avatarUrl ?? _avatarUrl,
        bankAccountNo: bankAccountNo ?? _bankAccountNo,
        bankName: bankName ?? _bankName,
        bankUsername: bankUsername ?? _bankUsername,
        birthdate: birthdate ?? _birthdate,
        lastActivityDate: lastActivityDate ?? _lastActivityDate,
        isLockedOut: isLockedOut ?? _isLockedOut,
        isActive: isActive ?? _isActive,
        activeDate: activeDate ?? _activeDate,
        level: level ?? _level,
        facebookUserId: facebookUserId ?? _facebookUserId,
        googleUserId: googleUserId ?? _googleUserId,
        emailVerifyToken: emailVerifyToken ?? _emailVerifyToken,
        isEmailVerified: isEmailVerified ?? _isEmailVerified,
        roleListCode: roleListCode ?? _roleListCode,
        rights: rights ?? _rights,
        profileType: profileType ?? _profileType,
        createdOnDate: createdOnDate ?? _createdOnDate,
        approvalStatus: approvalStatus ?? _approvalStatus,
        createdByUserName: createdByUserName ?? _createdByUserName,
        lastModifiedByUserId: lastModifiedByUserId ?? _lastModifiedByUserId,
        lastModifiedOnDate: lastModifiedOnDate ?? _lastModifiedOnDate,
        lastModifiedByUserName: lastModifiedByUserName ?? _lastModifiedByUserName,
        bio: bio ?? _bio,
        cardNumber: cardNumber ?? _cardNumber,
        totalBillPayment: totalBillPayment ?? _totalBillPayment,
        totalPoint: totalPoint ?? _totalPoint,
        almostExpiredPoint: almostExpiredPoint ?? _almostExpiredPoint,
        almostExpiredPointDate: almostExpiredPointDate ?? _almostExpiredPointDate,
        classId: classId ?? _classId,
        totalRemainingBillsToUpgradeClass: totalRemainingBillsToUpgradeClass ?? _totalRemainingBillsToUpgradeClass,
        picture: picture ?? _picture,
        accountId: accountId ?? _accountId,
        isUpdatedFacebookPassword: isUpdatedFacebookPassword ?? _isUpdatedFacebookPassword,
        isUpdatedApplePassword: isUpdatedApplePassword ?? _isUpdatedApplePassword,
        createdDate: createdDate ?? _createdDate,
        personalId: personalId ?? _personalId,
        addressStreet: addressStreet ?? _addressStreet,
        addressDistrict: addressDistrict ?? _addressDistrict,
        addressCity: addressCity ?? _addressCity,
        addressCityId: addressCityId ?? _addressCityId,
        addressDistrictId: addressDistrictId ?? _addressDistrictId,
      );

  String? get id => _id;

  String get userName => _userName ?? '';

  String get token => _token ?? '';

  String get name => _name ?? '';

  set name(value)  {
    _name = value;
  }

  String? get phoneNumber => _phoneNumber;
  set phoneNumber(value) => _phoneNumber = value;

  String? get countryCode => _countryCode;

  int? get gender => _gender;
  set gender(value) => _gender = value;

  String? get genderString => _genderString;

  String? get email => _email;
  set email(value) => _email = value;

  String get avatarUrl => _avatarUrl ?? '';
  set avatarUrl(value) => _avatarUrl = value;


  String? get bankAccountNo => _bankAccountNo;

  String? get bankName => _bankName;

  String? get bankUsername => _bankUsername;

  String? get birthdate => _birthdate;
  set birthdate(value) => _birthdate = value;

  String? get lastActivityDate => _lastActivityDate;

  bool? get isLockedOut => _isLockedOut;

  bool? get isActive => _isActive;

  String? get activeDate => _activeDate;

  num? get level => _level;

  String? get facebookUserId => _facebookUserId;

  String? get googleUserId => _googleUserId;

  String? get emailVerifyToken => _emailVerifyToken;

  bool? get isEmailVerified => _isEmailVerified;

  List<String>? get roleListCode => _roleListCode;
  List<String>? get right => _rights;

  String get profileType => _profileType ?? '';

  String get createdOnDate => _createdOnDate ?? '';

  String? get approvalStatus => _approvalStatus;

  String? get createdByUserName => _createdByUserName;

  String? get lastModifiedByUserId => _lastModifiedByUserId;

  String? get lastModifiedOnDate => _lastModifiedOnDate;

  String? get lastModifiedByUserName => _lastModifiedByUserName;

  String? get bio => _bio;

  String? get cardNumber => _cardNumber;

  double? get totalBillPayment => _totalBillPayment;

  int? get totalPoint => _totalPoint;

  int? get almostExpiredPoint => _almostExpiredPoint;

  String? get almostExpiredPointDate => _almostExpiredPointDate;

  String? get classId => _classId;

  double? get totalRemainingBillsToUpgradeClass => _totalRemainingBillsToUpgradeClass;

  String? get picture => _picture;

  String? get accountId => _accountId;

  bool? get isUpdatedFacebookPassword => _isUpdatedFacebookPassword;

  bool? get isUpdatedApplePassword => _isUpdatedApplePassword;

  String? get createdDate => _createdDate;

  // Address getters and setters - tương tự iOS/Android UserModel
  String? get personalId => _personalId;
  set personalId(String? value) => _personalId = value;

  String? get addressStreet => _addressStreet;
  set addressStreet(String? value) => _addressStreet = value;

  String? get addressDistrict => _addressDistrict;
  set addressDistrict(String? value) => _addressDistrict = value;

  String? get addressCity => _addressCity;
  set addressCity(String? value) => _addressCity = value;

  String? get addressCityId => _addressCityId;
  set addressCityId(String? value) => _addressCityId = value;

  String? get addressDistrictId => _addressDistrictId;
  set addressDistrictId(String? value) => _addressDistrictId = value;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = _id;
    map['userName'] = _userName;
    map['name'] = _name;
    map['phoneNumber'] = _phoneNumber;
    map['countryCode'] = _countryCode;
    map['gender'] = _gender;
    map['genderString'] = _genderString;
    map['email'] = _email;
    map['avatarUrl'] = _avatarUrl;
    map['bankAccountNo'] = _bankAccountNo;
    map['bankName'] = _bankName;
    map['bankUsername'] = _bankUsername;
    map['birthdate'] = _birthdate;
    map['lastActivityDate'] = _lastActivityDate;
    map['isLockedOut'] = _isLockedOut;
    map['isActive'] = _isActive;
    map['activeDate'] = _activeDate;
    map['level'] = _level;
    map['facebookUserId'] = _facebookUserId;
    map['googleUserId'] = _googleUserId;
    map['emailVerifyToken'] = _emailVerifyToken;
    map['isEmailVerified'] = _isEmailVerified;
    map['roleListCode'] = _roleListCode;
    map['rights'] = _rights;
    map['profileType'] = _profileType;
    map['createdOnDate'] = _createdOnDate;
    map['approvalStatus'] = _approvalStatus;
    map['createdByUserName'] = _createdByUserName;
    map['lastModifiedByUserId'] = _lastModifiedByUserId;
    map['lastModifiedOnDate'] = _lastModifiedOnDate;
    map['lastModifiedByUserName'] = _lastModifiedByUserName;
    map['bio'] = _bio;
    map['CardNumber'] = _cardNumber;
    map['TotalBillPayment'] = _totalBillPayment;
    map['TotalPoint'] = _totalPoint;
    map['AlmostExpiredPoint'] = _almostExpiredPoint;
    map['AlmostExpiredPointDate'] = _almostExpiredPointDate;
    map['ClassId'] = _classId;
    map['TotalRemainingBillsToUpgradeClass'] = _totalRemainingBillsToUpgradeClass;
    map['Picture'] = _picture;
    map['AccountId'] = _accountId;
    map['IsUpdatedFacebookPassword'] = _isUpdatedFacebookPassword;
    map['IsUpdatedApplePassword'] = _isUpdatedApplePassword;
    map['CreatedDate'] = _createdDate;
    map['PersonalId'] = _personalId;
    map['AddressStreet'] = _addressStreet;
    map['AddressDistrict'] = _addressDistrict;
    map['AddressCity'] = _addressCity;
    map['AddressCityId'] = _addressCityId;
    map['AddressDistrictId'] = _addressDistrictId;
    return map;
  }
}
