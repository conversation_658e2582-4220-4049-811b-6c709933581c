//
//  VoucherGot.swift
//  Booking
//
//  Created by <PERSON><PERSON>u on 8/13/19.
//  Copyright © 2019 ddkc. All rights reserved.
//

import Foundation
import ObjectMapper

class VoucherGot: Mappable {
    var status : Int?
    var voucherCode : String?
    var storylineTitle : String?
    var firstMessage : String?
    var firstMessageHighLight : String?
    var secondMessage : String?
    var secondMessageHighLight : String?

    required init?(map: Map) {

    }

    func mapping(map: Map) {

        status <- map["Status"]
        voucherCode <- map["VoucherCode"]
        storylineTitle <- map["StorylineTitle"]
        firstMessage <- map["FirstMessage"]
        firstMessageHighLight <- map["FirstMessageHighLight"]
        secondMessage <- map["SecondMessage"]
        secondMessageHighLight <- map["SecondMessageHighLight"]
    }
}
