package vn.zenity.betacineplex.view.auth

import android.os.Bundle
import android.view.View
import kotlinx.android.synthetic.main.fragment_changepassword.*
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.base.BaseFragment
import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.helper.extension.gone

/**
 * Created by Zenity.
 */

class ChangePasswordFragment : BaseFragment(), ChangePasswordContractor.View {

    companion object {
        fun getInstance(updateListener: (() -> Unit)? = null): ChangePasswordFragment {
            val frag = ChangePasswordFragment()
            frag.isUpdateFbPassword = true
            frag.updateListener = updateListener
            return frag
        }
    }

    private var isUpdateFbPassword = false
    private var updateListener: (() -> Unit)? = null

    override fun changePasswordSuccess(message: String) {
        showNotice(message) {
            back()
        }
    }

    override fun changePasswordError(message: String) {
        showNotice(message)
    }

    private val presenter = ChangePasswordPresenter()

    override fun getPresenter(): IBasePresenter<IBaseView>? {
        return presenter as? IBasePresenter<IBaseView>
    }

    override fun isShowToolbar(): Boolean {
        return false
    }

    override fun getLayoutRes(): Int {
        return R.layout.fragment_changepassword
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        btnConfirm.setOnClickListener {
            validate()
        }

        if (isUpdateFbPassword) {
            tvCurrentPassword.gone()
            edtOldPassword.gone()
            btnMenu.gone()
            tvTitle.text = getString(R.string.update_password)
        }
    }

    private fun validate() {
        val oldPass = edtOldPassword.text.trim()
        val newPass = edtNewPassword.text.trim()
        val confirmPass = edtConfirmPassword.text.trim()
        if (newPass.length < 6) {
            showNotice(getString(R.string.password_not_valid))
            edtNewPassword.focus()
            return
        }
        if (newPass != confirmPass) {
            showNotice(getString(R.string.password_confirm_not_equal))
            edtConfirmPassword.focus()
            return
        }
        if (isUpdateFbPassword) {
            presenter.updateFacebookPassword(newPass, confirmPass)
        } else {
            presenter.changePassword(oldPass, newPass, confirmPass)
        }
    }

    override fun onSaveInstanceState(outState: Bundle) {
//        outState.putBoolean("isUpdateFbPassword", isUpdateFbPassword)
        super.onSaveInstanceState(outState)
    }

    override fun onViewStateRestored(savedInstanceState: Bundle?) {
//        isUpdateFbPassword = savedInstanceState?.getBoolean("isUpdateFbPassword", isUpdateFbPassword) ?: false
        super.onViewStateRestored(savedInstanceState)

    }

    override fun onDestroyView() {
        super.onDestroyView()
        updateListener?.invoke()
        updateListener = null
    }
}
