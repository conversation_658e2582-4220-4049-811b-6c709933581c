import 'package:flutter/material.dart';
import 'package:flutter_app/models/project/notification.dart';
import 'package:flutter_app/pages/notification/index.dart';
import 'package:flutter_app/pages/other_tab/setting/index.dart';
import 'package:flutter_app/pages/promotion/news_detail_screen.dart';
import 'package:flutter_app/pages/voucher/free_voucher_screen.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_contacts/contact.dart';
import 'package:go_router/go_router.dart';

import '../../pages/cinema/index.dart';
import '../../pages/cinema/model/cinema_model.dart';
import '../../pages/other_tab/beta_cinema/_detail.dart';
import '../../pages/other_tab/recruitment/recruitment_screen.dart';
import '/core/index.dart';
import '/cubit/index.dart';
import '/models/index.dart';
import '/pages/index.dart';

class CRoute {
  static const String home = '/home';
  static const String splash = '/splash';
  static const String login = '/login';
  static const String register = 'register';
  static const String forgotPassword = 'forgot-password';
  static const String otpVerification = 'otp-verification';
  static const String resetPassword = 'reset-password';
  static const String myAccountInfo = 'my-account-info';
  static const String myAccountPass = 'my-account-password';
  static const String member = 'member';
  static const String map = 'map';
  static const String contacts = 'contacts';

  static const String order = 'order';
  static const String orderItem = 'orderItem';
  static const String orderDetail = 'orderDetail';
  static const String editOrder = 'editOrder';
  static const String product = 'product';
  static const String preview = 'preview';
  static const String history = 'history';
  static const String deliveryNote = 'deliveryNote';

  static const String notification = 'notification';

  ///beta
  static const String listFilmScreen = 'list-film-screen';
  static const String cinemaDetailScreen = 'cinema-detail-screen';
  static const String newsDetail = 'news-detail';
  static const String freeVoucher = 'free-voucher';
  static const String recruitment = 'recruitment';
  static const String settings = 'settings';
}

final rootNavigatorKey = GlobalKey<NavigatorState>();
final GoRouter routes = GoRouter(
  initialLocation: CRoute.splash,
  navigatorKey: rootNavigatorKey,
  routes: <RouteBase>[
    splashRoute(),
    introductionRoute(),
    homeRoute(),
  ],
  // Handle deep link URLs that should not be routed by GoRouter
  errorBuilder: (context, state) {
    final location = state.uri.toString();

    // Check if this is a deep link URL that should be handled by native code
    if (location.startsWith('betacineplexx://') ||
        location.startsWith('betacineplex://') ||
        location.contains('momo') ||
        location.contains('zalopay') ||
        location.contains('airpay')) {
      print('🔗 GoRouter: Ignoring deep link URL: $location');
      // Return to home instead of showing error
      WidgetsBinding.instance.addPostFrameCallback((_) {
        context.go(CRoute.home);
      });
      return const SizedBox.shrink(); // Return empty widget temporarily
    }

    // For other errors, show a proper error page
    return Scaffold(
      appBar: AppBar(title: const Text('Page Not Found')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            const Text('Page Not Found', style: TextStyle(fontSize: 24)),
            const SizedBox(height: 8),
            Text('Location: ${state.uri}', style: const TextStyle(fontSize: 14, color: Colors.grey)),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => context.go(CRoute.home),
              child: const Text('Go Home'),
            ),
          ],
        ),
      ),
    );
  },
);

GoRoute splashRoute() => GoRoute(
      name: CRoute.splash,
      path: CRoute.splash,
      builder: (BuildContext context, GoRouterState state) => const SplashPage(),
    );

GoRoute introductionRoute() => GoRoute(
        name: CRoute.login,
        path: CRoute.login,
        builder: (BuildContext context, GoRouterState state) => blocForm<MUser>(child: const LoginPage()),
        routes: <RouteBase>[
          GoRoute(
              name: CRoute.forgotPassword,
              path: CRoute.forgotPassword,
              builder: (BuildContext context, GoRouterState state) => blocForm<MUser>(child: const ForgotPassword()),
              routes: <RouteBase>[
                GoRoute(
                    name: CRoute.otpVerification,
                    path: CRoute.otpVerification,
                    builder: (BuildContext context, GoRouterState state) =>
                        blocForm<MUser>(child: OTPVerificationPage(email: state.uri.queryParameters['email']!)),
                    routes: <RouteBase>[
                      GoRoute(
                        name: CRoute.resetPassword,
                        path: CRoute.resetPassword,
                        builder: (BuildContext context, GoRouterState state) => blocForm<MUser>(
                            child: ResetPassword(resetPasswordToken: state.uri.queryParameters['resetPasswordToken']!)),
                      )
                    ])
              ]),
          GoRoute(
              name: CRoute.register,
              path: CRoute.register,
              builder: (BuildContext context, GoRouterState state) => blocForm<MUser>(
                    child: const RegisterPage(),
                  ))
        ]);

blocForm<T>({required Widget child}) => BlocListener<AuthC, AuthS>(
    listenWhen: (oldState, newState) => newState.status == AppStatus.fails,
    listener: (context, state) => GoRouter.of(context).pushNamed(CRoute.login),
    child: BlocProvider(
      create: (context) => BlocC<T>(),
      child: child,
    ));

GoRoute homeRoute() => GoRoute(
        name: CRoute.home,
        path: CRoute.home,
        builder: (BuildContext context, GoRouterState state) => const HomePage(),
        routes: <RouteBase>[
          GoRoute(
              path: CRoute.listFilmScreen,
              name: CRoute.listFilmScreen,
              builder: (BuildContext context, GoRouterState state) => ListFilmScreen(
                    isBooking: bool.parse(state.extra.toString()),
                  )),
          GoRoute(
              path: CRoute.cinemaDetailScreen,
              name: CRoute.cinemaDetailScreen,
              builder: (BuildContext context, GoRouterState state) => CinemaDetailScreen(
                    initialCinema: state.extra as Cinema,
                  )),
          GoRoute(
              path: CRoute.newsDetail,
              name: CRoute.newsDetail,
              builder: (BuildContext context, GoRouterState state) => NewsDetailScreen(
                    item: state.extra as PromotionItem,
                  )),
          GoRoute(
              path: CRoute.freeVoucher,
              name: CRoute.freeVoucher,
              builder: (BuildContext context, GoRouterState state) => const FreeVoucherScreen()),
          GoRoute(
              path: CRoute.recruitment,
              name: CRoute.recruitment,
              builder: (BuildContext context, GoRouterState state) => const RecruitmentScreen()),
          GoRoute(
              path: CRoute.settings,
              name: CRoute.settings,
              builder: (BuildContext context, GoRouterState state) => const SettingScreen()),
          GoRoute(
              name: CRoute.map,
              path: CRoute.map,
              builder: (BuildContext context, GoRouterState state) => blocForm(
                      child: WMap(
                    fullScreen: true,
                    onlyPoint: state.uri.queryParameters['onlyPoint'] == 'true',
                    address: state.extra as List<String>,
                    name: state.uri.queryParameters['name']!,
                    latLn: state.uri.queryParameters['latLn']!.split(',').map((e) => double.parse(e)).toList(),
                  ))),
          GoRoute(
              name: CRoute.contacts,
              path: CRoute.contacts,
              builder: (BuildContext context, GoRouterState state) => blocForm<Contact>(child: const Contracts())),
          GoRoute(
            name: CRoute.myAccountInfo,
            path: CRoute.myAccountInfo,
            builder: (BuildContext context, GoRouterState state) =>  const MyAccountInfo(),
          ),
          GoRoute(
              name: CRoute.myAccountPass,
              path: CRoute.myAccountPass,
              builder: (BuildContext context, GoRouterState state) => blocForm<MUser>(child: const MyAccountPass())),
          GoRoute(
              name: CRoute.member,
              path: CRoute.member,
              builder: (BuildContext context, GoRouterState state) =>  blocForm<MUser>(child:const ResponsiveMemberScreen())),
          GoRoute(
            name: CRoute.notification,
            path: CRoute.notification,
            builder: (BuildContext context, GoRouterState state) => blocForm<MNotification>(child: const NotificationPage()),
          ),

        ]);
