<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14490.70" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14490.49"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="SourceSansPro-Regular.ttf">
            <string>SourceSansPro-Regular</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="DonateVoucherViewController" customModule="Booking_dev" customModuleProvider="target">
            <connections>
                <outlet property="searchTextField" destination="qm0-md-FCb" id="Vld-ng-S6e"/>
                <outlet property="tableView" destination="dq0-2m-Q7P" id="Oce-qb-Bop"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="MYc-oF-QwX">
                    <rect key="frame" x="0.0" y="44" width="414" height="80"/>
                    <subviews>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="8ES-k6-zKh" customClass="RoundView" customModule="Booking_dev" customModuleProvider="target">
                            <rect key="frame" x="8" y="15" width="398" height="50"/>
                            <subviews>
                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_search" translatesAutoresizingMaskIntoConstraints="NO" id="hUm-LT-kXX">
                                    <rect key="frame" x="358" y="13" width="24" height="24"/>
                                    <constraints>
                                        <constraint firstAttribute="width" secondItem="hUm-LT-kXX" secondAttribute="height" id="jt5-P4-YXR"/>
                                        <constraint firstAttribute="width" constant="24" id="u9a-59-WFj"/>
                                    </constraints>
                                </imageView>
                                <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Tìm kiếm bạn bè…" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="qm0-md-FCb">
                                    <rect key="frame" x="12" y="0.0" width="334" height="50"/>
                                    <color key="textColor" red="0.070588235289999995" green="0.1215686275" blue="0.15686274510000001" alpha="1" colorSpace="calibratedRGB"/>
                                    <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                    <textInputTraits key="textInputTraits"/>
                                    <connections>
                                        <action selector="searchChanging:" destination="-1" eventType="editingChanged" id="Ucs-eo-U5s"/>
                                    </connections>
                                </textField>
                            </subviews>
                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <constraints>
                                <constraint firstItem="hUm-LT-kXX" firstAttribute="centerY" secondItem="8ES-k6-zKh" secondAttribute="centerY" id="DTJ-GA-JEe"/>
                                <constraint firstItem="qm0-md-FCb" firstAttribute="top" secondItem="8ES-k6-zKh" secondAttribute="top" id="PdP-am-be6"/>
                                <constraint firstItem="hUm-LT-kXX" firstAttribute="leading" secondItem="qm0-md-FCb" secondAttribute="trailing" constant="12" id="TI3-fr-icV"/>
                                <constraint firstAttribute="bottom" secondItem="qm0-md-FCb" secondAttribute="bottom" id="gUI-c9-aoL"/>
                                <constraint firstItem="qm0-md-FCb" firstAttribute="leading" secondItem="8ES-k6-zKh" secondAttribute="leading" constant="12" id="u2h-4s-swO"/>
                                <constraint firstAttribute="trailing" secondItem="hUm-LT-kXX" secondAttribute="trailing" constant="16" id="veA-li-JO2"/>
                                <constraint firstAttribute="height" constant="50" id="yw3-Bt-Qg3"/>
                            </constraints>
                            <userDefinedRuntimeAttributes>
                                <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                    <real key="value" value="5"/>
                                </userDefinedRuntimeAttribute>
                                <userDefinedRuntimeAttribute type="color" keyPath="shadowColor">
                                    <color key="value" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                </userDefinedRuntimeAttribute>
                                <userDefinedRuntimeAttribute type="number" keyPath="shadowOpacity">
                                    <real key="value" value="0.10000000000000001"/>
                                </userDefinedRuntimeAttribute>
                                <userDefinedRuntimeAttribute type="point" keyPath="shadowOffset">
                                    <point key="value" x="0.0" y="4"/>
                                </userDefinedRuntimeAttribute>
                                <userDefinedRuntimeAttribute type="number" keyPath="shadowRadius">
                                    <real key="value" value="2"/>
                                </userDefinedRuntimeAttribute>
                            </userDefinedRuntimeAttributes>
                        </view>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstItem="8ES-k6-zKh" firstAttribute="leading" secondItem="MYc-oF-QwX" secondAttribute="leading" constant="8" id="LdO-IT-fE6"/>
                        <constraint firstAttribute="trailing" secondItem="8ES-k6-zKh" secondAttribute="trailing" constant="8" id="VUF-Rq-DX7"/>
                        <constraint firstAttribute="height" constant="80" id="ho2-xY-zTC"/>
                        <constraint firstItem="8ES-k6-zKh" firstAttribute="centerY" secondItem="MYc-oF-QwX" secondAttribute="centerY" id="wBK-XP-98k"/>
                    </constraints>
                </view>
                <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" style="plain" separatorStyle="default" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="dq0-2m-Q7P">
                    <rect key="frame" x="0.0" y="124" width="414" height="738"/>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <connections>
                        <outlet property="dataSource" destination="-1" id="3Tt-Yb-CF0"/>
                        <outlet property="delegate" destination="-1" id="cqL-Xs-yri"/>
                    </connections>
                </tableView>
            </subviews>
            <color key="backgroundColor" red="0.95294117649999999" green="0.95294117649999999" blue="0.95294117649999999" alpha="1" colorSpace="calibratedRGB"/>
            <constraints>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="bottom" secondItem="dq0-2m-Q7P" secondAttribute="bottom" id="5wn-qz-pyF"/>
                <constraint firstItem="MYc-oF-QwX" firstAttribute="top" secondItem="fnl-2z-Ty3" secondAttribute="top" id="Fhp-5s-Rus"/>
                <constraint firstItem="dq0-2m-Q7P" firstAttribute="top" secondItem="MYc-oF-QwX" secondAttribute="bottom" id="I7b-6M-BFI"/>
                <constraint firstItem="MYc-oF-QwX" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" id="Uy7-0t-rlt"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="MYc-oF-QwX" secondAttribute="trailing" id="XFa-eO-cTm"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="dq0-2m-Q7P" secondAttribute="trailing" id="ZE2-CP-17X"/>
                <constraint firstItem="dq0-2m-Q7P" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" id="kvk-jd-YIS"/>
            </constraints>
            <viewLayoutGuide key="safeArea" id="fnl-2z-Ty3"/>
        </view>
    </objects>
    <resources>
        <image name="ic_search" width="24" height="24"/>
    </resources>
</document>
