import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_app/models/index.dart';

// NewsModel exactly like iOS/Android - matching all properties
class NewsModel {
  final String? storylineID;
  final int? storylineType;
  final String? tieu_de;
  final String? tieu_de_ko_dau;
  final String? tieu_de_phu;
  final String? tieu_de_limit;
  final String? tieu_de_phu_copy;
  final int? truc_tiep;
  final String? duong_dan_anh_dai_dien;
  final String? tieu_de_anh;
  final String? tieu_de_anh_ko_dau;
  final String? tom_tat_noi_dung;
  final String? tom_tat_noi_dung_phu;
  final String? tom_tat_noi_dung_limit;
  final String? tom_tat_anh_dai_dien;
  final String? publishOnDate;
  final String? breadcrumbs;
  final String? newsURI;
  final String? duong_dan_video_dai_dien;
  final String? keyWords;
  final List<NewsContentDetail>? noi_dung_chi_tiet;

  NewsModel({
    this.storylineID,
    this.storylineType,
    this.tieu_de,
    this.tieu_de_ko_dau,
    this.tieu_de_phu,
    this.tieu_de_limit,
    this.tieu_de_phu_copy,
    this.truc_tiep,
    this.duong_dan_anh_dai_dien,
    this.tieu_de_anh,
    this.tieu_de_anh_ko_dau,
    this.tom_tat_noi_dung,
    this.tom_tat_noi_dung_phu,
    this.tom_tat_noi_dung_limit,
    this.tom_tat_anh_dai_dien,
    this.publishOnDate,
    this.breadcrumbs,
    this.newsURI,
    this.duong_dan_video_dai_dien,
    this.keyWords,
    this.noi_dung_chi_tiet,
  });

  factory NewsModel.fromJson(Map<String, dynamic> json) {
    return NewsModel(
      storylineID: json['StorylineID'],
      storylineType: json['StorylineType'],
      tieu_de: json['Tieu_de'],
      tieu_de_ko_dau: json['Tieu_de_ko_dau'],
      tieu_de_phu: json['Tieu_de_phu'],
      tieu_de_limit: json['Tieu_de_limit'],
      tieu_de_phu_copy: json['Tieu_de_phu_copy'],
      truc_tiep: json['Truc_tiep'],
      duong_dan_anh_dai_dien: json['Duong_dan_anh_dai_dien'],
      tieu_de_anh: json['Tieu_de_anh'],
      tieu_de_anh_ko_dau: json['Tieu_de_anh_ko_dau'],
      tom_tat_noi_dung: json['Tom_tat_noi_dung'],
      tom_tat_noi_dung_phu: json['Tom_tat_noi_dung_phu'],
      tom_tat_noi_dung_limit: json['Tom_tat_noi_dung_limit'],
      tom_tat_anh_dai_dien: json['Tom_tat_anh_dai_dien'],
      publishOnDate: json['PublishOnDate'],
      breadcrumbs: json['Breadcrumbs'],
      newsURI: json['NewsURI'],
      duong_dan_video_dai_dien: json['Duong_dan_video_dai_dien'],
      keyWords: json['KeyWords'],
      noi_dung_chi_tiet: (json['Noi_dung_chi_tiet'] as List?)
          ?.map((e) => NewsContentDetail.fromJson(e))
          .toList(),
    );
  }

  // Get image URL with base URL like iOS
  String? get imageUrl {
    if (duong_dan_anh_dai_dien == null || duong_dan_anh_dai_dien!.isEmpty) {
      return null;
    }
    if (duong_dan_anh_dai_dien!.startsWith('http')) {
      return duong_dan_anh_dai_dien;
    }
    return 'https://api.betacineplex.vn${duong_dan_anh_dai_dien}';
  }

  // Get title like iOS
  String get title => tieu_de ?? '';
}

class NewsContentDetail {
  final String? id;
  final String? tieu_de;
  final String? storylineID;
  final String? objectID;
  final String? tableName;
  final String? createdByUserID;
  final String? thu_tu_hien_thi;
  final String? createdOnDate;
  final String? lastModifiedByUserID;
  final String? lastModifiedOnDate;
  final String? startDate;
  final String? endDate;
  final NewsParagraphData? paragraphData;

  NewsContentDetail({
    this.id,
    this.tieu_de,
    this.storylineID,
    this.objectID,
    this.tableName,
    this.createdByUserID,
    this.thu_tu_hien_thi,
    this.createdOnDate,
    this.lastModifiedByUserID,
    this.lastModifiedOnDate,
    this.startDate,
    this.endDate,
    this.paragraphData,
  });

  factory NewsContentDetail.fromJson(Map<String, dynamic> json) {
    return NewsContentDetail(
      id: json['ID'],
      tieu_de: json['Tieu_de'],
      storylineID: json['StorylineID'],
      objectID: json['ObjectID'],
      tableName: json['TableName'],
      createdByUserID: json['CreatedByUserID'],
      thu_tu_hien_thi: json['Thu_tu_hien_thi'],
      createdOnDate: json['CreatedOnDate'],
      lastModifiedByUserID: json['LastModifiedByUserID'],
      lastModifiedOnDate: json['LastModifiedOnDate'],
      startDate: json['StartDate'],
      endDate: json['EndDate'],
      paragraphData: json['ParagraphData'] != null
          ? NewsParagraphData.fromJson(json['ParagraphData'])
          : null,
    );
  }
}

class NewsParagraphData {
  final String? paragraphID;
  final String? paragraphContent;
  final String? paragraphDraftContent;
  final String? uri;
  final String? applicationId;
  final String? createdByUserID;
  final String? createdOnDate;
  final String? lastModifiedByUserID;
  final String? lastModifiedOnDate;
  final String? startDate;
  final String? endDate;
  final String? hasAlarm;
  final String? deleteSuccess;

  NewsParagraphData({
    this.paragraphID,
    this.paragraphContent,
    this.paragraphDraftContent,
    this.uri,
    this.applicationId,
    this.createdByUserID,
    this.createdOnDate,
    this.lastModifiedByUserID,
    this.lastModifiedOnDate,
    this.startDate,
    this.endDate,
    this.hasAlarm,
    this.deleteSuccess,
  });

  factory NewsParagraphData.fromJson(Map<String, dynamic> json) {
    return NewsParagraphData(
      paragraphID: json['ParagraphID'],
      paragraphContent: json['ParagraphContent'],
      paragraphDraftContent: json['ParagraphDraftContent'],
      uri: json['URI'],
      applicationId: json['ApplicationId'],
      createdByUserID: json['CreatedByUserID'],
      createdOnDate: json['CreatedOnDate'],
      lastModifiedByUserID: json['LastModifiedByUserID'],
      lastModifiedOnDate: json['LastModifiedOnDate'],
      startDate: json['StartDate'],
      endDate: json['EndDate'],
      hasAlarm: json['HasAlarm'],
      deleteSuccess: json['DeleteSuccess'],
    );
  }
}

// NewsTableViewCell equivalent - exactly like iOS NewsTableViewCell.swift/.xib
class NewsTableViewCell extends StatelessWidget {
  final PromotionItem news;
  final VoidCallback? onTap;

  const NewsTableViewCell({
    super.key,
    required this.news,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        child: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(4),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.2),
                spreadRadius: 1,
                blurRadius: 3,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: Row(
            children: [
              // Image view - exactly like iOS imvPromotion
              Container(
                width: 120,
                height: 90,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4),
                  color: Colors.grey[200],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(4),
                  child: news.imageUrl != null
                      ? CachedNetworkImage(
                          imageUrl: news.imageUrl!,
                          fit: BoxFit.cover,
                          placeholder: (context, url) => Container(
                            color: Colors.grey[200],
                            child: const Center(
                              child: CircularProgressIndicator(strokeWidth: 2),
                            ),
                          ),
                          errorWidget: (context, url, error) => Container(
                            color: Colors.grey[200],
                            child: const Icon(Icons.image_not_supported, color: Colors.grey),
                          ),
                        )
                      : Container(
                          color: Colors.grey[200],
                          child: const Icon(Icons.image, color: Colors.grey),
                        ),
                ),
              ),
              const SizedBox(width: 16),
              // Title label - exactly like iOS lbTitle
              Expanded(
                child: Text(
                  news.title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// NewAndDealsView equivalent - exactly like iOS NewAndDealsView.swift/.xib
class NewAndDealsView extends StatelessWidget {
  final List<PromotionItem> dataList;
  final Function(PromotionItem)? onItemSelected;
  final VoidCallback? onShowAll;

  const NewAndDealsView({
    super.key,
    required this.dataList,
    this.onItemSelected,
    this.onShowAll,
  });

  @override
  Widget build(BuildContext context) {
    // Calculate height based on data count, exactly like iOS
    // Original logic: height = CGFloat(data.count) * 120 + 50
    double calculatedHeight = (dataList.length * 120.0) + 50.0;

    return SizedBox(
      height: calculatedHeight,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          // News items list
          Expanded(
            child: ListView.builder(
              physics: const NeverScrollableScrollPhysics(),
              itemCount: dataList.length,
              padding: EdgeInsets.zero,
              itemBuilder: (context, index) {
                return NewsTableViewCell(
                  news: dataList[index],
                  onTap: () => onItemSelected?.call(dataList[index]),
                );
              },
            ),
          ),

        ],
      ),
    );
  }
}

class NewsAndDealsCard extends StatelessWidget {
  final List<PromotionItem> data;
  final Function(PromotionItem)? onItemSelected;
  final VoidCallback? onShowAll;

  const NewsAndDealsCard({
    super.key,
    required this.data,
    this.onItemSelected,
    this.onShowAll,
  });

  @override
  Widget build(BuildContext context) {
    // Use the actual NewAndDealsView exactly like iOS
    return NewAndDealsView(
      dataList: data,
      onItemSelected: onItemSelected,
      onShowAll: onShowAll,
    );
  }
}
