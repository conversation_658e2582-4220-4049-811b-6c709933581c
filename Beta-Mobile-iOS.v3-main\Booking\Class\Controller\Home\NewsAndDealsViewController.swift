//
//  NewsAndDealsViewController.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 3/14/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit

class NewsAndDealsViewController: BaseViewController {
    @IBOutlet weak var tabPager: ScrollPager!

    private var indexTab = 0

    fileprivate weak var pageViewController: UIPageViewController?

    lazy var listPromotionVC: ListNewsViewController = {
        let vc = UIStoryboard.home[.listNews] as! ListNewsViewController
        vc.type = .promotion
        vc.isLoadData = false
        return vc
    }()
    lazy var listNewsVC: ListNewsViewController = {
        let vc = UIStoryboard.home[.listNews] as! ListNewsViewController
        vc.type = .news
        vc.isLoadData = false
        return vc
    }()
    
    fileprivate var objects: [NewModel] = []

    override func viewDidLoad() {
        super.viewDidLoad()
        localizableTitle = "NewsAndDeals.Title"

        pageViewController = childViewControllers.first(where: { $0 is UIPageViewController }) as? UIPageViewController
        pageViewController?.dataSource = self
        pageViewController?.delegate = self
        pageViewController?.setViewControllers([listPromotionVC], direction: .forward, animated: false, completion: nil)

        tabPager.addSegmentsWithTitles(segmentTitles: ["NewsAndDeals.Promotion", "NewsAndDeals.News"])
        tabPager.font = UIFont(fontName: .Oswald, size: 20)
        tabPager.selectedFont = UIFont(fontName: .Oswald, size: 20)
        
        getNewCategory()
    }
    
    private func getNewCategory(){
        self.showLoading()
        EcmProvider.rx.request(.getNewEvent).mapObject(DDKCResponse<NewModel>.self)
            
            .subscribe(onNext:{[weak self] response in
                self?.dismissLoading()
                self?.handlerResponse(response, success: {
                    guard let objects = response.ListObject, objects.count >= 2 else{
                        print("Data wrong")
                        return
                    }
                    self?.objects = objects
                    self?.listPromotionVC.categoryId = objects[0].CategoryId
                    self?.listPromotionVC.getNews()
                    
                }, error: {
                    self?.dismissLoading()
                })
                }, onError: { error in
                    self.dismissLoading()
            }).disposed(by: disposeBag)
    }
}

extension NewsAndDealsViewController: UIPageViewControllerDataSource, UIPageViewControllerDelegate{
    func pageViewController(_ pageViewController: UIPageViewController, viewControllerAfter viewController: UIViewController) -> UIViewController? {
        if viewController == listPromotionVC {
            if listNewsVC.items.isEmpty {
                self.listNewsVC.categoryId = objects[1].CategoryId
                self.listNewsVC.getNews()
            }
            return listNewsVC
        }
        return nil
    }

    func pageViewController(_ pageViewController: UIPageViewController, viewControllerBefore viewController: UIViewController) -> UIViewController? {
        if viewController == listNewsVC {
            return listPromotionVC
        }
        return nil
    }

    func pageViewController(_ pageViewController: UIPageViewController, didFinishAnimating finished: Bool, previousViewControllers: [UIViewController], transitionCompleted completed: Bool) {
        guard completed else {
            return
        }
        guard let vc = pageViewController.viewControllers?.first else {
            return
        }
        if vc == listPromotionVC {
            tabPager.setSelectedIndex(index: 0, animated: true)
        } else {
            tabPager.setSelectedIndex(index: 1, animated: true)
        }
    }
}

extension NewsAndDealsViewController: ScrollPagerDelegate{
    func scrollPager(scrollPager: ScrollPager, changedIndex: Int) {
        self.indexTab = changedIndex
        if changedIndex == 0 {
            pageViewController?.setViewControllers([listPromotionVC], direction: .reverse, animated: true, completion: nil)
        } else {
            pageViewController?.setViewControllers([listNewsVC], direction: .forward, animated: true, completion: nil)
            if listNewsVC.items.isEmpty {
                self.listNewsVC.categoryId = objects[1].CategoryId
                self.listNewsVC.getNews()
            }
        }
    }
}
