//
//  CinemaByProvinceTableViewCell.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 8/19/19.
//  Copyright © 2019 ddkc. All rights reserved.
//

import UIKit

class CinemaByProvinceTableViewCell: UITableViewCell {

    @IBOutlet weak var collectionView: UICollectionView!
    @IBOutlet weak var cinemaProvince: UILabel!
    @IBOutlet weak var cinemaCountLabel: UILabel!
    @IBOutlet weak var arrowImageView: UIImageView!

    var cinemas: [CinemaModel] = []
    var province: CinemaProvinceModel?
    var indexPath: IndexPath?
    var selectHandler: (CinemaModel) -> Void = {_ in}
    var expandHandler: (IndexPath) -> Void = {_ in}

    override func awakeFromNib() {
        super.awakeFromNib()
        collectionView.register(UINib(nibName: CinemaCollectionViewCell.id, bundle: nil), forCellWithReuseIdentifier: CinemaCollectionViewCell.id)
        collectionView.isScrollEnabled = false
    }

    func configure(_ province: CinemaProvinceModel, index: IndexPath) {
        self.province = province
        indexPath = index
        cinemas = province.ListCinema ?? []
        cinemaProvince.text = province.CityName
        cinemaCountLabel.text = cinemas.count.toString
        collectionView.reloadData()

        tranformArrow()
    }

    @IBAction func expandTapped(_ sender: Any) {
        guard let index = indexPath else {
            return
        }
        tranformArrow()
        province?.isOpen = !(province?.isOpen ?? false)
        expandHandler(index)
    }

    private func tranformArrow() {
        arrowImageView.transform = .identity
        if province?.isOpen == true {
            arrowImageView.transform = CGAffineTransform(rotationAngle: -.pi / 2)
        } else {
            arrowImageView.transform = CGAffineTransform(rotationAngle: 0)
        }
    }

}

extension CinemaByProvinceTableViewCell: UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return cinemas.count
    }

    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        guard let cell = collectionView.dequeueReusableCell(withReuseIdentifier: CinemaCollectionViewCell.id, for: indexPath) as? CinemaCollectionViewCell else {
            return CinemaCollectionViewCell()
        }

        cell.configure(cinemas[indexPath.row])

        return cell
    }

    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        let itemWidth: CGFloat = (collectionView.frame.width - 20) / 2
        return CGSize(width: itemWidth, height: 175.0)
    }

    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        collectionView.deselectItem(at: indexPath, animated: true)
        selectHandler(cinemas[indexPath.row])
    }
}
