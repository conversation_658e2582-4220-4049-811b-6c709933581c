import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../other_tab/setting/policy_screen.dart';
import '/constants/index.dart';
import '/core/index.dart';
import '/cubit/index.dart';
import '/models/index.dart';
import '/utils/index.dart';
import '/widgets/index.dart';

class RegisterPage extends StatefulWidget {
  const RegisterPage({super.key});

  @override
  State<RegisterPage> createState() => _RegisterPageState();
}

class _RegisterPageState extends State<RegisterPage> {
  bool _acceptTerms = false;
  final bool _isCreateCard = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: appBar(title: 'pages.login.login.Register'.tr(), titleColor: Colors.white),
      body: SingleChildScrollView(
        child: Column(
          children: [
            const SizedBox(height: CSpace.xl3),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: CSpace.xl3),
              child: WForm<MUser>(list: _listFormItem),
            ),

            // Checkboxes matching iOS
            _buildTermsCheckbox(),

            const SizedBox(height: CSpace.xl3),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: CSpace.xl3),
              child: WButton(
                  onPressed: () async {
                    final ok = await _handleRegister();
                    if (!ok) return;
                    context.read<BlocC<MUser>>().submit(
                        api: (value, page, size, sort) {
                          value['isCreateCard'] = _isCreateCard;
                          value['reCaptchaToken'] = '';
                          // return print(value);
                          return RepositoryProvider.of<Api>(context).auth.register(body: value);
                        },
                        // onlyApi: true,
                        submit: (data) => _showSuccessDialog(context, data));
                  },
                  child: Text('pages.login.login.Register'.tr())),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text("pages.login.register.Do you already have an account?".tr(),
                    style: TextStyle(color: CColor.black.shade300, fontSize: CFontSize.sm)),
                WButton(
                  type: TextButton,
                  onPressed: () => Navigator.pop(context),
                  child: Text('pages.login.login.Log in'.tr(), style: const TextStyle(fontSize: CFontSize.sm)),
                )
              ],
            ),
          ],
        ),
      ),
    );
  }

  @override
  void initState() {
    _init();
    super.initState();
  }

  List<MFormItem> _listFormItem = [];

  /// Initialize form items to match iOS RegisterViewController exactly
  Future<void> _init() async {
    RegExp passwordRegExp = RegExp(r"^.{6,}$");
    RegExp emailRegExp = RegExp(r'^[^@]+@[^@]+\.[^@]+$');

    _listFormItem = [
      // Core fields - match iOS exactly
      MFormItem(
        label: 'Register.RequiredInfo'.tr().toUpperCase(),
        type: EFormItemType.title,
      ),
      MFormItem(
        name: 'FullName',
        label: 'pages.login.register.Fullname'.tr(),
        required: true,
        onValidator: (value, listController) {
          if (value == null || value.isEmpty) {
            return 'Register.FullNameRequired'.tr();
          }
          return null;
        },
      ),

      MFormItem(
        name: 'email',
        label: 'pages.login.login.Email address'.tr(),
        keyBoard: EFormItemKeyBoard.email,
        required: true,
        onValidator: (value, listController) {
          if (value != null && value.isNotEmpty && !emailRegExp.hasMatch(value)) {
            return 'Register.InvalidEmailFormat'.tr();
          }
          return null;
        },
      ),

      MFormItem(
        name: 'password',
        label: 'pages.login.login.Password'.tr(),
        password: true,
        required: true,
        onValidator: (value, listController) {
          if (value == null || !passwordRegExp.hasMatch(value)) {
            return 'Register.PasswordMinLength'.tr();
          }
          return null;
        },
      ),

      MFormItem(
        name: 'confirmPassword',
        label: 'pages.login.register.Re-enter password'.tr(),
        password: true,
        required: true,
        onValidator: (value, listController) {
          if (value != null && listController['password']!.text != '' && value != listController['password']!.text) {
            return 'Register.PasswordMismatch'.tr();
          }
          return null;
        },
      ),

      MFormItem(
        name: 'phoneOffice',
        label: 'pages.login.register.Phone number'.tr(),
        keyBoard: EFormItemKeyBoard.phone,
        required: true,
        onValidator: (value, listController) {
          if (value.isEmpty || value == null) {
            return 'Register.PhoneRequired'.tr();
          } else if (((value as String).length < 10 || value.length > 15 )  || !RegExp(r'^(?:[+0]9)?[0-9]{10,12}$').hasMatch(value)) {
            return 'Register.PhoneValid'.tr();
          }
          return null;
        },
      ),

      MFormItem(
        label: 'Register.AdditionalInfo'.tr().toUpperCase(),
        type: EFormItemType.title,
      ),
      // Additional iOS fields
      MFormItem(
        name: 'personalId',
        label: 'Register.PersonalId'.tr(),
        required: false,
        // keyBoard: EFormItemKeyBoard,
      ),
      MFormItem(
        type: EFormItemType.date,
        required: false,
        name: 'birthDate',
        // selectDateType: SelectDateType.before,
        label: 'pages.login.register.birthDate'.tr(),
        onValidator: (value, listController) {
          if (value == null || value.isEmpty) return null;

          try {
            // Parse từ dd/MM/yyyy sang DateTime
            final parts = value.split('/');
            if (parts.length != 3) return 'Ngày không hợp lệ';

            final day = int.parse(parts[0]);
            final month = int.parse(parts[1]);
            final year = int.parse(parts[2]);

            final selectedDate = DateTime(year, month, day);
            final now = DateTime.now();

            // Kiểm tra chênh lệch năm
            final differenceInYears = now.year - selectedDate.year;

            // Nếu cùng năm nhưng chưa đến ngày thì giảm bớt 1 năm
            if (differenceInYears == 10 &&
                (now.month < selectedDate.month ||
                    (now.month == selectedDate.month && now.day < selectedDate.day))) {
              return 'Năm sinh không hợp lệ';
            }

            if (differenceInYears < 10) {
              return 'Năm sinh không hợp lệ';
            }

            return null; // Hợp lệ
          } catch (e) {
            return 'Năm sinh không hợp lệ';
          }
        },

        // onValidator: (value, listController) {
        //   if (value == null || value.isEmpty) return null;
        //       print("******");
        //       print(value);
        //     // final selectedDate = DateTime.parse(value); // ISO string → DateTime
        //     // final now = DateTime.now();
        //     //
        //     // if (selectedDate.isAfter(now)) {
        //     //   return 'Ngày sinh không được lớn hơn hiện tại'; // Hoặc .tr()
        //     // }
        //   return value; // Hợp lệ
        // },
      ),

      MFormItem(
        type: EFormItemType.select,
        required: false,
        name: 'gender',
        label: 'pages.login.register.Gender'.tr(),
        items: [
          MOption(label: 'pages.login.register.Male'.tr(), value: '1'),
          MOption(label: 'pages.login.register.Female'.tr(), value: '2'),
          MOption(label: 'pages.login.register.Other'.tr(), value: '3'),
        ],
      ),
    ];
  }

  /// Handle registration with proper validation and RegisterModel
  Future<bool> _handleRegister() async {
    if (!_acceptTerms) {
      UDialog().showError(text: "pages.login.register.AcceptPolicy".tr());
      return false;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Register.TermsAgreement'.tr())), // Match Android string
      );
    }
    return true;
  }

  /// Build terms checkbox with clickable links - matches iOS/Android exactly
  Widget _buildTermsCheckbox() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Checkbox(
          value: _acceptTerms,
          onChanged: (value) => setState(() => _acceptTerms = value ?? false),
          materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
        ),
        Expanded(
          child: RichText(
            text: TextSpan(
              style: const TextStyle(
                color: Colors.black,
                fontSize: 15,
                height: 1.4,
              ),
              children: [
                TextSpan(text: 'Register.TermsText'.tr()),
                TextSpan(
                  text: 'Register.PrivacyPolicy'.tr(),
                  style: const TextStyle(
                    color: Colors.blue,
                    fontWeight: FontWeight.w500,
                  ),
                  recognizer: TapGestureRecognizer()..onTap = () => _openPrivacyPolicy(),
                ),
                TextSpan(text: 'Register.And'.tr()),
                TextSpan(
                  text: 'Register.TermsOfUse'.tr(),
                  style: const TextStyle(
                    color: Colors.blue,
                    fontWeight: FontWeight.w500,
                  ),
                  recognizer: TapGestureRecognizer()..onTap = () => _openTermsOfUse(),
                ),
                TextSpan(text: 'Register.OfBetacinemas'.tr()),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// Open privacy policy page - matches iOS/Android navigation
  void _openPrivacyPolicy() {
    // TODO: Navigate to privacy policy page
    // For now, show a placeholder dialog
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const PolicyScreen(policyType: PolicyType.terms),
      ),
    );
  }

  /// Open terms of use page - matches iOS/Android navigation
  void _openTermsOfUse() {
    // TODO: Navigate to terms of use page
    // For now, show a placeholder dialog

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const PolicyScreen(policyType: PolicyType.security),
      ),
    );
  }

  /// Show success dialog matching Android/iOS exactly
  /// Android: "Đã đăng ký thành công" + "Bạn đã được cấp thẻ thành viên số" + cardNumber
  /// iOS: RegisterResultViewController with card number display
  void _showSuccessDialog(BuildContext context, dynamic userData) {
    // Extract card number from response data
    String? cardNumber;
    if (userData != null) {
      if (userData is Map<String, dynamic>) {
        cardNumber = userData['CardNumber'] as String?;
      } else if (userData.toString().isNotEmpty) {
        // Try to extract card number from string response
        cardNumber = userData.toString();
      }
    }

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Container(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Success icon - matches Android ic_finish
              Container(
                width: 80,
                height: 80,
                decoration: const BoxDecoration(
                  color: Color(0xFF7ED321), // Green color from Android
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.check,
                  color: Colors.white,
                  size: 40,
                ),
              ),

              const SizedBox(height: 16),

              // Success title - matches Android "Đã đăng ký thành công"
              Text(
                ApiErrorHandler.handleSuccess('REGISTRATION_SUCCESSFULLY'),
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF7ED321),
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 24),

              // Card number section - matches Android/iOS
              if (cardNumber != null && cardNumber.isNotEmpty) ...[
                const Text(
                  'Bạn đã được cấp thẻ thành viên số',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.black87,
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 12),

                // Card number display - matches Android tvCardNumber
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Text(
                    cardNumber,
                    style: const TextStyle(
                      fontSize: 20,
                      fontFamily: 'Oswald',
                      fontWeight: FontWeight.bold,
                      color: Colors.black
                    ),
                  ),
                ),

                const SizedBox(height: 24),
              ] else ...[
                // No card number case
                const Text(
                  'Tài khoản của bạn đã được tạo thành công!',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.black87,
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 24),
              ],

              // OK button - matches Android/iOS
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    Navigator.of(context).pop();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: CColor.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text(
                    'OK',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
