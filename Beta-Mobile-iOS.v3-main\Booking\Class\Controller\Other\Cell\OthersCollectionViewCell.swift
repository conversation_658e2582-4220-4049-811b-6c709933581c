//
//  OthersCollectionViewCell.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 8/2/19.
//  Copyright © 2019 ddkc. All rights reserved.
//

import UIKit

class OthersCollectionViewCell: UICollectionViewCell {

    @IBOutlet weak var iconView: RoundView!
    @IBOutlet weak var iconImageView: UIImageView!
    @IBOutlet weak var titleLabel: UILabel!

    var item: OtherItem? {
        didSet {
            configure()
        }
    }

    override func awakeFromNib() {
        super.awakeFromNib()
    }

    private func configure() {
        guard let item = item else {
            return
        }

        iconView.backgroundColor = item.color.toColor
        iconImageView.image = UIImage.init(named: item.icon)
        titleLabel.text = item.text.localized
    }
}
