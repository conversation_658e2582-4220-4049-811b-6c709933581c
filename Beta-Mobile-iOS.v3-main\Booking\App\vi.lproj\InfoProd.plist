<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>Betacineplex</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>1.0.0</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>fb172022020116240</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>1.0.0</string>
	<key>FacebookAppID</key>
	<string>172022020116240</string>
	<key>FacebookDisplayName</key>
	<string>Betacineplex</string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>fbapi</string>
		<string>fb-messenger-share-api</string>
		<string>fbauth2</string>
		<string>fbshareextension</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSExceptionDomains</key>
		<dict>
			<key>http://**************:8802</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<true/>
				<key>NSIncludesSubdomains</key>
				<true/>
			</dict>
			<key>http://**************:8803</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<true/>
				<key>NSIncludesSubdomains</key>
				<true/>
			</dict>
		</dict>
	</dict>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>Sử dụng để lấy danh sách rạp gần bạn</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>Sử dụng để lấy danh sách rạp gần bạn</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Sử dụng để chụp ảnh avatar của bạn</string>
	<key>UIAppFonts</key>
	<array>
		<string>Oswald-Bold.ttf</string>
		<string>Oswald-ExtraLight.ttf</string>
		<string>Oswald-Light.ttf</string>
		<string>Oswald-Medium.ttf</string>
		<string>Oswald-Regular.ttf</string>
		<string>Oswald-SemiBold.ttf</string>
		<string>SourceSansPro-Black.ttf</string>
		<string>SourceSansPro-BlackItalic.ttf</string>
		<string>SourceSansPro-Bold.ttf</string>
		<string>SourceSansPro-BoldItalic.ttf</string>
		<string>SourceSansPro-ExtraLight.ttf</string>
		<string>SourceSansPro-ExtraLightItalic.ttf</string>
		<string>SourceSansPro-Italic.ttf</string>
		<string>SourceSansPro-Light.ttf</string>
		<string>SourceSansPro-LightItalic.ttf</string>
		<string>SourceSansPro-Regular.ttf</string>
		<string>SourceSansPro-SemiBold.ttf</string>
		<string>SourceSansPro-SemiBoldItalic.ttf</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<true/>
</dict>
</plist>
