import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_app/constants/index.dart';
import 'package:flutter_app/cubit/index.dart';
import 'package:flutter_app/models/project/notification.dart';
import 'package:flutter_app/pages/my_profile/member_screen_responsive.dart';
import 'package:flutter_app/pages/notification/index.dart';
import 'package:flutter_app/pages/other_tab/recruitment/recruitment_screen.dart';
import 'package:flutter_app/pages/other_tab/setting/index.dart';
import 'package:flutter_app/pages/other_tab/widget/other_cell.dart';
import 'package:flutter_app/pages/voucher/free_voucher_screen.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../service/language_service.dart';

class OtherScreen extends StatefulWidget {
  const OtherScreen({super.key});

  @override
  State<OtherScreen> createState() => _OtherScreenState();
}

class _OtherScreenState extends State<OtherScreen> {
  final List<OtherItem> _items = [
    OtherItem(color: 0xFF0093EE, icon: 'voucher-free', text: 'free_voucher'.tr()),
    OtherItem(color: 0xFF26C1C9, icon: 'cinema', text: 'ListCinema.Title'.tr()),
    OtherItem(color: 0xFF81C926, icon: 'member', text: 'Menu.Member'.tr()),
    OtherItem(color: 0xFFFD7B1F, icon: 'notification', text: 'Menu.Notification'.tr()),
    OtherItem(color: 0xFFD81B7B, icon: 'require', text: 'Menu.Recruitment'.tr()),
    OtherItem(color: 0xFFAB7DF6, icon: 'setting', text: 'Menu.Setting'.tr()),
  ];

  @override
  void initState() {
    // TODO: implement initState
    context.read<AuthC>().check(context: context);
    checkLanguage();
    super.initState();
  }

  void checkLanguage() async {}

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
             Padding(
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
              child: Text(
                'Other.Title'.tr(),
                style: const TextStyle(
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF223849),
                  fontFamily: 'Oswald',
                ),
              ),
            ),
            Expanded(
              child: GridView.builder(
                padding: const EdgeInsets.all(16),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  mainAxisSpacing: 18,
                  crossAxisSpacing: 16,
                  childAspectRatio: 1,
                ),
                itemCount: _items.length,
                itemBuilder: (context, index) {
                  return OtherCell(
                    item: _items[index],
                    onTap: () => _handleItemTap(_items[index]),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _handleItemTap(OtherItem item) async {
    switch (item.icon) {
      case 'voucher-free':
        // Navigate to Free Voucher screen
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const FreeVoucherScreen()),
        );
        break;
      case 'cinema':
        // Navigate to Cinema List screen
        context.goNamed(CRoute.listFilmScreen, extra: true);
        break;
      case 'member':
        final authState = context.read<AuthC>().state.user;
        if (authState == null) {
          // Navigate to login screen if not authenticated
          context.pushNamed(CRoute.login);
        } else {
          // Navigate to Member screen
          context.pushNamed(CRoute.member);

          // Navigator.push(
          //   context,
          //   MaterialPageRoute(builder: (context) => const ResponsiveMemberScreen()),
          // );
        }
        break;
      case 'notification':
        // Navigate to Notification screen
      context.pushNamed(CRoute.notification);
        // Navigator.push(
        //   context,
        //   MaterialPageRoute(
        //       builder: (context) => BlocProvider(create: (context) => BlocC<MNotification>(), child: const NotificationPage())),
        // );
        break;
      case 'require':
        // Navigate to Recruitment screen
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const RecruitmentScreen()),
        );
        break;
      case 'setting':
        // Navigate to Settings screen
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const SettingScreen()),
        );
        break;
    }
  }
}
