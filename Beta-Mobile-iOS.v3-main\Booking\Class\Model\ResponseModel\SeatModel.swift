//
//  SeatModel.swift
//  Booking-dev
//
//  Created by <PERSON><PERSON> on 5/27/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation
import ObjectMapper

class SeatStatus: Mappable {
    var Name: String?
    var Value: String?
    var Class: String?

    var isUsed: Bool {
        return Value == Values.used.rawValue
    }

    var isWay: Bool {
        return Value == Values.way.rawValue
    }

    var isBroken: Bool {
        return Value == Values.broken.rawValue
    }

    var isNotUsed: Bool {
        return Value == Values.notUsed.rawValue
    }

    required init?(map: Map) {

    }

    func mapping(map: Map) {
        Name <- map["Name"]
        Value <- map["Value"]
        Class <- map["Class"]
    }

    enum Values: String {
        case notUsed = "0"
        case used = "1"
        case way = "2"
        case broken = "3"
    }
}

enum SeatSoldStatus: Int
{
    case WAITING = -1 //chờ QR xử lý
    case SELECTING = 0//ghế đang chọn
    case EMPTY = 1//ghế trống
    case SELECTED = 2//ghế đang được người khác giữ
    case BOOKED = 3//ghế đã đặt chưa thanh toán
    case SOLD = 4//ghế đã thanh toán
    case SOLDED = 5//ghế đã thanh toán + lấy vé

    static func ==(_ leftInt: Int?, rightStatus: SeatSoldStatus) -> Bool {
        return leftInt == rightStatus.rawValue
    }

    static func ==(_ leftStatus: SeatSoldStatus, rightInt: Int?) -> Bool {
        return leftStatus.rawValue == rightInt
    }

    static func ==(_ leftStatus: SeatSoldStatus?, rightStatus: SeatSoldStatus) -> Bool {
        return leftStatus == rightStatus
    }
}

class SeatType: Mappable {
    var Name: String?
    var Value: String?
    var Background: String?

    var isNormal: Bool {
        return Value == Catalog.normal.rawValue
    }

    var isVip: Bool {
        return Value == Catalog.vip.rawValue
    }

    var isCouple: Bool {
        return Value == Catalog.couple.rawValue
    }

    required init?(map: Map) {

    }

    func mapping(map: Map) {
        Name <- map["Name"]
        Value <- map["Value"]
        Background <- map["Background"]
    }

    enum Catalog: String {
        case normal = "c0f1e9a8-c9f5-4b0d-8b10-f3108996e60b"
        case vip = "9f2dda7f-d09e-4d58-a504-5a6311345aae"
        case couple = "9beee28c-8cae-41d0-bd01-b0b22108432c"
    }
}

class SeatModel: Mappable, Equatable {
    var SeatName: String?
    var Status: SeatStatus?
    var seatType: SeatType?
    var SeatIndex: Int?
    var ClassStyle: String?
    var SoldStatus: Int?
    var coupleSeat: SeatModel? // weak

    var SeatRow: Int = 0
    var SeatCol: Int = 0
    var SeatRowName: String? {
        if let row = SeatName?.first {
            return String(row)
        }
        return nil
    }

    var SeatColName: String? {
        if let col = SeatName?.dropFirst() {
            return String(col)
        }
        return nil
    }

    var SeatNumber: String {
        if seatType?.isCouple == true {
//            return (SeatColName ?? "") + " - " + (coupleSeat?.SeatColName ?? "")
            return (coupleSeat?.SeatName ?? "") + " - " + (SeatName ?? "")
        }
//        return SeatColName ?? ""
        return SeatName ?? ""
    }

    required init?(map: Map) {

    }

    func mapping(map: Map) {
        SeatName <- map["SeatName"]
        Status <- map["Status"]
        seatType <- map["SeatType"]
        SeatIndex <- map["SeatIndex"]
        ClassStyle <- map["ClassStyle"]
        SoldStatus <- map["SoldStatus"]
    }

    static func == (lhs: SeatModel, rhs: SeatModel) -> Bool {
        return lhs === rhs
    }
}
