//
//  HistoryPointTableViewCell.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 8/4/19.
//  Copyright © 2019 ddkc. All rights reserved.
//

import UIKit

class HistoryPointTableViewCell: UITableViewCell {
    @IBOutlet weak var dateLabel: UILabel!
    @IBOutlet weak var stateLabel: UILabel!
    @IBOutlet weak var nameLabel: UILabel!
    @IBOutlet weak var pointLabel: UILabel!

    override func awakeFromNib() {
        super.awakeFromNib()
        // Initialization code
    }

    func configure(_ point: PointHistory) {
        dateLabel.text = point.dateString
        stateLabel.text = point.pointStatus.description.localized.uppercased()
        stateLabel.textColor = point.pointStatus.color
        pointLabel.text = (point.point ?? 0) > 0 ? "+\(point.point ?? 0)" : "\(point.point ?? 0)"

        nameLabel.isHidden = !point.pointStatus.showName
        nameLabel.text = "(\(point.accountName ?? ""))"
    }
    
}
