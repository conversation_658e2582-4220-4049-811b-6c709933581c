//
//  AreaCenemaHeaderView.swift
//  Booking-dev
//
//  Created by <PERSON><PERSON> on 4/9/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit

class AreaCinemaHeaderView: UITableViewHeaderFooterView {
    @IBOutlet weak var ivArrow: UIImageView!
    @IBOutlet weak var lbTitle: UILabel!
    @IBOutlet weak var lbSubTitle: UILabel!

    var onTapAction: ((AreaCinemaHeaderView) -> Void)?

    override func awakeFromNib() {
        super.awakeFromNib()
    }

    @IBAction func didTapOnHeader(_ sender: Any) {
        onTapAction?(self)
    }

    override func updateViewWithSection(_ tbSection: TableSection) {
        lbTitle.text = tbSection.title
        lbSubTitle.text = tbSection.subTitle
        ivArrow.transform = .identity
        if tbSection.isOpen {
            ivArrow.transform = CGAffineTransform(rotationAngle: -.pi / 2)
        } else {
            ivArrow.transform = CGAffineTransform(rotationAngle: 0)
        }
    }
}
