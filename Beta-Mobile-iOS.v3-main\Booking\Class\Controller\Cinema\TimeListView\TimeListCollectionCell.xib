<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14109" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14088"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="SourceSansPro-Bold.ttf">
            <string>SourceSansPro-Bold</string>
        </array>
        <array key="SourceSansPro-Regular.ttf">
            <string>SourceSansPro-Regular</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" id="WzN-p2-Zki" customClass="TimeListCollectionCell" customModule="Booking_dev" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="80" height="63"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" insetsLayoutMarginsFromSafeArea="NO">
                <rect key="frame" x="0.0" y="0.0" width="80" height="63"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="hGR-h4-D3j">
                        <rect key="frame" x="0.0" y="0.0" width="80" height="63"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="m48-Xa-LDr" customClass="RoundView" customModule="Booking_dev" customModuleProvider="target">
                                <rect key="frame" x="6" y="5" width="68" height="30"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="8:45" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="azx-eP-kor">
                                        <rect key="frame" x="12" y="5" width="44" height="20.5"/>
                                        <fontDescription key="fontDescription" name="SourceSansPro-Bold" family="Source Sans Pro" pointSize="16"/>
                                        <color key="textColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" red="0.90196078431372551" green="0.90196078431372551" blue="0.90196078431372551" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <constraints>
                                    <constraint firstItem="azx-eP-kor" firstAttribute="leading" secondItem="m48-Xa-LDr" secondAttribute="leading" constant="12" id="Clz-dZ-6SY"/>
                                    <constraint firstItem="azx-eP-kor" firstAttribute="centerX" secondItem="m48-Xa-LDr" secondAttribute="centerX" id="Z4f-0D-gFd"/>
                                    <constraint firstAttribute="height" constant="30" id="fJU-7d-zHs"/>
                                    <constraint firstItem="azx-eP-kor" firstAttribute="centerY" secondItem="m48-Xa-LDr" secondAttribute="centerY" id="lhh-CM-aAk"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                        <real key="value" value="15"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </view>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="120 trống" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="WIP-eL-mg2">
                                <rect key="frame" x="0.0" y="35" width="80" height="18"/>
                                <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="14"/>
                                <color key="textColor" red="0.1176470588" green="0.1215686275" blue="0.15686274510000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstAttribute="trailing" secondItem="WIP-eL-mg2" secondAttribute="trailing" id="IBx-N2-kYR"/>
                            <constraint firstItem="m48-Xa-LDr" firstAttribute="leading" secondItem="hGR-h4-D3j" secondAttribute="leading" constant="6" id="PUZ-6x-nbT"/>
                            <constraint firstItem="WIP-eL-mg2" firstAttribute="leading" secondItem="hGR-h4-D3j" secondAttribute="leading" id="STL-74-XCi"/>
                            <constraint firstItem="m48-Xa-LDr" firstAttribute="centerX" secondItem="hGR-h4-D3j" secondAttribute="centerX" id="VNY-sy-BpH"/>
                            <constraint firstItem="WIP-eL-mg2" firstAttribute="top" secondItem="m48-Xa-LDr" secondAttribute="bottom" id="eDN-wL-nJU"/>
                            <constraint firstItem="m48-Xa-LDr" firstAttribute="top" secondItem="hGR-h4-D3j" secondAttribute="top" constant="5" id="rWh-GX-o9e"/>
                        </constraints>
                    </view>
                </subviews>
            </view>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstAttribute="bottom" secondItem="hGR-h4-D3j" secondAttribute="bottom" id="PVV-FX-ctL"/>
                <constraint firstItem="hGR-h4-D3j" firstAttribute="leading" secondItem="WzN-p2-Zki" secondAttribute="leading" id="geI-SH-ajL"/>
                <constraint firstAttribute="trailing" secondItem="hGR-h4-D3j" secondAttribute="trailing" id="nk6-uI-l91"/>
                <constraint firstItem="hGR-h4-D3j" firstAttribute="top" secondItem="WzN-p2-Zki" secondAttribute="top" id="wm3-v6-hoe"/>
            </constraints>
            <viewLayoutGuide key="safeArea" id="qWn-gl-L3q"/>
            <size key="customSize" width="80" height="-27"/>
            <connections>
                <outlet property="lbEmptyNumber" destination="WIP-eL-mg2" id="8qY-UM-obk"/>
                <outlet property="lbTime" destination="azx-eP-kor" id="tJ0-J6-eyA"/>
                <outlet property="vRound" destination="m48-Xa-LDr" id="l20-Fg-b1d"/>
            </connections>
            <point key="canvasLocation" x="25" y="-60.5"/>
        </collectionViewCell>
    </objects>
</document>
