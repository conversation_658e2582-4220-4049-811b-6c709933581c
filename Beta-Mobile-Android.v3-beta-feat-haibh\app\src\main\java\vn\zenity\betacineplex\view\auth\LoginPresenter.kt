package vn.zenity.betacineplex.view.auth

import android.util.Log
import io.reactivex.disposables.Disposable
import vn.zenity.betacineplex.Manager.Network.APIClient
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.app.App
import vn.zenity.betacineplex.global.Constant
import vn.zenity.betacineplex.global.Global
import vn.zenity.betacineplex.global.Tracking
import vn.zenity.betacineplex.helper.extension.DeviceHelper
import vn.zenity.betacineplex.helper.extension.PreferencesHelper
import vn.zenity.betacineplex.helper.extension.applyOn
import vn.zenity.betacineplex.helper.extension.getString
import vn.zenity.betacineplex.model.DDKCReponse
import vn.zenity.betacineplex.model.RequestModel.LoginModel
import vn.zenity.betacineplex.model.UserModel
import java.lang.Exception
import java.lang.ref.WeakReference

class LoginPresenter : LoginContractor.Presenter {
    private var view: WeakReference<LoginContractor.View?>? = null
    private var disposable: Disposable? = null
    override fun attachView(view: LoginContractor.View) {
        this.view = WeakReference(view)
    }

    override fun detachView() {
        disposable?.dispose()
        this.view?.clear()
        this.view = null
    }

    override fun login(email: String, password: String, captchaToken: String) {
        Log.d("HaiBH", "login: "+captchaToken)
        Tracking.share().authBegin(this.view?.get()?.getViewContext(), "email")
        val model = LoginModel(email, password, captchaToken)
        model.DeviceId = DeviceHelper.shared.deviceId()
        this.view?.get()?.showLoading()
        disposable = APIClient.shared.accountAPI.login(model)
                .applyOn()
                .subscribe({ response ->
                    processResponse(response)
                }, { _ ->
                    this.view?.get()?.showLoginError(R.string.LOGIN_FAILED.getString())
                    this.view?.get()?.hideLoading()
                })

    }

    private fun processResponse(response: DDKCReponse<UserModel>) {
        Log.d("HaiBH", "processResponse isSuccess: "+response.isSuccess)
        try {
            if (response.isSuccess) {
                response.Data?.AccountId = response.Data?.UserId
                Global.share().user = response.Data
                if (response.Data?.AccountId != null) {
                    Log.d("HaiBH", "processResponse getProfile AccountId: "+response.Data?.AccountId)
                    Log.d("HaiBH", "processResponse getProfile Token: "+response.Data?.Token)
                    disposable = APIClient.shared.accountAPI.getProfile(response.Data?.AccountId!!).applyOn().subscribe(
                        { responseProfile ->
                            if (responseProfile.isSuccess) {
                                responseProfile.Data?.Token = Global.share().token
                                Global.share().user = responseProfile.Data
                                App.shared().registerFCMDevice()
                                Tracking.share().authComplete(this.view?.get()?.getViewContext(), "email")
                                if (responseProfile.Data?.IsUpdatedFacebookPassword == false) {
                                    this.view?.get()?.hideLoading()
                                    this.view?.get()?.showUpdateFBPassword()
                                } else {
                                    this.view?.get()?.hideLoading()
                                    this.view?.get()?.showHome()
                                }
                            } else {
                                Log.d("HaiBH", "responseProfile err: "+response.Message)
                                this.view?.get()?.showLoginError(responseProfile.Message
                                    ?: R.string.LOGIN_FAILED.getString())
                                this.view?.get()?.hideLoading()
                            }
                        }, { _ ->
                            Log.d("HaiBH", "processResponse 1: "+response.Message)
                            this.view?.get()?.showLoginError(R.string.LOGIN_FAILED.getString())
                            this.view?.get()?.hideLoading()
                        }
                    )
                } else {
                    Log.d("HaiBH", "processResponse 2: "+response.Message)
                    this.view?.get()?.showLoginError(response.Message
                        ?: R.string.LOGIN_FAILED.getString())
                    this.view?.get()?.hideLoading()
                }
            } else {
                Log.d("HaiBH", "processResponse 3: "+response.Message)
                this.view?.get()?.showLoginError(response.Message ?: R.string.LOGIN_FAILED.getString())
                this.view?.get()?.hideLoading()
            }
        }catch (e: Exception){
            Log.d("HaiBH", "processResponse Exception: "+e)

        }

    }

    override fun forgotPassword(email: String?) {
        this.view?.get()?.showForgotPassword(email)
    }

    override fun register() {
        this.view?.get()?.showRegister()
    }

    override fun loginFacebook(fbId: String, token: String, captchaToken: String) {
        this.view?.get()?.showLoading()
        val map = mapOf("Token" to token, "ReCaptchaToken" to captchaToken)
        disposable = APIClient.shared.accountAPI.loginFacebook(map)
                .applyOn()
                .subscribe({ response ->
                    processResponse(response)
                }, { error ->
                    this.view?.get()?.showLoginError(error.message ?: "")
                    this.view?.get()?.hideLoading()
                })
    }

    override fun registerFCMToken(token: String) {

    }

}
