PODS:
  - ActiveLabel (1.1.0)
  - Alamofire (4.9.1)
  - AlamofireImage (3.6.0):
    - Alamofire (~> 4.9)
  - Crashlytics (3.14.0):
    - Fabric (~> 1.10.2)
  - DRPLoadingSpinner (1.3.1):
    - DRPLoadingSpinner/core (= 1.3.1)
  - DRPLoadingSpinner/core (1.3.1)
  - DynamicBlurView (4.1.0)
  - <PERSON><PERSON><PERSON> (1.10.2)
  - FBAEMKit (16.2.1):
    - FBSDKCoreKit_Basics (= 16.2.1)
  - FBSDKCoreKit (16.2.1):
    - FBAEMKit (= 16.2.1)
    - FBSDKCoreKit_Basics (= 16.2.1)
  - FBSDKCoreKit_Basics (16.2.1)
  - FBSDKLoginKit (16.2.1):
    - FBSDKCoreKit (= 16.2.1)
  - FirebaseAnalytics (10.16.0):
    - FirebaseAnalytics/AdIdSupport (= 10.16.0)
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseAnalytics/AdIdSupport (10.16.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleAppMeasurement (= 10.16.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseCore (10.16.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Logger (~> 7.8)
  - FirebaseCoreInternal (10.16.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseInstallations (10.16.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - FirebaseMessaging (10.16.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Reachability (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleAppMeasurement (10.16.0):
    - GoogleAppMeasurement/AdIdSupport (= 10.16.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleAppMeasurement/AdIdSupport (10.16.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 10.16.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (10.16.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleDataTransport (9.2.5):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30910.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleMaps (6.2.1):
    - GoogleMaps/Maps (= 6.2.1)
  - GoogleMaps/Base (6.2.1)
  - GoogleMaps/Maps (6.2.1):
    - GoogleMaps/Base
  - GooglePlaces (6.2.1)
  - GoogleUtilities/AppDelegateSwizzler (7.11.5):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
  - GoogleUtilities/Environment (7.11.5):
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.11.5):
    - GoogleUtilities/Environment
  - GoogleUtilities/MethodSwizzler (7.11.5):
    - GoogleUtilities/Logger
  - GoogleUtilities/Network (7.11.5):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.11.5)"
  - GoogleUtilities/Reachability (7.11.5):
    - GoogleUtilities/Logger
  - GoogleUtilities/UserDefaults (7.11.5):
    - GoogleUtilities/Logger
  - iCarousel (1.8.3)
  - ImageSlideshow/Alamofire (1.8.3):
    - AlamofireImage (~> 3.0)
    - ImageSlideshow/Core
  - ImageSlideshow/Core (1.8.3)
  - IQKeyboardManagerSwift (6.5.16)
  - KMNavigationBarTransition (1.1.11)
  - Mixpanel-swift (4.1.4):
    - Mixpanel-swift/Complete (= 4.1.4)
  - Mixpanel-swift/Complete (4.1.4)
  - Moya (14.0.0-alpha.1):
    - Moya/Core (= 14.0.0-alpha.1)
  - Moya/Core (14.0.0-alpha.1):
    - Alamofire (~> 4.1)
    - Result (~> 4.1)
  - Moya/RxSwift (14.0.0-alpha.1):
    - Moya/Core
    - RxSwift (~> 5.0)
  - nanopb (2.30909.0):
    - nanopb/decode (= 2.30909.0)
    - nanopb/encode (= 2.30909.0)
  - nanopb/decode (2.30909.0)
  - nanopb/encode (2.30909.0)
  - ObjectMapper (4.2.0)
  - PKHUD (5.3.0)
  - PopupDialog (1.1.1):
    - DynamicBlurView (~> 4.0)
  - PromisesObjC (2.3.1)
  - PullToRefreshKit (0.8.8)
  - ReCaptcha/Core (1.5.0)
  - ReCaptcha/RxSwift (1.5.0):
    - ReCaptcha/Core
    - RxSwift (~> 5.0)
  - Result (4.1.0)
  - RSBarcodes_Swift (5.2.0)
  - RxCocoa (5.1.3):
    - RxRelay (~> 5)
    - RxSwift (~> 5)
  - RxRelay (5.1.3):
    - RxSwift (~> 5)
  - RxSwift (5.0.1)
  - SignalRSwift (2.0.3):
    - Alamofire (~> 4.2)
    - Starscream (~> 3.0)
  - Starscream (3.1.1)
  - SwiftDate (6.3.1)
  - SwiftSignalRClient (1.0.0)
  - "UITableView+FDTemplateLayoutCell (1.6)"
  - VisualEffectView (4.1.5)
  - youtube-ios-player-helper (1.0.4)

DEPENDENCIES:
  - ActiveLabel
  - Alamofire
  - AlamofireImage
  - Crashlytics
  - DRPLoadingSpinner (from `https://github.com/hiennv92/DRPLoadingSpinner.git`)
  - Fabric
  - FBSDKCoreKit
  - FBSDKLoginKit
  - FirebaseAnalytics
  - FirebaseMessaging
  - GoogleMaps
  - GooglePlaces
  - iCarousel
  - ImageSlideshow/Alamofire
  - IQKeyboardManagerSwift
  - KMNavigationBarTransition
  - Mixpanel-swift
  - Moya (~> 14.0.0-alpha.1)
  - Moya/RxSwift
  - ObjectMapper
  - PKHUD
  - PopupDialog
  - PullToRefreshKit
  - ReCaptcha/RxSwift
  - RSBarcodes_Swift
  - RxCocoa
  - RxSwift (~> 5.0.1)
  - SignalRSwift
  - SwiftDate
  - SwiftSignalRClient
  - "UITableView+FDTemplateLayoutCell"
  - VisualEffectView
  - youtube-ios-player-helper

SPEC REPOS:
  trunk:
    - ActiveLabel
    - Alamofire
    - AlamofireImage
    - Crashlytics
    - DynamicBlurView
    - Fabric
    - FBAEMKit
    - FBSDKCoreKit
    - FBSDKCoreKit_Basics
    - FBSDKLoginKit
    - FirebaseAnalytics
    - FirebaseCore
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleMaps
    - GooglePlaces
    - GoogleUtilities
    - iCarousel
    - ImageSlideshow
    - IQKeyboardManagerSwift
    - KMNavigationBarTransition
    - Mixpanel-swift
    - Moya
    - nanopb
    - ObjectMapper
    - PKHUD
    - PopupDialog
    - PromisesObjC
    - PullToRefreshKit
    - ReCaptcha
    - Result
    - RSBarcodes_Swift
    - RxCocoa
    - RxRelay
    - RxSwift
    - SignalRSwift
    - Starscream
    - SwiftDate
    - SwiftSignalRClient
    - "UITableView+FDTemplateLayoutCell"
    - VisualEffectView
    - youtube-ios-player-helper

EXTERNAL SOURCES:
  DRPLoadingSpinner:
    :git: https://github.com/hiennv92/DRPLoadingSpinner.git

CHECKOUT OPTIONS:
  DRPLoadingSpinner:
    :commit: ea16b4514bcd2a6fa83b79c6e7b7bda47bff03ad
    :git: https://github.com/hiennv92/DRPLoadingSpinner.git

SPEC CHECKSUMS:
  ActiveLabel: 5e3f4de79a1952d4604b845a0610d4776e4b82b3
  Alamofire: 85e8a02c69d6020a0d734f6054870d7ecb75cf18
  AlamofireImage: be9963c6582d68b39e89191f64c82a7d7bf40fdd
  Crashlytics: 9220f5bc89e7a618df411b4f639389dbfb0e03d2
  DRPLoadingSpinner: 725e530bf4ebe2cc351b1fbc94d24fe8ac3300fd
  DynamicBlurView: 58e18fae80bb614e34681a4486870e7d257b62e8
  Fabric: ea977e3cd9c20425516d3dafd3bf8c941c51223f
  FBAEMKit: 4763aa27b8f69eb9d2c274189e91388de1dbd88a
  FBSDKCoreKit: 40ae989ca1da1bbeba56acd4671dde64ad415506
  FBSDKCoreKit_Basics: 1257d71d2d692661290bf2af4bea5f9a101387af
  FBSDKLoginKit: bf4f96d0ce475de2da4b1800ce150bd8a6f120f3
  FirebaseAnalytics: 7b41efc4eba5ff841cc94d5994b5f339361258f4
  FirebaseCore: 65a801af84cca84361ef9eac3fd868656968a53b
  FirebaseCoreInternal: 26233f705cc4531236818a07ac84d20c333e505a
  FirebaseInstallations: b822f91a61f7d1ba763e5ccc9d4f2e6f2ed3b3ee
  FirebaseMessaging: 80b4a086d20ed4fd385a702f4bfa920e14f5064d
  GoogleAppMeasurement: 079d7632810e9d9704a99932547ba1554acc4342
  GoogleDataTransport: 54dee9d48d14580407f8f5fbf2f496e92437a2f2
  GoogleMaps: 20d7b12be49a14287f797e88e0e31bc4156aaeb4
  GooglePlaces: 94974aa119573d5acc2a35a699948dac838abd73
  GoogleUtilities: 13e2c67ede716b8741c7989e26893d151b2b2084
  iCarousel: d782f635afac190c49bb8ee455882284cff8b85f
  ImageSlideshow: 4694d7d699a1549582e7cc631fb392d8f91630f3
  IQKeyboardManagerSwift: 12d89768845bb77b55cc092ecc2b1f9370f06b76
  KMNavigationBarTransition: f622e909e51f7308fb5b9aa38cceaa9e7c374e9c
  Mixpanel-swift: 4991762c97c3535ce4e67c395a93e40ab7b666f7
  Moya: 65485239114b3b327a17dc2e839f6c8b7143aae9
  nanopb: b552cce312b6c8484180ef47159bc0f65a1f0431
  ObjectMapper: 1eb41f610210777375fa806bf161dc39fb832b81
  PKHUD: 98f3e4bc904b9c916f1c5bb6d765365b5357291b
  PopupDialog: 720c92befd8bc23c13442254945213db5612f149
  PromisesObjC: c50d2056b5253dadbd6c2bea79b0674bd5a52fa4
  PullToRefreshKit: 7cc53e720e69f135cffa1a57784dfb534f0222ab
  ReCaptcha: 40ae1dc390fd5837267674b9a6ae0978abb5eeeb
  Result: bd966fac789cc6c1563440b348ab2598cc24d5c7
  RSBarcodes_Swift: 416d11923d8dfff0e038a853d727d6c8254cb527
  RxCocoa: e2ed092d5432e4296aa818021d27de4090ac0354
  RxRelay: 5a18c2eb2d68326ebaf0112f80d837ae41b92b97
  RxSwift: e2dc62b366a3adf6a0be44ba9f405efd4c94e0c4
  SignalRSwift: 6f30893680780d4f4e9981795478ea43f5b63f3e
  Starscream: 4bb2f9942274833f7b4d296a55504dcfc7edb7b0
  SwiftDate: 72d28954e8e1c6c1c0f917ccc8005e4f83c7d4b2
  SwiftSignalRClient: f9a23a0407d490a799cc1a3c8835b8611128d76c
  "UITableView+FDTemplateLayoutCell": 5c949b4a5059c404b442926c0e80f81d10a2d66f
  VisualEffectView: 333acded3f79d088ae44aff57d5f3a62554e37b6
  youtube-ios-player-helper: e9b97535e816db3152179d84d999bc1807ecd689

PODFILE CHECKSUM: 132d2df21ca253b5d85d740c3c2b1a204b87494b

COCOAPODS: 1.13.0
