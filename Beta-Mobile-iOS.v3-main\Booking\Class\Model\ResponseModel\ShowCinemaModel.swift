//
//  ShowCinemaModel.swift
//  Booking-dev
//
//  Created by <PERSON><PERSON> on 5/23/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation
import ObjectMapper

class ShowCinemaModel: Mappable {
    var cinemaId : String?
    var cinemaName : String?
    var cinemaName_F : String?
    var address : String?
    var address_F : String?
    var latitude : String?
    var longtitude : String?
    var listFilm : [ListFilm]?

    var height: CGFloat {
        return (listFilm ?? []).map{ $0.height + 44.0 }.reduce(0, +)
    }

    required init?(map: Map) {

    }

    func mapping(map: Map) {

        cinemaId <- map["CinemaId"]
        cinemaName <- map["CinemaName"]
        cinemaName_F <- map["CinemaName_F"]
        address <- map["Address"]
        address_F <- map["Address_F"]
        latitude <- map["Latitude"]
        longtitude <- map["Longtitude"]
        listFilm <- map["ListFilm"]
    }

    func getName() -> String?{
        return Utils.shared.isEng() ? cinemaName_F : cinemaName
    }
    func getAddress() -> String?{
        return Utils.shared.isEng() ? address_F : address
    }

    func getDistanceDouble() -> Double{
        return Utils.shared.distanceFrom(latitude: latitude, longitude: longtitude)
    }

    func getDistance() -> String{
        return NSString(format: "%.1f", getDistanceDouble()) as String
    }
}

class ListFilm : Mappable {
    var filmId : String?
    var filmFormatCode : String?
    var filmFormatName : String?
    var filmFormatName_F : String?
    var sumOfShow : Int?
    var listShow : [ShowModel]?

    var dayList: [ShowModel] {
        return listShow ?? []
    }

    var height: CGFloat {
        return (dayList.count > 0 ? 1 : 0) * 72.0
    }

    required init?(map: Map) {

    }

    func mapping(map: Map) {

        filmId <- map["FilmId"]
        filmFormatCode <- map["FilmFormatCode"]
        filmFormatName <- map["FilmFormatName"]
        filmFormatName_F <- map["FilmFormatName_F"]
        sumOfShow <- map["SumOfShow"]
        listShow <- map["ListShow"]
    }

    var filmFormat: String? {
        return Utils.shared.isEng() ? filmFormatName_F : filmFormatName
    }

}
