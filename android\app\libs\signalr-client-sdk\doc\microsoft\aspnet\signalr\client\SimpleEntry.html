<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<title>SimpleEntry</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="SimpleEntry";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../microsoft/aspnet/signalr/client/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/SimpleEntry.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../microsoft/aspnet/signalr/client/SignalRFuture.html" title="class in microsoft.aspnet.signalr.client"><span class="strong">Prev Class</span></a></li>
<li><a href="../../../../microsoft/aspnet/signalr/client/StateChangedCallback.html" title="interface in microsoft.aspnet.signalr.client"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?microsoft/aspnet/signalr/client/SimpleEntry.html" target="_top">Frames</a></li>
<li><a href="SimpleEntry.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">microsoft.aspnet.signalr.client</div>
<h2 title="Class SimpleEntry" class="title">Class SimpleEntry&lt;K,V&gt;</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>microsoft.aspnet.signalr.client.SimpleEntry&lt;K,V&gt;</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl><dt><span class="strong">Type Parameters:</span></dt><dd><code>K</code> - Key</dd><dd><code>V</code> - Value</dd></dl>
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>java.util.Map.Entry&lt;K,V&gt;</dd>
</dl>
<hr>
<br>
<pre>public class <span class="strong">SimpleEntry&lt;K,V&gt;</span>
extends java.lang.Object
implements java.util.Map.Entry&lt;K,V&gt;</pre>
<div class="block">Simple Entry<K,V> implementation</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><strong><a href="../../../../microsoft/aspnet/signalr/client/SimpleEntry.html#SimpleEntry(K, V)">SimpleEntry</a></strong>(<a href="../../../../microsoft/aspnet/signalr/client/SimpleEntry.html" title="type parameter in SimpleEntry">K</a>&nbsp;key,
           <a href="../../../../microsoft/aspnet/signalr/client/SimpleEntry.html" title="type parameter in SimpleEntry">V</a>&nbsp;value)</code>
<div class="block">Initializes the SimpleEntry</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method_summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span>Methods</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../microsoft/aspnet/signalr/client/SimpleEntry.html" title="type parameter in SimpleEntry">K</a></code></td>
<td class="colLast"><code><strong><a href="../../../../microsoft/aspnet/signalr/client/SimpleEntry.html#getKey()">getKey</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../microsoft/aspnet/signalr/client/SimpleEntry.html" title="type parameter in SimpleEntry">V</a></code></td>
<td class="colLast"><code><strong><a href="../../../../microsoft/aspnet/signalr/client/SimpleEntry.html#getValue()">getValue</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../microsoft/aspnet/signalr/client/SimpleEntry.html" title="type parameter in SimpleEntry">V</a></code></td>
<td class="colLast"><code><strong><a href="../../../../microsoft/aspnet/signalr/client/SimpleEntry.html#setValue(V)">setValue</a></strong>(<a href="../../../../microsoft/aspnet/signalr/client/SimpleEntry.html" title="type parameter in SimpleEntry">V</a>&nbsp;value)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_java.util.Map.Entry">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;java.util.Map.Entry</h3>
<code>equals, hashCode</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="SimpleEntry(java.lang.Object,java.lang.Object)">
<!--   -->
</a><a name="SimpleEntry(K, V)">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>SimpleEntry</h4>
<pre>public&nbsp;SimpleEntry(<a href="../../../../microsoft/aspnet/signalr/client/SimpleEntry.html" title="type parameter in SimpleEntry">K</a>&nbsp;key,
           <a href="../../../../microsoft/aspnet/signalr/client/SimpleEntry.html" title="type parameter in SimpleEntry">V</a>&nbsp;value)</pre>
<div class="block">Initializes the SimpleEntry</div>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>key</code> - Entry key</dd><dd><code>value</code> - Entry value</dd></dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method_detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getKey()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getKey</h4>
<pre>public&nbsp;<a href="../../../../microsoft/aspnet/signalr/client/SimpleEntry.html" title="type parameter in SimpleEntry">K</a>&nbsp;getKey()</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>getKey</code>&nbsp;in interface&nbsp;<code>java.util.Map.Entry&lt;<a href="../../../../microsoft/aspnet/signalr/client/SimpleEntry.html" title="type parameter in SimpleEntry">K</a>,<a href="../../../../microsoft/aspnet/signalr/client/SimpleEntry.html" title="type parameter in SimpleEntry">V</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="getValue()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getValue</h4>
<pre>public&nbsp;<a href="../../../../microsoft/aspnet/signalr/client/SimpleEntry.html" title="type parameter in SimpleEntry">V</a>&nbsp;getValue()</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>getValue</code>&nbsp;in interface&nbsp;<code>java.util.Map.Entry&lt;<a href="../../../../microsoft/aspnet/signalr/client/SimpleEntry.html" title="type parameter in SimpleEntry">K</a>,<a href="../../../../microsoft/aspnet/signalr/client/SimpleEntry.html" title="type parameter in SimpleEntry">V</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="setValue(java.lang.Object)">
<!--   -->
</a><a name="setValue(V)">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setValue</h4>
<pre>public&nbsp;<a href="../../../../microsoft/aspnet/signalr/client/SimpleEntry.html" title="type parameter in SimpleEntry">V</a>&nbsp;setValue(<a href="../../../../microsoft/aspnet/signalr/client/SimpleEntry.html" title="type parameter in SimpleEntry">V</a>&nbsp;value)</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>setValue</code>&nbsp;in interface&nbsp;<code>java.util.Map.Entry&lt;<a href="../../../../microsoft/aspnet/signalr/client/SimpleEntry.html" title="type parameter in SimpleEntry">K</a>,<a href="../../../../microsoft/aspnet/signalr/client/SimpleEntry.html" title="type parameter in SimpleEntry">V</a>&gt;</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../microsoft/aspnet/signalr/client/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/SimpleEntry.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../microsoft/aspnet/signalr/client/SignalRFuture.html" title="class in microsoft.aspnet.signalr.client"><span class="strong">Prev Class</span></a></li>
<li><a href="../../../../microsoft/aspnet/signalr/client/StateChangedCallback.html" title="interface in microsoft.aspnet.signalr.client"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?microsoft/aspnet/signalr/client/SimpleEntry.html" target="_top">Frames</a></li>
<li><a href="SimpleEntry.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
