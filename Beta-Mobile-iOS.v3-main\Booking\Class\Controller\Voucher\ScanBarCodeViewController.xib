<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14490.70" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14490.49"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="Oswald-Bold.ttf">
            <string>Oswald-Bold</string>
        </array>
        <array key="SourceSansPro-Regular.ttf">
            <string>SourceSansPro-Regular</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="ScanBarCodeViewController" customModule="Booking_dev" customModuleProvider="target">
            <connections>
                <outlet property="cameraView" destination="K6y-yY-RZP" id="qnn-uZ-WpG"/>
                <outlet property="label" destination="6sZ-3A-47f" id="8z7-eZ-1Fz"/>
                <outlet property="noteLabel" destination="KNL-j3-Fwe" id="HkV-JS-smT"/>
                <outlet property="overlayView" destination="LeG-Zi-PcG" id="y36-EV-E4K"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ebW-jN-dqQ">
                    <rect key="frame" x="0.0" y="44" width="414" height="64"/>
                    <subviews>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Quét Barcode" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="6sZ-3A-47f">
                            <rect key="frame" x="16" y="14" width="111" height="30"/>
                            <fontDescription key="fontDescription" name="Oswald-Bold" family="Oswald" pointSize="20"/>
                            <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="lv8-Um-Cev">
                            <rect key="frame" x="358" y="9" width="40" height="40"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="40" id="Ex2-0N-hRD"/>
                                <constraint firstAttribute="width" constant="40" id="mqX-uu-G15"/>
                            </constraints>
                            <state key="normal" image="ic_close_white"/>
                            <connections>
                                <action selector="dismissTapped:" destination="-1" eventType="touchUpInside" id="4yn-an-a1a"/>
                            </connections>
                        </button>
                    </subviews>
                    <color key="backgroundColor" red="0.11764705882352941" green="0.12156862745098039" blue="0.15686274509803921" alpha="1" colorSpace="calibratedRGB"/>
                    <constraints>
                        <constraint firstItem="lv8-Um-Cev" firstAttribute="centerY" secondItem="6sZ-3A-47f" secondAttribute="centerY" id="Mw5-dK-3r0"/>
                        <constraint firstAttribute="height" constant="64" id="T5O-54-pls"/>
                        <constraint firstItem="6sZ-3A-47f" firstAttribute="leading" secondItem="ebW-jN-dqQ" secondAttribute="leading" constant="16" id="fon-Xe-17l"/>
                        <constraint firstAttribute="bottom" secondItem="6sZ-3A-47f" secondAttribute="bottom" constant="20" id="nU2-68-dDr"/>
                        <constraint firstAttribute="trailing" secondItem="lv8-Um-Cev" secondAttribute="trailing" constant="16" id="xMF-oo-8Oy"/>
                    </constraints>
                </view>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="K6y-yY-RZP">
                    <rect key="frame" x="0.0" y="108" width="414" height="754"/>
                    <subviews>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="LeG-Zi-PcG">
                            <rect key="frame" x="0.0" y="0.0" width="414" height="754"/>
                            <subviews>
                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="E85-U8-O1g">
                                    <rect key="frame" x="16" y="186" width="382" height="382"/>
                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <constraints>
                                        <constraint firstAttribute="width" secondItem="E85-U8-O1g" secondAttribute="height" id="zbg-nV-uvu"/>
                                    </constraints>
                                </view>
                                <view alpha="0.20000000000000001" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="9kR-L3-Nlw">
                                    <rect key="frame" x="398" y="0.0" width="16" height="754"/>
                                    <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <constraints>
                                        <constraint firstAttribute="width" constant="16" id="8VX-GR-Bc0"/>
                                    </constraints>
                                </view>
                                <view alpha="0.20000000298023224" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="QCC-zN-0sp">
                                    <rect key="frame" x="0.0" y="0.0" width="16" height="754"/>
                                    <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <constraints>
                                        <constraint firstAttribute="width" constant="16" id="5IG-yf-nCj"/>
                                    </constraints>
                                </view>
                                <view alpha="0.20000000298023224" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="OoB-y2-rog">
                                    <rect key="frame" x="16" y="568" width="382" height="186"/>
                                    <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                </view>
                                <view alpha="0.20000000298023224" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="d1f-0U-3qf">
                                    <rect key="frame" x="16" y="0.0" width="382" height="186"/>
                                    <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                </view>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Di chuyển camera đến vùng chứa mã Barcode để quét" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="KNL-j3-Fwe">
                                    <rect key="frame" x="16" y="651" width="382" height="20.5"/>
                                    <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                    <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <nil key="highlightedColor"/>
                                </label>
                            </subviews>
                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <constraints>
                                <constraint firstItem="9kR-L3-Nlw" firstAttribute="leading" secondItem="d1f-0U-3qf" secondAttribute="trailing" id="14C-6D-aBW"/>
                                <constraint firstItem="E85-U8-O1g" firstAttribute="centerY" secondItem="LeG-Zi-PcG" secondAttribute="centerY" id="A39-tr-ffU"/>
                                <constraint firstItem="9kR-L3-Nlw" firstAttribute="top" secondItem="LeG-Zi-PcG" secondAttribute="top" id="FZU-KQ-WDF"/>
                                <constraint firstItem="9kR-L3-Nlw" firstAttribute="leading" secondItem="E85-U8-O1g" secondAttribute="trailing" id="HGA-hB-qIc"/>
                                <constraint firstAttribute="bottom" secondItem="9kR-L3-Nlw" secondAttribute="bottom" id="K9J-Fg-Gg7"/>
                                <constraint firstItem="9kR-L3-Nlw" firstAttribute="leading" secondItem="OoB-y2-rog" secondAttribute="trailing" id="Kl7-Jc-Dme"/>
                                <constraint firstAttribute="bottom" secondItem="QCC-zN-0sp" secondAttribute="bottom" id="OBo-a8-sBh"/>
                                <constraint firstItem="E85-U8-O1g" firstAttribute="leading" secondItem="QCC-zN-0sp" secondAttribute="trailing" id="OxK-dc-CoQ"/>
                                <constraint firstItem="OoB-y2-rog" firstAttribute="top" secondItem="E85-U8-O1g" secondAttribute="bottom" id="RQZ-1Z-m8M"/>
                                <constraint firstItem="d1f-0U-3qf" firstAttribute="leading" secondItem="QCC-zN-0sp" secondAttribute="trailing" id="Syh-cB-K9l"/>
                                <constraint firstAttribute="bottom" secondItem="OoB-y2-rog" secondAttribute="bottom" id="dc2-j6-IdD"/>
                                <constraint firstItem="KNL-j3-Fwe" firstAttribute="centerY" secondItem="OoB-y2-rog" secondAttribute="centerY" id="djW-0M-BS8"/>
                                <constraint firstItem="E85-U8-O1g" firstAttribute="top" secondItem="d1f-0U-3qf" secondAttribute="bottom" id="eUH-H7-2bk"/>
                                <constraint firstItem="KNL-j3-Fwe" firstAttribute="leading" secondItem="OoB-y2-rog" secondAttribute="leading" id="fMx-FM-F5J"/>
                                <constraint firstItem="QCC-zN-0sp" firstAttribute="top" secondItem="LeG-Zi-PcG" secondAttribute="top" id="lUM-ed-x7C"/>
                                <constraint firstItem="QCC-zN-0sp" firstAttribute="leading" secondItem="LeG-Zi-PcG" secondAttribute="leading" id="lcp-qS-sct"/>
                                <constraint firstItem="OoB-y2-rog" firstAttribute="leading" secondItem="QCC-zN-0sp" secondAttribute="trailing" id="pmh-hn-ihu"/>
                                <constraint firstItem="d1f-0U-3qf" firstAttribute="top" secondItem="LeG-Zi-PcG" secondAttribute="top" id="qWu-yO-FxI"/>
                                <constraint firstItem="KNL-j3-Fwe" firstAttribute="trailing" secondItem="OoB-y2-rog" secondAttribute="trailing" id="uBr-pY-hbD"/>
                                <constraint firstAttribute="trailing" secondItem="9kR-L3-Nlw" secondAttribute="trailing" id="zac-36-7Mw"/>
                            </constraints>
                        </view>
                    </subviews>
                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstAttribute="trailing" secondItem="LeG-Zi-PcG" secondAttribute="trailing" id="7D4-l7-YHd"/>
                        <constraint firstItem="LeG-Zi-PcG" firstAttribute="leading" secondItem="K6y-yY-RZP" secondAttribute="leading" id="NwP-TB-hPs"/>
                        <constraint firstAttribute="bottom" secondItem="LeG-Zi-PcG" secondAttribute="bottom" id="QzP-8q-lYh"/>
                        <constraint firstItem="LeG-Zi-PcG" firstAttribute="top" secondItem="K6y-yY-RZP" secondAttribute="top" id="faS-mx-4e5"/>
                    </constraints>
                </view>
            </subviews>
            <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
            <constraints>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="ebW-jN-dqQ" secondAttribute="trailing" id="2Pe-VR-qpN"/>
                <constraint firstItem="ebW-jN-dqQ" firstAttribute="top" secondItem="fnl-2z-Ty3" secondAttribute="top" id="9D5-tg-pGJ"/>
                <constraint firstItem="ebW-jN-dqQ" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" id="E50-Cu-0HE"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="bottom" secondItem="K6y-yY-RZP" secondAttribute="bottom" id="GLl-KD-cQc"/>
                <constraint firstItem="K6y-yY-RZP" firstAttribute="top" secondItem="ebW-jN-dqQ" secondAttribute="bottom" id="UJo-pB-n7M"/>
                <constraint firstItem="K6y-yY-RZP" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" id="cDu-Gc-P6X"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="K6y-yY-RZP" secondAttribute="trailing" id="hVF-JD-inJ"/>
            </constraints>
            <viewLayoutGuide key="safeArea" id="fnl-2z-Ty3"/>
            <point key="canvasLocation" x="2.8985507246376816" y="97.767857142857139"/>
        </view>
    </objects>
    <resources>
        <image name="ic_close_white" width="24" height="24"/>
    </resources>
</document>
