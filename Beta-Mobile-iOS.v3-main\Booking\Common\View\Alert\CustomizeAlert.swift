//
//  CustomizeAlert.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 8/13/19.
//  Copyright © 2019 ddkc. All rights reserved.
//

import UIKit

enum MyAlertType {
    case successNormal(String, String)
    case successAttribute(String, String)
    case standard(String, [String])
    case standardTwoButton(String, [String])
}

class CustomizeAlert: UIViewController {
    @IBOutlet weak var successImageView: UIImageView!
    @IBOutlet weak var titleLabel: UILabel!
    @IBOutlet weak var contentLabel: UILabel!
    @IBOutlet weak var alertView: RoundView!
    @IBOutlet weak var alertHeight: NSLayoutConstraint!
    
    @IBOutlet weak var okButton: UIButton!

    let alertViewGrayColor = UIColor(red: 224.0/255.0, green: 224.0/255.0, blue: 224.0/255.0, alpha: 1)
    private var type: MyAlertType!

    var okHandler: () -> Void = {}

    init(type: MyAlertType) {
        self.type = type
        super.init(nibName: "CustomizeAlert", bundle: nil)
    }

    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        configureViews()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        setupView()
        animateView()

        okButton.addBorder(side: .Top, color: alertViewGrayColor, width: 1)
    }

    private func configureViews() {
        okButton.setTitle("Bt.Yes".localized, for: .normal)
        switch type {
        case .successNormal(let title, let content)?:
            titleLabel.text = title.localized.uppercased()
            contentLabel.text = content.localized
        case .successAttribute(let title, let content)?:
            titleLabel.text = title.localized.uppercased()
            let strings = content.components(separatedBy: "#")
            let attributedString = NSMutableAttributedString()

            attributedString.string(strings[0], font: UIFont(fontName: .SourceSansPro, style: .Regular, size: 16))
            attributedString.string(strings[1], font: UIFont(fontName: .SourceSansPro, style: .Bold, size: 16))
            attributedString.string(strings[2], font: UIFont(fontName: .SourceSansPro, style: .Regular, size: 16))
            contentLabel.attributedText = attributedString
        default:
            break
        }
    }

    func setupView() {
        alertView.layer.cornerRadius = 15
        self.view.backgroundColor = UIColor.black.withAlphaComponent(0.4)
    }

    func animateView() {
        alertView.alpha = 0;
        self.alertView.frame.origin.y = self.alertView.frame.origin.y + 50
        UIView.animate(withDuration: 0.2, animations: { () -> Void in
            self.alertView.alpha = 1.0;
            self.alertView.frame.origin.y = self.alertView.frame.origin.y - 50
        })
    }

    @IBAction func didTapOK(_ sender: Any) {
        self.dismiss(animated: true, completion: {
            self.okHandler()
        })
    }
    

}
