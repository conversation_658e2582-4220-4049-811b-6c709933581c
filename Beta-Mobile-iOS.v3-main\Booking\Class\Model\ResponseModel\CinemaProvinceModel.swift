//
//  CinemaProvinceModel.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 4/4/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation
import ObjectMapper

class CinemaProvinceModel : Mappable {
    var ListCinema: [CinemaModel]?
    var CityId: String?
    var CityName: String?

    var isOpen: Bool = false

    var height: CGFloat {
        if !isOpen {
            return 71.0
        }
        return ceil(CGFloat(ListCinema?.count ?? 0) / 2) * 180 + 71
    }

    required init?(map: Map) {

    }

    func mapping(map: Map) {
        ListCinema           <- map["ListCinema"]
        CityId               <- map["CityId"]
        CityName             <- map["CityName"]
    }
}
