//
//  TransparentAnimator.swift
//  Booking-dev
//
//  Created by <PERSON><PERSON> on 4/13/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit

extension UIViewController {
    var isBarTransparent: Bool {
        return self.barTransparent()
    }

    @objc func barTransparent() -> <PERSON><PERSON> {
        return false
    }
}

class TransparentPushAnimator: NSObject, UIViewControllerAnimatedTransitioning {
    func transitionDuration(using transitionContext: UIViewControllerContextTransitioning?) -> TimeInterval {
        return 0.5
    }

    func animateTransition(using transitionContext: UIViewControllerContextTransitioning) {
        let toViewController = transitionContext.viewController(forKey: .to)
        let fromViewController = transitionContext.viewController(forKey: .from)

        var frame = transitionContext.containerView.bounds
        frame.origin.x = frame.size.width
        toViewController?.view.frame = frame
        transitionContext.containerView.addSubview(toViewController!.view)

//        toViewController?.navigationController?.navigationBar.alpha = 0
        if toViewController?.isBarTransparent == true {
            toViewController?.navigationController?.navigationBar.applyGradientBackground([.init(white: 0, alpha: 0.6), .clear], vertical: true)
        } else {
            toViewController?.navigationController?.navigationBar.applyGradientBackground()
        }

        UIView.animate(withDuration: self.transitionDuration(using: transitionContext), animations: {
//            toViewController?.navigationController?.navigationBar.alpha = 1.0
            frame.origin.x = 0
            toViewController?.view.frame = frame
        }) { (finished) in
            transitionContext.completeTransition(!transitionContext.transitionWasCancelled)
        }
    }
}

class TransparentPopAnimator: NSObject, UIViewControllerAnimatedTransitioning {
    func transitionDuration(using transitionContext: UIViewControllerContextTransitioning?) -> TimeInterval {
        return 0.5
    }

    func animateTransition(using transitionContext: UIViewControllerContextTransitioning) {
        let toViewController = transitionContext.viewController(forKey: .to)
        let fromViewController = transitionContext.viewController(forKey: .from)

        transitionContext.containerView.addSubview(toViewController!.view)
        var frame = fromViewController?.view.frame ?? .zero
//        toViewController?.navigationController?.navigationBar.alpha = 0
        if toViewController?.isBarTransparent == true {
            toViewController?.navigationController?.navigationBar.applyGradientBackground([.init(white: 0, alpha: 0.6), .clear], vertical: true)
        } else {
            toViewController?.navigationController?.navigationBar.applyGradientBackground()
        }
        fromViewController?.navigationController?.navigationBar.setNeedsLayout()

        UIView.animate(withDuration: self.transitionDuration(using: transitionContext), animations: {
//            toViewController?.navigationController?.navigationBar.alpha = 1.0
            frame.origin.x = frame.size.width
            fromViewController?.view.frame = frame
//            if toViewController?.isBarTransparent == true {
//                toViewController?.navigationController?.navigationBar.applyGradientBackground([.init(white: 0, alpha: 0.6), .clear], vertical: true)
//            } else {
//                toViewController?.navigationController?.navigationBar.applyGradientBackground()
//            }
        }) { (finished) in
            transitionContext.completeTransition(!transitionContext.transitionWasCancelled)
        }
    }
}
