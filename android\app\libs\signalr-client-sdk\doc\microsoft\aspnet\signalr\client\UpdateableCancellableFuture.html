<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<title>UpdateableCancellableFuture</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="UpdateableCancellableFuture";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../microsoft/aspnet/signalr/client/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/UpdateableCancellableFuture.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../microsoft/aspnet/signalr/client/StateChangedCallback.html" title="interface in microsoft.aspnet.signalr.client"><span class="strong">Prev Class</span></a></li>
<li><a href="../../../../microsoft/aspnet/signalr/client/Version.html" title="class in microsoft.aspnet.signalr.client"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?microsoft/aspnet/signalr/client/UpdateableCancellableFuture.html" target="_top">Frames</a></li>
<li><a href="UpdateableCancellableFuture.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">microsoft.aspnet.signalr.client</div>
<h2 title="Class UpdateableCancellableFuture" class="title">Class UpdateableCancellableFuture&lt;V&gt;</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../../microsoft/aspnet/signalr/client/SignalRFuture.html" title="class in microsoft.aspnet.signalr.client">microsoft.aspnet.signalr.client.SignalRFuture</a>&lt;V&gt;</li>
<li>
<ul class="inheritance">
<li>microsoft.aspnet.signalr.client.UpdateableCancellableFuture&lt;V&gt;</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>java.util.concurrent.Future&lt;V&gt;</dd>
</dl>
<hr>
<br>
<pre>public class <span class="strong">UpdateableCancellableFuture&lt;V&gt;</span>
extends <a href="../../../../microsoft/aspnet/signalr/client/SignalRFuture.html" title="class in microsoft.aspnet.signalr.client">SignalRFuture</a>&lt;V&gt;</pre>
<div class="block">An updateable SignalRFuture that, when cancelled, triggers cancellation on an
 internal instance</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><strong><a href="../../../../microsoft/aspnet/signalr/client/UpdateableCancellableFuture.html#UpdateableCancellableFuture(microsoft.aspnet.signalr.client.SignalRFuture)">UpdateableCancellableFuture</a></strong>(<a href="../../../../microsoft/aspnet/signalr/client/SignalRFuture.html" title="class in microsoft.aspnet.signalr.client">SignalRFuture</a>&lt;?&gt;&nbsp;token)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method_summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span>Methods</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../microsoft/aspnet/signalr/client/UpdateableCancellableFuture.html#cancel()">cancel</a></strong>()</code>
<div class="block">Cancels the operation</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../microsoft/aspnet/signalr/client/UpdateableCancellableFuture.html#setFuture(microsoft.aspnet.signalr.client.SignalRFuture)">setFuture</a></strong>(<a href="../../../../microsoft/aspnet/signalr/client/SignalRFuture.html" title="class in microsoft.aspnet.signalr.client">SignalRFuture</a>&lt;?&gt;&nbsp;token)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_microsoft.aspnet.signalr.client.SignalRFuture">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;microsoft.aspnet.signalr.client.<a href="../../../../microsoft/aspnet/signalr/client/SignalRFuture.html" title="class in microsoft.aspnet.signalr.client">SignalRFuture</a></h3>
<code><a href="../../../../microsoft/aspnet/signalr/client/SignalRFuture.html#cancel(boolean)">cancel</a>, <a href="../../../../microsoft/aspnet/signalr/client/SignalRFuture.html#done(microsoft.aspnet.signalr.client.Action)">done</a>, <a href="../../../../microsoft/aspnet/signalr/client/SignalRFuture.html#errorWasTriggered()">errorWasTriggered</a>, <a href="../../../../microsoft/aspnet/signalr/client/SignalRFuture.html#get()">get</a>, <a href="../../../../microsoft/aspnet/signalr/client/SignalRFuture.html#get(long, java.util.concurrent.TimeUnit)">get</a>, <a href="../../../../microsoft/aspnet/signalr/client/SignalRFuture.html#isCancelled()">isCancelled</a>, <a href="../../../../microsoft/aspnet/signalr/client/SignalRFuture.html#isDone()">isDone</a>, <a href="../../../../microsoft/aspnet/signalr/client/SignalRFuture.html#onCancelled(java.lang.Runnable)">onCancelled</a>, <a href="../../../../microsoft/aspnet/signalr/client/SignalRFuture.html#onError(microsoft.aspnet.signalr.client.ErrorCallback)">onError</a>, <a href="../../../../microsoft/aspnet/signalr/client/SignalRFuture.html#setResult(V)">setResult</a>, <a href="../../../../microsoft/aspnet/signalr/client/SignalRFuture.html#triggerError(java.lang.Throwable)">triggerError</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="UpdateableCancellableFuture(microsoft.aspnet.signalr.client.SignalRFuture)">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>UpdateableCancellableFuture</h4>
<pre>public&nbsp;UpdateableCancellableFuture(<a href="../../../../microsoft/aspnet/signalr/client/SignalRFuture.html" title="class in microsoft.aspnet.signalr.client">SignalRFuture</a>&lt;?&gt;&nbsp;token)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method_detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="cancel()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cancel</h4>
<pre>public&nbsp;void&nbsp;cancel()</pre>
<div class="block"><strong>Description copied from class:&nbsp;<code><a href="../../../../microsoft/aspnet/signalr/client/SignalRFuture.html#cancel()">SignalRFuture</a></code></strong></div>
<div class="block">Cancels the operation</div>
<dl>
<dt><strong>Overrides:</strong></dt>
<dd><code><a href="../../../../microsoft/aspnet/signalr/client/SignalRFuture.html#cancel()">cancel</a></code>&nbsp;in class&nbsp;<code><a href="../../../../microsoft/aspnet/signalr/client/SignalRFuture.html" title="class in microsoft.aspnet.signalr.client">SignalRFuture</a>&lt;<a href="../../../../microsoft/aspnet/signalr/client/UpdateableCancellableFuture.html" title="type parameter in UpdateableCancellableFuture">V</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="setFuture(microsoft.aspnet.signalr.client.SignalRFuture)">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setFuture</h4>
<pre>public&nbsp;void&nbsp;setFuture(<a href="../../../../microsoft/aspnet/signalr/client/SignalRFuture.html" title="class in microsoft.aspnet.signalr.client">SignalRFuture</a>&lt;?&gt;&nbsp;token)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../microsoft/aspnet/signalr/client/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/UpdateableCancellableFuture.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../microsoft/aspnet/signalr/client/StateChangedCallback.html" title="interface in microsoft.aspnet.signalr.client"><span class="strong">Prev Class</span></a></li>
<li><a href="../../../../microsoft/aspnet/signalr/client/Version.html" title="class in microsoft.aspnet.signalr.client"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?microsoft/aspnet/signalr/client/UpdateableCancellableFuture.html" target="_top">Frames</a></li>
<li><a href="UpdateableCancellableFuture.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
