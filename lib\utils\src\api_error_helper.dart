import 'package:easy_localization/easy_localization.dart';
import 'error_code_mapper.dart';

/// Helper class for feature-specific API error handling
/// Provides convenient methods for different app features
class ApiErrorHelper {
  
  /// Handle authentication-related errors (login, register, etc.)
  static String handleAuthError(String? errorMessage) {
    return ApiErrorHandler.handleErrorForFeature(
      errorMessage, 
      'auth',
      fallbackMessage: 'Auth.AuthenticationFailed'.tr(),
    );
  }
  
  /// Handle profile/account update errors
  static String handleProfileError(String? errorMessage) {
    return ApiErrorHandler.handleErrorForFeature(
      errorMessage,
      'profile', 
      fallbackMessage: 'Profile.UpdateFailed'.tr(),
    );
  }
  
  /// Handle payment-related errors
  static String handlePaymentError(String? errorMessage) {
    return ApiErrorHandler.handleErrorForFeature(
      errorMessage,
      'payment',
      fallbackMessage: 'Payment.PaymentFailed'.tr(),
    );
  }
  
  /// Handle booking-related errors
  static String handleBookingError(String? errorMessage) {
    return ApiErrorHandler.handleErrorForFeature(
      errorMessage,
      'booking',
      fallbackMessage: 'Booking.BookingFailed'.tr(),
    );
  }
  
  /// Handle file upload errors
  static String handleUploadError(String? errorMessage) {
    return ApiErrorHandler.handleErrorForFeature(
      errorMessage,
      'upload',
      fallbackMessage: 'Upload.UploadFailed'.tr(),
    );
  }
  
  /// Handle network/connectivity errors
  static String handleNetworkError(String? errorMessage) {
    return ApiErrorHandler.handleError(
      errorMessage,
      fallbackMessage: 'Network.ConnectionFailed'.tr(),
      showOriginalIfNotMapped: false,
    );
  }
  
  /// Show success message with proper mapping
  static String handleSuccessMessage(String? successMessage, {String? fallback}) {
    if (successMessage == null || successMessage.isEmpty) {
      return fallback ?? 'Common.Success'.tr();
    }
    
    final mapped = successMessage.mapErrorCode();
    return mapped != successMessage ? mapped : (fallback ?? successMessage);
  }
  
  /// Check if error should be shown to user or logged silently
  static bool shouldShowToUser(String? errorMessage) {
    if (errorMessage == null || errorMessage.isEmpty) return false;
    
    final upperMessage = errorMessage.toUpperCase();
    
    // Don't show technical errors to users
    final technicalErrors = [
      'TIMEOUT',
      'CONNECTION_FAILED', 
      'SERVER_ERROR',
      'INTERNAL_ERROR',
      'DATABASE_ERROR',
      'NETWORK_ERROR'
    ];
    
    return !technicalErrors.any((error) => upperMessage.contains(error));
  }
  
  /// Get user-friendly error message or generic message for technical errors
  static String getUserFriendlyError(String? errorMessage) {
    if (!shouldShowToUser(errorMessage)) {
      return 'Common.TechnicalError'.tr();
    }
    
    return ApiErrorHandler.handleError(errorMessage);
  }
}

/// Extension for easy error handling in API calls
extension ApiResponseErrorHandling on String? {
  /// Map error for authentication features
  String mapAuthError() => ApiErrorHelper.handleAuthError(this);
  
  /// Map error for profile features  
  String mapProfileError() => ApiErrorHelper.handleProfileError(this);
  
  /// Map error for payment features
  String mapPaymentError() => ApiErrorHelper.handlePaymentError(this);
  
  /// Map error for booking features
  String mapBookingError() => ApiErrorHelper.handleBookingError(this);
  
  /// Map error for upload features
  String mapUploadError() => ApiErrorHelper.handleUploadError(this);
  
  /// Get user-friendly error message
  String mapUserFriendlyError() => ApiErrorHelper.getUserFriendlyError(this);
  
  /// Map as success message
  String mapSuccessMessage({String? fallback}) => 
    ApiErrorHelper.handleSuccessMessage(this, fallback: fallback);
}
