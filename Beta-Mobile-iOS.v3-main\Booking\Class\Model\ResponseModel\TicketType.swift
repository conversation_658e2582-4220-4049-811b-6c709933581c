//
//  TicketType.swift
//  Booking-dev
//
//  Created by <PERSON><PERSON> on 5/27/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation
import ObjectMapper

class TicketType: Mappable {
    var PriceCardId: String?
    var TicketTypeId: String?
    var Price: Int?
    var TicketClassId: String?
    var IsPackage: Bool?
    var SeatTypeId: String?
    var ObjectId: String?
    var FilmFormatId: String?
    var VAT: Int?
    var Name: String?
    var Code: String?
    var Status: Bool?
    var PriceTicket: Int?
    var PriceBeforeVat: Int?
    var DiscountId: String?

    var isVip: Bool {
        return SeatTypeId == SeatType.Catalog.vip.rawValue
    }

    var isCouple: Bool {
        return SeatTypeId == SeatType.Catalog.couple.rawValue
    }

    var isNormal: Bool {
        return SeatTypeId == SeatType.Catalog.normal.rawValue
    }

    required init?(map: Map) {

    }

    func mapping(map: Map) {
        PriceCardId <- map["PriceCardId"]
        TicketTypeId <- map["TicketTypeId"]
        Price <- map["Price"]
        TicketClassId <- map["TicketClassId"]
        IsPackage <- map["IsPackage"]
        SeatTypeId <- map["SeatTypeId"]
        ObjectId <- map["ObjectId"]
        FilmFormatId <- map["FilmFormatId"]
        VAT <- map["VAT"]
        Name <- map["Name"]
        Code <- map["Code"]
        Status <- map["Status"]
        PriceTicket <- map["PriceTicket"]
        PriceBeforeVat <- map["PriceBeforeVat"]
        DiscountId <- map["DiscountId"]
    }
}
