<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<title>LogLevel</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="LogLevel";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../microsoft/aspnet/signalr/client/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/LogLevel.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../microsoft/aspnet/signalr/client/Logger.html" title="interface in microsoft.aspnet.signalr.client"><span class="strong">Prev Class</span></a></li>
<li><a href="../../../../microsoft/aspnet/signalr/client/MessageReceivedHandler.html" title="interface in microsoft.aspnet.signalr.client"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?microsoft/aspnet/signalr/client/LogLevel.html" target="_top">Frames</a></li>
<li><a href="LogLevel.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#enum_constant_summary">Enum Constants</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#enum_constant_detail">Enum Constants</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">microsoft.aspnet.signalr.client</div>
<h2 title="Enum LogLevel" class="title">Enum LogLevel</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>java.lang.Enum&lt;<a href="../../../../microsoft/aspnet/signalr/client/LogLevel.html" title="enum in microsoft.aspnet.signalr.client">LogLevel</a>&gt;</li>
<li>
<ul class="inheritance">
<li>microsoft.aspnet.signalr.client.LogLevel</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>java.io.Serializable, java.lang.Comparable&lt;<a href="../../../../microsoft/aspnet/signalr/client/LogLevel.html" title="enum in microsoft.aspnet.signalr.client">LogLevel</a>&gt;</dd>
</dl>
<hr>
<br>
<pre>public enum <span class="strong">LogLevel</span>
extends java.lang.Enum&lt;<a href="../../../../microsoft/aspnet/signalr/client/LogLevel.html" title="enum in microsoft.aspnet.signalr.client">LogLevel</a>&gt;</pre>
<div class="block">Represents a logging level</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== ENUM CONSTANT SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="enum_constant_summary">
<!--   -->
</a>
<h3>Enum Constant Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Enum Constant Summary table, listing enum constants, and an explanation">
<caption><span>Enum Constants</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Enum Constant and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><strong><a href="../../../../microsoft/aspnet/signalr/client/LogLevel.html#Critical">Critical</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><strong><a href="../../../../microsoft/aspnet/signalr/client/LogLevel.html#Information">Information</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><strong><a href="../../../../microsoft/aspnet/signalr/client/LogLevel.html#Verbose">Verbose</a></strong></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method_summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span>Methods</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../microsoft/aspnet/signalr/client/LogLevel.html" title="enum in microsoft.aspnet.signalr.client">LogLevel</a></code></td>
<td class="colLast"><code><strong><a href="../../../../microsoft/aspnet/signalr/client/LogLevel.html#valueOf(java.lang.String)">valueOf</a></strong>(java.lang.String&nbsp;name)</code>
<div class="block">Returns the enum constant of this type with the specified name.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../microsoft/aspnet/signalr/client/LogLevel.html" title="enum in microsoft.aspnet.signalr.client">LogLevel</a>[]</code></td>
<td class="colLast"><code><strong><a href="../../../../microsoft/aspnet/signalr/client/LogLevel.html#values()">values</a></strong>()</code>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_java.lang.Enum">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Enum</h3>
<code>compareTo, equals, getDeclaringClass, hashCode, name, ordinal, toString, valueOf</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>getClass, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ ENUM CONSTANT DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="enum_constant_detail">
<!--   -->
</a>
<h3>Enum Constant Detail</h3>
<a name="Critical">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Critical</h4>
<pre>public static final&nbsp;<a href="../../../../microsoft/aspnet/signalr/client/LogLevel.html" title="enum in microsoft.aspnet.signalr.client">LogLevel</a> Critical</pre>
</li>
</ul>
<a name="Information">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Information</h4>
<pre>public static final&nbsp;<a href="../../../../microsoft/aspnet/signalr/client/LogLevel.html" title="enum in microsoft.aspnet.signalr.client">LogLevel</a> Information</pre>
</li>
</ul>
<a name="Verbose">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Verbose</h4>
<pre>public static final&nbsp;<a href="../../../../microsoft/aspnet/signalr/client/LogLevel.html" title="enum in microsoft.aspnet.signalr.client">LogLevel</a> Verbose</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method_detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="valueOf(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>valueOf</h4>
<pre>public static&nbsp;<a href="../../../../microsoft/aspnet/signalr/client/LogLevel.html" title="enum in microsoft.aspnet.signalr.client">LogLevel</a>&nbsp;valueOf(java.lang.String&nbsp;name)</pre>
<div class="block">Returns the enum constant of this type with the specified name.
The string must match <i>exactly</i> an identifier used to declare an
enum constant in this type.  (Extraneous whitespace characters are 
not permitted.)</div>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>name</code> - the name of the enum constant to be returned.</dd>
<dt><span class="strong">Returns:</span></dt><dd>the enum constant with the specified name</dd>
<dt><span class="strong">Throws:</span></dt>
<dd><code>java.lang.IllegalArgumentException</code> - if this enum type has no constant
with the specified name</dd>
<dd><code>java.lang.NullPointerException</code> - if the argument is null</dd></dl>
</li>
</ul>
<a name="values()">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>values</h4>
<pre>public static&nbsp;<a href="../../../../microsoft/aspnet/signalr/client/LogLevel.html" title="enum in microsoft.aspnet.signalr.client">LogLevel</a>[]&nbsp;values()</pre>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.  This method may be used to iterate
over the constants as follows:
<pre>
for (LogLevel c : LogLevel.values())
&nbsp;   System.out.println(c);
</pre></div>
<dl><dt><span class="strong">Returns:</span></dt><dd>an array containing the constants of this enum type, in
the order they are declared</dd></dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../microsoft/aspnet/signalr/client/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/LogLevel.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../microsoft/aspnet/signalr/client/Logger.html" title="interface in microsoft.aspnet.signalr.client"><span class="strong">Prev Class</span></a></li>
<li><a href="../../../../microsoft/aspnet/signalr/client/MessageReceivedHandler.html" title="interface in microsoft.aspnet.signalr.client"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?microsoft/aspnet/signalr/client/LogLevel.html" target="_top">Frames</a></li>
<li><a href="LogLevel.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#enum_constant_summary">Enum Constants</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#enum_constant_detail">Enum Constants</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
