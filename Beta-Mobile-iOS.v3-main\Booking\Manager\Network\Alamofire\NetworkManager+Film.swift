//
//  NetworkManager+Film.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 4/10/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation

extension Bool{
    func toString() -> String{
        return self ? "true" : "false"
    }
}
typealias FilmHandler = (_ response: DDKCResult<FilmModel>?, _ error: Error?) -> Void
typealias ShowFilmHandler = (_ response: DDKCResult<ShowFilmModel>?, _ error: Error?) -> Void
extension NetworkManager{
    func getListFilm(isShowing: Bool, completionHandler: @escaping FilmHandler){
        let path = "api/v1/erp/films?isShowing=\(isShowing.toString())"
        self.getRequest(path: path, completionHandler: completionHandler)
    }
    
    func getListFilmSpecial(completionHandler: @escaping ShowFilmHandler){
        let path = "api/v1/erp/shows/films?sneakShow=false)"
        self.getRequest(path: path, completionHandler: completionHandler)
    }
}
