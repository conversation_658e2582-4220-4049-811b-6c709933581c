//
//  FreeVoucherTableViewCell.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 8/10/19.
//  Copyright © 2019 ddkc. All rights reserved.
//

import UIKit

class FreeVoucherTableViewCell: UITableViewCell {

    @IBOutlet weak var previewImageView: UIImageView!
    @IBOutlet weak var alphaView: UIView!
    @IBOutlet weak var lostCodeLabel: UILabel!
    @IBOutlet weak var nameLabel: UILabel!
    @IBOutlet weak var parentView: UIView!

    override func awakeFromNib() {
        super.awakeFromNib()
        parentView.layer.cornerRadius = 5.0
        parentView.clipsToBounds = true
        lostCodeLabel.text = "out_of_voucher".localized.uppercased()
    }

    func configure(_ voucher: FreeVoucher) {
        nameLabel.text = voucher.tieu_de
        if let urlString = voucher.duong_dan_anh_dai_dien, let url = URL(string: urlString) {
            previewImageView.af_setImage(withURL: url)
        }

        lostCodeLabel.isHidden = (voucher.isExistVoucherCode ?? false)
        alphaView.isHidden = lostCodeLabel.isHidden
    }
    
}
