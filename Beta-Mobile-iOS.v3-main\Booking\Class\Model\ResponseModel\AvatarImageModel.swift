//
//  AvatarImageModel.swift
//  Booking-dev
//
//  Created by <PERSON><PERSON> on 5/24/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation
import ObjectMapper

class AvatarImageModel: Mappable {
    var ImageBase64: String?
    var AvatarUrl: String?
    var Extension: String?

    required init?(map: Map) {

    }

    func mapping(map: Map) {
        ImageBase64 <- map["ImageBase64"]
        AvatarUrl <- map["AvatarUrl"]
        Extension <- map["Extension"]
    }
}
