package vn.zenity.betacineplex.Manager.local

import androidx.room.*
import vn.zenity.betacineplex.model.UserModel

@Dao
interface UserDao {
    @Query("SELECT * FROM UserModel LIMIT 1")
    fun getCurrentUser(): UserModel?

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun updateCurrentUser(userModel: UserModel)

    @Query("DELETE FROM UserModel")
    fun removeUser()

    @Delete
    fun removeUser(userModel: UserModel)
}