<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<title>S-Index</title>
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="S-Index";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../microsoft/aspnet/signalr/client/package-summary.html">Package</a></li>
<li>Class</li>
<li>Use</li>
<li><a href="../microsoft/aspnet/signalr/client/package-tree.html">Tree</a></li>
<li class="navBarCell1Rev">Index</li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-15.html">Prev Letter</a></li>
<li><a href="index-17.html">Next Letter</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-filesindex-16.html" target="_top">Frames</a></li>
<li><a href="index-16.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">L</a>&nbsp;<a href="index-11.html">M</a>&nbsp;<a href="index-12.html">N</a>&nbsp;<a href="index-13.html">O</a>&nbsp;<a href="index-14.html">P</a>&nbsp;<a href="index-15.html">R</a>&nbsp;<a href="index-16.html">S</a>&nbsp;<a href="index-17.html">T</a>&nbsp;<a href="index-18.html">U</a>&nbsp;<a href="index-19.html">V</a>&nbsp;<a name="_S_">
<!--   -->
</a>
<h2 class="title">S</h2>
<dl>
<dt><span class="strong"><a href="../microsoft/aspnet/signalr/client/Connection.html#send(java.lang.Object)">send(Object)</a></span> - Method in class microsoft.aspnet.signalr.client.<a href="../microsoft/aspnet/signalr/client/Connection.html" title="class in microsoft.aspnet.signalr.client">Connection</a></dt>
<dd>
<div class="block">Sends a serialized object</div>
</dd>
<dt><span class="strong"><a href="../microsoft/aspnet/signalr/client/Connection.html#send(java.lang.String)">send(String)</a></span> - Method in class microsoft.aspnet.signalr.client.<a href="../microsoft/aspnet/signalr/client/Connection.html" title="class in microsoft.aspnet.signalr.client">Connection</a></dt>
<dd>&nbsp;</dd>
<dt><span class="strong"><a href="../microsoft/aspnet/signalr/client/ConnectionBase.html#send(java.lang.String)">send(String)</a></span> - Method in interface microsoft.aspnet.signalr.client.<a href="../microsoft/aspnet/signalr/client/ConnectionBase.html" title="interface in microsoft.aspnet.signalr.client">ConnectionBase</a></dt>
<dd>
<div class="block">Sends data using the connection</div>
</dd>
<dt><span class="strong"><a href="../microsoft/aspnet/signalr/client/CalendarSerializer.html#serialize(java.util.Calendar, java.lang.reflect.Type, com.google.gson.JsonSerializationContext)">serialize(Calendar, Type, JsonSerializationContext)</a></span> - Method in class microsoft.aspnet.signalr.client.<a href="../microsoft/aspnet/signalr/client/CalendarSerializer.html" title="class in microsoft.aspnet.signalr.client">CalendarSerializer</a></dt>
<dd>&nbsp;</dd>
<dt><span class="strong"><a href="../microsoft/aspnet/signalr/client/DateSerializer.html#serialize(java.util.Date, java.lang.reflect.Type, com.google.gson.JsonSerializationContext)">serialize(Date, Type, JsonSerializationContext)</a></span> - Method in class microsoft.aspnet.signalr.client.<a href="../microsoft/aspnet/signalr/client/DateSerializer.html" title="class in microsoft.aspnet.signalr.client">DateSerializer</a></dt>
<dd>
<div class="block">Serializes a Date to a JsonElement containing a ISO-8601 formatted date</div>
</dd>
<dt><span class="strong"><a href="../microsoft/aspnet/signalr/client/DateSerializer.html#serialize(java.util.Date)">serialize(Date)</a></span> - Static method in class microsoft.aspnet.signalr.client.<a href="../microsoft/aspnet/signalr/client/DateSerializer.html" title="class in microsoft.aspnet.signalr.client">DateSerializer</a></dt>
<dd>
<div class="block">Serializes a Date object to an ISO-8601 formatted date string</div>
</dd>
<dt><span class="strong"><a href="../microsoft/aspnet/signalr/client/Connection.html#setCredentials(microsoft.aspnet.signalr.client.Credentials)">setCredentials(Credentials)</a></span> - Method in class microsoft.aspnet.signalr.client.<a href="../microsoft/aspnet/signalr/client/Connection.html" title="class in microsoft.aspnet.signalr.client">Connection</a></dt>
<dd>&nbsp;</dd>
<dt><span class="strong"><a href="../microsoft/aspnet/signalr/client/ConnectionBase.html#setCredentials(microsoft.aspnet.signalr.client.Credentials)">setCredentials(Credentials)</a></span> - Method in interface microsoft.aspnet.signalr.client.<a href="../microsoft/aspnet/signalr/client/ConnectionBase.html" title="interface in microsoft.aspnet.signalr.client">ConnectionBase</a></dt>
<dd>
<div class="block">Sets the credentials the connection should use</div>
</dd>
<dt><span class="strong"><a href="../microsoft/aspnet/signalr/client/MessageResult.html#setDisconnect(boolean)">setDisconnect(boolean)</a></span> - Method in class microsoft.aspnet.signalr.client.<a href="../microsoft/aspnet/signalr/client/MessageResult.html" title="class in microsoft.aspnet.signalr.client">MessageResult</a></dt>
<dd>&nbsp;</dd>
<dt><span class="strong"><a href="../microsoft/aspnet/signalr/client/UpdateableCancellableFuture.html#setFuture(microsoft.aspnet.signalr.client.SignalRFuture)">setFuture(SignalRFuture&lt;?&gt;)</a></span> - Method in class microsoft.aspnet.signalr.client.<a href="../microsoft/aspnet/signalr/client/UpdateableCancellableFuture.html" title="class in microsoft.aspnet.signalr.client">UpdateableCancellableFuture</a></dt>
<dd>&nbsp;</dd>
<dt><span class="strong"><a href="../microsoft/aspnet/signalr/client/Connection.html#setGroupsToken(java.lang.String)">setGroupsToken(String)</a></span> - Method in class microsoft.aspnet.signalr.client.<a href="../microsoft/aspnet/signalr/client/Connection.html" title="class in microsoft.aspnet.signalr.client">Connection</a></dt>
<dd>&nbsp;</dd>
<dt><span class="strong"><a href="../microsoft/aspnet/signalr/client/ConnectionBase.html#setGroupsToken(java.lang.String)">setGroupsToken(String)</a></span> - Method in interface microsoft.aspnet.signalr.client.<a href="../microsoft/aspnet/signalr/client/ConnectionBase.html" title="interface in microsoft.aspnet.signalr.client">ConnectionBase</a></dt>
<dd>
<div class="block">Sets the groups token the connection should use</div>
</dd>
<dt><span class="strong"><a href="../microsoft/aspnet/signalr/client/MessageResult.html#setInitialize(boolean)">setInitialize(boolean)</a></span> - Method in class microsoft.aspnet.signalr.client.<a href="../microsoft/aspnet/signalr/client/MessageResult.html" title="class in microsoft.aspnet.signalr.client">MessageResult</a></dt>
<dd>&nbsp;</dd>
<dt><span class="strong"><a href="../microsoft/aspnet/signalr/client/HeartbeatMonitor.html#setKeepAliveData(microsoft.aspnet.signalr.client.KeepAliveData)">setKeepAliveData(KeepAliveData)</a></span> - Method in class microsoft.aspnet.signalr.client.<a href="../microsoft/aspnet/signalr/client/HeartbeatMonitor.html" title="class in microsoft.aspnet.signalr.client">HeartbeatMonitor</a></dt>
<dd>
<div class="block">Sets the Keep Alive data</div>
</dd>
<dt><span class="strong"><a href="../microsoft/aspnet/signalr/client/Connection.html#setMessageId(java.lang.String)">setMessageId(String)</a></span> - Method in class microsoft.aspnet.signalr.client.<a href="../microsoft/aspnet/signalr/client/Connection.html" title="class in microsoft.aspnet.signalr.client">Connection</a></dt>
<dd>&nbsp;</dd>
<dt><span class="strong"><a href="../microsoft/aspnet/signalr/client/ConnectionBase.html#setMessageId(java.lang.String)">setMessageId(String)</a></span> - Method in interface microsoft.aspnet.signalr.client.<a href="../microsoft/aspnet/signalr/client/ConnectionBase.html" title="interface in microsoft.aspnet.signalr.client">ConnectionBase</a></dt>
<dd>
<div class="block">Sets the message id the connection should use</div>
</dd>
<dt><span class="strong"><a href="../microsoft/aspnet/signalr/client/HeartbeatMonitor.html#setOnTimeout(java.lang.Runnable)">setOnTimeout(Runnable)</a></span> - Method in class microsoft.aspnet.signalr.client.<a href="../microsoft/aspnet/signalr/client/HeartbeatMonitor.html" title="class in microsoft.aspnet.signalr.client">HeartbeatMonitor</a></dt>
<dd>
<div class="block">Sets the "Timeout" event handler</div>
</dd>
<dt><span class="strong"><a href="../microsoft/aspnet/signalr/client/HeartbeatMonitor.html#setOnWarning(java.lang.Runnable)">setOnWarning(Runnable)</a></span> - Method in class microsoft.aspnet.signalr.client.<a href="../microsoft/aspnet/signalr/client/HeartbeatMonitor.html" title="class in microsoft.aspnet.signalr.client">HeartbeatMonitor</a></dt>
<dd>
<div class="block">Sets the "Warning" event handler</div>
</dd>
<dt><span class="strong"><a href="../microsoft/aspnet/signalr/client/MessageResult.html#setReconnect(boolean)">setReconnect(boolean)</a></span> - Method in class microsoft.aspnet.signalr.client.<a href="../microsoft/aspnet/signalr/client/MessageResult.html" title="class in microsoft.aspnet.signalr.client">MessageResult</a></dt>
<dd>&nbsp;</dd>
<dt><span class="strong"><a href="../microsoft/aspnet/signalr/client/SignalRFuture.html#setResult(V)">setResult(V)</a></span> - Method in class microsoft.aspnet.signalr.client.<a href="../microsoft/aspnet/signalr/client/SignalRFuture.html" title="class in microsoft.aspnet.signalr.client">SignalRFuture</a></dt>
<dd>
<div class="block">Sets a result to the future and finishes its execution</div>
</dd>
<dt><span class="strong"><a href="../microsoft/aspnet/signalr/client/SimpleEntry.html#setValue(V)">setValue(V)</a></span> - Method in class microsoft.aspnet.signalr.client.<a href="../microsoft/aspnet/signalr/client/SimpleEntry.html" title="class in microsoft.aspnet.signalr.client">SimpleEntry</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../microsoft/aspnet/signalr/client/SignalRFuture.html" title="class in microsoft.aspnet.signalr.client"><span class="strong">SignalRFuture</span></a>&lt;<a href="../microsoft/aspnet/signalr/client/SignalRFuture.html" title="type parameter in SignalRFuture">V</a>&gt; - Class in <a href="../microsoft/aspnet/signalr/client/package-summary.html">microsoft.aspnet.signalr.client</a></dt>
<dd>
<div class="block">Represents long running SignalR operations</div>
</dd>
<dt><span class="strong"><a href="../microsoft/aspnet/signalr/client/SignalRFuture.html#SignalRFuture()">SignalRFuture()</a></span> - Constructor for class microsoft.aspnet.signalr.client.<a href="../microsoft/aspnet/signalr/client/SignalRFuture.html" title="class in microsoft.aspnet.signalr.client">SignalRFuture</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../microsoft/aspnet/signalr/client/SimpleEntry.html" title="class in microsoft.aspnet.signalr.client"><span class="strong">SimpleEntry</span></a>&lt;<a href="../microsoft/aspnet/signalr/client/SimpleEntry.html" title="type parameter in SimpleEntry">K</a>,<a href="../microsoft/aspnet/signalr/client/SimpleEntry.html" title="type parameter in SimpleEntry">V</a>&gt; - Class in <a href="../microsoft/aspnet/signalr/client/package-summary.html">microsoft.aspnet.signalr.client</a></dt>
<dd>
<div class="block">Simple Entry<K,V> implementation</div>
</dd>
<dt><span class="strong"><a href="../microsoft/aspnet/signalr/client/SimpleEntry.html#SimpleEntry(K, V)">SimpleEntry(K, V)</a></span> - Constructor for class microsoft.aspnet.signalr.client.<a href="../microsoft/aspnet/signalr/client/SimpleEntry.html" title="class in microsoft.aspnet.signalr.client">SimpleEntry</a></dt>
<dd>
<div class="block">Initializes the SimpleEntry</div>
</dd>
<dt><span class="strong"><a href="../microsoft/aspnet/signalr/client/Connection.html#start()">start()</a></span> - Method in class microsoft.aspnet.signalr.client.<a href="../microsoft/aspnet/signalr/client/Connection.html" title="class in microsoft.aspnet.signalr.client">Connection</a></dt>
<dd>
<div class="block">Starts the connection using the best available transport</div>
</dd>
<dt><span class="strong"><a href="../microsoft/aspnet/signalr/client/Connection.html#start(microsoft.aspnet.signalr.client.transport.ClientTransport)">start(ClientTransport)</a></span> - Method in class microsoft.aspnet.signalr.client.<a href="../microsoft/aspnet/signalr/client/Connection.html" title="class in microsoft.aspnet.signalr.client">Connection</a></dt>
<dd>&nbsp;</dd>
<dt><span class="strong"><a href="../microsoft/aspnet/signalr/client/ConnectionBase.html#start(microsoft.aspnet.signalr.client.transport.ClientTransport)">start(ClientTransport)</a></span> - Method in interface microsoft.aspnet.signalr.client.<a href="../microsoft/aspnet/signalr/client/ConnectionBase.html" title="interface in microsoft.aspnet.signalr.client">ConnectionBase</a></dt>
<dd>
<div class="block">Starts the connection</div>
</dd>
<dt><span class="strong"><a href="../microsoft/aspnet/signalr/client/HeartbeatMonitor.html#start(microsoft.aspnet.signalr.client.KeepAliveData, microsoft.aspnet.signalr.client.ConnectionBase)">start(KeepAliveData, ConnectionBase)</a></span> - Method in class microsoft.aspnet.signalr.client.<a href="../microsoft/aspnet/signalr/client/HeartbeatMonitor.html" title="class in microsoft.aspnet.signalr.client">HeartbeatMonitor</a></dt>
<dd>
<div class="block">Starts the monitor</div>
</dd>
<dt><span class="strong"><a href="../microsoft/aspnet/signalr/client/Connection.html#stateChanged(microsoft.aspnet.signalr.client.StateChangedCallback)">stateChanged(StateChangedCallback)</a></span> - Method in class microsoft.aspnet.signalr.client.<a href="../microsoft/aspnet/signalr/client/Connection.html" title="class in microsoft.aspnet.signalr.client">Connection</a></dt>
<dd>&nbsp;</dd>
<dt><span class="strong"><a href="../microsoft/aspnet/signalr/client/ConnectionBase.html#stateChanged(microsoft.aspnet.signalr.client.StateChangedCallback)">stateChanged(StateChangedCallback)</a></span> - Method in interface microsoft.aspnet.signalr.client.<a href="../microsoft/aspnet/signalr/client/ConnectionBase.html" title="interface in microsoft.aspnet.signalr.client">ConnectionBase</a></dt>
<dd>
<div class="block">Sets the handler for the "StateChanged" event</div>
</dd>
<dt><span class="strong"><a href="../microsoft/aspnet/signalr/client/StateChangedCallback.html#stateChanged(microsoft.aspnet.signalr.client.ConnectionState, microsoft.aspnet.signalr.client.ConnectionState)">stateChanged(ConnectionState, ConnectionState)</a></span> - Method in interface microsoft.aspnet.signalr.client.<a href="../microsoft/aspnet/signalr/client/StateChangedCallback.html" title="interface in microsoft.aspnet.signalr.client">StateChangedCallback</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../microsoft/aspnet/signalr/client/StateChangedCallback.html" title="interface in microsoft.aspnet.signalr.client"><span class="strong">StateChangedCallback</span></a> - Interface in <a href="../microsoft/aspnet/signalr/client/package-summary.html">microsoft.aspnet.signalr.client</a></dt>
<dd>
<div class="block">Callback invoked when a connection changes its state</div>
</dd>
<dt><span class="strong"><a href="../microsoft/aspnet/signalr/client/Connection.html#stop()">stop()</a></span> - Method in class microsoft.aspnet.signalr.client.<a href="../microsoft/aspnet/signalr/client/Connection.html" title="class in microsoft.aspnet.signalr.client">Connection</a></dt>
<dd>&nbsp;</dd>
<dt><span class="strong"><a href="../microsoft/aspnet/signalr/client/ConnectionBase.html#stop()">stop()</a></span> - Method in interface microsoft.aspnet.signalr.client.<a href="../microsoft/aspnet/signalr/client/ConnectionBase.html" title="interface in microsoft.aspnet.signalr.client">ConnectionBase</a></dt>
<dd>
<div class="block">Aborts the connection and closes it</div>
</dd>
<dt><span class="strong"><a href="../microsoft/aspnet/signalr/client/HeartbeatMonitor.html#stop()">stop()</a></span> - Method in class microsoft.aspnet.signalr.client.<a href="../microsoft/aspnet/signalr/client/HeartbeatMonitor.html" title="class in microsoft.aspnet.signalr.client">HeartbeatMonitor</a></dt>
<dd>
<div class="block">Stops the heartbeat monitor</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">L</a>&nbsp;<a href="index-11.html">M</a>&nbsp;<a href="index-12.html">N</a>&nbsp;<a href="index-13.html">O</a>&nbsp;<a href="index-14.html">P</a>&nbsp;<a href="index-15.html">R</a>&nbsp;<a href="index-16.html">S</a>&nbsp;<a href="index-17.html">T</a>&nbsp;<a href="index-18.html">U</a>&nbsp;<a href="index-19.html">V</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../microsoft/aspnet/signalr/client/package-summary.html">Package</a></li>
<li>Class</li>
<li>Use</li>
<li><a href="../microsoft/aspnet/signalr/client/package-tree.html">Tree</a></li>
<li class="navBarCell1Rev">Index</li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-15.html">Prev Letter</a></li>
<li><a href="index-17.html">Next Letter</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-filesindex-16.html" target="_top">Frames</a></li>
<li><a href="index-16.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
