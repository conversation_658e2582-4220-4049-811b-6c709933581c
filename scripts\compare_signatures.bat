@echo off
echo ========================================
echo 🔍 COMPARING SIGNATURES - Android vs Flutter
echo ========================================
echo.

REM Check if keytool is available
where keytool >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ ERROR: keytool not found in PATH!
    echo    Please add Java JDK bin directory to PATH
    echo    Example: C:\Program Files\Java\jdk-17\bin
    pause
    exit /b 1
)

echo 📋 Checking keystore signature...
echo.

REM Check production keystore signature
if exist "android\keystore\beta_cineplex_app_key.jks" (
    echo 🔑 Production keystore signature:
    echo ----------------------------------------
    keytool -list -v -keystore "android\keystore\beta_cineplex_app_key.jks" -alias "beta cineplex" -storepass "Betacorpvn@123" -keypass "Betacorpvn@123" | findstr /C:"SHA1:" /C:"SHA256:"
    echo.
) else (
    echo ❌ Production keystore not found!
    pause
    exit /b 1
)

REM Check if Flutter APK exists
if exist "build\app\outputs\flutter-apk\app-release.apk" (
    echo 📱 Flutter APK signature:
    echo ----------------------------------------
    keytool -printcert -jarfile "build\app\outputs\flutter-apk\app-release.apk" | findstr /C:"SHA1:" /C:"SHA256:"
    echo.
) else (
    echo ⚠️  Flutter APK not found. Run build_for_chplay_update.bat first.
    echo.
)

REM Check if Android APK exists for comparison
if exist "Beta-Mobile-Android.v3-beta-feat-haibh\app\release\app-release.apk" (
    echo 🤖 Android APK signature:
    echo ----------------------------------------
    keytool -printcert -jarfile "Beta-Mobile-Android.v3-beta-feat-haibh\app\release\app-release.apk" | findstr /C:"SHA1:" /C:"SHA256:"
    echo.
) else (
    echo ⚠️  Android APK not found for comparison.
    echo.
)

echo ========================================
echo 🎯 SIGNATURE VERIFICATION GUIDE:
echo ========================================
echo.
echo ✅ For successful CH Play update:
echo    - Flutter APK signature MUST match keystore signature
echo    - Flutter APK signature SHOULD match Android APK signature
echo.
echo ❌ If signatures don't match:
echo    1. Check keystore path in build.gradle
echo    2. Verify keyAlias and passwords
echo    3. Ensure using production keystore (not debug)
echo    4. Clean and rebuild: flutter clean && flutter build apk --release
echo.
echo 📋 Manual verification:
echo    1. Extract both APKs' META-INF/CERT.RSA files
echo    2. Compare using: keytool -printcert -file CERT.RSA
echo    3. SHA1 and SHA256 fingerprints must be identical
echo.

pause
