//
//  ListNewsViewController.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 4/24/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit

class ListNewsViewController: BaseViewController {
    @IBOutlet weak var tableView: UITableView!

    fileprivate let promotionCellId = "NewsTableViewCell"
    var items: [NewsModel] = []

    var type: NewsType = .promotion
    
    /**
     * @note: false if come from HomeVC. tinhvv fixed folow get data
     */
    var isLoadData: Bool = true
    
    var categoryId: String?

    fileprivate lazy var refreshControl: UIRefreshControl = {
        let refreshControl = UIRefreshControl(frame: CGRect.zero)
        refreshControl.addTarget(self, action: #selector(self.refreshData), for: .valueChanged)
        return refreshControl
    }()

    override func viewDidLoad() {
        super.viewDidLoad()

        tableView.register(UINib(nibName: promotionCellId, bundle: nil), forCellReuseIdentifier: promotionCellId)
        tableView.addSubview(refreshControl)
        tableView.contentInset = UIEdgeInsets(top: 0, left: 0, bottom: 70, right: 0)

        if isLoadData {
            self.refreshData()
        }
    }

    override func showLoading() {
        refreshControl.beginRefreshing()
    }

    override func dismissLoading() {
        refreshControl.endRefreshing()
    }

    @objc func refreshData() {
        if !isLoadData {
            getNews()
        } else {
            self.items.removeAll()
            if type == .promotion {
                self.getPromotionCategory()
            } else {
                self.getNewCategory()
            }
        }
        
    }

    enum NewsType: Int {
        case promotion, news
    }
}

extension ListNewsViewController: UITableViewDataSource, UITableViewDelegate {
    func numberOfSections(in tableView: UITableView) -> Int {
        return 1
    }

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return items.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: promotionCellId, for: indexPath) as! NewsTableViewCell
        cell.fillData(items[indexPath.row])
        return cell
    }

    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: false)

        let vc = UIStoryboard.home[.newsDetail] as! NewsDetailViewController
        vc.type = NewType.news(items[indexPath.row])
        vc.hidesBottomBarWhenPushed = true
        show(vc, sender: nil)
    }
}

extension ListNewsViewController {
    private func getNewCategory(){
        self.showLoading()
        EcmProvider.rx.request(.getNewEvent).mapObject(DDKCResponse<NewModel>.self)
            
            .subscribe(onNext:{[weak self] response in
                self?.handlerResponse(response, success: {
                    guard let objects = response.ListObject else{
                        print("Data wrong")
                        self?.dismissLoading()
                        return
                    }
                    if objects.isEmpty == false {
                        let requestGroup = DispatchGroup()
                        let _ = DispatchQueue.global(qos: .userInitiated)
                        DispatchQueue.concurrentPerform(iterations: objects.count) { (i) in
                            let categoryId = objects[i].CategoryId
                            requestGroup.enter()
                            self?.getListNew(categoryId, completionHander: {
                                requestGroup.leave()
                            })
                        }

                        requestGroup.notify(queue: DispatchQueue.main){
                            print("Get data completion")
                            self?.dismissLoading()
                            self?.tableView.reloadData()

                        }
                    } else {
                        self?.dismissLoading()
                    }
                }, error: {
                    self?.dismissLoading()
                })
                }, onError: { error in
                    self.dismissLoading()
            }).disposed(by: disposeBag)
    }

    private func getListNew(_ newId: String?, completionHander: @escaping () -> Void){
        guard let id = newId else {
            return
        }
        EcmProvider.rx.request(.getNewWithId(id, Config.PageSize, 1)).mapObject(DDKCResponse<NewsModel>.self)
            
            .subscribe(onNext:{[weak self] response in
                self?.handlerResponse(response, success: {
//                    self?.dismissLoading()
                    guard let items = response.ListObject else {
                        completionHander()
                        return
                    }
                    self?.items.append(contentsOf: items)
                    completionHander()
                }, error: {
//                    self?.dismissLoading()
                    completionHander()
                })
            }).disposed(by: disposeBag)
    }
    
    func getNews(){
        guard let id = self.categoryId else {
            return
        }
        self.showLoading()
        EcmProvider.rx.request(.getNewWithId(id, Config.PageSize, 1)).mapObject(DDKCResponse<NewsModel>.self)
            
            .subscribe(onNext:{[weak self] response in
                self?.dismissLoading()
                guard let `self` = self else {return}
                self.handlerResponse(response, success: {[weak self] in
                    guard let `self` = self else {return}
                    guard let items = response.ListObject else {
                        return
                    }
                    DispatchQueue.main.async {
                        self.items = items
                        self.tableView.reloadData()
                    }
                }, error: {
                })
            }).disposed(by: disposeBag)
    }

    private func getPromotionCategory(){
        self.showLoading()
        EcmProvider.rx.request(.getNewPromotion).mapObject(DDKCResponse<NewModel>.self)
            
            .subscribe(onNext:{[weak self] response in
                self?.handlerResponse(response, success: {
                    guard let objects = response.ListObject else{
                        print("Data wrong")
                        self?.dismissLoading()
                        return
                    }
                    if objects.isEmpty == false {
                        let requestGroup = DispatchGroup()
                        let _ = DispatchQueue.global(qos: .userInitiated)
                        DispatchQueue.concurrentPerform(iterations: objects.count) { (i) in
                            let categoryId = objects[i].CategoryId
                            requestGroup.enter()
                            self?.getListPromotion(categoryId, completionHander: {
                                requestGroup.leave()
                            })
                        }

                        requestGroup.notify(queue: DispatchQueue.main){
                            print("Get data completion")
                            self?.dismissLoading()
                            self?.tableView.reloadData()

                        }
                    } else {
                        self?.dismissLoading()
                    }
                }, error: {
                    self?.dismissLoading()
                })
            }).disposed(by: disposeBag)
    }

    private func getListPromotion(_ categoryId: String?, completionHander: @escaping () -> Void){
        guard let id = categoryId else {
            return
        }
        EcmProvider.rx.request(.getNewForCategory(id, nil, nil)).mapObject(DDKCResponse<NewsModel>.self)
            
            .subscribe(onNext:{[weak self] response in
                self?.handlerResponse(response, success: {
                    self?.dismissLoading()
                    guard let items = response.ListObject else {
                        completionHander()
                        return
                    }
                    self?.items.append(contentsOf: items)
                    DispatchQueue.main.async {
//                        self?.dismissLoading()
//                        self?.tableView.reloadData()
                        completionHander()
                    }
                }, error: {
                    completionHander()
//                    self?.dismissLoading()
                })
            }).disposed(by: disposeBag)
    }
}

