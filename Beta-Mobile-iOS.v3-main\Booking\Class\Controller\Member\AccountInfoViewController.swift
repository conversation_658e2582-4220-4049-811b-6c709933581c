//
//  AccountInfoViewController.swift
//  Booking-dev
//
//  Created by <PERSON><PERSON> on 4/18/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit
import SwiftDate
import IQKeyboardManagerSwift

class AccountInfoViewController: BaseViewController {
    @IBOutlet weak var lbEmail: LocalizableLabel!
    @IBOutlet weak var tfFirstName: InputTextField!
    @IBOutlet weak var tfLastName: InputTextField!
    @IBOutlet weak var tfName: InputTextField!
    @IBOutlet weak var tfGender: PickerTextField!
    @IBOutlet weak var tfBirthdate: DateTextField!
    @IBOutlet weak var tfPhoneNumber: InputTextField!
    @IBOutlet weak var tfCity: PickerTextField!
    @IBOutlet weak var tfDistrict: PickerTextField!
    @IBOutlet weak var tfAddress: InputTextField!
    @IBOutlet weak var cmndTextField: InputTextField!
    
    private var listCity: [CityModel]?
    private var listDistrict: [CityModel]?

    let genders = [Gender.Male, .Female, .Other]
    var genderSelected: Gender?
    var citySelected: CityModel?
    var districtSelected: CityModel?
    
    override func viewDidLoad() {
        super.viewDidLoad()

        localizableTitle = "AccountInfo.Title"
        loadData()
        initCityPicker()
        setupGenderData()
        getListCity()

        // remove confirm VC from navigation controller
        if var listVC = self.navigationController?.viewControllers {
            if let confirmIndex = listVC.index(where: { $0 is ConfirmPassViewController }){
                listVC.remove(at: confirmIndex)
                self.navigationController?.viewControllers = listVC
            }
            if let updateIndex = listVC.index(where: { $0 is UpdatePasswordViewController}) {
                listVC.remove(at: updateIndex)
                self.navigationController?.viewControllers = listVC
            }
        }
    }

    func loadData() {
        guard let user = Global.shared.user else { return }
        lbEmail.text = user.Email
//        tfFirstName.text = user.FirstName
//        tfLastName.text = user.LastName
        tfName.text = user.FullName
        tfBirthdate.setText(text: user.BirthDate ?? "")
        tfPhoneNumber.text = user.PhoneOffice
        cmndTextField.text = user.PersonalId
        tfCity.text = user.AddressCity
        tfDistrict.text = user.AddressDistrict
        tfAddress.text = user.AddressStreet

        citySelected = CityModel(JSON: [:])
        citySelected?.Id = user.AddressCityId
        citySelected?.Name = user.AddressCity

        districtSelected = CityModel(JSON: [:])
        districtSelected?.Id = user.AddressDistrictId
        districtSelected?.Name = user.AddressDistrict

        if let cityId = citySelected?.Id {
            self.getListDistrict(cityId)
        }

        self.genderSelected = Gender(rawValue: user.Gender ?? 0)
        tfGender.text = Gender(rawValue: user.Gender ?? 0)?.description
    }
    
    func initCityPicker(){
        tfCity.didSelectItemAtIndex = {[weak self] index in
            guard let `self` = self, let cities = self.listCity else {return}
            let city = cities[index]
            guard city.Id != self.citySelected?.Id else { return }
            self.citySelected = city
            self.tfDistrict.text = nil
            self.districtSelected = nil
            self.getListDistrict(city.Id)
        }
        
        tfDistrict.didSelectItemAtIndex = {[weak self] index in
            guard let `self` = self, let districts = self.listDistrict else {return}
            self.districtSelected = districts[index]
        }
    }
    
    func setupGenderData(){
        tfGender.listItem = genders.map{$0.description}
        tfGender.didSelectItemAtIndex = { [weak self] index in
            guard let `self` = self else {return}
            self.genderSelected = self.genders[index]
        }
    }

    @IBAction func updateBtPressed(_ sender: Any) {
        view.endEditing(true)
        if self.validate() {
            self.update()
        }
    }
}

extension AccountInfoViewController{
    private func validate() -> Bool{
//        if tfFirstName.text?.isEmpty != false {
//            UIAlertController.showAlert(self, message: "Alert.FirstNameNotEmpty".localized, handler: { _ in
//                //                self.firstNameTextField.becomeFirstResponder()
//            })
//            return false
//        }
//
//        if tfLastName.text?.isEmpty != false {
//            UIAlertController.showAlert(self, message: "Alert.LastNameNotEmpty".localized, handler: { _ in
//                //                self.lastNameTextField.becomeFirstResponder()
//            })
//            return false
//        }
        if tfName.text?.isEmpty != false {
            UIAlertController.showAlert(self, message: "Alert.NameNotEmpty".localized, handler: { _ in
                //                self.firstNameTextField.becomeFirstResponder()
            })
            return false
        }


        if tfPhoneNumber.text?.isEmpty != false {
            UIAlertController.showAlert(self, message: "Alert.PhoneNotEmpty".localized, handler: { _ in
                //                self.phoneTextField.becomeFirstResponder()
            })
            return false
        }

        if tfPhoneNumber.text?.removeAllSpaces().isPhone() != true {
            UIAlertController.showAlert(self, message: "Alert.PhoneInvalid".localized, handler: { _ in
                //                self.phoneTextField.becomeFirstResponder()
            })
            return false
        }

        if tfBirthdate.text?.isEmpty != false {
            UIAlertController.showAlert(self, message: "Alert.BithdayNotEmpty".localized, handler: { _ in
                //                self.birthdayTextField.becomeFirstResponder()
            })
            return false
        }

        if let birthdate = tfBirthdate.text?.toDate("dd/MM/yyyy"), Date().years(from: birthdate.date) < 12 {
            UIAlertController.showAlert(self, message: "Alert.BirthdayInvalid".localized, handler: { _ in
                //                self.birthdayTextField.becomeFirstResponder()
            })
            return false
        }

        if let cmnd = cmndTextField.text, !cmnd.isEmpty, !cmnd.isValidPersonalId() {
            UIAlertController.showAlert(self, message: "Alert.IDInvalid".localized, handler: { _ in
            })
            return false
        }
        
        
        return true
    }
    
    private func update(){
        guard let user = Global.shared.user, let userId = user.AccountId else {
            return
        }

        let birthdate = tfBirthdate.getDate() ?? ""
        let model = RegisterModel(name: tfName.text!, passport: cmndTextField.text, phone: tfPhoneNumber.text!.removeAllSpaces(), birthDay: birthdate, gender: genderSelected?.rawValue, city: citySelected, district: districtSelected, address: tfAddress.text)
        self.showLoading()
        AccountProvider.rx.request(.updateProfile(userId, model)).mapObject(DDKCResponse<UserModel>.self)
            
            .subscribe(onNext:{[weak self] response in
                guard let `self` = self else {return}
                self.dismissLoading()
                guard let _ = response.Object else{
                    print("Data wrong")
                    self.flashError(title: "Alert.Error".localized, message: response.Message ?? "Alert.ErrorServer".localized)
                    return
                }
                if response.isSuccess(){
                    self.flashSuccess()
                    self.navigationController?.popViewController(animated: true)
                    if let newUser = response.Object {
                        user.FirstName = newUser.FirstName
                        user.LastName = newUser.LastName
                        user.FullName = newUser.FullName
                        user.PhoneOffice = newUser.PhoneOffice
                        user.PersonalId = newUser.PersonalId
                        user.BirthDate = newUser.BirthDate
                        user.Gender = newUser.Gender
                        user.AddressDistrict = newUser.AddressDistrict
                        user.AddressDistrictId = newUser.AddressDistrictId
                        user.AddressCity = newUser.AddressCity
                        user.AddressCityId = newUser.AddressCityId
                        user.AddressStreet = newUser.AddressStreet
                        Global.shared.saveUser(user)
                    }
                }else{
                    UIAlertController.showAlert(self, message: response.Message ?? "Alert.ErrorServer".localized)
                }
            }).disposed(by: disposeBag)
    }
    
    private func getListCity(){
        self.showLoading()
        CityProvider.rx.request(.listCity).mapObject(DDKCResponse<CityModel>.self)
            
            .subscribe(onNext:{[weak self] response in
                self?.dismissLoading()
                guard let items = response.ListObject else {return}
                self?.tfCity.listItem = items.map{$0.Name ?? ""}
                self?.listCity = items
            }).disposed(by: disposeBag)
    }
    
    private func getListDistrict(_ cityId: String?){
        guard let id = cityId else {
            return
        }
        self.showLoading()
        CityProvider.rx.request(.listDistrict(id)).mapObject(DDKCResponse<CityModel>.self)
            
            .subscribe(onNext:{[weak self] response in
                self?.dismissLoading()
                guard let items = response.ListObject else {return}
                self?.tfDistrict.listItem = items.map{$0.Name ?? ""}
                self?.listDistrict = items
            }).disposed(by: disposeBag)
    }
}

extension AccountInfoViewController: UITextFieldDelegate {
    func textFieldShouldReturn(_ textField: UITextField) -> Bool {
        if textField == tfAddress {
            updateBtPressed(textField)
        } else {
            IQKeyboardManager.shared.goNext()
        }
        return true
    }
}
