<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<title>HeartbeatMonitor</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="HeartbeatMonitor";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../microsoft/aspnet/signalr/client/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HeartbeatMonitor.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../microsoft/aspnet/signalr/client/FutureHelper.html" title="class in microsoft.aspnet.signalr.client"><span class="strong">Prev Class</span></a></li>
<li><a href="../../../../microsoft/aspnet/signalr/client/InvalidProtocolVersionException.html" title="class in microsoft.aspnet.signalr.client"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?microsoft/aspnet/signalr/client/HeartbeatMonitor.html" target="_top">Frames</a></li>
<li><a href="HeartbeatMonitor.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">microsoft.aspnet.signalr.client</div>
<h2 title="Class HeartbeatMonitor" class="title">Class HeartbeatMonitor</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>microsoft.aspnet.signalr.client.HeartbeatMonitor</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="strong">HeartbeatMonitor</span>
extends java.lang.Object</pre>
<div class="block">Heartbeat Monitor to detect slow or timed out connections</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><strong><a href="../../../../microsoft/aspnet/signalr/client/HeartbeatMonitor.html#HeartbeatMonitor()">HeartbeatMonitor</a></strong>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method_summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span>Methods</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../microsoft/aspnet/signalr/client/HeartbeatMonitor.html#beat()">beat</a></strong>()</code>
<div class="block">Alerts the monitor that a beat was detected</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>microsoft.aspnet.signalr.client.KeepAliveData</code></td>
<td class="colLast"><code><strong><a href="../../../../microsoft/aspnet/signalr/client/HeartbeatMonitor.html#getKeepAliveData()">getKeepAliveData</a></strong>()</code>
<div class="block">Returns the Keep Alive data</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>java.lang.Runnable</code></td>
<td class="colLast"><code><strong><a href="../../../../microsoft/aspnet/signalr/client/HeartbeatMonitor.html#getOnTimeout()">getOnTimeout</a></strong>()</code>
<div class="block">Returns the "Timeout" event handler</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>java.lang.Runnable</code></td>
<td class="colLast"><code><strong><a href="../../../../microsoft/aspnet/signalr/client/HeartbeatMonitor.html#getOnWarning()">getOnWarning</a></strong>()</code>
<div class="block">Returns the "Warning" event handler</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../microsoft/aspnet/signalr/client/HeartbeatMonitor.html#setKeepAliveData(microsoft.aspnet.signalr.client.KeepAliveData)">setKeepAliveData</a></strong>(microsoft.aspnet.signalr.client.KeepAliveData&nbsp;keepAliveData)</code>
<div class="block">Sets the Keep Alive data</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../microsoft/aspnet/signalr/client/HeartbeatMonitor.html#setOnTimeout(java.lang.Runnable)">setOnTimeout</a></strong>(java.lang.Runnable&nbsp;onTimeout)</code>
<div class="block">Sets the "Timeout" event handler</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../microsoft/aspnet/signalr/client/HeartbeatMonitor.html#setOnWarning(java.lang.Runnable)">setOnWarning</a></strong>(java.lang.Runnable&nbsp;onWarning)</code>
<div class="block">Sets the "Warning" event handler</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../microsoft/aspnet/signalr/client/HeartbeatMonitor.html#start(microsoft.aspnet.signalr.client.KeepAliveData, microsoft.aspnet.signalr.client.ConnectionBase)">start</a></strong>(microsoft.aspnet.signalr.client.KeepAliveData&nbsp;keepAliveData,
     <a href="../../../../microsoft/aspnet/signalr/client/ConnectionBase.html" title="interface in microsoft.aspnet.signalr.client">ConnectionBase</a>&nbsp;connection)</code>
<div class="block">Starts the monitor</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../microsoft/aspnet/signalr/client/HeartbeatMonitor.html#stop()">stop</a></strong>()</code>
<div class="block">Stops the heartbeat monitor</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="HeartbeatMonitor()">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>HeartbeatMonitor</h4>
<pre>public&nbsp;HeartbeatMonitor()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method_detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="beat()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>beat</h4>
<pre>public&nbsp;void&nbsp;beat()</pre>
<div class="block">Alerts the monitor that a beat was detected</div>
</li>
</ul>
<a name="getKeepAliveData()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getKeepAliveData</h4>
<pre>public&nbsp;microsoft.aspnet.signalr.client.KeepAliveData&nbsp;getKeepAliveData()</pre>
<div class="block">Returns the Keep Alive data</div>
</li>
</ul>
<a name="getOnTimeout()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOnTimeout</h4>
<pre>public&nbsp;java.lang.Runnable&nbsp;getOnTimeout()</pre>
<div class="block">Returns the "Timeout" event handler</div>
</li>
</ul>
<a name="getOnWarning()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOnWarning</h4>
<pre>public&nbsp;java.lang.Runnable&nbsp;getOnWarning()</pre>
<div class="block">Returns the "Warning" event handler</div>
</li>
</ul>
<a name="setKeepAliveData(microsoft.aspnet.signalr.client.KeepAliveData)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setKeepAliveData</h4>
<pre>public&nbsp;void&nbsp;setKeepAliveData(microsoft.aspnet.signalr.client.KeepAliveData&nbsp;keepAliveData)</pre>
<div class="block">Sets the Keep Alive data</div>
</li>
</ul>
<a name="setOnTimeout(java.lang.Runnable)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOnTimeout</h4>
<pre>public&nbsp;void&nbsp;setOnTimeout(java.lang.Runnable&nbsp;onTimeout)</pre>
<div class="block">Sets the "Timeout" event handler</div>
</li>
</ul>
<a name="setOnWarning(java.lang.Runnable)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOnWarning</h4>
<pre>public&nbsp;void&nbsp;setOnWarning(java.lang.Runnable&nbsp;onWarning)</pre>
<div class="block">Sets the "Warning" event handler</div>
</li>
</ul>
<a name="start(microsoft.aspnet.signalr.client.KeepAliveData, microsoft.aspnet.signalr.client.ConnectionBase)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>start</h4>
<pre>public&nbsp;void&nbsp;start(microsoft.aspnet.signalr.client.KeepAliveData&nbsp;keepAliveData,
         <a href="../../../../microsoft/aspnet/signalr/client/ConnectionBase.html" title="interface in microsoft.aspnet.signalr.client">ConnectionBase</a>&nbsp;connection)</pre>
<div class="block">Starts the monitor</div>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>keepAliveData</code> - Data for keep-alive timings</dd><dd><code>connection</code> - Connection to monitor</dd></dl>
</li>
</ul>
<a name="stop()">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>stop</h4>
<pre>public&nbsp;void&nbsp;stop()</pre>
<div class="block">Stops the heartbeat monitor</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../microsoft/aspnet/signalr/client/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HeartbeatMonitor.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../microsoft/aspnet/signalr/client/FutureHelper.html" title="class in microsoft.aspnet.signalr.client"><span class="strong">Prev Class</span></a></li>
<li><a href="../../../../microsoft/aspnet/signalr/client/InvalidProtocolVersionException.html" title="class in microsoft.aspnet.signalr.client"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?microsoft/aspnet/signalr/client/HeartbeatMonitor.html" target="_top">Frames</a></li>
<li><a href="HeartbeatMonitor.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
