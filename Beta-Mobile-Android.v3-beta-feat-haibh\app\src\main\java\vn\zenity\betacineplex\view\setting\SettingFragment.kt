package vn.zenity.betacineplex.view.setting

import android.os.Bundle
import android.view.View
import kotlinx.android.synthetic.main.fragment_setting.*
import vn.zenity.betacineplex.BuildConfig
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.app.App
import vn.zenity.betacineplex.base.BaseFragment
import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.global.Constant
import vn.zenity.betacineplex.helper.extension.PreferencesHelper
import vn.zenity.betacineplex.helper.extension.gone
import vn.zenity.betacineplex.helper.extension.visible
import vn.zenity.betacineplex.helper.thirtypart.LocaleHelper
import vn.zenity.betacineplex.view.auth.CompanyProfileFragment
import vn.zenity.betacineplex.view.auth.PaymentPolicyFragment
import vn.zenity.betacineplex.view.auth.PrivacyPolicyFragment
import vn.zenity.betacineplex.view.auth.TermOfUseFragment

/**
 * Created by Zenity.
 */

class SettingFragment : BaseFragment(), SettingContractor.View {

    private val presenter = SettingPresenter()

    override fun getPresenter(): IBasePresenter<IBaseView>? {
        return presenter as? IBasePresenter<IBaseView>
    }

    override fun isShowToolbar(): Boolean {
        return false
    }

    override fun getLayoutRes(): Int {
        return R.layout.fragment_setting
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        if (BuildConfig.DEBUG) {
            tvVersion.text = VersionFragment.versionDev
        } else {
            tvVersion.text = BuildConfig.VERSION_NAME
        }
        version.setOnClickListener {
            openFragment(VersionFragment())
        }
        term.setOnClickListener {
            openFragment(TermOfUseFragment())
        }
        paymentPolicy.setOnClickListener {
            openFragment(PaymentPolicyFragment())
        }
        privacyPolicy.setOnClickListener {
            openFragment(PrivacyPolicyFragment())
        }
        companyInfo.setOnClickListener {
            openFragment(CompanyProfileFragment())
        }
        question.setOnClickListener {
            openFragment(QuestionAnswerFragment())
        }

        if (App.shared().getCurrentLang() != Constant.Lang.en) {
            ivCheckTiengViet.visible()
            ivCheckEnglish.gone()
        } else {
            ivCheckTiengViet.gone()
            ivCheckEnglish.visible()
        }

        settingTiengViet.setOnClickListener {
            if (App.shared().getCurrentLang() == Constant.Lang.en) {
//                App.shared().changeLanguage(Constant.Lang.vi)
                LocaleHelper.setLocale(App.shared(), Constant.Lang.vi)
                PreferencesHelper.shared.putValue(Constant.Key.language, Constant.Lang.vi)
                settingTiengViet?.postDelayed({
                    App.shared().restartApp()
                }, 200)
            }
        }

        settingEnglish.setOnClickListener {
            if (App.shared().getCurrentLang() != Constant.Lang.en) {
                LocaleHelper.setLocale(App.shared(), Constant.Lang.en)
                PreferencesHelper.shared.putValue(Constant.Key.language, Constant.Lang.en)
                settingEnglish?.postDelayed({
                    App.shared().restartApp()
                }, 200)
            }
        }
    }
}
