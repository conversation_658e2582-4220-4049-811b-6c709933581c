//
//  HomeCollectionViewCell.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 8/5/19.
//  Copyright © 2019 ddkc. All rights reserved.
//

import UIKit

class HomeCollectionViewCell: UICollectionViewCell {

    @IBOutlet weak var cImageView: UIImageView!
    @IBOutlet weak var posterImageView: UIImageView!
    @IBOutlet weak var nameLabel: UILabel!
    @IBOutlet weak var durationLabel: UILabel!
    @IBOutlet weak var hotImageView: UIImageView!
    @IBOutlet weak var dateLabel: UILabel!
    @IBOutlet weak var nameTop: NSLayoutConstraint!

    override func awakeFromNib() {
        super.awakeFromNib()
        // Initialization code
    }

    func fillData(film: FilmModel, type: FilmStateType){

        nameLabel.text = film.getName()
        durationLabel.text = "\(film.Duration ?? 0) \("Home.Minute".localized)"

        let imageURL = Config.BaseURLResource + (film.MainPosterUrl ?? "")
        if let url = URL(string: imageURL) {
            posterImageView.af_setImage(withURL: url, placeholderImage: #imageLiteral(resourceName: "bg1.png"))
        }

        if film.FilmRestrictAgeName == FilmModel.RestrictAge.c13 {
            cImageView.image = #imageLiteral(resourceName: "ic_c13")
        } else if film.FilmRestrictAgeName == FilmModel.RestrictAge.c16 {
            cImageView.image = #imageLiteral(resourceName: "ic_c16")
        } else if film.FilmRestrictAgeName == FilmModel.RestrictAge.c18 {
            cImageView.image = #imageLiteral(resourceName: "ic_c18")
        } else if film.FilmRestrictAgeName == FilmModel.RestrictAge.p {
            cImageView.image = #imageLiteral(resourceName: "ic_p")
        } else {
            cImageView.image = nil
        }

        hotImageView.isHidden = !film.IsHot
        dateLabel.isHidden = type != .comming
        durationLabel.isHidden = type == .comming

        nameTop.constant = type == .comming ? 20 : 2

        if let openningDate = film.OpeningDate {
            let date = Date.dateFromServerSavis(openningDate)
            dateLabel.text = date.toString(dateFormat: "dd-MM-yyyy")
        }
    }

}
