/* Javadoc style sheet */

/* Define colors, fonts and other style attributes here to override the defaults */

/* Page background color */
body { background-color: #FFFFFF; color:#000000 }

/* Headings */
h1 { font-size: 145% }

/* Table colors */
.TableHeadingColor     { background: #CCCCFF; color:#000000 } /* Dark mauve */
.TableSubHeadingColor  { background: #EEEEFF; color:#000000 } /* Light mauve */
.TableRowColor         { background: #FFFFFF; color:#000000 } /* White */

/* Font used in left-hand frame lists */
.FrameTitleFont   { font-size: 100%; font-family: Helvetica, Arial, sans-serif; color:#000000 }
.FrameHeadingFont { font-size:  90%; font-family: Helvetica, Arial, sans-serif; color:#000000 }
.FrameItemFont    { font-size:  90%; font-family: Helvetica, Arial, sans-serif; color:#000000 }

/* Navigation bar fonts and colors */
.NavBarCell1    { background-color:#EEEEFF; color:#000000} /* Light mauve */
.NavBarCell1Rev { background-color:#00008B; color:#FFFFFF} /* Dark Blue */
.NavBarFont1    { font-family: Arial, Helvetica, sans-serif; color:#000000;color:#000000;}
.NavBarFont1Rev { font-family: Arial, Helvetica, sans-serif; color:#FFFFFF;color:#FFFFFF;}

.NavBarCell2    { font-family: Arial, Helvetica, sans-serif; background-color:#FFFFFF; color:#000000}
.NavBarCell3    { font-family: Arial, Helvetica, sans-serif; background-color:#FFFFFF; color:#000000}

