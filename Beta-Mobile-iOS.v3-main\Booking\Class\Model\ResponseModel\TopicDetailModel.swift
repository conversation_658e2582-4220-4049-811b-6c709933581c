//
//  TopicDetailModel.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 4/13/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation
import ObjectMapper

class TopicDetailModel : Mappable {
    var Id: String?
    var ChuDe: String?
    var Title: String?
    var Content: String?
    var AnswerContent: String?
    


    required init?(map: Map) {

    }

    func mapping(map: Map) {
        Id                   <- map["Id"]
        ChuDe                <- map["ChuDe"]
        Title                <- map["Title"]
        Content              <- map["Content"]
        AnswerContent        <- map["AnswerContent"]
    }
}
