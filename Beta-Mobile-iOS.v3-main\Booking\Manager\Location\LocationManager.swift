//
//  LocationManager.swift
//  Booking-dev
//
//  Created by <PERSON><PERSON> on 5/19/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation
import CoreLocation
import Alamofire

class LocationManager: NSObject {
    static let shared = LocationManager()

    fileprivate lazy var locationManager = CLLocationManager()
    fileprivate var completionHandler: ((CLLocation?) -> Void)?
    var currentLocation: CLLocation?
    fileprivate var isShowAlert: Bool = false

    var isEnable: Bool {
        get {
            let status = CLLocationManager.authorizationStatus()
            if status == .authorizedAlways || status == .authorizedWhenInUse {
                return UserDefaults.standard.bool(forKey: DefaultKey.locationEnable.rawValue)
            }
            return false
        }
        set {
            UserDefaults.standard.set(newValue, forKey: DefaultKey.locationEnable.rawValue)
        }
    }

    override init() {
    }

    func startTracking(_ completionHandler: ((CLLocation?)-> Void)? = nil) {
        guard UserDefaults.standard.bool(forKey: DefaultKey.locationEnable.rawValue) else {
            completionHandler?(nil)
            return
        }
        let status = CLLocationManager.authorizationStatus()
        switch status {
        case .authorizedAlways, .authorizedWhenInUse:
            requestCurrentLocation(completionHandler)
        case .denied, .restricted:
            completionHandler?(nil)
            break
        case .notDetermined:
            self.completionHandler = completionHandler
            locationManager.requestWhenInUseAuthorization()
        }
    }

    func requestCurrentLocation(_ completionHandler: ((CLLocation?)-> Void)? = nil) {
        guard UserDefaults.standard.bool(forKey: DefaultKey.locationEnable.rawValue) else {
            completionHandler?(nil)
            return
        }
        self.completionHandler = completionHandler
        locationManager.delegate = self
        if CLLocationManager.locationServicesEnabled() {
            locationManager.desiredAccuracy = kCLLocationAccuracyHundredMeters
            locationManager.requestLocation()
            completionHandler?(self.currentLocation)
        } else {
            completionHandler?(nil)
        }
    }
    
    func systemEnable() -> Bool {
        if CLLocationManager.locationServicesEnabled() {
            switch CLLocationManager.authorizationStatus() {
            case .notDetermined, .restricted, .denied:
                return false
            case .authorizedAlways, .authorizedWhenInUse:
                return true
            }
        } else {
            print("Location services are not enabled")
            return false
        }
    }

    var topViewController: UIViewController? {
        if var topController = UIApplication.shared.keyWindow?.rootViewController {
            while let presentedViewController = topController.presentedViewController {
                topController = presentedViewController
            }

            return topController
        }
        return nil
    }
}

extension LocationManager: CLLocationManagerDelegate {
    func locationManager(_ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]) {
        if let location = locations.first {
            UserDefaults.standard.set(location.coordinate.latitude, forKey: Constant.Lat)
            UserDefaults.standard.set(location.coordinate.longitude, forKey: Constant.Lng)
            UserDefaults.standard.synchronize()
            self.currentLocation = location
            self.completionHandler?(location)
            self.completionHandler = nil
        }
    }

    func locationManager(_ manager: CLLocationManager, didFailWithError error: Error) {
        self.completionHandler?(nil)
        self.completionHandler = nil
    }

    func locationManager(_ manager: CLLocationManager, didChangeAuthorization status: CLAuthorizationStatus) {
        if status == .authorizedAlways || status == .authorizedWhenInUse {
            requestCurrentLocation()
        } else if status == .denied || status == .restricted {
//            openLocationAlert()
        }
    }
    
    func openLocationAlert() {
        guard isShowAlert == false else {
            return
        }
        isShowAlert = true
        let alert = UIAlertController(title: nil, message: "LocationNoPermission".localized, preferredStyle: .alert)
        let cancelAction = UIAlertAction(title: "Bt.OK".localized, style: .cancel) { action in
            
        }
        alert.addAction(cancelAction)
        topViewController?.present(alert, animated: true, completion: nil)
    }

    func getRouteBetween(_ source: CLLocation, desination: CLLocation) {
        let urlString = "https://maps.googleapis.com/maps/api/directions/json?origin=\(source.coordinate.latitude),\(source.coordinate.longitude)&destination=\(desination.coordinate.latitude),\(desination.coordinate.longitude)&sensor=true&key=\(Config.gmsApiKey)"

        Alamofire.request(urlString).responseJSON { response in

            }.responseString {
                print($0.value ?? "")
        }
    }
}
