package vn.zenity.betacineplex.view.user.point

import android.graphics.Color
import android.os.Bundle
import androidx.core.content.res.ResourcesCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import kotlinx.android.synthetic.main.fragment_voucher_history.*
import kotlinx.android.synthetic.main.item_notice_book_history.view.*
import kotlinx.android.synthetic.main.item_point_history.view.*
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.base.BaseFragment
import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.global.Constant
import vn.zenity.betacineplex.helper.extension.*
import vn.zenity.betacineplex.model.PointHistoryModel

/**
 * Created by Zenity.
 */

class PointHistoryFragment : BaseFragment(), PointHistoryContractor.View {

    private val presenter = PointHistoryPresenter()

    override fun getPresenter(): IBasePresenter<IBaseView>? {
        return presenter as? IBasePresenter<IBaseView>
    }

    override fun isShowToolbar(): Boolean {
        return false
    }

    override fun getLayoutRes(): Int {
        return R.layout.fragment_voucher_history
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        recyclerView.layoutManager = LinearLayoutManager(context)
        recyclerView.adapter = Adapter()
        tvTitle.text = getString(R.string.used_points_histories)
        presenter.getUsedPointHistories()
    }

    override fun showUsedPointHistories(histories: List<PointHistoryModel>) {
        (recyclerView.adapter as? Adapter)?.useVoucherHistories = histories
        (recyclerView.adapter as? Adapter)?.notifyDataSetChanged()
    }

    private inner class Adapter(var useVoucherHistories: List<PointHistoryModel> = listOf()) : RecyclerView.Adapter<Holder>() {

        override fun getItemCount(): Int {
            return useVoucherHistories.size + 1
        }

        override fun getItemViewType(position: Int): Int {
            return position
        }

        override fun onBindViewHolder(holder: Holder, position: Int) {
            if (position == 0) {
                holder.itemView.tvNotice.setTextWithSpecialText(
                        getString(R.string.point_history_notice), getString(R.string.the_last_3_months)) {
                    it.isUnderlineText = false
                    it.typeface = <EMAIL>?.let { it1 -> ResourcesCompat.getFont(it1, R.font.sanspro_bold) }
                    it.color = holder.itemView.tvNotice.currentTextColor
                    it.linkColor = holder.itemView.tvNotice.currentTextColor
                }
            } else {
                val point = useVoucherHistories[position - 1]
                holder.itemView.apply {
                    tvTime.text = point.Date?.dateConvertFormat(Constant.DateFormat.requestServer, showFormat = "dd/MM/yyyy,HH:mm")
                    if (TextUtils.isEmpty(point.AccountName)) {
                        tvUserGive.gone()
                    } else {
                        tvUserGive.text = "(${point.AccountName})"
                        tvUserGive.visible()
                    }
                    tvUsedPoint.text = "${if (point.Point > 0) "+" else ""}${point.Point}"
                    tvPointStatus.setTextColor(Color.parseColor(
                            when (point.StatusType) {
                                5 -> "#fd7c02"
                                2, 3, 4 -> "#fd2802"
                                6 -> "#7ed321"
                                else -> "#3fb7f9"
                            }
                    ))
                    val typeName = when (point.StatusType) {
                        1 -> R.string.earn_points
                        2 -> R.string.spend_points
                        3, 4 -> R.string.transaction_cancel
                        5 -> R.string.give_point
                        6 -> R.string.receive_point
                        else -> R.string.empty
                    }.getString()
                    tvPointStatus.text = if (TextUtils.isEmpty(typeName)) point.StatusName else typeName
                }
            }
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): Holder {
            if (viewType == 0) {
                val item = LayoutInflater.from(parent.context).inflate(R.layout.item_notice_book_history, parent, false)
                return Holder(item)
            }
            val item = LayoutInflater.from(parent.context).inflate(R.layout.item_point_history, parent, false)
            return Holder(item)
        }
    }

    private inner class Holder(itemView: View) : RecyclerView.ViewHolder(itemView)
}
