// import 'dart:async';
// import 'dart:convert';
// import 'package:flutter/foundation.dart';
// import 'dart:io';
// import 'package:flutter/material.dart';
// import 'package:flutter_app/core/index.dart';
// import 'package:flutter_app/pages/Movie_schedule/model/Film_model.dart';
// import 'package:flutter_app/pages/cinema/model/combo_list_model.dart';
// import 'package:flutter_app/pages/cinema/model/list_seat_model.dart';
// import 'package:flutter_app/pages/voucher/api/api_test.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:intl/intl.dart';
// import 'package:url_launcher/url_launcher.dart';
//
// import 'package:webview_flutter/webview_flutter.dart';
// import 'package:webview_flutter_platform_interface/webview_flutter_platform_interface.dart';
// import 'package:webview_flutter_wkwebview/webview_flutter_wkwebview.dart';
// // #docregion platform_imports
// // Import for Android features.
// // import 'package:webview_flutter_android/webview_flutter_android.dart';
// // Import for iOS/macOS features.
//
//
// import '../../../cubit/index.dart';
// import '../../../utils/index.dart';
// import 'package:webview_flutter_android/webview_flutter_android.dart';
// /// Màn hình thanh toán WebView - tương ứng với PaymentViewController trong iOS
// ///
// /// Màn hình này hiển thị trang web thanh toán và xử lý các tương tác thanh toán
// /// Nó cũng theo dõi thông tin combo, voucher và các thông tin thanh toán khác
// /// Tương ứng với PaymentViewController.swift trong repo iOS
// class WebViewPaymentScreen extends StatefulWidget {
//   final String htmlData;           // HTML data từ API booking
//   final String baseUrl;            // Base URL cho WebView
//   final FilmModel? film;           // Thông tin phim
//   final String? combo;           // Thông tin ghế
//   final ListSeatModel? listSeat;   // Thông tin ghế và suất chiếu
//   final String? cinemaId;          // ID rạp chiếu phim
//   final String? cinemaName;        // Tên rạp chiếu phim
//   final Function(String?) onPaymentSuccess;       // Callback khi thanh toán thành công
//   final Function(String?) onPaymentFailed;        // Callback khi thanh toán thất bại
//   final Function() onPaymentWaiting;              // Callback khi đang chờ thanh toán
//   final Function(String) onPaymentMethodSelected; // Callback khi chọn phương thức thanh toán
//   final int? totalPrice;           // Tổng giá tiền
//
//   const WebViewPaymentScreen({
//     super.key,
//     required this.htmlData,
//     required this.baseUrl,
//     this.film,
//     this.combo,
//     this.listSeat,
//     this.cinemaId,
//     this.totalPrice,
//     this.cinemaName,
//     required this.onPaymentSuccess,
//     required this.onPaymentFailed,
//     required this.onPaymentWaiting,
//     required this.onPaymentMethodSelected,
//   });
//
//   @override
//   _WebViewPaymentScreenState createState() => _WebViewPaymentScreenState();
// }
//
// class _WebViewPaymentScreenState extends State<WebViewPaymentScreen> {
//   /// The WebViewController for managing the WebView
//   late WebViewController _webViewController;
//
//   /// Loading state for the WebView
//   bool _isLoading = true;
//
//   /// Title of the current page in the WebView
//   String? _title;
//
//   /// Stores the AirPay order ID for tracking payment status
//   String? _airPayOrderId;
//
//   @override
//   void initState() {
//     super.initState();
//
//     /// Initialize platform-specific WebView settings
//     /// iOS and Android have different WebView implementations
//     /// that need to be configured separately
//     _initWebView();
//   }
//
//   void _initWebView() {
//     late final PlatformWebViewControllerCreationParams params;
//     if (WebViewPlatform.instance is WebKitWebViewPlatform) {
//       // iOS/macOS specific setup - tương tự iOS WKWebViewConfiguration
//       params = WebKitWebViewControllerCreationParams(
//         allowsInlineMediaPlayback: true,
//         mediaTypesRequiringUserAction: const <PlaybackMediaTypes>{},
//       );
//     } else if (WebViewPlatform.instance is AndroidWebViewPlatform) {
//       // Android specific setup
//       params = AndroidWebViewControllerCreationParams();
//     } else {
//       params = const PlatformWebViewControllerCreationParams();
//     }
//     // #enddocregion platform_setup
//
//     _webViewController = WebViewController.fromPlatformCreationParams(params)
//       ..setJavaScriptMode(JavaScriptMode.unrestricted)
//       ..setBackgroundColor(const Color(0x00000000))
//       ..enableZoom(false)
//       // ..setUserAgent(
//       //     'Mozilla/5.0 (Linux; Android 10; Mobile) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36')
//       ..setNavigationDelegate(
//         NavigationDelegate(
//           onProgress: (int progress) {
//             if (progress == 100 && mounted) {
//               setState(() {
//                 _isLoading = false;
//               });
//             }
//           },
//           onPageStarted: (String url) {
//             if (mounted) {
//               setState(() {
//                 _isLoading = true;
//               });
//             }
//           },
//           onPageFinished: (String url) {
//             print('✅ WebView page finished loading: $url');
//             if (!mounted) return;
//             setState(() {
//               _isLoading = false;
//             });
//
//             // Wait a bit for JavaScript to be fully loaded before calling methods
//             Future.delayed(const Duration(milliseconds: 1000), () {
//               _updateBookingInfo();
//             });
//
//             _webViewController.getTitle().then((title) {
//               print('📄 WebView page title: $title');
//               if (mounted) {
//                 setState(() {
//                   _title = title;
//                 });
//               }
//             });
//           },
//           onWebResourceError: (WebResourceError error) {
//             print('WebView error: ${error.description}');
//           },
//           /// Xử lý khi WebView yêu cầu điều hướng đến một URL mới - tương ứng với webView:decidePolicyFor:decisionHandler: trong iOS
//           ///
//           /// Phương thức này kiểm tra URL đích và quyết định cách xử lý:
//           /// - Nếu là URL thanh toán bên ngoài (airpay, momo, zalopay), mở trong trình duyệt bên ngoài
//           /// - Nếu là URL thanh toán nội địa hoặc quốc tế, cho phép điều hướng trong WebView
//           /// - Nếu là URL khác, cho phép điều hướng trong WebView
//           onNavigationRequest: (NavigationRequest request) async {
//             final url = request.url;
//             print("Navigation request to: $url");
//
//             // Xử lý các URL thanh toán bên ngoài - tương ứng với iOS
//             if (url.contains('airpay.vn') && await canLaunchUrl(Uri.parse(url))) {
//               widget.onPaymentMethodSelected('airpay');
//               _trackPayment('confirm');
//               _launchExternalUrl(url);
//
//               // Extract order_id from URL
//               final uri = Uri.parse(url);
//               _airPayOrderId = uri.queryParameters['order_id'];
//
//               return NavigationDecision.prevent;
//             } else if (url.contains('momo.vn') &&  await canLaunchUrl(Uri.parse(url))) {
//               widget.onPaymentMethodSelected('momo');
//               _trackPayment('confirm');
//               _launchExternalUrl(url);
//               return NavigationDecision.prevent;
//             } else if (url.contains('gateway.zalopay.vn') &&  await canLaunchUrl(Uri.parse(url))) {
//               widget.onPaymentMethodSelected('zalopay');
//               _trackPayment('confirm');
//               _launchExternalUrl(url);
//               return NavigationDecision.prevent;
//             } else if (url.contains('mtf.onepay.vn/paygate/vpcpay.op')) {
//               widget.onPaymentMethodSelected('noidia');
//               _trackPayment('confirm');
//               print('💳 Navigating to OnePay domestic payment');
//               return NavigationDecision.navigate;
//             } else if (url.contains('mtf.onepay.vn/promotion/vpcpost.op')) {
//               widget.onPaymentMethodSelected('quocte');
//               _trackPayment('confirm');
//               print('💳 Navigating to OnePay international payment');
//               return NavigationDecision.navigate;
//             } else if (url.contains('onepay.vn/paygate')) {
//               print('🏦 Navigating to OnePay paygate');
//               return NavigationDecision.navigate;
//             }
//             // Xử lý các URL Beta Voucher và Điểm BETA - không có trong iOS nhưng cần thiết cho Flutter
//             else if (url.contains('voucher') || url.contains('coupon') || url.contains('beta-point')) {
//               print("Navigating to voucher/coupon/beta-point page: $url");
//               // Cho phép điều hướng trong WebView
//               return NavigationDecision.navigate;
//             }
//
//             // Cho phép điều hướng đến các URL khác
//             return NavigationDecision.navigate;
//           },
//           onUrlChange: (UrlChange change) {
//             print('🔄 WebView URL changed: ${change.url}');
//             _handleUrlChange(change.url);
//           },
//         ),
//       )
//       // iOS-style script handler - tương tự userContentController.add(self, name: "scriptHandler")
//       ..addJavaScriptChannel(
//         'scriptHandler',
//         onMessageReceived: (JavaScriptMessage message) {
//           if (!mounted) return;
//           print('📱 Received message from iOS-style scriptHandler: ${message.message}');
//           _handleJavaScriptMessage(message.message);
//         },
//       )
//       // Android-style message handler - tương tự window.androidkit
//       ..addJavaScriptChannel(
//         'androidkit',
//         onMessageReceived: (JavaScriptMessage message) {
//           if (!mounted) return;
//           print('📱 Received message from Android-style androidkit: ${message.message}');
//           _handleJavaScriptMessage(message.message);
//         },
//       );
//
//     // Load HTML content with enhanced message handling
//     _loadEnhancedHtmlContent();
//   }
//
//   /// Inject viewport meta tag script - tương tự iOS WKUserScript injection
//   ///
//   /// Trong iOS: let jscript = "var meta = document.createElement('meta'); meta.setAttribute('name', 'viewport'); meta.setAttribute('content', 'width=device-width'); document.getElementsByTagName('head')[0].appendChild(meta);"
//   void _injectViewportScript() {
//     const viewportScript = """
//       var meta = document.createElement('meta');
//       meta.setAttribute('name', 'viewport');
//       meta.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
//       document.getElementsByTagName('head')[0].appendChild(meta);
//     """;
//
//     _webViewController.runJavaScript(viewportScript).catchError((error) {
//       print('Error injecting viewport script: $error');
//     });
//   }
//
//   /// Load HTML content with enhanced message handling - tương tự iOS configuration
//   ///
//   /// Thêm support cho cả iOS-style (webkit.messageHandlers) và Android-style (androidkit) message handlers
//   void _loadEnhancedHtmlContent() {
//     // Enhanced HTML with cross-platform message handling
//     final enhancedHtml = _enhanceHtmlWithMessageHandlers(widget.htmlData);
//
//     // Load enhanced HTML
//     _webViewController.loadHtmlString(enhancedHtml, baseUrl: widget.baseUrl);
//
//     // Inject viewport script after loading
//     Future.delayed(const Duration(milliseconds: 500), () {
//       if (mounted) {
//         _injectViewportScript();
//       }
//     });
//   }
//
//   /// Enhance HTML with cross-platform message handlers
//   ///
//   /// Thêm JavaScript functions để support cả iOS và Android message handling
//   String _enhanceHtmlWithMessageHandlers(String originalHtml) {
//     const messageHandlerScript = '''
// <script>
// // Cross-platform message handler - tương tự iOS và Android
// function sendMessage(message) {
//   console.log('Sending message:', message);
//
//   // Try iOS-style first (webkit.messageHandlers.scriptHandler)
//   try {
//     if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.scriptHandler) {
//       window.webkit.messageHandlers.scriptHandler.postMessage(message);
//       console.log('Message sent via iOS webkit.messageHandlers.scriptHandler');
//       return;
//     }
//   } catch (e) {
//     console.log('iOS webkit handler not available:', e);
//   }
//
//   // Try Flutter scriptHandler channel
//   try {
//     if (window.scriptHandler) {
//       window.scriptHandler.postMessage(message);
//       console.log('Message sent via Flutter scriptHandler');
//       return;
//     }
//   } catch (e) {
//     console.log('Flutter scriptHandler not available:', e);
//   }
//
//   // Try Android-style (androidkit)
//   try {
//     if (window.androidkit) {
//       window.androidkit.postMessage(message);
//       console.log('Message sent via Android androidkit');
//       return;
//     }
//   } catch (e) {
//     console.log('Android androidkit not available:', e);
//   }
//
//   console.warn('No message handler available');
// }
//
// // Override existing message calls to use unified sendMessage
// if (typeof CallDieuKhoan === 'undefined') {
//   var CallDieuKhoan = function() {
//     sendMessage("policy");
//   }
// }
//
// // Override other message functions if they exist
// if (typeof timeExpired !== 'undefined') {
//   var originalTimeExpired = timeExpired;
//   timeExpired = function() {
//     sendMessage("timeout");
//     if (originalTimeExpired) originalTimeExpired();
//   }
// }
// </script>
// ''';
//
//     // Insert the script before closing </head> tag or at the beginning if no head
//     if (originalHtml.contains('</head>')) {
//       return originalHtml.replaceFirst('</head>', '$messageHandlerScript</head>');
//     } else if (originalHtml.contains('<html>')) {
//       return originalHtml.replaceFirst('<html>', '<html>$messageHandlerScript');
//     } else {
//       return '$messageHandlerScript$originalHtml';
//     }
//   }
//
//   /// Handle URL changes with enhanced payment flow logic
//   ///
//   /// Xử lý các URL changes để handle đúng payment flow và tránh bị stuck
//   void _handleUrlChange(String? url) {
//     if (url == null) return;
//
//     print('🔄 Processing URL change: $url');
//
//     // Handle payment result URLs
//     if (url.contains('ketquathanhtoan')) {
//       print('📄 Payment result URL detected - processing payment result');
//       _handlePaymentResult(url);
//       return;
//     }
//
//     // Handle OnePay payment flow
//     if (url.contains('onepay.vn')) {
//       print('💳 OnePay URL detected: $url');
//       _handleOnepayFlow(url);
//       return;
//     }
//
//     // Handle about:blank - critical issue
//     if (url == 'about:blank') {
//       print('⚠️ Blank page detected - implementing recovery strategy');
//       _handleBlankPage();
//       return;
//     }
//
//     // Handle other URLs
//     print('🌐 Other URL: $url');
//   }
//
//   /// Handle OnePay payment flow URLs
//   void _handleOnepayFlow(String url) {
//     if (url.contains('/paygate/')) {
//       print('🏦 OnePay paygate detected - payment in progress');
//       // Payment gateway page - normal flow
//       setState(() {
//         _isLoading = false;
//       });
//     } else if (url.contains('/onecomm-pay/')) {
//       print('💰 OnePay onecomm-pay detected - payment initiated');
//       // Payment initiation - normal flow
//       setState(() {
//         _isLoading = false;
//       });
//     }
//   }
//
//   /// Handle payment result URL
//   void _handlePaymentResult(String url) {
//     print('✅ Processing payment result from URL: $url');
//
//     // Extract payment result from URL parameters
//     final uri = Uri.parse(url);
//     final params = uri.queryParameters;
//
//     // Check for common payment result parameters
//     if (params.containsKey('vpc_TxnResponseCode') ||
//         params.containsKey('responseCode') ||
//         params.containsKey('resultCode')) {
//
//       print('📊 Payment result parameters found: $params');
//
//       // Process payment result
//       _processPaymentResult(params);
//     } else {
//       print('⚠️ No payment result parameters found in URL');
//     }
//   }
//
//   /// Process payment result parameters
//   void _processPaymentResult(Map<String, String> params) {
//     // Common success codes for different payment gateways
//     final successCodes = ['0', '00', '000'];
//
//     String? responseCode = params['vpc_TxnResponseCode'] ??
//                           params['responseCode'] ??
//                           params['resultCode'];
//
//     if (responseCode != null && successCodes.contains(responseCode)) {
//       print('✅ Payment successful with code: $responseCode');
//       _trackPayment('success');
//       widget.onPaymentSuccess(params['transactionId'] ?? params['vpc_TransactionNo']);
//     } else {
//       print('❌ Payment failed with code: $responseCode');
//       _trackPayment('fail', responseCode, 'Payment failed');
//       widget.onPaymentFailed('Thanh toán thất bại. Mã lỗi: $responseCode');
//     }
//   }
//
//   /// Handle blank page with multiple recovery strategies
//   void _handleBlankPage() {
//     print('🔧 Implementing blank page recovery strategies');
//
//     // Strategy 1: Wait and check if it's temporary
//     Future.delayed(const Duration(seconds: 1), () async {
//       if (!mounted) return;
//
//       try {
//         final currentUrl = await _webViewController.currentUrl();
//         print('🔍 Current URL after 1 second: $currentUrl');
//
//         if (currentUrl == 'about:blank') {
//           print('🔄 Still on blank page, trying recovery strategy 2');
//           _attemptRecoveryStrategy2();
//         }
//       } catch (e) {
//         print('❌ Error checking current URL: $e');
//         _attemptRecoveryStrategy2();
//       }
//     });
//   }
//
//   /// Recovery strategy 2: Try to go back or reload
//   void _attemptRecoveryStrategy2() {
//     print('🔄 Attempting recovery strategy 2: Go back or reload');
//
//     _webViewController.canGoBack().then((canGoBack) {
//       if (canGoBack) {
//         print('⬅️ Can go back, attempting to go back');
//         _webViewController.goBack();
//
//         // Check if back was successful after 2 seconds
//         Future.delayed(const Duration(seconds: 2), () {
//           _checkRecoverySuccess();
//         });
//       } else {
//         print('🔄 Cannot go back, reloading original content');
//         _reloadOriginalContent();
//       }
//     }).catchError((error) {
//       print('❌ Error checking canGoBack: $error');
//       _reloadOriginalContent();
//     });
//   }
//
//   /// Check if recovery was successful
//   void _checkRecoverySuccess() async {
//     if (!mounted) return;
//
//     try {
//       final currentUrl = await _webViewController.currentUrl();
//       print('🔍 URL after recovery attempt: $currentUrl');
//
//       if (currentUrl == 'about:blank') {
//         print('❌ Recovery failed, reloading original content');
//         _reloadOriginalContent();
//       } else {
//         print('✅ Recovery successful, now on: $currentUrl');
//       }
//     } catch (e) {
//       print('❌ Error checking recovery success: $e');
//       _reloadOriginalContent();
//     }
//   }
//
//   /// Reload original content as final fallback
//   void _reloadOriginalContent() {
//     print('🔄 Reloading original payment content');
//
//     try {
//       _webViewController.loadHtmlString(widget.htmlData, baseUrl: widget.baseUrl);
//
//       // Re-inject scripts after reload
//       Future.delayed(const Duration(milliseconds: 1000), () {
//         if (mounted) {
//           _injectViewportScript();
//           _updateBookingInfo();
//         }
//       });
//
//       setState(() {
//         _isLoading = false;
//       });
//
//       print('✅ Original content reloaded successfully');
//     } catch (e) {
//       print('❌ Error reloading original content: $e');
//     }
//   }
//
//   void _updateBookingInfo() {
//     _webViewController.runJavaScriptReturningResult("typeof screenType !== 'undefined' ? screenType : 'unknown'").then((result) {
//       final screenTypeStr = result.toString().replaceAll('"', '');
//       if (screenTypeStr == "payment") {
//         _loadBookingInfo();
//       }
//     }).catchError((error) {
//       print('Error checking screen type: $error');
//       // Fallback: always try to load booking info
//       _loadBookingInfo();
//     });
//   }
//
//   /// Cập nhật thông tin đặt vé trong WebView - tương ứng với updateBookingInfo trong iOS
//   ///
//   /// Phương thức này gửi thông tin phim, rạp, ngày chiếu, giờ chiếu, combo, tổng tiền, phòng chiếu, poster phim
//   /// và mã định dạng phim đến WebView để hiển thị cho người dùng
//   Future<void> _loadBookingInfo() async {
//     final film = widget.film;
//     final listSeat = widget.listSeat;
//
//     if (film == null || listSeat == null) return;
//
//     try {
//       // Format date and time - tương ứng với iOS
//       final dateFormat = DateFormat('dd/MM/yyyy');
//       final timeFormat = DateFormat('HH:mm');
//
//       DateTime? date;
//       DateTime? time;
//
//       try {
//         // Trong iOS: date = listSeat?.NgayChieu?.toDate("yyyy-MM-dd'T'HH:mm:ss")
//         // Trong iOS: time = listSeat?.GioChieu?.toDate("yyyy-MM-dd'T'HH:mm:ss")
//         date = listSeat.ngayChieu != null ? DateTime.parse(listSeat.ngayChieu!) : null;
//         time = listSeat.gioChieu != null ? DateTime.parse(listSeat.gioChieu!) : null;
//       } catch (e) {
//         print('Error parsing date/time: $e');
//       }
//
//       final dateStr = date != null ? dateFormat.format(date) : '';
//       final timeStr = time != null ? timeFormat.format(time) : '';
//
//       // Kiểm tra xem các biến JavaScript đã được khởi tạo chưa
//       _webViewController.runJavaScriptReturningResult("typeof listCombo").then((result) {
//         if (result == "undefined") {
//           print("Warning: listCombo is undefined, initializing it");
//           _webViewController.runJavaScript("var listCombo = [];").catchError((error) {
//             print('Error initializing listCombo: $error');
//           });
//         }
//       }).catchError((error) {
//         print('Error checking listCombo: $error');
//       });
//
//       _webViewController.runJavaScriptReturningResult("typeof dataBooking").then((result) {
//         if (result == "undefined") {
//           print("Warning: dataBooking is undefined, initializing it");
//           _webViewController.runJavaScript("var dataBooking = {};").catchError((error) {
//             print('Error initializing dataBooking: $error');
//           });
//         }
//       }).catchError((error) {
//         print('Error checking dataBooking: $error');
//       });
//
//       // Sử dụng combo string từ widget hoặc mặc định là chuỗi rỗng
//       final combo = widget.combo ?? '';
//
//       // Build JavaScript method call - tương ứng chính xác với iOS
//       final method = "getBookingInfo({'FilmName': '${film.getName()}', "
//           "'FilmInfo': '${film.getFinalOptions()}', "
//           "'CinemaName': '${listSeat.tenRap ?? ""}', "
//           "'DateShow': '$dateStr', "
//           "'ShowTime': '$timeStr', "
//           "'Combo': '$combo', "
//           "'TotalMoney': '${_formatCurrency(widget.totalPrice ?? 0)}', "
//           "'Screen': '${listSeat.phongChieu ?? ""}', "
//           "'FilmPoster': '${ApiService.baseUrlImage}${film.MainPosterUrl ?? ""}', "
//           "'FilmFormatCode': '${listSeat.filmFormatCode ?? ""}'});";
//
//       print("Booking info method: $method");
//       _webViewController.runJavaScript(method).catchError((error) {
//         print('Error updating booking info: $error');
//       });
//
//       // Load customer info if available - tương ứng với iOS
//       final user = context.read<AuthC>().state.user;
//       if (user != null) {
//         // Trong iOS: webView.evaluateJavaScript("getCustomerInfo({'customerId': '\(user.AccountId ?? "")', 'customerCard': '\(user.CardNumber ?? "")'});")
//         final customerInfoMethod = "getCustomerInfo({'customerId': '${user.accountId ?? ""}', 'customerCard': '${user.cardNumber ?? ""}'});";
//         print("Customer info method: $customerInfoMethod");
//         _webViewController.runJavaScript(customerInfoMethod).catchError((error) {
//           print('Error updating customer info: $error');
//         });
//       }
//     } catch (e) {
//       print('Error in loadBookingInfo: $e');
//     }
//   }
//
//   String _formatCurrency(int amount) {
//     final formatter = NumberFormat("#,###", "vi_VN");
//     return formatter.format(amount);
//   }
//
//   /// Phân tích dữ liệu combo từ HTML - tương ứng với getComboByHtml trong iOS
//   ///
//   /// Phương thức này trích xuất thông tin combo từ HTML và trả về tổng số combo và tổng giá trị
//   /// @return Mảng [comboTotal, comboAmount] chứa tổng số combo và tổng giá trị
//   List<int?> getComboByHtml(String content) {
//     ComboListModel? comboList;
//
//     // Tìm và phân tích biến listCombo từ HTML
//     const pattern = "var listCombo = JSON.parse(";
//     final index = widget.htmlData.indexOf(pattern);
//     if (index != -1) {
//       var text = widget.htmlData.substring(index + pattern.length);
//       const endPattern = ");";
//       final endIndex = text.indexOf(endPattern);
//       if (endIndex != -1) {
//         text = text.substring(0, endIndex);
//         text = text.replaceAll("'", "");
//
//         try {
//           comboList = ComboListModel.fromJsonString(text);
//         } catch (e) {
//           print('Error parsing combo list: $e');
//         }
//       }
//     }
//
//     int? comboTotal;
//     int? comboAmount;
//
//     if (comboList?.elements != null) {
//       for (var cb in comboList!.elements!) {
//         final comboName = cb.combo?.name;
//         final comboPrice = cb.combo?.priceAfterVAT;
//
//         if (comboName != null && comboPrice != null) {
//           final nameIndex = content.indexOf(comboName);
//           if (nameIndex != -1) {
//             var text = content.substring(nameIndex + comboName.length);
//             const quantityPattern = "combo-quantity";
//             final quantityIndex = text.indexOf(quantityPattern);
//
//             if (quantityIndex != -1) {
//               text = text.substring(quantityIndex + quantityPattern.length);
//               final startIndex = text.indexOf(">");
//
//               if (startIndex != -1) {
//                 text = text.substring(startIndex + 1);
//                 final endIndex = text.indexOf("<");
//
//                 if (endIndex != -1) {
//                   text = text.substring(0, endIndex);
//                   final quantity = int.tryParse(text);
//
//                   if (quantity != null) {
//                     comboTotal = (comboTotal ?? 0) + quantity;
//                     comboAmount = (comboAmount ?? 0) + (quantity * comboPrice);
//                   }
//                 }
//               }
//             }
//           }
//         }
//       }
//     }
//
//     return [comboTotal, comboAmount];
//   }
//
//   Future<void> _launchExternalUrl(String url) async {
//     if (await canLaunchUrl(Uri.parse(url))) {
//       await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
//     } else {
//       print('Could not launch $url');
//     }
//   }
//
//   void _handleJavaScriptMessage(String message) {
//     print('📱 Received message from JavaScript: $message');
//
//     try {
//       // Try to parse as JSON first
//       final Map<String, dynamic> jsonData = jsonDecode(message);
//
//       if (jsonData.containsKey('type')) {
//         final String type = jsonData['type'];
//
//         if (type == 'payment_success') {
//           _trackPayment('success');
//           _getTransactionId();
//         } else if (type == 'payment_failed') {
//           final String errorMsg = jsonData['message'] ?? 'Thanh toán thất bại';
//           _trackPayment('fail', jsonData['code'], errorMsg);
//           widget.onPaymentFailed(errorMsg);
//         } else if (type == 'booking_seat_failed') {
//           final String errorMsg = jsonData['message'] ?? 'Đặt ghế thất bại';
//           widget.onPaymentFailed(errorMsg);
//         } else if (type == 'awaiting_payment') {
//           widget.onPaymentWaiting();
//         } else if (type == 'confirm') {
//           // Xử lý khi người dùng xác nhận thanh toán - tương ứng với iOS
//           _webViewController.runJavaScriptReturningResult("document.documentElement.innerHTML.toString()").then((html) {
//             if (html is String) {
//               // Lấy thông tin combo từ HTML
//               final comboInfo = getComboByHtml(html);
//               _totalCombos = comboInfo[0];
//               _totalComboAmount = comboInfo[1];
//
//               // Lấy các thông tin khác từ HTML
//               String? totalAmount = _getElementByClassName(html, 'total-money-name');
//               totalAmount = totalAmount?.replaceAll(",", "").replaceAll("đ", "");
//               _totalAmount = int.tryParse(totalAmount ?? "");
//
//               String? discountAmount = _getElementByClassName(html, 'coupon-discount');
//               discountAmount = discountAmount?.replaceAll(",", "").replaceAll("đ", "");
//               _discountAmount = int.tryParse(discountAmount ?? "");
//
//               String? paymentAmount = _getElementByClassName(html, 'money-need-pay');
//               paymentAmount = paymentAmount?.replaceAll(",", "").replaceAll("đ", "");
//               _paymentAmount = int.tryParse(paymentAmount ?? "");
//
//               String? redeemVouchers = _getElementByClassName(html, 'beta-voucher-value');
//               redeemVouchers = redeemVouchers?.replaceAll(",", "").replaceAll("đ", "");
//               _redeemVouchers = int.tryParse(redeemVouchers ?? "");
//
//               String? redeemPoints = _getElementByClassName(html, 'beta-point-value');
//               redeemPoints = redeemPoints?.replaceAll(",", "").replaceAll("đ", "");
//               _redeemPoints = int.tryParse(redeemPoints ?? "");
//
//               print("Combo info: Total combos: $_totalCombos, Total amount: $_totalComboAmount");
//               print("Payment info: Total: $_totalAmount, Discount: $_discountAmount, Payment: $_paymentAmount");
//               print("Vouchers: $_redeemVouchers, Points: $_redeemPoints");
//
//               // Gọi _trackPayment sau khi đã cập nhật các biến
//               _trackPayment('confirm');
//             }
//           }).catchError((error) {
//             print('Error getting HTML content: $error');
//           });
//         }
//       }
//     } catch (e) {
//       // If not JSON, handle as string
//       if (message == 'payment_susccess' || message == 'payment_success') {
//         _trackPayment('success');
//         _getTransactionId();
//       } else if (message == 'payment_failed') {
//         _trackPayment('fail', message, 'Thanh toán thất bại');
//         widget.onPaymentFailed('Thanh toán thất bại');
//       } else if (message == 'booking_seat_failed') {
//         widget.onPaymentFailed('Đặt ghế thất bại');
//       } else if (message == 'awaiting_payment') {
//         widget.onPaymentWaiting();
//       } else if (message == 'policy') {
//         /// Navigate to terms and conditions page
//         /// This opens the terms and conditions in a browser window
//         _launchExternalUrl('${widget.baseUrl}/terms-and-conditions');
//       }
//     }
//   }
//
//   /// Lấy giá trị của phần tử HTML theo tên class - tương ứng với getElementByClassName trong iOS
//   String? _getElementByClassName(String content, String className) {
//     final pattern = 'class="$className"';
//     final index = content.indexOf(pattern);
//     if (index != -1) {
//       var text = content.substring(index + pattern.length);
//       final startIndex = text.indexOf(">");
//       if (startIndex != -1) {
//         text = text.substring(startIndex + 1);
//         final endIndex = text.indexOf("<");
//         if (endIndex != -1) {
//           return text.substring(0, endIndex).trim();
//         }
//       }
//     }
//     return null;
//   }
//
//   void _getTransactionId() {
//     _webViewController.runJavaScriptReturningResult("getTransactionId();").then((result) {
//       if (result is String) {
//         widget.onPaymentSuccess(result);
//       } else {
//         widget.onPaymentSuccess(null);
//       }
//     }).catchError((error) {
//       print('Error getting transaction ID: $error');
//       widget.onPaymentSuccess(null);
//     });
//   }
//
//   // Biến để lưu trữ thông tin combo và thanh toán - tương ứng với iOS
//   int? _totalCombos;
//   int? _totalComboAmount;
//   int? _totalAmount;
//   int? _discountAmount;
//   int? _paymentAmount;
//   int? _redeemVouchers;
//   int? _redeemPoints;
//
//   /// Tracks payment events for analytics
//   ///
//   /// This method logs payment events to help track user behavior and payment success rates
//   /// @param type The type of payment event (confirm, success, fail)
//   /// @param errorCode Optional error code if payment failed
//   /// @param errorMsg Optional error message if payment failed
//   void _trackPayment(String type, [String? errorCode, String? errorMsg]) {
//     final film = widget.film;
//     final totalPrice = widget.totalPrice;
//     final listSeat = widget.listSeat;
//
//     // Tính toán số lượng ghế theo loại - tương ứng với iOS
//     final normalSeats = listSeat?.showSeats?.where((seat) =>
//       seat.seatType?.isNormal == true).length ?? 0;
//     final vipSeats = listSeat?.showSeats?.where((seat) =>
//       seat.seatType?.isVip == true).length ?? 0;
//     final doubleSeats = listSeat?.showSeats?.where((seat) =>
//       seat.seatType?.isCouple == true).length ?? 0;
//     final totalSeats = normalSeats + vipSeats + doubleSeats;
//
//     // Create event data
//     final Map<String, dynamic> eventData = {
//       'event': 'payment_$type',
//       'payment_method': _title ?? 'unknown',
//       'amount': totalPrice,
//       'film_id': film?.FilmId,
//       'film_name': film?.Name,
//       'cinema_id': widget.cinemaId,
//       'cinema_name': widget.cinemaName,
//       'normal_seats': normalSeats,
//       'vip_seats': vipSeats,
//       'double_seats': doubleSeats,
//       'total_seats': totalSeats,
//       'total_amount': _totalAmount,
//       'discount_amount': _discountAmount,
//       'payment_amount': _paymentAmount,
//       'total_combos': _totalCombos,
//       'total_combo_amount': _totalComboAmount,
//       'redeem_vouchers': _redeemVouchers,
//       'redeem_points': _redeemPoints,
//     };
//
//     // Add error information if available
//     if (errorCode != null) {
//       eventData['error_code'] = errorCode;
//     }
//
//     if (errorMsg != null) {
//       eventData['error_message'] = errorMsg;
//     }
//
//     // Log the event
//     print('Payment tracking: $eventData');
//
//     // In a real implementation, this would send the data to an analytics service
//     // For example: FirebaseAnalytics.instance.logEvent(name: 'payment_$type', parameters: eventData);
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     // Sử dụng WillPopScope vì PopScope có vấn đề với onPopInvokedWithResult
//     // Mặc dù WillPopScope đã deprecated, nhưng nó vẫn hoạt động tốt và chúng ta đang tập trung vào việc sửa lỗi với WebView
//     return WillPopScope(
//       onWillPop: () async {
//         final shouldPop = await _handleBackPress();
//         return shouldPop;
//       },
//       child: Scaffold(
//         appBar: appBar(
//           title:_title ?? 'Thanh toán' ,
//           titleColor: Colors.white,
//           leading: IconButton(
//             icon: const Icon(Icons.arrow_back_ios),
//             onPressed: () async {
//               final shouldPop = await _handleBackPress();
//               if (shouldPop && context.mounted) {
//                 Navigator.of(context).pop();
//               }
//             },
//           ),
//         ),
//         body: SafeArea(
//           child: Stack(
//             children: [
//               WebViewWidget(controller: _webViewController),
//               if (_isLoading)
//                 Container(
//                   color: Colors.white.withAlpha(204), // 0.8 * 255 = 204
//                   child: const Center(
//                     child: Column(
//                       mainAxisSize: MainAxisSize.min,
//                       children: [
//                         CircularProgressIndicator(
//                           valueColor: AlwaysStoppedAnimation<Color>(Colors.red),
//                         ),
//                         SizedBox(height: 16),
//                         Text(
//                           'Đang tải thông tin thanh toán...',
//                           style: TextStyle(
//                             fontSize: 16,
//                             fontWeight: FontWeight.w500,
//                           ),
//                         ),
//                       ],
//                     ),
//                   ),
//                 ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }
//
//   /// Xử lý khi người dùng nhấn nút back - tương ứng với leftButtonPressed trong iOS
//   ///
//   /// Phương thức này kiểm tra loại màn hình hiện tại và xử lý tương ứng:
//   /// - Nếu đang ở màn hình voucher, coupon hoặc beta-point, gọi backToMain() để quay lại trang thanh toán
//   /// - Nếu đang ở màn hình khác và có thể quay lại, gọi goBack()
//   /// - Nếu đang ở URL khác với baseUrl, tải lại HTML gốc
//   /// - Nếu đang ở trang thanh toán chính, hiển thị hộp thoại xác nhận hủy thanh toán
//   Future<bool> _handleBackPress() async {
//     try {
//       // Kiểm tra loại màn hình hiện tại bằng cách gọi JavaScript screenType với error handling
//       final screenType = await _webViewController.runJavaScriptReturningResult("typeof screenType !== 'undefined' ? screenType : 'unknown'");
//
//        // Chuyển đổi kết quả JavaScript thành chuỗi và loại bỏ dấu ngoặc kép
//       final screenTypeStr = screenType.toString().replaceAll('"', '');
//       print("Current screen type: $screenTypeStr");
//
//       // Nếu đang ở màn hình voucher, coupon hoặc beta-point, gọi backToMain() để quay lại trang thanh toán
//       if (screenTypeStr == "voucher" || screenTypeStr == "coupon" || screenTypeStr == "beta-point") {
//         await _webViewController.runJavaScript("backToMain();");
//         return false;
//       }
//
//       // Nếu đang ở màn hình khác và có thể quay lại
//       if (await _webViewController.canGoBack()) {
//         _webViewController.goBack();
//         return false;
//       } else {
//         // Nếu đang ở URL khác với baseUrl, tải lại HTML gốc
//         final currentUrl = await _webViewController.currentUrl();
//         if (currentUrl != null && !currentUrl.contains(widget.baseUrl)) {
//           _webViewController.loadHtmlString(widget.htmlData, baseUrl: widget.baseUrl);
//           return false;
//         } else {
//           // Nếu đang ở trang thanh toán chính, hiển thị hộp thoại xác nhận hủy thanh toán
//           bool shouldPop = false;
//           await UDialog().showConfirm(
//             title: 'Hủy thanh toán',
//             text: 'Bạn có chắc chắn muốn hủy thanh toán không?',
//             btnOkText: 'Có',
//             myContext: context,
//             btnCancelText: 'Không',
//             btnOkOnPress: () {
//               Navigator.of(context).pop();
//               shouldPop = true;
//             },
//             btnCancelOnPress: () {
//               Navigator.of(context).pop();
//             },
//           );
//           // await showDialog(
//           //   context: context,
//           //   builder: (context) => AlertDialog(
//           //     title: const Text('Hủy thanh toán'),
//           //     content: const Text('Bạn có chắc chắn muốn hủy thanh toán không?'),
//           //     actions: [
//           //       TextButton(
//           //         onPressed: () {
//           //           Navigator.of(context).pop();
//           //         },
//           //         child: const Text('Không'),
//           //       ),
//           //       TextButton(
//           //         onPressed: () {
//           //           shouldPop = true;
//           //           Navigator.of(context).pop();
//           //         },
//           //         child: const Text('Có'),
//           //       ),
//           //     ],
//           //   ),
//           // );
//
//           return shouldPop;
//         }
//       }
//     } catch (e) {
//       print("Error in _handleBackPress: $e");
//
//       // Enhanced error handling for WebView navigation
//       try {
//         // First try to check current URL
//         final currentUrl = await _webViewController.currentUrl();
//         print("Current URL: $currentUrl");
//
//         // Enhanced handling for external payment URLs
//         if (currentUrl != null) {
//           if (currentUrl.contains('onepay.vn') || currentUrl.contains('paygate')) {
//             print('🔄 On OnePay URL, attempting smart navigation');
//             if (await _webViewController.canGoBack()) {
//               _webViewController.goBack();
//               return false;
//             } else {
//               print('🔄 Cannot go back from OnePay, reloading original content');
//               _reloadOriginalContent();
//               return false;
//             }
//           } else if (currentUrl == 'about:blank') {
//             print('🔄 On blank page, implementing recovery');
//             _handleBlankPage();
//             return false;
//           } else if (currentUrl.contains('ketquathanhtoan')) {
//             print('🔄 On payment result page, processing result');
//             _handlePaymentResult(currentUrl);
//             return false;
//           }
//         }
//
//         // For other cases, try normal back navigation
//         if (await _webViewController.canGoBack()) {
//           _webViewController.goBack();
//           return false;
//         }
//       } catch (backError) {
//         print("Error in fallback navigation: $backError");
//       }
//
//       // Final fallback: Show confirmation dialog
//       if (context.mounted) {
//         bool shouldPop = false;
//         await UDialog().showConfirm(
//           title: 'Hủy thanh toán',
//           text: 'Có lỗi xảy ra với trang thanh toán. Bạn có muốn quay lại không?',
//           btnOkText: 'Có',
//           myContext: context,
//           btnCancelText: 'Không',
//           btnOkOnPress: () {
//             Navigator.of(context).pop();
//             shouldPop = true;
//           },
//           btnCancelOnPress: () {
//             Navigator.of(context).pop();
//           },
//         );
//         return shouldPop;
//       }
//
//       return true;
//     }
//   }
// }
