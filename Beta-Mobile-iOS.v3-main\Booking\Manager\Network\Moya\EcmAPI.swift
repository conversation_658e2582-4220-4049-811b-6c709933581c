//
//  EcmAPI.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 4/5/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation
import Moya


public enum Ecm{
    case getNewPromotion
    case getNewEvent
    case getNotification
    case getRecruitment(Int?,Int?)
    
    case getNewForCategory(String,Int?,Int?)
    case getNewWithId(String, Int?, Int?)
    case getTopic
    case getFAQs(String)
    
    case getTermId
    case getPaymentPolicyId
    case getSecurityId
    case getCompanyInfoId

    case getNotificationCount(String)
    case getPrice
    
    case getNotificationByUserID(String)
    case getNumberUnread(String)
    case updateRead(Int, Int)
    case getNotificationDetail(Int)

    case getFreeVoucher
}

let EcmProvider = MoyaProvider<Ecm>(plugins: [NetworkLoggerPlugin(verbose: true, responseDataFormatter: nil)])

extension Ecm: TargetType {
    
    public var baseURL: URL { return URL(string: Config.BaseURL)! }
    
    
    public var path: String {
        switch self {
        case .getNewPromotion:
            var path = "api/v1/ecm/categories/news-promotion"
            if Utils.shared.isEng(){
                path = path + "/en"
            }
            return path
        case .getNewEvent:
            var path = "api/v1/ecm/categories/news-events"
            if Utils.shared.isEng(){
                path = path + "/en"
            }
            return path
        case .getNotification:
            var path = "api/v1/ecm/categories/notifications"
            if Utils.shared.isEng(){
                path = path + "/en"
            }
            return path
        case .getRecruitment(_,_):
            var path = "api/v1/ecm/tuyendung"
            path = path + (Utils.shared.isEng() ? "/en" : "/vi")
            return path
        case .getTopic:
            var path = "api/v1/ecm/topics"
            path = path + (Utils.shared.isEng() ? "/en" : "/vi")
            return path
        case .getNewForCategory(let id, _, _):
            return "api/v1/ecm/\(id)/news"
        case .getNewWithId(let id, _, _):
            return "api/v1/ecm/{\(id)}/news"
        case .getFAQs(let id):
            return "api/v1/ecm/{\(id)}/faqs"
        case .getTermId, .getSecurityId, .getPaymentPolicyId, .getCompanyInfoId:
            return "api/v1/ecm/parameter"
        case .getNotificationCount(_):
            var path = "api/v1/ecm/categories/count/notifications"
            if Utils.shared.isEng() {
                path += "/en"
            }
            return path
        case .getPrice:
            return "api/v1/ecm/parameter"
            
        case .getNotificationByUserID:
            return "api/v2/erp/notifications"
        case .getNumberUnread(let userId):
            return "api/v1/erp/notifications/\(userId)/total-unread"
        case .updateRead:
            return "api/v2/erp/notifications/read-status"
        case .getNotificationDetail(let id):
            return "api/v1/erp/notifications/\(id)/campaign-detail"

        case .getFreeVoucher:
            return "api/v1/ecm/voucher-public/news"
        }
    }
    public var method: Moya.Method {
        switch self {
        case .updateRead(_):
            return .put
        default:
            return .get
        }
    }
    public var parameters: [String: Any]? {
        switch self {
        case .getFreeVoucher:
            return ["pageSize": Config.PageSize,
                    "pageNumber": 1]
        case .getRecruitment(let pageSize, let pageNumber):
            var size = Config.PageSize
            if let pageSize = pageSize{
                size = pageSize
            }
            var num = 1
            if let pageNumber = pageNumber{
                num = pageNumber
            }
            return ["pageSize": size,
                    "pageNumber": num]
            
        case .getNewWithId(_,let pageSize,let pageNumber):
            if pageSize == nil && pageNumber == nil {
                return [:]
            }
            var size = Config.PageSize
            if let pageSize = pageSize{
                size = pageSize
            }
            var num = 1
            if let pageNumber = pageNumber{
                num = pageNumber
            }
            return ["pageSize": size,
                    "pageNumber": num]
            
        case .getNewForCategory(_,let pageSize,let pageNumber):
            var size = Config.PageSize
            if let pageSize = pageSize{
                size = pageSize
            }
            var num = 1
            if let pageNumber = pageNumber{
                num = pageNumber
            }
            
            /// Fake request for recruitment
            if size == 99, num == 99{
                return [:]
            }
            
            return ["pageSize": size,
                    "pageNumber": num]
        case .getTermId:
            var value = "mobile:app:dieukhoan:"
            value = value + (Utils.shared.isEng() ? "en" : "vi")
            return ["code": value]
        case .getPaymentPolicyId:
            var value = "mobile:app:dieukhoan-thanhtoan:"
            value = value + (Utils.shared.isEng() ? "en" : "vi")
            return ["code": value]
        case .getSecurityId:
            var value = "mobile:app:dieukhoan-baomat:"
            value = value + (Utils.shared.isEng() ? "en" : "vi")
            return ["code": value]
        case .getCompanyInfoId:
            var value = "mobile:app:thongtin-congty:"
            value = value + (Utils.shared.isEng() ? "en" : "vi")
            return ["code": value]
        case .getPrice:
            var value = "mobile:app:giave:"
            value = value + (Utils.shared.isEng() ? "en" : "vi")
            return ["code": value]
        case .getNotificationCount(let date):
            return ["dt": date]
        case .updateRead(let id, let code):
            return ["Id": id,
                    "ScreenCode": code]
        default:
            return [:]
        }
    }

    public var task: Task {
        switch self {
        case .updateRead:
            return Task.requestParameters(parameters: self.parameters!, encoding: JSONEncoding.default)
        default:
            return Task.requestParameters(parameters: self.parameters!, encoding: URLEncoding.default)
        }
    }

    public var headers: [String : String]? {
        return Global.shared.headers()
    }
    
    public var sampleData: Data{
        return "".data(using: String.Encoding.utf8)!
    }
}
