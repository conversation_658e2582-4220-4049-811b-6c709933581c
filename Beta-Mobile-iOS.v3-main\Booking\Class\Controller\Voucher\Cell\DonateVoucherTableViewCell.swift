//
//  DonateVoucherTableViewCell.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 8/3/19.
//  Copyright © 2019 ddkc. All rights reserved.
//

import UIKit

class DonateVoucherTableViewCell: UITableViewCell {

    @IBOutlet weak var avatarImageView: RoundImageView!
    @IBOutlet weak var nameLabel: UILabel!
    @IBOutlet weak var emailLabel: UILabel!
    @IBOutlet weak var donateButton: RoundButton!

    private var indexPath: IndexPath?
    var donateHandler: (IndexPath) -> Void = {_ in}
    
    override func awakeFromNib() {
        super.awakeFromNib()
        // Initialization code
    }

    func configure(_ user: ShortUser, index: IndexPath) {
        indexPath = index
        let csCopy = CharacterSet(bitmapRepresentation: CharacterSet.urlPathAllowed.bitmapRepresentation)
        let imageURL = Config.BaseURLImage + (user.avatar?.addingPercentEncoding(withAllowedCharacters: csCopy) ?? "")
        if let url = URL(string: imageURL) {
            avatarImageView.af_setImage(withURL: url)
        }
        nameLabel.text = user.name
        emailLabel.text = user.email
    }
    
    @IBAction func didTapDonate(_ sender: Any) {
        guard let index = indexPath else {
            return
        }
        donateHandler(index)
    }

}
