//
//  FilmAPI.swift
//  Booking-dev
//
//  Created by <PERSON><PERSON> on 4/3/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation
import Moya
import Alamofire

class DefaultAlamofireManager: Alamofire.SessionManager {
    static let sharedManager: DefaultAlamofireManager = {
        let configuration = URLSessionConfiguration.default
        configuration.httpAdditionalHeaders = Alamofire.SessionManager.defaultHTTPHeaders
        configuration.timeoutIntervalForRequest = 20 // as seconds, you can set your request timeout
        configuration.timeoutIntervalForResource = 20 // as seconds, you can set your resource timeout
        configuration.requestCachePolicy = .useProtocolCachePolicy
        return DefaultAlamofireManager(configuration: configuration)
    }()
}

public enum Film{
    case listFilm(Bool?)
    case filmDetail(String)
    case showFilm(Bool?)
    case filmShowDate(String)
    case filmShow(String, String)
    case showSeat(String)
    case booking(CreateBookingModel)
    case banner
}

let FilmProvider = MoyaProvider<Film>(manager: DefaultAlamofireManager.sharedManager, plugins: [NetworkLoggerPlugin(verbose: true, responseDataFormatter: nil)])

extension Film: TargetType {
    
    public var baseURL: URL { return URL(string: Config.BaseURL)! }
    
    
    public var path: String {
        switch self {
        case .listFilm(_):
            return "api/v2/erp/films"
        case .filmDetail(let id):
            return "api/v2/erp/films/{\(id)}"
        case .showFilm:
            return "api/v1/erp/shows/films"
        case .filmShowDate(let id):
            return "api/v2/erp/films/{\(id)}/show-dates"
        case .filmShow(let id, _):
            return "api/v2/erp/films/{\(id)}/shows"
        case .showSeat(let id):
            return "api/v1/erp/shows/{\(id)}"
        case .booking(_):
            return "booking"
        case .banner:
            return "api/v1/erp/banner-slider"
        }                                                   
    }
    public var method: Moya.Method {
        switch self {
        case .booking(_):
            return .post
        default:
            return .get
        }
    }
    public var parameters: [String: Any]? {
        switch self {
        case .listFilm(let isShowing):
            var params: [String: Any] = [:]
            if let isShowing = isShowing{
                params["isShowing"] = "\(isShowing.toString())"
            }
            return params
        case .filmDetail(_):
            return [:]
        case .showFilm(let sneakShow):
            var params: [String: Any] = [:]
            if let sneakShow = sneakShow{
                params["sneakShow"] = "\(sneakShow.toString())"
            }
            return params
        case .filmShowDate(_):
            return [:]
        case .filmShow(_, let date):
            return ["dateShow": date]
        case .booking(let bookObj):
            return bookObj.toJSON()
        default:
            return [:]
        }
    }
    public var task: Task {
        switch self {
        case .booking(_):
            return Task.requestParameters(parameters: self.parameters!, encoding: JSONEncoding.default)
        default:
            return Task.requestParameters(parameters: self.parameters!, encoding: URLEncoding.default)
        }
    }
    public var headers: [String : String]? {
        return Global.shared.headers()
    }
    
    public var sampleData: Data{
        return "".data(using: String.Encoding.utf8)!
    }
}

