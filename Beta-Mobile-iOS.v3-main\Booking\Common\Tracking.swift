//
//  Tracking.swift
//  Booking
//
//  Created by  baothg on 16/08/2022.
//  Copyright © 2022 ddkc. All rights reserved.
//

import Foundation
import Mixpanel
import FirebaseAnalytics

class Tracking {
    static let shared = Tracking()
    var lastTheature: String?
    
    init(){
        
    }
    
    func setToken() {
        Mixpanel.initialize(token: Config.MixpanelToken, trackAutomaticEvents: true)
    }
    
    func track(event: String?, properties: Properties? = nil) {
        let current = UNUserNotificationCenter.current()
        current.getNotificationSettings(completionHandler: { permission in
            if(permission.authorizationStatus == .authorized){
                self.trackWithPush(pushEnabled: true, event: event, properties: properties)
            }else{
                self.trackWithPush(pushEnabled: false, event: event, properties: properties)
            }
        })
    }
    
    func trackWithPush(pushEnabled: Bool, event: String?, properties: Properties? = nil){
        if let distinctId = Global.shared.user?.UserId {
            let create = Global.shared.user?.CreatedOnDate;
            let avatar = Global.shared.user?.Picture;
            let email = Global.shared.user?.Email;
            let name = Global.shared.user?.FullName;
            let phone = Global.shared.user?.PhoneOffice;
            let gender = Global.shared.user?.Gender;
            let dob = Global.shared.user?.BirthDate;
            let city = Global.shared.user?.AddressCity;
            let userId = distinctId
            let lastPurchaseDate = Date()
            let membershipClass = Global.shared.user?.ClassName;
            let membershipPointAccumulated = Global.shared.user?.TotalAccumulatedPoints;
            let membershipPointAvailable = Global.shared.user?.AvailablePoint;
            let membershipTotalPoint = Global.shared.user?.TotalPoint;
            
            let dobDate = dob?.toDate("yyyy-MM-dd'T'HH:mm:ss")
            let ddob = dobDate?.day
            let mdob = dobDate?.month
            let ydob = dobDate?.year
            
            //Mixpanel
            Mixpanel.mainInstance().identify(distinctId:distinctId )
            var properties: Properties = [
                "$distinct_id": distinctId,
                "$create": create,
                "$avatar": avatar,
                "$email": email,
                "$name": name,
                "$phone": phone,
                "gender": gender,
                "dob": dob,
                "ddob": ddob,
                "mdob": mdob,
                "ydob": ydob,
                "membership_class":membershipClass,
                "point_accumulated":membershipPointAccumulated,
                "point_available":membershipPointAvailable,
                "total_point":membershipTotalPoint,
                "city": city,
                "push_enabled": pushEnabled,
                //            "membership_class": "",
                //            "membership_point_accumulated":"",
                //            "membership_point_available":"",
                //            "membership_point_used":"",
                "user_id": userId,
            ]
            //confirm_payment
            if event == "pay_success" {
                properties["last_purchase_date"] =  lastPurchaseDate
            }
            if let lastTheature = lastTheature {
                properties["last_theature"] = lastTheature
            }
            Mixpanel.mainInstance().people.set(properties:  properties)
            
            
            //Analytics
            Analytics.setUserID(distinctId)
            Analytics.setUserProperty(distinctId, forName: "$distinct_id")
            Analytics.setUserProperty(create, forName: "$create")
            Analytics.setUserProperty(avatar, forName: "$avatar")
            Analytics.setUserProperty(email, forName: "$email")
            Analytics.setUserProperty(name, forName: "$name")
            Analytics.setUserProperty(phone, forName: "$phone")
            if let gender = gender {
                Analytics.setUserProperty(String(gender), forName: "gender")
            }
            Analytics.setUserProperty(dob, forName: "dob")

            if let ddob = ddob {
                Analytics.setUserProperty(String(ddob), forName: "ddob")
            }
            
            if let mdob = mdob {
                Analytics.setUserProperty(String(mdob), forName: "mdob")
            }
            
            if let ydob = ydob {
                Analytics.setUserProperty(String(ydob), forName: "ydob")
            }
            
            Analytics.setUserProperty(membershipClass, forName: "membership_class")
            if let membershipPointAccumulated = membershipPointAccumulated {
              Analytics.setUserProperty(String(membershipPointAccumulated), forName: "point_accumulated")
            }
            if let membershipPointAvailable = membershipPointAvailable {
                Analytics.setUserProperty(String(membershipPointAvailable), forName: "point_available")
            }
            if let membershipTotalPoint = membershipTotalPoint {
                Analytics.setUserProperty(String(membershipTotalPoint), forName: "total_point")
            }
            
            Analytics.setUserProperty(city, forName: "city")
            Analytics.setUserProperty(String(pushEnabled), forName: "push_enabled")
            Analytics.setUserProperty(userId, forName: "user_id")
       
            if event == "pay_success" {
                Analytics.setUserProperty(lastPurchaseDate.toString(), forName: "last_purchase_date")
            }
        }
        if let event = event {
            Mixpanel.mainInstance().track(event: event,properties: properties)
            Analytics.logEvent(event, parameters: properties)
            print("tracking: \(event)")
        }
    }
    
    
    //user is required to sign-in/sign-up
    func authBegin(method: String?) {
        return  track(event: "auth_begin", properties: ["method": method])
    }
    
    //user sign-up success
    func authComplete(method: String?) {
        return  track(event: "auth_complete", properties: ["method": method])
    }
    
    //user user select a theater
    func selectTheater(cinemaId: String?, cinemaName: String?) {
        lastTheature = cinemaName
        return  track(event: "select_theater", properties: ["cinema_id": cinemaId,"cinema_name":cinemaName])
    }
    
    //user user view movie details and showtimes
    func selectMovie(movieId: String?, movieName: String?) {
        return  track(event: "select_movie", properties: ["movie_id": movieId,"movie_name":movieName])
    }
    
    //user complete selecting a showtime
    func selectShowtimeComplete(cinemaId: String?, cinemaName: String?, movieId: String?, movieName: String?, date: Date?, time: Date?) {
        return  track(event: "select_showtime_complete", properties: ["cinema_id": cinemaId,"cinema_name":cinemaName, "movie_id":movieId,"movie_name": movieName, "date": date,"time":time ])
    }
    
    //user complete selecting seats
    func selectSeatComplete(cinemaId: String?, cinemaName: String?, movieId: String?, movieName: String?, date: Date?, time: Date?, screen: String?, normalSeats: Int?, vipSeats: Int?, doubleSeats: Int?, totalSeats: Int?, totalAmount: Int? ) {
        return  track(event: "select_seat_complete", properties:  [
            "cinema_id": cinemaId,
            "cinema_name":cinemaName,
            "movie_id":movieId,
            "movie_name": movieName,
            "date": date,
            "time":time,
            "screen":screen,
            "normal_seats":normalSeats,
            "vip_seats":vipSeats,
            "double_seats":doubleSeats,
            "total_seats":totalSeats,
            "total_amount": totalAmount ])
    }
    
    //user confirm payment
    func confirmPayment(cinemaId: String?, cinemaName: String?, movieId: String?, movieName: String?, date: Date?, time: Date?, screen: String?, normalSeats: Int?, vipSeats: Int?, doubleSeats: Int?, totalSeats: Int?, totalAmount: Int?, discountAmount: Int?, paymentAmount: Int?, totalCombos: Int? ,totalComboAmount: Int?,redeemVouchers: Int?,redeemPoints: Int?,  paymentMethod: String?,orderId: String?, channel: String?   ) {
        return  track(event: "confirm_payment", properties:  [
            "cinema_id": cinemaId,
            "cinema_name":cinemaName,
            "movie_id":movieId,
            "movie_name": movieName,
            "date": date,
            "time":time,
            "screen": screen,
            "normal_seats":normalSeats,
            "vip_seats":vipSeats,
            "double_seats":doubleSeats,
            "total_seats":totalSeats,
            "total_amount": totalAmount,
            "discount_amount":discountAmount,
            "payment_amount":paymentAmount,
            "total_combos":totalCombos,
            "total_combo_amount":totalComboAmount,
            "redeem_vouchers":redeemVouchers,
            "redeem_points":redeemPoints,
            "payment_method":paymentMethod,
            "order_id":orderId,
            "channel":channel  ])
    }
    
    //user pay order success
    func paySuccess(cinemaId: String?, cinemaName: String?, movieId: String?, movieName: String?, date: Date?, time: Date?, screen: String?, normalSeats: Int?, vipSeats: Int?, doubleSeats: Int?, totalSeats: Int?, totalAmount: Int?, discountAmount: Int?, paymentAmount: Int?, totalCombos: Int?,totalComboAmount: Int?,redeemVouchers: Int?,redeemPoints: Int?,  paymentMethod: String?,orderId: String?, channel: String?    ) {
        return  track(event: "pay_success", properties:  [
            "cinema_id": cinemaId,
            "cinema_name":cinemaName,
            "movie_id":movieId,
            "movie_name": movieName,
            "date": date,
            "time":time,
            "screen":screen,
            "normal_seats":normalSeats,
            "vip_seats":vipSeats,
            "double_seats":doubleSeats,
            "total_seats":totalSeats,
            "total_amount": totalAmount,
            "discount_amount":discountAmount,
            "payment_amount":paymentAmount,
            "total_combos":totalCombos,
            "total_combo_amount":totalComboAmount,
            "redeem_vouchers":redeemVouchers,
            "redeem_points":redeemPoints,
            "payment_method":paymentMethod,
            "order_id":orderId,
            "channel":channel  ])
    }
    
    
    //user pay order fail
    func payFail(cinemaId: String?, cinemaName: String?, movieId: String?, movieName: String?, date: Date?, time: Date?, screen: String?, normalSeats: Int?, vipSeats: Int?, doubleSeats: Int?, totalSeats: Int?, totalAmount: Int?, discountAmount: Int?, paymentAmount: Int?, totalCombos: Int?,totalComboAmount: Int?,redeemVouchers: Int?,redeemPoints: Int?,  paymentMethod: String?,orderId: String?, channel: String?    , errorCode: String?, errorMsg:String?  ) {
        return  track(event: "pay_fail", properties:  [
            "cinema_id": cinemaId,
            "cinema_name":cinemaName,
            "movie_id":movieId,
            "movie_name": movieName,
            "date": date,
            "time":time,
            "screen":screen,
            "normal_seats":normalSeats,
            "vip_seats":vipSeats,
            "double_seats":doubleSeats,
            "total_seats":totalSeats,
            "total_amount": totalAmount,
            "discount_amount":discountAmount,
            "payment_amount":paymentAmount,
            "total_combos":totalCombos,
            "total_combo_amount":totalComboAmount,
            "redeem_vouchers":redeemVouchers,
            "redeem_points":redeemPoints,
            "payment_method":paymentMethod,
            "order_id":orderId,
            "channel":channel,
            "error_code":errorCode,
            "error_msg":errorMsg
        ])
    }
    
    //logout
    func logout(){
        Mixpanel.mainInstance().reset(completion: nil)
        Analytics.resetAnalyticsData()
    }
}


