<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="18122" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="18093"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="Oswald-Regular.ttf">
            <string>Oswald-Regular</string>
        </array>
        <array key="SourceSansPro-Bold.ttf">
            <string>SourceSansPro-Bold</string>
        </array>
        <array key="SourceSansPro-Regular.ttf">
            <string>SourceSansPro-Regular</string>
        </array>
    </customFonts>
    <scenes>
        <!--Payment View Controller-->
        <scene sceneID="p6T-Pj-spr">
            <objects>
                <viewController storyboardIdentifier="PaymentViewController" id="auA-n3-iqP" customClass="PaymentViewController" customModule="Beta_Cinemas" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="kVc-AA-jlr">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <viewLayoutGuide key="safeArea" id="ZhP-LH-Qll"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Bpz-lg-8m9" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-137" y="178"/>
        </scene>
        <!--Payment Point View Controller-->
        <scene sceneID="TIv-iH-vsW">
            <objects>
                <viewController storyboardIdentifier="PaymentPointViewController" id="W8O-gd-JbF" customClass="PaymentPointViewController" customModule="Beta_Cinemas" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="6LC-iA-4j3">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="0ad-5T-JbD" userLabel="View2">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="70"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Điểm hiện có" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="qn6-dv-w7e" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                        <rect key="frame" x="20" y="26" width="75" height="24"/>
                                        <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="16"/>
                                        <color key="textColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <nil key="highlightedColor"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="BetaPoint.CurrentPoint"/>
                                        </userDefinedRuntimeAttributes>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="90" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="0Dc-Vb-F5l">
                                        <rect key="frame" x="107" y="20.5" width="28" height="35.5"/>
                                        <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="28"/>
                                        <color key="textColor" red="0.01176470588" green="0.34901960780000002" blue="0.61568627450000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="0Dc-Vb-F5l" firstAttribute="leading" secondItem="qn6-dv-w7e" secondAttribute="trailing" constant="12" id="GAK-sF-p2J"/>
                                    <constraint firstItem="qn6-dv-w7e" firstAttribute="leading" secondItem="0ad-5T-JbD" secondAttribute="leading" constant="20" id="Kn7-Jd-UZn"/>
                                    <constraint firstItem="qn6-dv-w7e" firstAttribute="top" secondItem="0ad-5T-JbD" secondAttribute="top" constant="26" id="XsA-jP-AJA"/>
                                    <constraint firstItem="0Dc-Vb-F5l" firstAttribute="centerY" secondItem="qn6-dv-w7e" secondAttribute="centerY" id="Zwd-oX-gbx"/>
                                    <constraint firstAttribute="bottom" secondItem="qn6-dv-w7e" secondAttribute="bottom" constant="20" id="iCn-x2-qFz"/>
                                </constraints>
                            </view>
                            <view alpha="0.10000000149011612" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="WxU-2h-4QY" userLabel="lineView2">
                                <rect key="frame" x="20" y="70" width="335" height="1"/>
                                <color key="backgroundColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="1" id="GCH-Y4-ica"/>
                                </constraints>
                            </view>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Nhập điểm" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="1fO-jA-T1C" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                <rect key="frame" x="20" y="91" width="170.5" height="24"/>
                                <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="16"/>
                                <color key="textColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Số tiền được giảm" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="I3T-37-263" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                <rect key="frame" x="210.5" y="91" width="144.5" height="24"/>
                                <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="16"/>
                                <color key="textColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="Zp5-7R-Gtn" customClass="RoundTextField" customModule="Beta_Cinemas" customModuleProvider="target">
                                <rect key="frame" x="20" y="123" width="170.5" height="50"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="50" id="T5Q-it-yXr"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                <textInputTraits key="textInputTraits"/>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                        <real key="value" value="2"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                        <real key="value" value="1"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                        <color key="value" red="0.58431372549019611" green="0.58431372549019611" blue="0.58431372549019611" alpha="0.59999999999999998" colorSpace="calibratedRGB"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </textField>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="= 0d" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="XfA-S0-IFo">
                                <rect key="frame" x="210.5" y="135.5" width="144.5" height="25.5"/>
                                <fontDescription key="fontDescription" name="SourceSansPro-Bold" family="Source Sans Pro" pointSize="20"/>
                                <color key="textColor" red="0.01176470588" green="0.34901960780000002" blue="0.61568627450000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Số điểm nhỏ nhất là 20 và lớn nhất là 49" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="zEK-Ke-Kz0">
                                <rect key="frame" x="20" y="181" width="170.5" height="37.5"/>
                                <fontDescription key="fontDescription" name="HelveticaNeue-Italic" family="Helvetica Neue" pointSize="16"/>
                                <color key="textColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="D9b-Up-9Af" customClass="RoundView" customModule="Beta_Cinemas" customModuleProvider="target">
                                <rect key="frame" x="20" y="238.5" width="335" height="56"/>
                                <subviews>
                                    <button opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="ifv-yg-FdH" customClass="GradientButton" customModule="Beta_Cinemas" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="0.0" width="335" height="56"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="56" id="g69-Oa-p4I"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="20"/>
                                        <state key="normal" title="ĐỔI ĐIỂM">
                                            <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        </state>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                <real key="value" value="4"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="color" keyPath="firstColor">
                                                <color key="value" red="0.99215686270000003" green="0.15686274510000001" blue="0.0078431372550000003" alpha="0.97999999999999998" colorSpace="custom" customColorSpace="sRGB"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="color" keyPath="secondColor">
                                                <color key="value" red="0.99215686270000003" green="0.48627450979999998" blue="0.0078431372550000003" alpha="0.98999999999999999" colorSpace="custom" customColorSpace="sRGB"/>
                                            </userDefinedRuntimeAttribute>
                                        </userDefinedRuntimeAttributes>
                                    </button>
                                </subviews>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="ifv-yg-FdH" firstAttribute="top" secondItem="D9b-Up-9Af" secondAttribute="top" id="6Rb-w3-A1f"/>
                                    <constraint firstAttribute="trailing" secondItem="ifv-yg-FdH" secondAttribute="trailing" id="Ax9-7e-GZ6"/>
                                    <constraint firstAttribute="bottom" secondItem="ifv-yg-FdH" secondAttribute="bottom" id="GoT-Uf-1f6"/>
                                    <constraint firstItem="ifv-yg-FdH" firstAttribute="leading" secondItem="D9b-Up-9Af" secondAttribute="leading" id="NuL-0g-L8M"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                        <real key="value" value="4"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="color" keyPath="shadowColor">
                                        <color key="value" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="number" keyPath="shadowOpacity">
                                        <real key="value" value="0.20000000000000001"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="point" keyPath="shadowOffset">
                                        <point key="value" x="0.0" y="6"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="number" keyPath="shadowRadius">
                                        <real key="value" value="4"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="NXx-fQ-UqL"/>
                        <color key="backgroundColor" red="0.95294117649999999" green="0.95294117649999999" blue="0.95294117649999999" alpha="1" colorSpace="calibratedRGB"/>
                        <constraints>
                            <constraint firstItem="Zp5-7R-Gtn" firstAttribute="top" secondItem="1fO-jA-T1C" secondAttribute="bottom" constant="8" id="16F-nJ-xV7"/>
                            <constraint firstItem="WxU-2h-4QY" firstAttribute="centerX" secondItem="6LC-iA-4j3" secondAttribute="centerX" id="2XS-uG-V0R"/>
                            <constraint firstItem="WxU-2h-4QY" firstAttribute="top" secondItem="0ad-5T-JbD" secondAttribute="bottom" id="4rY-bK-LMQ"/>
                            <constraint firstItem="NXx-fQ-UqL" firstAttribute="trailing" secondItem="D9b-Up-9Af" secondAttribute="trailing" constant="20" id="85O-vI-iTt"/>
                            <constraint firstItem="I3T-37-263" firstAttribute="width" secondItem="1fO-jA-T1C" secondAttribute="width" multiplier="0.85" id="BlY-F3-n6P"/>
                            <constraint firstItem="XfA-S0-IFo" firstAttribute="trailing" secondItem="I3T-37-263" secondAttribute="trailing" id="I05-rN-IDr"/>
                            <constraint firstItem="zEK-Ke-Kz0" firstAttribute="width" secondItem="Zp5-7R-Gtn" secondAttribute="width" id="MiY-1u-cKS"/>
                            <constraint firstAttribute="trailing" secondItem="I3T-37-263" secondAttribute="trailing" constant="20" id="PR3-dk-8vV"/>
                            <constraint firstAttribute="trailing" secondItem="0ad-5T-JbD" secondAttribute="trailing" id="QHb-Sq-Fu7"/>
                            <constraint firstItem="XfA-S0-IFo" firstAttribute="centerY" secondItem="Zp5-7R-Gtn" secondAttribute="centerY" id="ZUY-AQ-0Cd"/>
                            <constraint firstItem="Zp5-7R-Gtn" firstAttribute="trailing" secondItem="1fO-jA-T1C" secondAttribute="trailing" id="ahS-JI-xB3"/>
                            <constraint firstItem="zEK-Ke-Kz0" firstAttribute="top" secondItem="Zp5-7R-Gtn" secondAttribute="bottom" constant="8" id="bgJ-rA-dXr"/>
                            <constraint firstItem="D9b-Up-9Af" firstAttribute="leading" secondItem="6LC-iA-4j3" secondAttribute="leading" constant="20" id="cb8-v8-Izm"/>
                            <constraint firstItem="Zp5-7R-Gtn" firstAttribute="leading" secondItem="1fO-jA-T1C" secondAttribute="leading" id="eBd-KH-zYg"/>
                            <constraint firstItem="D9b-Up-9Af" firstAttribute="top" secondItem="zEK-Ke-Kz0" secondAttribute="bottom" constant="20" id="f7K-Bz-Al7"/>
                            <constraint firstItem="WxU-2h-4QY" firstAttribute="leading" secondItem="6LC-iA-4j3" secondAttribute="leading" constant="20" id="g1i-j1-NWA"/>
                            <constraint firstItem="1fO-jA-T1C" firstAttribute="leading" secondItem="6LC-iA-4j3" secondAttribute="leading" constant="20" id="gBG-Ks-f6v"/>
                            <constraint firstItem="XfA-S0-IFo" firstAttribute="leading" secondItem="I3T-37-263" secondAttribute="leading" id="gWx-DC-j7D"/>
                            <constraint firstItem="0ad-5T-JbD" firstAttribute="leading" secondItem="6LC-iA-4j3" secondAttribute="leading" id="gjr-9D-byH"/>
                            <constraint firstItem="I3T-37-263" firstAttribute="top" secondItem="WxU-2h-4QY" secondAttribute="bottom" constant="20" id="iUI-6O-OO1"/>
                            <constraint firstItem="zEK-Ke-Kz0" firstAttribute="leading" secondItem="6LC-iA-4j3" secondAttribute="leading" constant="20" id="lWn-P5-V1a"/>
                            <constraint firstItem="1fO-jA-T1C" firstAttribute="top" secondItem="WxU-2h-4QY" secondAttribute="bottom" constant="20" id="p6H-xO-eYN"/>
                            <constraint firstItem="0ad-5T-JbD" firstAttribute="top" secondItem="6LC-iA-4j3" secondAttribute="top" id="vch-Je-6X3"/>
                            <constraint firstItem="I3T-37-263" firstAttribute="leading" secondItem="1fO-jA-T1C" secondAttribute="trailing" constant="20" id="wBo-MI-gF0"/>
                        </constraints>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="zkg-Pk-aJh" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="504" y="178"/>
        </scene>
    </scenes>
    <designables>
        <designable name="1fO-jA-T1C">
            <size key="intrinsicContentSize" width="62" height="24"/>
        </designable>
        <designable name="I3T-37-263">
            <size key="intrinsicContentSize" width="104" height="24"/>
        </designable>
        <designable name="ifv-yg-FdH">
            <size key="intrinsicContentSize" width="68" height="42"/>
        </designable>
        <designable name="qn6-dv-w7e">
            <size key="intrinsicContentSize" width="75" height="24"/>
        </designable>
    </designables>
</document>
