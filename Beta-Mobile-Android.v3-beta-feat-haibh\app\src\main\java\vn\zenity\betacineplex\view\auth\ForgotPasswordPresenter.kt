package vn.zenity.betacineplex.view.auth

import io.reactivex.disposables.Disposable
import vn.zenity.betacineplex.Manager.Network.APIClient
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.helper.extension.applyOn
import vn.zenity.betacineplex.helper.extension.getString
import java.lang.ref.WeakReference

/**
 * Created by Zenity.
 */

class ForgotPasswordPresenter : ForgotPasswordContractor.Presenter {

    var disposable: Disposable? = null

    override fun forgotPassword(email: String) {
        view?.get()?.showLoading()
        disposable = APIClient.shared.accountAPI.forgotPassword(hashMapOf("UserName" to email))
                .applyOn()
                .subscribe({
                    if (it.Data?.Result == true) {
                        view?.get()?.showForgotSuccess(R.string.forgot_password_success.getString())
                    } else {
                        view?.get()?.showAlert(R.string.forgot_password_error.getString())
                    }
                    view?.get()?.hideLoading()
                }, {
                    view?.get()?.showAlert(R.string.forgot_password_error.getString())
                    view?.get()?.hideLoading()
                })
    }

    private var view: WeakReference<ForgotPasswordContractor.View?>? = null
    override fun attachView(view: ForgotPasswordContractor.View) {
        this.view = WeakReference(view)
    }

    override fun detachView() {
        disposable?.dispose()
        this.view?.clear()
        this.view = null
    }
}
