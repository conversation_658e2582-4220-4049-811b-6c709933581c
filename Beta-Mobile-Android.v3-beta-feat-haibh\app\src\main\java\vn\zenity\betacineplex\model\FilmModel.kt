package vn.zenity.betacineplex.model

import com.thoughtbot.expandablerecyclerview.models.ExpandableGroup
import vn.zenity.betacineplex.app.App
import vn.zenity.betacineplex.helper.extension.toImageUrl

/**
 * Created by tinhvv on 4/11/18.
 */
data class FilmModel(
        var CinemaId: String? = null,
        var CinemaName: String? = null,
        var CinemaName_F: String? = null,
        var Latitude: String? = null,
        var Longtitude: String? = null,
        var ListFilm: MutableList<FilmBooking> = mutableListOf()
) : ExpandableGroup<FilmBooking>(CinemaName, ListFilm){

    override fun getTitle(): String {
        return Name ?: ""
    }

    override fun getItems(): MutableList<FilmBooking> {
        return ListFilm
    }

    override fun getItemCount(): Int {
        return ListFilm.size
    }

    var FilmId: String? = null
    var FilmGroupId: String? = null
    var Code: String? = null
    var Name: String? = null
        get() {
            if (!App.shared().isLangVi() && Name_F != null) {
                return Name_F
            }
            return field
        }
    var Name_F: String? = null
    var ShortDescription: String? = null
        get() {
            if (!App.shared().isLangVi() && ShortDescription_F != null) {
                return ShortDescription_F
            }
            return field
        }
    var ShortDescription_F: String? = null
    var OpeningDate: String? = null
    var GioChieu: String? = null
    var Rate: String? = null
    var Duration: Int? = null
    var FilmGenreName: String? = null
        get() {
            if (!App.shared().isLangVi() && FilmGenreName_F != null) {
                return FilmGenreName_F
            }
            return field
        }
    var FilmGenreName_F: String? = null
    var FilmRestrictAgeName: String? = null
    var SubtitleName: String? = null
    var SubtitleName_F: String? = null
    var FilmFormatName: String? = null
        get() {
            if (!App.shared().isLangVi() && FilmFormatName_F != null) {
                return FilmFormatName_F
            }
            return field
        }
    var FilmFormatCode: String? = null
    var FilmFormatName_F: String? = null
    var TrailerURL: String? = null
    var Order: String? = null
    var Status: Boolean? = null
    var NowShowing: Boolean? = null
    var Director: String? = null
    var MainPosterUrl: String? = null
        get() = field?.toImageUrl()

    var filmPosterUrl: String? = null
        get() {
            val url = ListPosterUrl?.firstOrNull { it.MainPoster == false }?.AbsolutePath
                    ?: MainPosterUrl
            return url?.toImageUrl()
        }
    var ListPosterUrl: ArrayList<ListPosterUrlModel>? = null
    var ListFilmGenre: ArrayList<ListFilmGenreModel>? = null
    val Actors: String? = null
    val MainLanguage: String? = null
    val Description: String? = null
    var RestrictAgeString: String? = null
    var RestrictAge: String? = null
    var DubbingCode: String? = null
    var DubbingName: String? = null
    var HasShow: Boolean? = null
    var HasSneakShow: Boolean? = null

    var restrictAge: Int = 0
        get() {
            return when (FilmRestrictAgeName?.toLowerCase()
                    ?: "p") {
                "c18" -> 18
                "c16" -> 16
                "c13" -> 13
                else -> 0
            }
        }

    var filmGenner: String? = null
        get() {
            ListFilmGenre?.let {
                if (!it.isEmpty()) {
                    val first = it.first()
                    val second: ListFilmGenreModel? = if (it.size > 1) it[1] else null
                    return first.Name + (if (second != null) ", ${second.Name}" else "")
                }
            }
            return FilmGenreName
        }

    var FilmFormat: String? = null
        get() {
//            val isEn = !App.shared().isLangVi()
//            val pd = if (isEn) "Subtitles" else "Phụ đề"
//            val st = if (isEn) "Subtitle" else "Phụ đề"
//            val lt = if (isEn) "Dubbing" else "Lồng tiếng"
//            val tm = if (isEn) "Presentation" else "Thuyết minh"
//
//            val formatName = (if (isEn) FilmFormatName_F else FilmFormatName) ?: return ""
//
//            return if (formatName.contains(st)) {
//                formatName.replace(pd, if (isEn) "- SUB" else "- PD").replace(st, if (isEn) "- SUB" else "- PD")
//            } else {
//                formatName.replace(lt, if (isEn) "- DUB" else "- LT").replace(tm, if (isEn) "- DUB" else "- LT")
//            }
            return FilmFormatName ?: field
        }

    var IsHot: Boolean = false
}