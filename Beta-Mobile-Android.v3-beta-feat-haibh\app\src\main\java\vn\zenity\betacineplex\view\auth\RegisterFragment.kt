package vn.zenity.betacineplex.view.auth

import android.content.Context
import android.os.Bundle
import android.text.Spannable
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.view.View
import com.tsongkha.spinnerdatepicker.SpinnerDatePickerDialogBuilder
import kotlinx.android.synthetic.main.fragment_register.*
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.base.BaseActivity
import vn.zenity.betacineplex.base.BaseFragment
import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.global.Constant
import vn.zenity.betacineplex.helper.extension.*
import vn.zenity.betacineplex.model.CityModel
import vn.zenity.betacineplex.model.RequestModel.RegisterModel
import java.util.*


/**
 * Created by Zenity.
 */

class RegisterFragment : BaseFragment(), RegisterContractor.View {

    override fun showLogin() {
        back()
    }

    override fun showRegisterSuccess(message: String, cardNumber: String) {
        activity?.runOnUiThread {
            context?.let {
                showDialogRegisterSuccess(it, cardNumber) {
                    back()
                }
            }
        }
    }

    private val presenter = RegisterPresenter()
    private val registerModel = RegisterModel()
    private var birthDay: String? = null
    private var year: Int? = null
    private var month: Int? = null
    private var day: Int? = null

    override fun getPresenter(): IBasePresenter<IBaseView>? {
        return presenter as? IBasePresenter<IBaseView>
    }

    override fun isShowToolbar(): Boolean {
        return false
    }

    override fun getLayoutRes(): Int {
        return R.layout.fragment_register
    }

    override val isTransfStatus = false

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupNotice()
//        selectionCity.setOnClickListener {
//            presenter.getCity()
//        }
//        selectionDistrict.setOnClickListener {
//            registerModel.AddressCityId?.let {
//                presenter.getDistrict(it)
//            } ?: presenter.getCity()
//        }
        selectionGender.setOnClickListener {
            context?.let {
                showGenderSelection(it, getString(R.string.select_gender)) { id, gender ->
                    selectionGender.setText(gender)
                    registerModel.Gender = "$id"
                }
            }
        }

        selectionBirthday.setOnClickListener {
            val calendar = Calendar.getInstance()
            SpinnerDatePickerDialogBuilder()
                    .context(context)
                    .callback { _, year, monthOfYear, dayOfMonth ->
                        birthDay = "${if (dayOfMonth < 10) "0" else ""}$dayOfMonth-${if (monthOfYear < 9) "0" else ""}${monthOfYear + 1}-$year"
                        this.year = year
                        this.month = monthOfYear
                        this.day = dayOfMonth
                        selectionBirthday.setText(birthDay)
                    }
                    .spinnerTheme(R.style.DatePickerStyle)
                    .showTitle(true)
                    .showDaySpinner(true)
                    .defaultDate(year ?: calendar.get(Calendar.YEAR) - 10, month ?: 0, day ?: 1)
                    .maxDate(calendar.get(Calendar.YEAR) - 10, calendar.get(Calendar.MONTH), calendar.get(Calendar.DAY_OF_MONTH))
                    .minDate(calendar.get(Calendar.YEAR) - 100, calendar.get(Calendar.MONTH), calendar.get(Calendar.DAY_OF_MONTH))
                    .build()
                    .show()
        }

        btnRegister.setOnClickListener {
            validate()
        }
    }

    private fun validate() {
//        registerModel.FirstName = edtFirstname.text.trim()
//        if (registerModel.FirstName.isEmpty()) {
//            edtFirstname.focus()
//            return
//        }
//        registerModel.LastName = edtLastname.text.trim()
//        if (registerModel.LastName.isEmpty()) {
//            edtLastname.focus()
//            return
//        }

        registerModel.FullName = edtFullname.text.trim()
        if (registerModel.FullName.isEmpty()) {
            showNotice(getString(R.string.fullname_can_not_empty))
            edtFullname.focus()
            return
        }
        registerModel.Email = edtEmailRegister.text.trim()
        if (!registerModel.Email.isEmail()) {
            showNotice(getString(R.string.error_email_is_invalidate))
            edtEmailRegister.focus()
            return
        }
        registerModel.Password = edtPasswordRegister.text.trim()
        if (registerModel.Password.isBlank() || registerModel.Password.length < 6) {
            showNotice(getString(R.string.password_not_valid))
            edtPasswordRegister.focus()
            return
        }
        if (registerModel.Password != edtRepeatPassword.text.trim()) {
            showNotice(getString(R.string.password_confirm_not_equal))
            edtRepeatPassword.focus()
            return
        }
        registerModel.PhoneOffice = edtPhone.text.trim()
        if (registerModel.PhoneOffice.isBlank()) {
            showNotice(getString(R.string.phone_number_cannot_empty))
            edtPhone.focus()
            return
        }
//        registerModel.PersonalId = edtCardNumber.text.trim()
//        if (registerModel.PersonalId.isEmpty()) {
//            edtCardNumber.focus()
//            return
//        }
        val birth = birthDay?.dateConvertFormat(Constant.DateFormat.dateVi, Constant.DateFormat.requestServer)
//        if (birth == null) {
//            showNotice( getString(R.string.birthday_cannot_empty))
//            return
//        }
        registerModel.BirthDate = birth ?: ""
//        registerModel.AddressStreet = edtAddress.text.trim()

        if (!checkbox.isChecked) {
            showNotice(getString(R.string.you_must_agree_with_term))
            return
        }

        (activity as? BaseActivity)?.checkCaptcha { captcha ->
            registerModel.ReCaptchaToken = captcha ?: ""
            presenter.register(registerModel)
        }
    }

    override fun showListCity(cities: ArrayList<CityModel>) {
//        activity?.runOnUiThread {
//            context?.let {
//                showCitiSelection(it, getString(R.string.select_city), cities) {
//                    registerModel.AddressCity = it.Name
//                    registerModel.AddressCityId = it.Id
//                    selectionCity.setText(it.Name)
//                }
//            }
//        }
    }

    override fun showDistrict(cityId: String, cities: ArrayList<CityModel>) {
//        activity?.runOnUiThread {
//            context?.let {
//                showCitiSelection(it, getString(R.string.select_district), cities) {
//                    registerModel.AddressDistrict = it.Name
//                    registerModel.AddressDistrictId = it.Id
//                    selectionDistrict.setText(it.Name)
//                }
//            }
//        }
    }

    override fun getViewContext(): Context? {
        return this.context;
    }

    private fun setupNotice() {
        val privacyPolicy = R.string.term.getString().toLowerCase()
        val term = R.string.condition.getString().toLowerCase()
        val termandcondition = R.string.requried_term_and_condition.getString()
        val textSpan = Spannable.Factory.getInstance().newSpannable(termandcondition)
        val startT = termandcondition.indexOf(term)
        textSpan.setSpan(object : ClickableSpan() {
            override fun onClick(ab: View) {
                openFragment(TermOfUseFragment())
            }

            override fun updateDrawState(ds: TextPaint) {
                super.updateDrawState(ds)
                ds.color = R.color.colorPrimaryDark.getColor()
                ds.linkColor = R.color.colorPrimaryDark.getColor()
                ds.isUnderlineText = false
            }

        }, startT, startT + term.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)


        val startC = termandcondition.indexOf(privacyPolicy)
        textSpan.setSpan(object : ClickableSpan() {
            override fun onClick(ab: View) {
                openFragment(PrivacyPolicyFragment())
            }


            override fun updateDrawState(ds: TextPaint) {
                super.updateDrawState(ds)
                ds.color = R.color.colorPrimaryDark.getColor()
                ds.linkColor = R.color.colorPrimaryDark.getColor()
                ds.isUnderlineText = false
            }

        }, startC, startC + term.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        tvNotice?.text = textSpan
        tvNotice?.movementMethod = LinkMovementMethod.getInstance()
    }
}
