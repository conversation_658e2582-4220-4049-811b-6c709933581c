package vn.zenity.betacineplex.view.user.point

import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView

/**
 * Created by Zenity.
 */

interface BetaPointContractor {
    interface View : IBaseView {
        fun showPoints(accumulation: Int, used: Int, available: Int, remain: Int, expiredOn: String)
    }

    interface Presenter : IBasePresenter<View> {
        fun getBetaPoint(accountId: String)
    }
}
