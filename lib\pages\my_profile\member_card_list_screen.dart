import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_app/core/index.dart';
import 'package:flutter_app/cubit/index.dart';
import 'package:flutter_app/models/index.dart';
import 'package:flutter_app/models/card_model.dart';
import 'package:flutter_app/utils/index.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

/// Member Card List Screen - tương tự iOS MemberCardViewController
class MemberCardListScreen extends StatefulWidget {
  const MemberCardListScreen({super.key});

  @override
  State<MemberCardListScreen> createState() => _MemberCardListScreenState();
}

class _MemberCardListScreenState extends State<MemberCardListScreen> {
  bool _isLoading = true;
  MUser? _user;
  List<CardModel> _cards = [];

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  /// Load card data - tương tự iOS getUserCard()
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Get user data
      final authState = context.read<AuthC>().state;
      if (authState.user != null) {
        _user = authState.user;
        final userId = _user?.accountId ?? _user?.id ?? '';

        if (userId.isEmpty) {
          print('❌ User ID is empty');
          return;
        }

        // Get user cards - tương tự iOS AccountProvider.rx.request(.getListCard(userId))
        final api = RepositoryProvider.of<Api>(context);
        final cardResponse = await api.auth.getListCard(userId);

        if (cardResponse != null) {
          final List<dynamic> rawCards = cardResponse.data['content'] ?? [];
          setState(() {
            _cards = rawCards.map((json) => CardModel.fromJson(json)).toList();
          });
          print('✅ Loaded ${_cards.length} cards');
        } else {
          print('❌ Card API failed: ${cardResponse?.message}');
        }
      }
    } catch (e) {
      print('❌ Error loading member cards: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: appBar(
          title: 'Member.MemberCard'.tr(), // tương tự iOS "MemberCard.Title"
          titleColor: Colors.white),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _user == null
              ? const Center(child: Text('Vui lòng đăng nhập để xem thông tin thẻ thành viên'))
              : _cards.isEmpty
                  ? _buildEmptyState()
                  : _buildCardList(),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.credit_card,
            size: 80,
            color: Colors.grey.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          const Text(
            'Chưa có thẻ thành viên nào',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: _loadData,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('Làm mới'),
          ),
        ],
      ),
    );
  }

  Widget _buildCardList() {
    return RefreshIndicator(
      onRefresh: _loadData,
      color: Colors.blue,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _cards.length,
        itemBuilder: (context, index) {
          final card = _cards[index];
          return _buildCardItem(card);
        },
      ),
    );
  }

  /// Build card item - tương tự iOS MemberCardCell
  Widget _buildCardItem(CardModel card) {
    final isActive = card.isActive;
    final cardColor = isActive ? Colors.black : Colors.grey;

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: isActive ? Border.all(color: Colors.blue, width: 2) : null,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Card name and status - tương tự iOS lbCardName, lbStatus
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  card.getDisplayName(false), // Use Vietnamese name
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: cardColor,
                  ),
                ),
                if (isActive)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.blue,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Text(
                      'Đang sử dụng',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 8),

            // Card number - tương tự iOS lbCardNumber
            Row(
              children: [
                const Icon(Icons.credit_card, size: 16, color: Colors.grey),
                const SizedBox(width: 8),
                Text(
                  'Số thẻ: ${card.cardNumber ?? 'N/A'}',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: cardColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),

            // Register date - tương tự iOS lbRegisterDate
            Row(
              children: [
                const Icon(Icons.calendar_today, size: 16, color: Colors.grey),
                const SizedBox(width: 8),
                Text(
                  'Ngày đăng ký: ${card.formattedRegisterDate}',
                  style: TextStyle(
                    fontSize: 14,
                    color: cardColor,
                  ),
                ),
              ],
            ),

            // Description if available
            if (card.description != null && card.description!.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                card.description!,
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.black87,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
