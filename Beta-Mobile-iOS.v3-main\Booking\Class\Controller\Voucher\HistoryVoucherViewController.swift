//
//  HistoryVoucherViewController.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 8/3/19.
//  Copyright © 2019 ddkc. All rights reserved.
//

import UIKit

class HistoryVoucherViewController: BaseViewController {

    @IBOutlet weak var descriptionLabel: UILabel!
    @IBOutlet weak var tableView: UITableView!

    var type: DonateType = .voucher

    private var voucherHistories: [VoucherHistory] = []
    private var pointHistories: [PointHistory] = []

    private var isVoucher: Bool {
        return type == .voucher
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        localizableTitle = isVoucher ? "voucher_history" : "point_history"
        tableView.separatorStyle = .none
        tableView.allowsSelection = false

        let description1 = type == .voucher ? "voucher_history_note".localized : "point_history_note".localized
        let strings = description1.components(separatedBy: "#")
        let attributedString = NSMutableAttributedString()

        attributedString.string(strings[0], font: UIFont(fontName: .SourceSansPro, style: .Regular, size: 16))
        attributedString.string(strings[1], font: UIFont(fontName: .SourceSansPro, style: .Bold, size: 16))
        attributedString.string(strings[2], font: UIFont(fontName: .SourceSansPro, style: .Regular, size: 16))

        descriptionLabel.attributedText = attributedString

        if isVoucher {
            tableView.registerCell(id: HistoryVoucherTableViewCell.id)
            getVoucherHistories()
        } else {
            tableView.registerCell(id: HistoryPointTableViewCell.id)
            getPointHistories()
        }
    }

    private func getVoucherHistories() {
        self.showLoading()
        VoucherProvider.rx.request(.history).mapObject(DDKCResponse<VoucherHistory>.self)

            .subscribe(onNext: {[weak self] response in
                guard let `self` = self else {return}
                self.dismissLoading()
                guard response.isSuccess() else{
                    if let message = response.Message, !message.isEmpty {
                        self.flashError(title: message)
                    } else {
                        self.flashError(title: "Error".localized)
                    }
                    return
                }

                self.voucherHistories = response.ListObject ?? []
                self.tableView.reloadData()
            }).disposed(by: disposeBag)
    }

    private func getPointHistories() {
        self.showLoading()
        VoucherProvider.rx.request(.historyPoint).mapObject(DDKCResponse<PointHistory>.self)

            .subscribe(onNext: {[weak self] response in
                guard let `self` = self else {return}
                self.dismissLoading()
                guard response.isSuccess() else{
                    if let message = response.Message, !message.isEmpty {
                        self.flashError(title: message)
                    } else {
                        self.flashError(title: "Error".localized)
                    }
                    return
                }

                self.pointHistories = response.ListObject ?? []
                self.tableView.reloadData()
            }).disposed(by: disposeBag)
    }

}

extension HistoryVoucherViewController: UITableViewDelegate, UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return isVoucher ? voucherHistories.count : pointHistories.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        if isVoucher {
            guard let cell = tableView.dequeueReusableCell(withIdentifier: HistoryVoucherTableViewCell.id) as? HistoryVoucherTableViewCell else {
                return HistoryVoucherTableViewCell()
            }
            cell.configure(voucherHistories[indexPath.row])
            return cell
        } else {
            guard let cell = tableView.dequeueReusableCell(withIdentifier: HistoryPointTableViewCell.id) as? HistoryPointTableViewCell else {
                return HistoryPointTableViewCell()
            }
            cell.configure(pointHistories[indexPath.row])
            return cell
        }
    }

    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 106
    }
}
