<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="19529" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="19519"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="Oswald-Regular.ttf">
            <string>Oswald-Regular</string>
        </array>
        <array key="SourceSansPro-Regular.ttf">
            <string>SourceSansPro-Regular</string>
        </array>
    </customFonts>
    <scenes>
        <!--Login View Controller-->
        <scene sceneID="uoh-84-DTD">
            <objects>
                <viewController storyboardIdentifier="LoginViewController" id="UNi-Wa-oZd" customClass="LoginViewController" customModule="Beta_Cinemas" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="mXz-37-hKo">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Sk5-12-NmO">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Hh6-UW-rvH">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="698"/>
                                        <subviews>
                                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Email hoặc Tên đăng nhập" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="Ziw-TD-ybt" customClass="InputTextField" customModule="Beta_Cinemas" customModuleProvider="target">
                                                <rect key="frame" x="20" y="80" width="335" height="50"/>
                                                <color key="tintColor" red="0.72549019609999998" green="0.72549019609999998" blue="0.72549019609999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="50" id="Gsq-iL-XSC"/>
                                                    <constraint firstAttribute="height" constant="50" id="lgu-SQ-bDx"/>
                                                </constraints>
                                                <color key="textColor" red="0.72549019609999998" green="0.72549019609999998" blue="0.72549019609999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                                <textInputTraits key="textInputTraits" keyboardType="emailAddress" returnKeyType="next" textContentType="username"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="image" keyPath="leftIcon" value="ic_mail"/>
                                                    <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Login.Email"/>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <outlet property="delegate" destination="UNi-Wa-oZd" id="Bp0-Sl-cS3"/>
                                                </connections>
                                            </textField>
                                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Mật khẩu" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="R8x-FR-Eqq" customClass="InputTextField" customModule="Beta_Cinemas" customModuleProvider="target">
                                                <rect key="frame" x="20" y="148" width="335" height="50"/>
                                                <color key="tintColor" red="0.72549019609999998" green="0.72549019609999998" blue="0.72549019609999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="50" id="IFF-FT-xec"/>
                                                    <constraint firstAttribute="height" constant="50" id="K3e-39-7yh"/>
                                                </constraints>
                                                <color key="textColor" red="0.72549019609999998" green="0.72549019609999998" blue="0.72549019609999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                                <textInputTraits key="textInputTraits" returnKeyType="go" secureTextEntry="YES" textContentType="password"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="image" keyPath="leftIcon" value="ic_password"/>
                                                    <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Login.Password"/>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <outlet property="delegate" destination="UNi-Wa-oZd" id="Gsr-Il-xY4"/>
                                                </connections>
                                            </textField>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" hasAttributedTitle="YES" translatesAutoresizingMaskIntoConstraints="NO" id="pvp-dH-r1b" customClass="LocalizableButton" customModule="Beta_Cinemas" customModuleProvider="target">
                                                <rect key="frame" x="20" y="218" width="118" height="31"/>
                                                <state key="normal">
                                                    <attributedString key="attributedTitle">
                                                        <fragment content="Quên mật khẩu?">
                                                            <attributes>
                                                                <color key="NSColor" red="0.0**********2352941" green="0.34901960784313724" blue="0.61568627450980395" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                <font key="NSFont" metaFont="system" size="16"/>
                                                                <integer key="NSUnderline" value="1"/>
                                                            </attributes>
                                                        </fragment>
                                                    </attributedString>
                                                </state>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Login.ForgotPassword"/>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <segue destination="vOc-Mr-gnY" kind="show" id="GZA-Tr-UWF"/>
                                                </connections>
                                            </button>
                                            <button opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="lp7-0U-v6W" customClass="GradientButton" customModule="Beta_Cinemas" customModuleProvider="target">
                                                <rect key="frame" x="20" y="273" width="335" height="56"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="56" id="2tO-DZ-ek1"/>
                                                    <constraint firstAttribute="height" constant="56" id="9De-Dm-rOT"/>
                                                    <constraint firstAttribute="height" relation="lessThanOrEqual" constant="56" id="NCZ-oH-5xF"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="20"/>
                                                <state key="normal" title="ĐĂNG NHẬP">
                                                    <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                </state>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                        <real key="value" value="4"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="point" keyPath="shadowOffset">
                                                        <point key="value" x="0.0" y="6"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="color" keyPath="shadowColor">
                                                        <color key="value" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="shadowOpacity">
                                                        <real key="value" value="0.20000000000000001"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="shadowRadius">
                                                        <real key="value" value="14"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="color" keyPath="firstColor">
                                                        <color key="value" red="0.99215686270000003" green="0.15686274510000001" blue="0.0078431372550000003" alpha="0.97999999999999998" colorSpace="custom" customColorSpace="sRGB"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="color" keyPath="secondColor">
                                                        <color key="value" red="0.99215686270000003" green="0.48627450979999998" blue="0.0078431372550000003" alpha="0.98999999999999999" colorSpace="custom" customColorSpace="sRGB"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Login.Login"/>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <action selector="didTapLogin:" destination="UNi-Wa-oZd" eventType="touchUpInside" id="xlS-Rj-maF"/>
                                                </connections>
                                            </button>
                                            <button opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="W0Q-Iu-t8R" customClass="GradientButton" customModule="Beta_Cinemas" customModuleProvider="target">
                                                <rect key="frame" x="20" y="433" width="335" height="56"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="56" id="7Nb-Dy-kcS"/>
                                                    <constraint firstAttribute="height" relation="lessThanOrEqual" constant="56" id="Oea-Cg-AFr"/>
                                                    <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="56" id="gNY-3X-AKk"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="20"/>
                                                <state key="normal" title="ĐĂNG NHẬP BẰNG FACEBOOK">
                                                    <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                </state>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                        <real key="value" value="4"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="point" keyPath="shadowOffset">
                                                        <point key="value" x="0.0" y="6"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="color" keyPath="shadowColor">
                                                        <color key="value" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="shadowOpacity">
                                                        <real key="value" value="0.20000000000000001"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="shadowRadius">
                                                        <real key="value" value="14"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Login.LoginWithFacebook"/>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <action selector="didTapLoginFB:" destination="UNi-Wa-oZd" eventType="touchUpInside" id="QdD-ji-qIj"/>
                                                </connections>
                                            </button>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="S1g-fc-g93" userLabel="lineView">
                                                <rect key="frame" x="20" y="540" width="335" height="1"/>
                                                <color key="backgroundColor" red="0.*****************" green="0.*****************" blue="0.*****************" alpha="0.10000000000000001" colorSpace="custom" customColorSpace="sRGB"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="1" id="2UA-qn-yiK"/>
                                                </constraints>
                                            </view>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Hoặc" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="7is-02-4AH" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                                <rect key="frame" x="165" y="530.5" width="45" height="20.5"/>
                                                <color key="backgroundColor" red="0.95294117649999999" green="0.95294117649999999" blue="0.95294117649999999" alpha="1" colorSpace="calibratedRGB"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="45" id="AUO-1c-tf8"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                                <color key="textColor" red="0.72549019609999998" green="0.72549019609999998" blue="0.72549019609999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                <nil key="highlightedColor"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Or"/>
                                                </userDefinedRuntimeAttributes>
                                            </label>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="rrK-pD-7pN" customClass="LocalizableButton" customModule="Beta_Cinemas" customModuleProvider="target">
                                                <rect key="frame" x="102" y="591" width="171" height="42"/>
                                                <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="20"/>
                                                <state key="normal" title="Đăng ký tài khoản BETA">
                                                    <color key="titleColor" red="0.*****************" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                </state>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Login.RegisterBETAAccount"/>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <segue destination="L3Q-ca-OsR" kind="show" id="KyC-3x-b1I"/>
                                                </connections>
                                            </button>
                                            <button opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="m60-tU-fYx" customClass="LocalizableButton" customModule="Beta_Cinemas" customModuleProvider="target">
                                                <rect key="frame" x="20" y="353" width="335" height="56"/>
                                                <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="56" id="NlP-Xe-bEz"/>
                                                    <constraint firstAttribute="height" relation="lessThanOrEqual" constant="56" id="Rah-Yn-B35"/>
                                                    <constraint firstAttribute="height" relation="lessThanOrEqual" constant="56" id="bau-SQ-A5l"/>
                                                    <constraint firstAttribute="height" constant="56" id="hcn-JY-lgm"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="20"/>
                                                <state key="normal" title="ĐĂNG NHẬP BẰNG APPLE">
                                                    <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                </state>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                        <real key="value" value="4"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="point" keyPath="shadowOffset">
                                                        <point key="value" x="0.0" y="6"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="color" keyPath="shadowColor">
                                                        <color key="value" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="shadowOpacity">
                                                        <real key="value" value="0.20000000000000001"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="shadowRadius">
                                                        <real key="value" value="14"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Login.LoginWithApple"/>
                                                    <userDefinedRuntimeAttribute type="color" keyPath="firstColor">
                                                        <color key="value" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="color" keyPath="secondColor">
                                                        <color key="value" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <action selector="didTapLoginApple:" destination="UNi-Wa-oZd" eventType="touchUpInside" id="dKp-AS-1o8"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                        <color key="backgroundColor" red="0.95294117649999999" green="0.95294117649999999" blue="0.95294117649999999" alpha="1" colorSpace="calibratedRGB"/>
                                        <constraints>
                                            <constraint firstItem="Ziw-TD-ybt" firstAttribute="leading" secondItem="Hh6-UW-rvH" secondAttribute="leading" constant="20" id="4xD-SL-fcH"/>
                                            <constraint firstItem="R8x-FR-Eqq" firstAttribute="top" secondItem="Ziw-TD-ybt" secondAttribute="bottom" constant="18" id="72E-6Y-f7B"/>
                                            <constraint firstItem="W0Q-Iu-t8R" firstAttribute="leading" secondItem="Hh6-UW-rvH" secondAttribute="leading" constant="20" id="BrK-BJ-sSF"/>
                                            <constraint firstAttribute="trailing" secondItem="Ziw-TD-ybt" secondAttribute="trailing" constant="20" id="Buw-Gs-dXZ"/>
                                            <constraint firstAttribute="trailing" secondItem="lp7-0U-v6W" secondAttribute="trailing" constant="20" id="Cn6-0j-XEd"/>
                                            <constraint firstItem="pvp-dH-r1b" firstAttribute="top" secondItem="R8x-FR-Eqq" secondAttribute="bottom" constant="20" id="DEx-6F-p17"/>
                                            <constraint firstAttribute="trailing" secondItem="S1g-fc-g93" secondAttribute="trailing" constant="20" id="HiF-Ek-Bo2"/>
                                            <constraint firstItem="rrK-pD-7pN" firstAttribute="centerX" secondItem="Hh6-UW-rvH" secondAttribute="centerX" id="IcS-dz-Cpj"/>
                                            <constraint firstItem="lp7-0U-v6W" firstAttribute="top" secondItem="pvp-dH-r1b" secondAttribute="bottom" constant="24" id="Kfj-vL-Z12"/>
                                            <constraint firstItem="m60-tU-fYx" firstAttribute="top" secondItem="lp7-0U-v6W" secondAttribute="bottom" constant="24" id="La0-d9-knH"/>
                                            <constraint firstItem="m60-tU-fYx" firstAttribute="leading" secondItem="Hh6-UW-rvH" secondAttribute="leading" constant="20" symbolic="YES" id="No6-av-M8u"/>
                                            <constraint firstItem="Ziw-TD-ybt" firstAttribute="top" secondItem="Hh6-UW-rvH" secondAttribute="top" constant="80" id="PHz-Qk-B2c"/>
                                            <constraint firstAttribute="bottom" secondItem="rrK-pD-7pN" secondAttribute="bottom" constant="65" id="Qtv-Um-uAC"/>
                                            <constraint firstItem="rrK-pD-7pN" firstAttribute="top" secondItem="7is-02-4AH" secondAttribute="bottom" constant="40" id="UgP-jS-IJr"/>
                                            <constraint firstAttribute="trailing" secondItem="W0Q-Iu-t8R" secondAttribute="trailing" constant="20" id="YUO-hH-TiO"/>
                                            <constraint firstItem="W0Q-Iu-t8R" firstAttribute="top" secondItem="m60-tU-fYx" secondAttribute="bottom" constant="24" id="ZXw-KT-0VK"/>
                                            <constraint firstAttribute="trailing" secondItem="m60-tU-fYx" secondAttribute="trailing" constant="20" symbolic="YES" id="fG5-hy-LN9"/>
                                            <constraint firstItem="pvp-dH-r1b" firstAttribute="leading" secondItem="Hh6-UW-rvH" secondAttribute="leading" constant="20" id="fQP-dr-9Hf"/>
                                            <constraint firstItem="lp7-0U-v6W" firstAttribute="leading" secondItem="Hh6-UW-rvH" secondAttribute="leading" constant="20" id="fdB-1g-b39"/>
                                            <constraint firstItem="S1g-fc-g93" firstAttribute="leading" secondItem="Hh6-UW-rvH" secondAttribute="leading" constant="20" id="gRy-0U-iOb"/>
                                            <constraint firstItem="7is-02-4AH" firstAttribute="centerX" secondItem="Hh6-UW-rvH" secondAttribute="centerX" id="hdw-2k-X97"/>
                                            <constraint firstItem="S1g-fc-g93" firstAttribute="top" secondItem="W0Q-Iu-t8R" secondAttribute="bottom" constant="51" id="pDC-uc-SOM"/>
                                            <constraint firstItem="S1g-fc-g93" firstAttribute="centerY" secondItem="7is-02-4AH" secondAttribute="centerY" id="t0t-jo-1Jv"/>
                                            <constraint firstAttribute="trailing" secondItem="R8x-FR-Eqq" secondAttribute="trailing" constant="20" id="vhd-88-qtd"/>
                                            <constraint firstItem="R8x-FR-Eqq" firstAttribute="leading" secondItem="Hh6-UW-rvH" secondAttribute="leading" constant="20" id="zYg-Ab-bU0"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="Hh6-UW-rvH" firstAttribute="width" secondItem="Sk5-12-NmO" secondAttribute="width" id="CWV-w3-5HD"/>
                                    <constraint firstItem="Hh6-UW-rvH" firstAttribute="leading" secondItem="Sk5-12-NmO" secondAttribute="leading" id="SLh-jI-HXc"/>
                                    <constraint firstItem="Hh6-UW-rvH" firstAttribute="top" secondItem="Sk5-12-NmO" secondAttribute="top" id="jJE-vf-gNh"/>
                                    <constraint firstAttribute="trailing" secondItem="Hh6-UW-rvH" secondAttribute="trailing" id="sOa-QW-G3Y"/>
                                    <constraint firstAttribute="bottom" secondItem="Hh6-UW-rvH" secondAttribute="bottom" id="wGS-vW-9L6"/>
                                </constraints>
                            </scrollView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="Vdy-9E-SR6"/>
                        <color key="backgroundColor" red="0.95294**********18" green="0.95294**********18" blue="0.95294**********18" alpha="1" colorSpace="calibratedRGB"/>
                        <constraints>
                            <constraint firstItem="Sk5-12-NmO" firstAttribute="leading" secondItem="Vdy-9E-SR6" secondAttribute="leading" id="WYC-as-mxO"/>
                            <constraint firstItem="Sk5-12-NmO" firstAttribute="top" secondItem="Vdy-9E-SR6" secondAttribute="top" id="a9g-y5-EA4"/>
                            <constraint firstItem="Vdy-9E-SR6" firstAttribute="bottom" secondItem="Sk5-12-NmO" secondAttribute="bottom" id="li2-s7-0L1"/>
                            <constraint firstItem="Vdy-9E-SR6" firstAttribute="trailing" secondItem="Sk5-12-NmO" secondAttribute="trailing" id="lpM-Fq-pz5"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="btForgotPass" destination="pvp-dH-r1b" id="IXo-gG-lbw"/>
                        <outlet property="tfEmail" destination="Ziw-TD-ybt" id="3wq-xE-hAs"/>
                        <outlet property="tfPassword" destination="R8x-FR-Eqq" id="EzT-9J-aMP"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="5ae-ba-aGi" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-66.400000000000006" y="-115.59220389805098"/>
        </scene>
        <!--Register Result View Controller-->
        <scene sceneID="v1p-uK-C3A">
            <objects>
                <viewController storyboardIdentifier="RegisterResultViewController" id="a4Z-Jz-cph" customClass="RegisterResultViewController" customModule="Beta_Cinemas" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="awM-nA-LPl">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" ambiguous="YES" text="đăng ký thành công" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="kro-Jj-De6" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                <rect key="frame" x="117" y="170" width="141" height="30"/>
                                <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="20"/>
                                <color key="textColor" red="0.494**********2355" green="0.82745098039215681" blue="0.1294**********234" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <nil key="highlightedColor"/>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Register.Success"/>
                                </userDefinedRuntimeAttributes>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" ambiguous="YES" text="Bạn đã được cấp thẻ thành viên số" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="7Ex-AE-Lne" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                <rect key="frame" x="20" y="250" width="335" height="20.5"/>
                                <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                <color key="textColor" red="0.28627450980392155" green="0.29803921568627451" blue="0.38431372549019605" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <nil key="highlightedColor"/>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Register.YouReceivedCardNumber"/>
                                </userDefinedRuntimeAttributes>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" ambiguous="YES" text="**********" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ube-Xu-R1R">
                                <rect key="frame" x="128" y="274.5" width="119.5" height="30.5"/>
                                <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="24"/>
                                <color key="textColor" red="0.**********2352941" green="0.12156862745098039" blue="0.15686274509803921" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <imageView userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" ambiguous="YES" image="icFinish" translatesAutoresizingMaskIntoConstraints="NO" id="kqA-sp-EwL">
                                <rect key="frame" x="166.5" y="120" width="42" height="42"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="42" id="eGP-cm-71S"/>
                                    <constraint firstAttribute="width" constant="42" id="uTf-EN-pSN"/>
                                </constraints>
                            </imageView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="ygg-uC-4TN"/>
                        <color key="backgroundColor" red="0.95294117649999999" green="0.95294117649999999" blue="0.95294117649999999" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <constraint firstItem="7Ex-AE-Lne" firstAttribute="centerX" secondItem="awM-nA-LPl" secondAttribute="centerX" id="5On-H6-MHo"/>
                            <constraint firstItem="7Ex-AE-Lne" firstAttribute="top" secondItem="kro-Jj-De6" secondAttribute="bottom" constant="50" id="SMP-hq-b0R"/>
                            <constraint firstItem="ygg-uC-4TN" firstAttribute="bottom" relation="greaterThanOrEqual" secondItem="ube-Xu-R1R" secondAttribute="bottom" constant="50" id="gh3-hb-9gN"/>
                            <constraint firstItem="ube-Xu-R1R" firstAttribute="top" secondItem="7Ex-AE-Lne" secondAttribute="bottom" constant="4" id="hby-4y-fqj"/>
                            <constraint firstItem="7Ex-AE-Lne" firstAttribute="leading" secondItem="ygg-uC-4TN" secondAttribute="leading" constant="20" id="hhC-Kl-njc"/>
                            <constraint firstItem="kro-Jj-De6" firstAttribute="centerX" secondItem="awM-nA-LPl" secondAttribute="centerX" id="kgW-yw-UtG"/>
                            <constraint firstItem="kqA-sp-EwL" firstAttribute="centerX" secondItem="awM-nA-LPl" secondAttribute="centerX" id="pq8-1Z-KVi"/>
                            <constraint firstItem="kqA-sp-EwL" firstAttribute="top" relation="greaterThanOrEqual" secondItem="ygg-uC-4TN" secondAttribute="top" priority="750" constant="50" id="rcO-GR-pIS"/>
                            <constraint firstItem="kro-Jj-De6" firstAttribute="top" secondItem="kqA-sp-EwL" secondAttribute="bottom" constant="8" id="uoy-Uv-OgB"/>
                            <constraint firstItem="ube-Xu-R1R" firstAttribute="centerX" secondItem="awM-nA-LPl" secondAttribute="centerX" id="zil-Zf-rAX"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="lbCardNumber" destination="ube-Xu-R1R" id="o5M-y4-SEm"/>
                        <outlet property="lbCardTitle" destination="7Ex-AE-Lne" id="l6g-rh-cxK"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Um1-KY-afn" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-399" y="630"/>
        </scene>
        <!--Register View Controller-->
        <scene sceneID="jHK-f5-1Kw">
            <objects>
                <viewController storyboardIdentifier="RegisterViewController" id="L3Q-ca-OsR" customClass="RegisterViewController" customModule="Beta_Cinemas" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="alV-Fp-b6c">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="1180"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="4Hh-TA-bfO">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="1180"/>
                                <subviews>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="18" translatesAutoresizingMaskIntoConstraints="NO" id="mge-u1-kPm">
                                        <rect key="frame" x="20" y="0.0" width="335" height="937.5"/>
                                        <subviews>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="J1p-ec-jJp">
                                                <rect key="frame" x="0.0" y="0.0" width="335" height="128"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="THÔNG TIN BẮT BUỘC" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="xkI-XK-blp" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                                        <rect key="frame" x="8" y="22" width="327" height="106"/>
                                                        <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="20"/>
                                                        <color key="textColor" red="0.28627450980392155" green="0.29803921568627451" blue="0.38431372549019605" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                        <nil key="highlightedColor"/>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Register.RequiredInfo"/>
                                                        </userDefinedRuntimeAttributes>
                                                    </label>
                                                    <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="1oy-4w-FAM" customClass="RoundView" customModule="Beta_Cinemas" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="22" width="8" height="8"/>
                                                        <color key="backgroundColor" red="0.99215686274509807" green="0.15686274509803921" blue="0.0078431372549019607" alpha="1" colorSpace="calibratedRGB"/>
                                                        <constraints>
                                                            <constraint firstAttribute="width" constant="8" id="b4S-jU-a1w"/>
                                                            <constraint firstAttribute="height" constant="8" id="wmS-1a-ucQ"/>
                                                        </constraints>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                                <real key="value" value="4"/>
                                                            </userDefinedRuntimeAttribute>
                                                        </userDefinedRuntimeAttributes>
                                                    </view>
                                                </subviews>
                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstItem="xkI-XK-blp" firstAttribute="top" secondItem="J1p-ec-jJp" secondAttribute="top" constant="22" id="3bU-NQ-TeH"/>
                                                    <constraint firstItem="1oy-4w-FAM" firstAttribute="leading" secondItem="J1p-ec-jJp" secondAttribute="leading" id="LuC-cc-Myv"/>
                                                    <constraint firstAttribute="trailing" secondItem="xkI-XK-blp" secondAttribute="trailing" id="Y63-Jv-V7B"/>
                                                    <constraint firstAttribute="bottom" secondItem="xkI-XK-blp" secondAttribute="bottom" id="iLE-G4-and"/>
                                                    <constraint firstItem="1oy-4w-FAM" firstAttribute="top" secondItem="J1p-ec-jJp" secondAttribute="top" constant="22" id="m6W-1A-mpX"/>
                                                    <constraint firstItem="xkI-XK-blp" firstAttribute="leading" secondItem="1oy-4w-FAM" secondAttribute="trailing" id="zPC-Mx-kCv"/>
                                                </constraints>
                                            </view>
                                            <textField hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Họ tên" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="rNE-hg-oSq" customClass="InputTextField" customModule="Beta_Cinemas" customModuleProvider="target">
                                                <rect key="frame" x="0.0" y="137" width="335" height="50"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="50" id="lRj-5k-dgR"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                <textInputTraits key="textInputTraits" autocapitalizationType="words" returnKeyType="next" textContentType="name"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="image" keyPath="leftIcon" value="ic_firstname"/>
                                                    <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Register.FirstName"/>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <outlet property="delegate" destination="L3Q-ca-OsR" id="YQH-OB-ASv"/>
                                                </connections>
                                            </textField>
                                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Họ tên" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="m1I-56-V0K" customClass="InputTextField" customModule="Beta_Cinemas" customModuleProvider="target">
                                                <rect key="frame" x="0.0" y="146" width="335" height="50"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="50" id="qci-Hh-w94"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                <textInputTraits key="textInputTraits" autocapitalizationType="words" returnKeyType="next" textContentType="name"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="image" keyPath="leftIcon" value="ic_name"/>
                                                    <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Register.Name"/>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <outlet property="delegate" destination="L3Q-ca-OsR" id="qf3-Iv-mN7"/>
                                                </connections>
                                            </textField>
                                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Email" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="a8C-sI-7vJ" customClass="InputTextField" customModule="Beta_Cinemas" customModuleProvider="target">
                                                <rect key="frame" x="0.0" y="214" width="335" height="50"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="50" id="n8l-Az-fB1"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                <textInputTraits key="textInputTraits" keyboardType="emailAddress" returnKeyType="next" textContentType="username"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="image" keyPath="leftIcon" value="ic_mail"/>
                                                    <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Register.Email"/>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <outlet property="delegate" destination="L3Q-ca-OsR" id="8eb-mz-3ih"/>
                                                </connections>
                                            </textField>
                                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Mật khẩu" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="zBm-Vj-hbN" customClass="InputTextField" customModule="Beta_Cinemas" customModuleProvider="target">
                                                <rect key="frame" x="0.0" y="282" width="335" height="50"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="50" id="EUp-hE-u3x"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                <textInputTraits key="textInputTraits" returnKeyType="next" secureTextEntry="YES" textContentType="password"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="image" keyPath="leftIcon" value="ic_password"/>
                                                    <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Register.Password"/>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <outlet property="delegate" destination="L3Q-ca-OsR" id="4o4-1j-CTp"/>
                                                </connections>
                                            </textField>
                                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Nhập lại mật khẩu" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="EF8-JO-TYf" customClass="InputTextField" customModule="Beta_Cinemas" customModuleProvider="target">
                                                <rect key="frame" x="0.0" y="350" width="335" height="50"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="50" id="O78-Ud-4D4"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                <textInputTraits key="textInputTraits" returnKeyType="next" secureTextEntry="YES" textContentType="password"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="image" keyPath="leftIcon" value="ic_comfirm_password"/>
                                                    <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Register.RePassword"/>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <outlet property="delegate" destination="L3Q-ca-OsR" id="rsU-vv-uwq"/>
                                                </connections>
                                            </textField>
                                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Số điện thoại" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="VOx-U2-5hO" customClass="InputTextField" customModule="Beta_Cinemas" customModuleProvider="target">
                                                <rect key="frame" x="0.0" y="418" width="335" height="50"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="50" id="9FD-M9-HSF"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                <textInputTraits key="textInputTraits" keyboardType="phonePad" returnKeyType="go" textContentType="tel"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="image" keyPath="leftIcon" value="ic_phone"/>
                                                    <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Register.Phone"/>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <outlet property="delegate" destination="L3Q-ca-OsR" id="bWX-qq-jQk"/>
                                                </connections>
                                            </textField>
                                            <textField hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="CMND/ Hộ chiếu" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="N5g-za-hTI" customClass="InputTextField" customModule="Beta_Cinemas" customModuleProvider="target">
                                                <rect key="frame" x="0.0" y="477" width="335" height="50"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="50" id="efq-vh-9pc"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                <textInputTraits key="textInputTraits" keyboardType="numbersAndPunctuation" returnKeyType="next"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="image" keyPath="leftIcon" value="ic_cmnd"/>
                                                    <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Register.CMND"/>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <outlet property="delegate" destination="L3Q-ca-OsR" id="2hz-B6-fOh"/>
                                                </connections>
                                            </textField>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ysN-v7-TVi">
                                                <rect key="frame" x="0.0" y="486" width="335" height="128"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="THÔNG TIN BỔ SUNG" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="rKt-CC-gFz" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="4" width="335" height="124"/>
                                                        <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="20"/>
                                                        <color key="textColor" red="0.*****************" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                        <nil key="highlightedColor"/>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Register.AdditionalInfo"/>
                                                        </userDefinedRuntimeAttributes>
                                                    </label>
                                                </subviews>
                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstItem="rKt-CC-gFz" firstAttribute="top" secondItem="ysN-v7-TVi" secondAttribute="top" constant="4" id="8gu-W9-npF"/>
                                                    <constraint firstAttribute="trailing" secondItem="rKt-CC-gFz" secondAttribute="trailing" id="GCH-cM-TLE"/>
                                                    <constraint firstAttribute="bottom" secondItem="rKt-CC-gFz" secondAttribute="bottom" id="YMX-Qp-ag9"/>
                                                    <constraint firstItem="rKt-CC-gFz" firstAttribute="leading" secondItem="ysN-v7-TVi" secondAttribute="leading" id="zCw-fH-M9M"/>
                                                </constraints>
                                            </view>
                                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Ngày sinh" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="vlV-fe-LUj" customClass="DateTextField" customModule="Beta_Cinemas" customModuleProvider="target">
                                                <rect key="frame" x="0.0" y="632" width="335" height="50"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="50" id="EKq-TU-QtC"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                <textInputTraits key="textInputTraits" returnKeyType="next"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="image" keyPath="leftIcon" value="ic_birthday"/>
                                                    <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Register.Birthday"/>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <outlet property="delegate" destination="L3Q-ca-OsR" id="iOJ-bU-Uv2"/>
                                                </connections>
                                            </textField>
                                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Giới tính" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="bIP-Yg-Xlq" customClass="PickerTextField" customModule="Beta_Cinemas" customModuleProvider="target">
                                                <rect key="frame" x="0.0" y="700" width="335" height="50"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="50" id="JqW-tN-FGp"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                <textInputTraits key="textInputTraits" returnKeyType="go"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="image" keyPath="leftIcon" value="ic_sex"/>
                                                    <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Register.Gender"/>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <outlet property="delegate" destination="L3Q-ca-OsR" id="28T-ay-Eoj"/>
                                                </connections>
                                            </textField>
                                            <textField hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Tỉnh/ Thành phố" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="eXc-wz-DkB" customClass="PickerTextField" customModule="Beta_Cinemas" customModuleProvider="target">
                                                <rect key="frame" x="0.0" y="759" width="335" height="50"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="50" id="gjT-hu-awo"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                <textInputTraits key="textInputTraits" returnKeyType="next" textContentType="address-level2"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="image" keyPath="leftIcon" value="ic_tower"/>
                                                    <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Register.City"/>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <outlet property="delegate" destination="L3Q-ca-OsR" id="3UJ-Uk-Tig"/>
                                                </connections>
                                            </textField>
                                            <textField hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Quận/ Huyện" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="JkL-6f-9Zx" customClass="PickerTextField" customModule="Beta_Cinemas" customModuleProvider="target">
                                                <rect key="frame" x="0.0" y="759" width="335" height="50"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="50" id="ANf-9D-F94"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                <textInputTraits key="textInputTraits" returnKeyType="next" textContentType="address-level1"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="image" keyPath="leftIcon" value="ic_home"/>
                                                    <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Register.District"/>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <outlet property="delegate" destination="L3Q-ca-OsR" id="TAy-lY-Lij"/>
                                                </connections>
                                            </textField>
                                            <textField hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Địa chỉ liên hệ" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="XOC-Ss-xfd" customClass="InputTextField" customModule="Beta_Cinemas" customModuleProvider="target">
                                                <rect key="frame" x="0.0" y="759" width="335" height="50"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="50" id="Yzp-l3-0uM"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                <textInputTraits key="textInputTraits" returnKeyType="go" textContentType="street-address"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="image" keyPath="leftIcon" value="ic_address"/>
                                                    <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Register.Address"/>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <outlet property="delegate" destination="L3Q-ca-OsR" id="nzH-kW-B0l"/>
                                                </connections>
                                            </textField>
                                            <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="3e5-JJ-Ckv">
                                                <rect key="frame" x="0.0" y="759" width="335" height="12"/>
                                                <subviews>
                                                    <button opaque="NO" contentMode="scaleToFill" selected="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Oc6-c2-yqK">
                                                        <rect key="frame" x="0.0" y="12" width="22" height="22"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="22" id="COX-Ws-LCY"/>
                                                            <constraint firstAttribute="width" constant="22" id="NLh-Db-U75"/>
                                                        </constraints>
                                                        <state key="normal" image="ic_checkbox_normal"/>
                                                        <state key="selected" image="ic_checkbox_select"/>
                                                        <connections>
                                                            <action selector="createCardBoxPressed:" destination="L3Q-ca-OsR" eventType="touchUpInside" id="63s-qF-Vyh"/>
                                                        </connections>
                                                    </button>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Nhận thẻ thành viên trực tuyến" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="hta-Xc-axS" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                                        <rect key="frame" x="30" y="12" width="305" height="0.0"/>
                                                        <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                                        <color key="textColor" red="0.*****************" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                        <nil key="highlightedColor"/>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Register.ReceiveMemberCard"/>
                                                        </userDefinedRuntimeAttributes>
                                                    </label>
                                                </subviews>
                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstItem="hta-Xc-axS" firstAttribute="top" secondItem="3e5-JJ-Ckv" secondAttribute="top" constant="12" id="88x-2w-pUt"/>
                                                    <constraint firstAttribute="trailing" secondItem="hta-Xc-axS" secondAttribute="trailing" id="CgW-MC-hvp"/>
                                                    <constraint firstItem="Oc6-c2-yqK" firstAttribute="leading" secondItem="3e5-JJ-Ckv" secondAttribute="leading" id="LtH-Dv-qgv"/>
                                                    <constraint firstItem="Oc6-c2-yqK" firstAttribute="top" secondItem="3e5-JJ-Ckv" secondAttribute="top" constant="12" id="PEC-Du-SnZ"/>
                                                    <constraint firstItem="hta-Xc-axS" firstAttribute="leading" secondItem="Oc6-c2-yqK" secondAttribute="trailing" constant="8" id="i1m-dj-fLK"/>
                                                    <constraint firstAttribute="bottom" secondItem="hta-Xc-axS" secondAttribute="bottom" id="pix-3r-JkO"/>
                                                </constraints>
                                            </view>
                                            <view contentMode="scaleToFill" verticalHuggingPriority="750" verticalCompressionResistancePriority="250" translatesAutoresizingMaskIntoConstraints="NO" id="cEI-er-FpC">
                                                <rect key="frame" x="0.0" y="768" width="335" height="52.5"/>
                                                <subviews>
                                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Hhi-iW-e22">
                                                        <rect key="frame" x="0.0" y="6" width="22" height="22"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="22" id="cue-Kk-Rk8"/>
                                                            <constraint firstAttribute="width" constant="22" id="xu7-2f-FKa"/>
                                                        </constraints>
                                                        <state key="normal" image="ic_checkbox_normal"/>
                                                        <state key="selected" image="ic_checkbox_select"/>
                                                        <connections>
                                                            <action selector="acceptCheckboxPressed:" destination="L3Q-ca-OsR" eventType="touchUpInside" id="mih-mq-d20"/>
                                                        </connections>
                                                    </button>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="1000" verticalCompressionResistancePriority="1000" text="I am committed to complying with the Betacineplex privacy policy and terms of use." textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="LeM-QY-NzJ" customClass="ActiveLabel" customModule="ActiveLabel">
                                                        <rect key="frame" x="30" y="6" width="301" height="40.5"/>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                                        <color key="textColor" red="0.*****************" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstItem="Hhi-iW-e22" firstAttribute="leading" secondItem="cEI-er-FpC" secondAttribute="leading" id="1MZ-rK-zD4"/>
                                                    <constraint firstItem="Hhi-iW-e22" firstAttribute="top" secondItem="cEI-er-FpC" secondAttribute="top" constant="6" id="5wd-kw-zys"/>
                                                    <constraint firstAttribute="trailing" secondItem="LeM-QY-NzJ" secondAttribute="trailing" constant="4" id="fD8-cY-reC"/>
                                                    <constraint firstAttribute="bottom" secondItem="LeM-QY-NzJ" secondAttribute="bottom" constant="6" id="lIT-5M-pqn"/>
                                                    <constraint firstItem="LeM-QY-NzJ" firstAttribute="top" secondItem="cEI-er-FpC" secondAttribute="top" constant="6" id="mJf-JQ-2cZ"/>
                                                    <constraint firstItem="LeM-QY-NzJ" firstAttribute="leading" secondItem="Hhi-iW-e22" secondAttribute="trailing" constant="8" id="xTQ-ou-noB"/>
                                                </constraints>
                                            </view>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="RUV-Sh-Who" customClass="GradientButton" customModule="Beta_Cinemas" customModuleProvider="target">
                                                <rect key="frame" x="0.0" y="838.5" width="335" height="56"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="56" id="dm2-jN-0ph"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="20"/>
                                                <state key="normal" title="ĐĂNG KÝ">
                                                    <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                </state>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="color" keyPath="shadowColor">
                                                        <color key="value" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="shadowOpacity">
                                                        <real key="value" value="0.20000000000000001"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="point" keyPath="shadowOffset">
                                                        <point key="value" x="0.0" y="6"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="shadowRadius">
                                                        <real key="value" value="14"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                        <real key="value" value="4"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Register.Register"/>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <action selector="registerButtonPressed:" destination="L3Q-ca-OsR" eventType="touchUpInside" id="GWg-vM-apq"/>
                                                </connections>
                                            </button>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="hnU-V5-P7a">
                                                <rect key="frame" x="0.0" y="912.5" width="335" height="25"/>
                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="25" id="YIQ-cK-MCW"/>
                                                </constraints>
                                            </view>
                                        </subviews>
                                        <color key="backgroundColor" red="0.95294117649999999" green="0.95294117649999999" blue="0.95294117649999999" alpha="1" colorSpace="calibratedRGB"/>
                                        <constraints>
                                            <constraint firstItem="RUV-Sh-Who" firstAttribute="leading" secondItem="mge-u1-kPm" secondAttribute="leading" id="FRc-VE-2Ji"/>
                                            <constraint firstAttribute="trailing" secondItem="RUV-Sh-Who" secondAttribute="trailing" id="G0g-bQ-Gps"/>
                                        </constraints>
                                    </stackView>
                                </subviews>
                                <color key="backgroundColor" red="0.95294117649999999" green="0.95294117649999999" blue="0.95294117649999999" alpha="1" colorSpace="calibratedRGB"/>
                                <constraints>
                                    <constraint firstItem="mge-u1-kPm" firstAttribute="width" secondItem="4Hh-TA-bfO" secondAttribute="width" constant="-40" id="EgP-Co-utG"/>
                                    <constraint firstItem="mge-u1-kPm" firstAttribute="leading" secondItem="4Hh-TA-bfO" secondAttribute="leading" constant="20" id="HEW-xq-jYQ"/>
                                    <constraint firstAttribute="bottom" secondItem="mge-u1-kPm" secondAttribute="bottom" id="j4M-xA-WsT"/>
                                    <constraint firstItem="mge-u1-kPm" firstAttribute="top" secondItem="4Hh-TA-bfO" secondAttribute="top" id="kH0-mx-yFP"/>
                                    <constraint firstAttribute="trailing" secondItem="mge-u1-kPm" secondAttribute="trailing" constant="20" id="obh-5Z-1ah"/>
                                </constraints>
                            </scrollView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="o8R-Zm-hHh"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="o8R-Zm-hHh" firstAttribute="trailing" secondItem="4Hh-TA-bfO" secondAttribute="trailing" id="T43-oN-eLS"/>
                            <constraint firstItem="o8R-Zm-hHh" firstAttribute="bottom" secondItem="4Hh-TA-bfO" secondAttribute="bottom" id="V0g-cg-EUH"/>
                            <constraint firstItem="4Hh-TA-bfO" firstAttribute="top" secondItem="o8R-Zm-hHh" secondAttribute="top" id="d1P-by-k5E"/>
                            <constraint firstItem="4Hh-TA-bfO" firstAttribute="leading" secondItem="o8R-Zm-hHh" secondAttribute="leading" id="kOW-nI-xPP"/>
                        </constraints>
                    </view>
                    <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
                    <size key="freeformSize" width="375" height="1200"/>
                    <connections>
                        <outlet property="acceptCheckbox" destination="Hhi-iW-e22" id="V0r-Fe-Ijt"/>
                        <outlet property="addressTextField" destination="XOC-Ss-xfd" id="9FP-8p-kQz"/>
                        <outlet property="birthdayTextField" destination="vlV-fe-LUj" id="XmK-aX-LrH"/>
                        <outlet property="btnRegister" destination="RUV-Sh-Who" id="HIr-Sd-TQ1"/>
                        <outlet property="cityTextField" destination="eXc-wz-DkB" id="Qrt-yc-gf2"/>
                        <outlet property="cmndTextField" destination="N5g-za-hTI" id="zH2-9s-eAH"/>
                        <outlet property="districtTextField" destination="JkL-6f-9Zx" id="6EQ-hu-6rA"/>
                        <outlet property="emailTextField" destination="a8C-sI-7vJ" id="3hd-vD-6Ix"/>
                        <outlet property="firstNameTextField" destination="rNE-hg-oSq" id="Bxa-cC-Qzf"/>
                        <outlet property="genderTextField" destination="bIP-Yg-Xlq" id="RVm-cb-TZ0"/>
                        <outlet property="nameTextField" destination="m1I-56-V0K" id="nVm-7K-rGe"/>
                        <outlet property="passwordTextField" destination="zBm-Vj-hbN" id="E9j-4T-BbA"/>
                        <outlet property="phoneTextField" destination="VOx-U2-5hO" id="ac4-tW-F06"/>
                        <outlet property="policyConfirmLabel" destination="LeM-QY-NzJ" id="NtL-pb-4WF"/>
                        <outlet property="receiveCardCheckbox" destination="Oc6-c2-yqK" id="HDL-0r-hr4"/>
                        <outlet property="repasswordTextField" destination="EF8-JO-TYf" id="eSz-Bp-Zqj"/>
                        <outlet property="scrollView" destination="4Hh-TA-bfO" id="2a1-Pt-U5X"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="XyQ-Cj-gzf" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="621.60000000000002" y="-117.84107946026987"/>
        </scene>
        <!--Forgot Pass View Controller-->
        <scene sceneID="icP-jA-T0d">
            <objects>
                <viewController storyboardIdentifier="ForgotPassViewController" id="vOc-Mr-gnY" customClass="ForgotPassViewController" customModule="Beta_Cinemas" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="dx9-mO-oaX">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="647"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Ydr-Ot-ZWX" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                <rect key="frame" x="20" y="20" width="335" height="40.5"/>
                                <string key="text">Mật khẩu mới sẽ được gửi về 
Email tài khoản của bạn!</string>
                                <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                <color key="textColor" red="0.**********" green="0.**********" blue="0.15686274510000001" alpha="1" colorSpace="calibratedRGB"/>
                                <nil key="highlightedColor"/>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="ForgotPass.GuideTitle"/>
                                </userDefinedRuntimeAttributes>
                            </label>
                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Email" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="Slz-af-spG" customClass="InputTextField" customModule="Beta_Cinemas" customModuleProvider="target">
                                <rect key="frame" x="20" y="78.5" width="335" height="50"/>
                                <color key="tintColor" red="0.72549019609999998" green="0.72549019609999998" blue="0.72549019609999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="50" id="0uU-pp-2OY"/>
                                </constraints>
                                <color key="textColor" red="0.72549019609999998" green="0.72549019609999998" blue="0.72549019609999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                <textInputTraits key="textInputTraits" keyboardType="emailAddress" returnKeyType="go"/>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="image" keyPath="leftIcon" value="ic_mail"/>
                                    <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="ForgotPass.Email"/>
                                </userDefinedRuntimeAttributes>
                                <connections>
                                    <outlet property="delegate" destination="vOc-Mr-gnY" id="AHp-Fu-gxY"/>
                                </connections>
                            </textField>
                            <button opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="yZr-jA-who" customClass="GradientButton" customModule="Beta_Cinemas" customModuleProvider="target">
                                <rect key="frame" x="20" y="152.5" width="335" height="56"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="56" id="MTr-B4-5KR"/>
                                    <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="56" id="hP2-c1-ybS"/>
                                </constraints>
                                <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="20"/>
                                <state key="normal" title="GỬI">
                                    <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                </state>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                        <real key="value" value="4"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="point" keyPath="shadowOffset">
                                        <point key="value" x="0.0" y="6"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="color" keyPath="shadowColor">
                                        <color key="value" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="number" keyPath="shadowOpacity">
                                        <real key="value" value="0.20000000000000001"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="number" keyPath="shadowRadius">
                                        <real key="value" value="14"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Bt.SEND"/>
                                </userDefinedRuntimeAttributes>
                                <connections>
                                    <action selector="didTapSend:" destination="vOc-Mr-gnY" eventType="touchUpInside" id="M1g-tY-ycj"/>
                                </connections>
                            </button>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="U9V-6S-g1i"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="Ydr-Ot-ZWX" firstAttribute="top" secondItem="U9V-6S-g1i" secondAttribute="top" constant="20" id="Hop-aJ-rll"/>
                            <constraint firstItem="yZr-jA-who" firstAttribute="leading" secondItem="U9V-6S-g1i" secondAttribute="leading" constant="20" id="HzY-tk-mkV"/>
                            <constraint firstItem="U9V-6S-g1i" firstAttribute="trailing" secondItem="Ydr-Ot-ZWX" secondAttribute="trailing" constant="20" id="Ir8-iW-Qha"/>
                            <constraint firstItem="U9V-6S-g1i" firstAttribute="trailing" secondItem="Slz-af-spG" secondAttribute="trailing" constant="20" id="JXF-LY-9Tm"/>
                            <constraint firstItem="Ydr-Ot-ZWX" firstAttribute="leading" secondItem="U9V-6S-g1i" secondAttribute="leading" constant="20" id="Jek-jK-hXc"/>
                            <constraint firstItem="Slz-af-spG" firstAttribute="top" secondItem="Ydr-Ot-ZWX" secondAttribute="bottom" constant="18" id="Kdj-Zc-abR"/>
                            <constraint firstItem="U9V-6S-g1i" firstAttribute="trailing" secondItem="yZr-jA-who" secondAttribute="trailing" constant="20" id="WOo-er-vVg"/>
                            <constraint firstItem="yZr-jA-who" firstAttribute="top" secondItem="Slz-af-spG" secondAttribute="bottom" constant="24" id="vC5-S1-0YL"/>
                            <constraint firstItem="Slz-af-spG" firstAttribute="leading" secondItem="U9V-6S-g1i" secondAttribute="leading" constant="20" id="wOi-nC-NQv"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="Ecj-wn-Wf6"/>
                    <connections>
                        <outlet property="tfEmail" destination="Slz-af-spG" id="LE9-vU-pj4"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="xvs-WT-Xxd" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1300" y="-116"/>
        </scene>
        <!--Update Password View Controller-->
        <scene sceneID="kHp-9o-iv6">
            <objects>
                <viewController storyboardIdentifier="UpdatePasswordViewController" id="c45-xf-5AR" customClass="UpdatePasswordViewController" customModule="Beta_Cinemas" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="iap-3b-BDB">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="3WH-IV-9wY">
                                <rect key="frame" x="20" y="0.0" width="335" height="256.5"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Nhập mật khẩu mới" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="a3w-Be-MEr" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="22" width="335" height="20.5"/>
                                        <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                        <color key="textColor" red="0.*****************" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <nil key="highlightedColor"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="ChangePass.EnterNewPass"/>
                                        </userDefinedRuntimeAttributes>
                                    </label>
                                    <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Mật khẩu mới" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="9xf-Pa-CC9" customClass="InputTextField" customModule="Beta_Cinemas" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="48.5" width="335" height="50"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="50" id="Fe1-5k-1S0"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                        <textInputTraits key="textInputTraits" returnKeyType="next" secureTextEntry="YES"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="image" keyPath="leftIcon" value="ic_password"/>
                                            <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="ChangePass.NewPass"/>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <outlet property="delegate" destination="c45-xf-5AR" id="DTr-qQ-eW9"/>
                                        </connections>
                                    </textField>
                                    <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Nhập lại khẩu mới" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="QMQ-1P-LNS" customClass="InputTextField" customModule="Beta_Cinemas" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="116.5" width="335" height="50"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="50" id="hie-YO-4Qc"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                        <textInputTraits key="textInputTraits" returnKeyType="go" secureTextEntry="YES"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="image" keyPath="leftIcon" value="ic_comfirm_password"/>
                                            <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="ChangePass.ReNewPass"/>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <outlet property="delegate" destination="c45-xf-5AR" id="5KF-tA-Htc"/>
                                        </connections>
                                    </textField>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="8u8-Et-6V2" customClass="GradientButton" customModule="Beta_Cinemas" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="200.5" width="335" height="56"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="56" id="2nd-kt-WHm"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="20"/>
                                        <state key="normal" title="CẬP NHẬT"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                <real key="value" value="4"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="color" keyPath="secondColor">
                                                <color key="value" red="0.99215686270000003" green="0.48627450979999998" blue="0.0078431372550000003" alpha="0.98999999999999999" colorSpace="custom" customColorSpace="sRGB"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="color" keyPath="firstColor">
                                                <color key="value" red="0.99215686270000003" green="0.15686274510000001" blue="0.0078431372550000003" alpha="0.97999999999999998" colorSpace="custom" customColorSpace="sRGB"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="number" keyPath="shadowOpacity">
                                                <real key="value" value="0.20000000000000001"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="color" keyPath="shadowColor">
                                                <color key="value" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="point" keyPath="shadowOffset">
                                                <point key="value" x="0.0" y="6"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="number" keyPath="shadowRadius">
                                                <real key="value" value="4"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Bt.Update"/>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <action selector="updateBtPressed:" destination="c45-xf-5AR" eventType="touchUpInside" id="bUV-Ty-hY4"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="8u8-Et-6V2" firstAttribute="top" secondItem="QMQ-1P-LNS" secondAttribute="bottom" constant="34" id="0fX-Hn-T0N"/>
                                    <constraint firstItem="9xf-Pa-CC9" firstAttribute="leading" secondItem="3WH-IV-9wY" secondAttribute="leading" id="2iv-pW-65s"/>
                                    <constraint firstItem="a3w-Be-MEr" firstAttribute="top" secondItem="3WH-IV-9wY" secondAttribute="top" constant="22" id="Ddw-Oc-ai0"/>
                                    <constraint firstItem="QMQ-1P-LNS" firstAttribute="leading" secondItem="3WH-IV-9wY" secondAttribute="leading" id="IUL-k7-vHA"/>
                                    <constraint firstAttribute="trailing" secondItem="QMQ-1P-LNS" secondAttribute="trailing" id="N7S-XY-NMo"/>
                                    <constraint firstItem="a3w-Be-MEr" firstAttribute="leading" secondItem="3WH-IV-9wY" secondAttribute="leading" id="Rkm-2c-dUo"/>
                                    <constraint firstAttribute="trailing" secondItem="9xf-Pa-CC9" secondAttribute="trailing" id="S6a-lr-fRk"/>
                                    <constraint firstAttribute="trailing" secondItem="a3w-Be-MEr" secondAttribute="trailing" id="agT-uT-cP9"/>
                                    <constraint firstItem="9xf-Pa-CC9" firstAttribute="top" secondItem="a3w-Be-MEr" secondAttribute="bottom" constant="6" id="akh-X4-bRX"/>
                                    <constraint firstAttribute="trailing" secondItem="8u8-Et-6V2" secondAttribute="trailing" id="juL-zz-NuH"/>
                                    <constraint firstAttribute="bottom" secondItem="8u8-Et-6V2" secondAttribute="bottom" id="lCZ-bH-B2f"/>
                                    <constraint firstItem="QMQ-1P-LNS" firstAttribute="top" secondItem="9xf-Pa-CC9" secondAttribute="bottom" constant="18" id="s8G-Fh-jQG"/>
                                    <constraint firstItem="8u8-Et-6V2" firstAttribute="leading" secondItem="3WH-IV-9wY" secondAttribute="leading" id="tpZ-Uk-6ev"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="2eN-yH-Xgv"/>
                        <color key="backgroundColor" red="0.95294117649999999" green="0.95294117649999999" blue="0.95294117649999999" alpha="1" colorSpace="calibratedRGB"/>
                        <constraints>
                            <constraint firstItem="3WH-IV-9wY" firstAttribute="centerX" secondItem="iap-3b-BDB" secondAttribute="centerX" id="BRD-bH-Ofc"/>
                            <constraint firstItem="3WH-IV-9wY" firstAttribute="leading" secondItem="2eN-yH-Xgv" secondAttribute="leading" constant="20" id="OMy-Rm-mRA"/>
                            <constraint firstItem="3WH-IV-9wY" firstAttribute="top" secondItem="2eN-yH-Xgv" secondAttribute="top" id="f7z-q1-qbX"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="tfNewPass" destination="9xf-Pa-CC9" id="vAC-0r-Ew2"/>
                        <outlet property="tfNewPass2" destination="QMQ-1P-LNS" id="xTz-oR-7hC"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="mNj-dQ-IyD" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1286" y="510"/>
        </scene>
    </scenes>
    <designables>
        <designable name="7Ex-AE-Lne">
            <size key="intrinsicContentSize" width="228.5" height="20.5"/>
        </designable>
        <designable name="7is-02-4AH">
            <size key="intrinsicContentSize" width="34.5" height="20.5"/>
        </designable>
        <designable name="8u8-Et-6V2">
            <size key="intrinsicContentSize" width="74" height="42"/>
        </designable>
        <designable name="9xf-Pa-CC9">
            <size key="intrinsicContentSize" width="87" height="17"/>
        </designable>
        <designable name="EF8-JO-TYf">
            <size key="intrinsicContentSize" width="116" height="17"/>
        </designable>
        <designable name="JkL-6f-9Zx">
            <size key="intrinsicContentSize" width="83.5" height="18.5"/>
        </designable>
        <designable name="LeM-QY-NzJ">
            <size key="intrinsicContentSize" width="556" height="20.5"/>
        </designable>
        <designable name="N5g-za-hTI">
            <size key="intrinsicContentSize" width="107" height="18.5"/>
        </designable>
        <designable name="QMQ-1P-LNS">
            <size key="intrinsicContentSize" width="115" height="17"/>
        </designable>
        <designable name="R8x-FR-Eqq">
            <size key="intrinsicContentSize" width="61.5" height="21"/>
        </designable>
        <designable name="RUV-Sh-Who">
            <size key="intrinsicContentSize" width="66" height="42"/>
        </designable>
        <designable name="Slz-af-spG">
            <size key="intrinsicContentSize" width="38" height="22"/>
        </designable>
        <designable name="VOx-U2-5hO">
            <size key="intrinsicContentSize" width="85" height="18.5"/>
        </designable>
        <designable name="W0Q-Iu-t8R">
            <size key="intrinsicContentSize" width="216" height="42"/>
        </designable>
        <designable name="XOC-Ss-xfd">
            <size key="intrinsicContentSize" width="90.5" height="18.5"/>
        </designable>
        <designable name="Ydr-Ot-ZWX">
            <size key="intrinsicContentSize" width="191.5" height="40.5"/>
        </designable>
        <designable name="Ziw-TD-ybt">
            <size key="intrinsicContentSize" width="175.5" height="22"/>
        </designable>
        <designable name="a3w-Be-MEr">
            <size key="intrinsicContentSize" width="131.5" height="20.5"/>
        </designable>
        <designable name="a8C-sI-7vJ">
            <size key="intrinsicContentSize" width="34.5" height="18.5"/>
        </designable>
        <designable name="bIP-Yg-Xlq">
            <size key="intrinsicContentSize" width="53.5" height="18.5"/>
        </designable>
        <designable name="eXc-wz-DkB">
            <size key="intrinsicContentSize" width="105" height="18.5"/>
        </designable>
        <designable name="hta-Xc-axS">
            <size key="intrinsicContentSize" width="207" height="20.5"/>
        </designable>
        <designable name="kro-Jj-De6">
            <size key="intrinsicContentSize" width="141" height="30"/>
        </designable>
        <designable name="lp7-0U-v6W">
            <size key="intrinsicContentSize" width="88" height="42"/>
        </designable>
        <designable name="m1I-56-V0K">
            <size key="intrinsicContentSize" width="43" height="18.5"/>
        </designable>
        <designable name="m60-tU-fYx">
            <size key="intrinsicContentSize" width="184" height="42"/>
        </designable>
        <designable name="pvp-dH-r1b">
            <size key="intrinsicContentSize" width="118" height="31"/>
        </designable>
        <designable name="rKt-CC-gFz">
            <size key="intrinsicContentSize" width="152" height="30"/>
        </designable>
        <designable name="rNE-hg-oSq">
            <size key="intrinsicContentSize" width="43" height="18.5"/>
        </designable>
        <designable name="rrK-pD-7pN">
            <size key="intrinsicContentSize" width="171" height="42"/>
        </designable>
        <designable name="vlV-fe-LUj">
            <size key="intrinsicContentSize" width="64" height="18.5"/>
        </designable>
        <designable name="xkI-XK-blp">
            <size key="intrinsicContentSize" width="159.5" height="30"/>
        </designable>
        <designable name="yZr-jA-who">
            <size key="intrinsicContentSize" width="30" height="42"/>
        </designable>
        <designable name="zBm-Vj-hbN">
            <size key="intrinsicContentSize" width="60" height="17"/>
        </designable>
    </designables>
    <resources>
        <image name="icFinish" width="42" height="42"/>
        <image name="ic_address" width="26" height="26"/>
        <image name="ic_birthday" width="26" height="26"/>
        <image name="ic_checkbox_normal" width="24" height="24"/>
        <image name="ic_checkbox_select" width="22" height="22"/>
        <image name="ic_cmnd" width="26" height="26"/>
        <image name="ic_comfirm_password" width="26" height="26"/>
        <image name="ic_firstname" width="26" height="26"/>
        <image name="ic_home" width="26" height="26"/>
        <image name="ic_mail" width="26" height="26"/>
        <image name="ic_name" width="26" height="26"/>
        <image name="ic_password" width="26" height="26"/>
        <image name="ic_phone" width="26" height="26"/>
        <image name="ic_sex" width="26" height="26"/>
        <image name="ic_tower" width="26" height="26"/>
    </resources>
</document>
