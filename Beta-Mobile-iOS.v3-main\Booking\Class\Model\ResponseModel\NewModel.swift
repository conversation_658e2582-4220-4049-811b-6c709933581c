//
//  NewModel.swift
//  Booking
//
//  Created by <PERSON><PERSON> Vu on 4/5/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation
import ObjectMapper

class NewModel : Mappable {
     var CategoryId : String?
     var ParentCategoryId : String?
     var Description : String?
     var Order : Int?
     var SubChild : [AnyObject]?
     var Level : Int?
     var TermId : String?
     var Name : String?


    required init?(map: Map) {

    }

    func mapping(map: Map) {
        CategoryId           <- map["CategoryId"]
        ParentCategoryId     <- map["ParentCategoryId"]
        Description          <- map["Description"]
        Order                <- map["Order"]
        SubChild             <- map["SubChild"]
        Level                <- map["Level"]
        TermId               <- map["TermId"]
        Name                 <- map["Name"]
    }
}
