<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14490.70" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14490.49"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="Oswald-Light.ttf">
            <string>Oswald-<PERSON></string>
        </array>
        <array key="Oswald-Regular.ttf">
            <string>Oswald-Regular</string>
        </array>
        <array key="SourceSansPro-Bold.ttf">
            <string>SourceSansPro-Bold</string>
        </array>
        <array key="SourceSansPro-Italic.ttf">
            <string>SourceSansPro-Italic</string>
        </array>
        <array key="SourceSansPro-SemiBold.ttf">
            <string>SourceSansPro-SemiBold</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" rowHeight="234" id="KGk-i7-Jjw" customClass="MyVoucherTableViewCell" customModule="Booking_dev" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="320" height="234"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="233.5"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="g08-zB-yJ3">
                        <rect key="frame" x="8" y="6" width="304" height="221.5"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="dwb-Uf-PvK" customClass="RoundView" customModule="Booking_dev" customModuleProvider="target">
                                <rect key="frame" x="0.0" y="0.0" width="304" height="40"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="a8n-rn-Dkf">
                                        <rect key="frame" x="16" y="6.5" width="224" height="27"/>
                                        <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="18"/>
                                        <color key="textColor" red="0.28627450980392155" green="0.29803921568627451" blue="0.38431372549019605" alpha="1" colorSpace="calibratedRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Miễn phí" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="wka-si-ctf">
                                        <rect key="frame" x="248" y="11" width="40" height="18"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="40" id="FA6-NY-xHD"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" name="Oswald-Light" family="Oswald" pointSize="12"/>
                                        <color key="textColor" red="0.011764705882352941" green="0.34901960784313724" blue="0.61568627450980395" alpha="1" colorSpace="calibratedRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="trailing" secondItem="wka-si-ctf" secondAttribute="trailing" constant="16" id="1ag-CB-eGa"/>
                                    <constraint firstItem="wka-si-ctf" firstAttribute="leading" secondItem="a8n-rn-Dkf" secondAttribute="trailing" constant="8" id="HDi-zc-Jos"/>
                                    <constraint firstItem="a8n-rn-Dkf" firstAttribute="centerY" secondItem="dwb-Uf-PvK" secondAttribute="centerY" id="KWF-ny-qUG"/>
                                    <constraint firstItem="wka-si-ctf" firstAttribute="centerY" secondItem="dwb-Uf-PvK" secondAttribute="centerY" id="PRB-y1-OLu"/>
                                    <constraint firstAttribute="height" constant="40" id="hcs-sU-EIq"/>
                                    <constraint firstItem="a8n-rn-Dkf" firstAttribute="leading" secondItem="dwb-Uf-PvK" secondAttribute="leading" constant="16" id="tLv-dc-Kw7"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                        <real key="value" value="5"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="8mU-iX-jHJ" customClass="RoundView" customModule="Booking_dev" customModuleProvider="target">
                                <rect key="frame" x="0.0" y="44" width="304" height="177.5"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="LXo-21-403">
                                        <rect key="frame" x="16" y="16" width="272" height="25.5"/>
                                        <fontDescription key="fontDescription" name="SourceSansPro-Bold" family="Source Sans Pro" pointSize="20"/>
                                        <color key="textColor" red="0.070588235294117646" green="0.12156862745098039" blue="0.15686274509803921" alpha="1" colorSpace="calibratedRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ETe-Fh-YGt">
                                        <rect key="frame" x="16" y="93.5" width="272" height="18"/>
                                        <fontDescription key="fontDescription" name="SourceSansPro-SemiBold" family="Source Sans Pro" pointSize="14"/>
                                        <color key="textColor" red="0.99215686274509807" green="0.15686274509803921" blue="0.0078431372549019607" alpha="1" colorSpace="calibratedRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="dc2-ZY-neX" customClass="GradientButton" customModule="Booking_dev" customModuleProvider="target">
                                        <rect key="frame" x="16" y="121.5" width="132" height="40"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="40" id="2oh-8c-VUZ"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="16"/>
                                        <state key="normal" title="SỬ DỤNG">
                                            <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        </state>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                <real key="value" value="3"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="use"/>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <action selector="useTapped:" destination="KGk-i7-Jjw" eventType="touchUpInside" id="MeV-xT-M7e"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="TaI-Wf-nxa" customClass="RoundButton" customModule="Booking_dev" customModuleProvider="target">
                                        <rect key="frame" x="156" y="121.5" width="132" height="40"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="40" id="u5w-69-Scq"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="16"/>
                                        <state key="normal" title="TẶNG">
                                            <color key="titleColor" red="0.011764705882352941" green="0.34901960784313724" blue="0.61568627450980395" alpha="1" colorSpace="calibratedRGB"/>
                                        </state>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                                <color key="value" red="0.011764705882352941" green="0.34901960784313724" blue="0.61568627450980395" alpha="1" colorSpace="calibratedRGB"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                                <real key="value" value="1"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                <real key="value" value="3"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="donate"/>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <action selector="tTapped:" destination="KGk-i7-Jjw" eventType="touchUpInside" id="t9Y-lT-CCG"/>
                                        </connections>
                                    </button>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="gOx-TA-KHh">
                                        <rect key="frame" x="16" y="45.5" width="272" height="40"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="40" id="SXj-vw-ymZ"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" name="SourceSansPro-Italic" family="Source Sans Pro" pointSize="14"/>
                                        <color key="textColor" red="0.28627450980392155" green="0.29803921568627451" blue="0.38431372549019605" alpha="1" colorSpace="calibratedRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="TaI-Wf-nxa" firstAttribute="leading" secondItem="dc2-ZY-neX" secondAttribute="trailing" constant="8" id="0ab-sP-9aB"/>
                                    <constraint firstItem="ETe-Fh-YGt" firstAttribute="leading" secondItem="LXo-21-403" secondAttribute="leading" id="14y-Zk-uWX"/>
                                    <constraint firstAttribute="bottom" secondItem="TaI-Wf-nxa" secondAttribute="bottom" constant="16" id="1EY-JH-Eem"/>
                                    <constraint firstItem="gOx-TA-KHh" firstAttribute="leading" secondItem="LXo-21-403" secondAttribute="leading" id="30A-Lu-Frj"/>
                                    <constraint firstAttribute="bottom" secondItem="dc2-ZY-neX" secondAttribute="bottom" constant="16" id="Rpv-KN-RsR"/>
                                    <constraint firstItem="ETe-Fh-YGt" firstAttribute="trailing" secondItem="LXo-21-403" secondAttribute="trailing" id="Wii-8w-yNT"/>
                                    <constraint firstItem="gOx-TA-KHh" firstAttribute="trailing" secondItem="LXo-21-403" secondAttribute="trailing" id="cK4-dk-Hvh"/>
                                    <constraint firstItem="gOx-TA-KHh" firstAttribute="top" secondItem="LXo-21-403" secondAttribute="bottom" constant="4" id="cgz-7g-w1z"/>
                                    <constraint firstItem="LXo-21-403" firstAttribute="leading" secondItem="8mU-iX-jHJ" secondAttribute="leading" constant="16" id="dzI-HZ-GtL"/>
                                    <constraint firstItem="LXo-21-403" firstAttribute="top" secondItem="8mU-iX-jHJ" secondAttribute="top" constant="16" id="h1k-Mf-oZ7"/>
                                    <constraint firstAttribute="trailing" secondItem="LXo-21-403" secondAttribute="trailing" constant="16" id="mbo-LE-UTm"/>
                                    <constraint firstItem="dc2-ZY-neX" firstAttribute="leading" secondItem="8mU-iX-jHJ" secondAttribute="leading" constant="16" id="nnd-SY-h2S"/>
                                    <constraint firstAttribute="trailing" secondItem="TaI-Wf-nxa" secondAttribute="trailing" constant="16" id="r4O-Kt-nLH"/>
                                    <constraint firstItem="ETe-Fh-YGt" firstAttribute="top" secondItem="gOx-TA-KHh" secondAttribute="bottom" constant="8" id="s5S-mn-K9n"/>
                                    <constraint firstItem="TaI-Wf-nxa" firstAttribute="width" secondItem="dc2-ZY-neX" secondAttribute="width" id="zWW-KB-fZx"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                        <real key="value" value="5"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="color" keyPath="shadowColor">
                                        <color key="value" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="number" keyPath="shadowOpacity">
                                        <real key="value" value="0.10000000000000001"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="point" keyPath="shadowOffset">
                                        <point key="value" x="0.0" y="4"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="number" keyPath="shadowRadius">
                                        <real key="value" value="2"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="o1v-1j-BHD" customClass="DashView" customModule="Booking_dev" customModuleProvider="target">
                                <rect key="frame" x="4" y="40" width="296" height="4"/>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="4" id="CLf-gh-zus"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="dashNumber">
                                        <integer key="value" value="25"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="color" keyPath="dashColor">
                                        <color key="value" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="number" keyPath="dashWidth">
                                        <real key="value" value="4"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </view>
                        </subviews>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="8mU-iX-jHJ" firstAttribute="top" secondItem="o1v-1j-BHD" secondAttribute="bottom" id="1LV-Fn-wbu"/>
                            <constraint firstItem="dwb-Uf-PvK" firstAttribute="leading" secondItem="g08-zB-yJ3" secondAttribute="leading" id="Ggn-Cx-WOI"/>
                            <constraint firstAttribute="trailing" secondItem="o1v-1j-BHD" secondAttribute="trailing" constant="4" id="I2r-fm-YbP"/>
                            <constraint firstAttribute="bottom" secondItem="8mU-iX-jHJ" secondAttribute="bottom" id="IXI-pt-ZI4"/>
                            <constraint firstAttribute="trailing" secondItem="dwb-Uf-PvK" secondAttribute="trailing" id="Pk8-R7-Zbi"/>
                            <constraint firstItem="o1v-1j-BHD" firstAttribute="top" secondItem="dwb-Uf-PvK" secondAttribute="bottom" id="Uwf-06-S0R"/>
                            <constraint firstItem="dwb-Uf-PvK" firstAttribute="top" secondItem="g08-zB-yJ3" secondAttribute="top" id="cDz-gR-uQ8"/>
                            <constraint firstItem="8mU-iX-jHJ" firstAttribute="leading" secondItem="g08-zB-yJ3" secondAttribute="leading" id="lG5-NL-Ljf"/>
                            <constraint firstItem="o1v-1j-BHD" firstAttribute="leading" secondItem="g08-zB-yJ3" secondAttribute="leading" constant="4" id="lM5-dE-taQ"/>
                            <constraint firstAttribute="trailing" secondItem="8mU-iX-jHJ" secondAttribute="trailing" id="v31-gn-1eS"/>
                        </constraints>
                    </view>
                </subviews>
                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                <constraints>
                    <constraint firstAttribute="bottom" secondItem="g08-zB-yJ3" secondAttribute="bottom" constant="6" id="Js3-ST-5cQ"/>
                    <constraint firstAttribute="trailing" secondItem="g08-zB-yJ3" secondAttribute="trailing" constant="8" id="UcA-8c-Ftw"/>
                    <constraint firstItem="g08-zB-yJ3" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="8" id="fxj-8g-gVa"/>
                    <constraint firstItem="g08-zB-yJ3" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" constant="6" id="jkl-Ag-1QX"/>
                </constraints>
            </tableViewCellContentView>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <viewLayoutGuide key="safeArea" id="njF-e1-oar"/>
            <connections>
                <outlet property="codeLabel" destination="a8n-rn-Dkf" id="6ZY-sC-oqa"/>
                <outlet property="contentHeight" destination="SXj-vw-ymZ" id="ViX-cN-dg6"/>
                <outlet property="contentLabel" destination="gOx-TA-KHh" id="Ng8-gM-QZw"/>
                <outlet property="dateLabel" destination="ETe-Fh-YGt" id="HhL-00-CZO"/>
                <outlet property="stateLabel" destination="wka-si-ctf" id="dyM-jG-qv6"/>
                <outlet property="tButton" destination="TaI-Wf-nxa" id="acI-gI-WqT"/>
                <outlet property="titleLabel" destination="LXo-21-403" id="7Iu-AH-lCh"/>
                <outlet property="useButton" destination="dc2-ZY-neX" id="rvB-Fp-nvK"/>
            </connections>
            <point key="canvasLocation" x="131.8840579710145" y="215.625"/>
        </tableViewCell>
    </objects>
</document>
