//
//  RouteManager.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 8/15/19.
//  Copyright © 2019 ddkc. All rights reserved.
//

import Foundation
import RxSwift

enum RouteType: Int {
    case none = 0
    case showByFilm = 1
    case cinemaDetail = 2
    case filmDetail = 4
    case showByCinema = 5
    case promotionDetail = 6
    case freeVoucher = 7
    case transactionDetail = 8
    case voucherList = 9
    case point = 10
    case voucherDetail = 11
}

class RouteManager {

    private var vc: BaseViewController
    private var type: RouteType
    private var params: [Any]

    init(vc: BaseViewController, type: RouteType, params: [Any]) {
        self.vc = vc
        self.type = type
        self.params = params
    }

    func route() {

        if params.isEmpty {
            return
        }

        switch type {
        case .showByFilm:
            gotoChooseTime()
        case .cinemaDetail:
            gotoCinemaDetail(false)
        case .filmDetail:
            gotoFilmDetail()
        case .showByCinema:
            gotoCinemaDetail(true)
        case .promotionDetail:
            gotoNews()
        case .freeVoucher:
            gotoFreeVoucher()
        case .transactionDetail:
            gotoTransactionDetail()
        case .voucherList:
            AppDelegate.shared.gotoMyVoucher()
            vc.navigationController?.popToRootViewController(animated: false)
        case .point:
            gotoPointVC()
        case .voucherDetail:
            gotoVoucherDetail()
        default:
            break
        }
    }

    private func gotoVoucherDetail() {
        guard let id = params[0] as? String else {
            return
        }
        let vc = UIStoryboard.home[.newsDetail] as! NewsDetailViewController
        vc.type = NewType.voucherFromDirectLink(id, false)
        vc.hidesBottomBarWhenPushed = true
        vc.isFromRouter = true
        self.vc.show(vc)
    }

    private func gotoPointVC() {
        let vc = UIStoryboard.member[.rewardPoints] as! RewardPointsViewController
        vc.isFromRouter = true
        vc.hidesBottomBarWhenPushed = true
        self.vc.show(vc, sender: nil)
    }

    private func gotoTransactionDetail() {
        guard let id = params[0] as? String else {
            return
        }
        let vc = UIStoryboard.member[.transactionDetail] as! TransactionDetailViewController
        vc.invoiceId = id
        vc.hidesBottomBarWhenPushed = true
        vc.isFromRouter = true
        self.vc.show(vc, sender: nil)
    }

    private func gotoFreeVoucher() {
        guard let id = params[0] as? String else {
            return
        }
        let vc = UIStoryboard.home[.newsDetail] as! NewsDetailViewController
        vc.type = NewType.voucherFromDirectLink(id, false)
        vc.hidesBottomBarWhenPushed = true
        vc.isFromRouter = true
        self.vc.show(vc, sender: nil)
    }

    private func gotoNews(){
        guard let id = params[0] as? String else {
            return
        }
        vc.showLoading()
        EcmProvider.rx.request(.getNewWithId(id, nil, nil)).mapObject(DDKCResponse<NewsModel>.self)
            .subscribe(onNext:{[weak self] response in
                guard let `self` = self else {return}
                self.vc.dismissLoading()
                self.vc.handlerResponse(response, success: {
                    guard let object = response.Object else{
                        print("Wrong data")
                        return
                    }
                    let vc = UIStoryboard.home[.newsDetail] as! NewsDetailViewController
                    vc.type = NewType.news(object)
                    vc.hidesBottomBarWhenPushed = true
                    vc.isFromRouter = true
                    self.vc.show(vc, sender: nil)
                }, error: {

                })
                }, onError: { error in
                    self.vc.dismissLoading()
                    UIAlertController.showAlert(self.vc, message: error.localizedDescription)
            }).disposed(by: vc.disposeBag)
    }

    func gotoCinemaDetail(_ isChoosed: Bool = false) {
        guard let id = params[0] as? String else {
            return
        }
        vc.showLoading()
        CinemaProvider.rx.request(.cinemaDetail(id)).mapObject(DDKCResponse<CinemaModel>.self).subscribe(onNext: { response in
            self.vc.dismissLoading()
            if let cinema = response.Object {
                if isChoosed {
                    let vc = UIStoryboard.cinema[.chooseCinema] as! ChooseCinemasViewController
                    vc.cinema = cinema
                    vc.hidesBottomBarWhenPushed = true
                    vc.isFromRouter = true
                    self.vc.show(vc)
                } else {
                    let vc = UIStoryboard.cinema[.cinemaDetail] as! CinemaDetailViewController
                    vc.cinema = cinema
                    vc.hidesBottomBarWhenPushed = true
                    vc.isFromRouter = true
                    self.vc.show(vc)
                }

            }
        }).disposed(by: vc.disposeBag)
    }

    private func gotoFilmDetail() {
        guard let id = params[0] as? String else {
            return
        }
        let filmVC = UIStoryboard.film[.filmDetail] as! FilmDetailViewController
        filmVC.filmId = id
        filmVC.hidesBottomBarWhenPushed = true
        vc.show(filmVC)
    }

    private func gotoChooseTime(){
        guard let id = params[0] as? String else {
            return
        }
        vc.showLoading()
        FilmProvider.rx.request(.filmDetail(id)).mapObject(DDKCResponse<FilmModel>.self)
            .subscribe(onNext:{ response in
                self.vc.dismissLoading()
                if let film = response.Object {
                    let chooseTimeVC = UIStoryboard.film[.filmChooseTime] as! FilmChooseTimeViewController
                    chooseTimeVC.film = film
                    chooseTimeVC.fromHome = true
                    chooseTimeVC.hidesBottomBarWhenPushed = true
                    chooseTimeVC.isFromRouter = true
                    self.vc.show(chooseTimeVC)
                }
            }).disposed(by: vc.disposeBag)
    }

}
