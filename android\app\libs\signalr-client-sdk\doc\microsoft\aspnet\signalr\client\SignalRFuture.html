<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<title>SignalRFuture</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="SignalRFuture";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../microsoft/aspnet/signalr/client/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/SignalRFuture.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../microsoft/aspnet/signalr/client/PlatformComponent.html" title="interface in microsoft.aspnet.signalr.client"><span class="strong">Prev Class</span></a></li>
<li><a href="../../../../microsoft/aspnet/signalr/client/SimpleEntry.html" title="class in microsoft.aspnet.signalr.client"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?microsoft/aspnet/signalr/client/SignalRFuture.html" target="_top">Frames</a></li>
<li><a href="SignalRFuture.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">microsoft.aspnet.signalr.client</div>
<h2 title="Class SignalRFuture" class="title">Class SignalRFuture&lt;V&gt;</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>microsoft.aspnet.signalr.client.SignalRFuture&lt;V&gt;</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>java.util.concurrent.Future&lt;V&gt;</dd>
</dl>
<dl>
<dt>Direct Known Subclasses:</dt>
<dd><a href="../../../../microsoft/aspnet/signalr/client/UpdateableCancellableFuture.html" title="class in microsoft.aspnet.signalr.client">UpdateableCancellableFuture</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="strong">SignalRFuture&lt;V&gt;</span>
extends java.lang.Object
implements java.util.concurrent.Future&lt;V&gt;</pre>
<div class="block">Represents long running SignalR operations</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><strong><a href="../../../../microsoft/aspnet/signalr/client/SignalRFuture.html#SignalRFuture()">SignalRFuture</a></strong>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method_summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span>Methods</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../microsoft/aspnet/signalr/client/SignalRFuture.html#cancel()">cancel</a></strong>()</code>
<div class="block">Cancels the operation</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../microsoft/aspnet/signalr/client/SignalRFuture.html#cancel(boolean)">cancel</a></strong>(boolean&nbsp;mayInterruptIfRunning)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../microsoft/aspnet/signalr/client/SignalRFuture.html" title="class in microsoft.aspnet.signalr.client">SignalRFuture</a>&lt;<a href="../../../../microsoft/aspnet/signalr/client/SignalRFuture.html" title="type parameter in SignalRFuture">V</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../microsoft/aspnet/signalr/client/SignalRFuture.html#done(microsoft.aspnet.signalr.client.Action)">done</a></strong>(<a href="../../../../microsoft/aspnet/signalr/client/Action.html" title="interface in microsoft.aspnet.signalr.client">Action</a>&lt;<a href="../../../../microsoft/aspnet/signalr/client/SignalRFuture.html" title="type parameter in SignalRFuture">V</a>&gt;&nbsp;action)</code>
<div class="block">Handles the completion of the Future.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../microsoft/aspnet/signalr/client/SignalRFuture.html#errorWasTriggered()">errorWasTriggered</a></strong>()</code>
<div class="block">Indicates if an error was triggered</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../microsoft/aspnet/signalr/client/SignalRFuture.html" title="type parameter in SignalRFuture">V</a></code></td>
<td class="colLast"><code><strong><a href="../../../../microsoft/aspnet/signalr/client/SignalRFuture.html#get()">get</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../microsoft/aspnet/signalr/client/SignalRFuture.html" title="type parameter in SignalRFuture">V</a></code></td>
<td class="colLast"><code><strong><a href="../../../../microsoft/aspnet/signalr/client/SignalRFuture.html#get(long, java.util.concurrent.TimeUnit)">get</a></strong>(long&nbsp;timeout,
   java.util.concurrent.TimeUnit&nbsp;unit)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../microsoft/aspnet/signalr/client/SignalRFuture.html#isCancelled()">isCancelled</a></strong>()</code>
<div class="block">Indicates if the operation is cancelled</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../microsoft/aspnet/signalr/client/SignalRFuture.html#isDone()">isDone</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../microsoft/aspnet/signalr/client/SignalRFuture.html#onCancelled(java.lang.Runnable)">onCancelled</a></strong>(java.lang.Runnable&nbsp;onCancelled)</code>
<div class="block">Handles the cancellation event</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../microsoft/aspnet/signalr/client/SignalRFuture.html" title="class in microsoft.aspnet.signalr.client">SignalRFuture</a>&lt;<a href="../../../../microsoft/aspnet/signalr/client/SignalRFuture.html" title="type parameter in SignalRFuture">V</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../microsoft/aspnet/signalr/client/SignalRFuture.html#onError(microsoft.aspnet.signalr.client.ErrorCallback)">onError</a></strong>(<a href="../../../../microsoft/aspnet/signalr/client/ErrorCallback.html" title="interface in microsoft.aspnet.signalr.client">ErrorCallback</a>&nbsp;errorCallback)</code>
<div class="block">Handles error during the execution of the Future.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../microsoft/aspnet/signalr/client/SignalRFuture.html#setResult(V)">setResult</a></strong>(<a href="../../../../microsoft/aspnet/signalr/client/SignalRFuture.html" title="type parameter in SignalRFuture">V</a>&nbsp;result)</code>
<div class="block">Sets a result to the future and finishes its execution</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../microsoft/aspnet/signalr/client/SignalRFuture.html#triggerError(java.lang.Throwable)">triggerError</a></strong>(java.lang.Throwable&nbsp;error)</code>
<div class="block">Triggers an error for the Future</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="SignalRFuture()">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>SignalRFuture</h4>
<pre>public&nbsp;SignalRFuture()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method_detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="cancel()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cancel</h4>
<pre>public&nbsp;void&nbsp;cancel()</pre>
<div class="block">Cancels the operation</div>
</li>
</ul>
<a name="cancel(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cancel</h4>
<pre>public&nbsp;boolean&nbsp;cancel(boolean&nbsp;mayInterruptIfRunning)</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>cancel</code>&nbsp;in interface&nbsp;<code>java.util.concurrent.Future&lt;<a href="../../../../microsoft/aspnet/signalr/client/SignalRFuture.html" title="type parameter in SignalRFuture">V</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="done(microsoft.aspnet.signalr.client.Action)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>done</h4>
<pre>public&nbsp;<a href="../../../../microsoft/aspnet/signalr/client/SignalRFuture.html" title="class in microsoft.aspnet.signalr.client">SignalRFuture</a>&lt;<a href="../../../../microsoft/aspnet/signalr/client/SignalRFuture.html" title="type parameter in SignalRFuture">V</a>&gt;&nbsp;done(<a href="../../../../microsoft/aspnet/signalr/client/Action.html" title="interface in microsoft.aspnet.signalr.client">Action</a>&lt;<a href="../../../../microsoft/aspnet/signalr/client/SignalRFuture.html" title="type parameter in SignalRFuture">V</a>&gt;&nbsp;action)</pre>
<div class="block">Handles the completion of the Future. If the future was already
 completed, it triggers the handler right away.</div>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>action</code> - The handler</dd></dl>
</li>
</ul>
<a name="errorWasTriggered()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>errorWasTriggered</h4>
<pre>public&nbsp;boolean&nbsp;errorWasTriggered()</pre>
<div class="block">Indicates if an error was triggered</div>
<dl><dt><span class="strong">Returns:</span></dt><dd>True if an error was triggered</dd></dl>
</li>
</ul>
<a name="get()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get</h4>
<pre>public&nbsp;<a href="../../../../microsoft/aspnet/signalr/client/SignalRFuture.html" title="type parameter in SignalRFuture">V</a>&nbsp;get()
      throws java.lang.InterruptedException,
             java.util.concurrent.ExecutionException</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>get</code>&nbsp;in interface&nbsp;<code>java.util.concurrent.Future&lt;<a href="../../../../microsoft/aspnet/signalr/client/SignalRFuture.html" title="type parameter in SignalRFuture">V</a>&gt;</code></dd>
<dt><span class="strong">Throws:</span></dt>
<dd><code>java.lang.InterruptedException</code></dd>
<dd><code>java.util.concurrent.ExecutionException</code></dd></dl>
</li>
</ul>
<a name="get(long, java.util.concurrent.TimeUnit)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get</h4>
<pre>public&nbsp;<a href="../../../../microsoft/aspnet/signalr/client/SignalRFuture.html" title="type parameter in SignalRFuture">V</a>&nbsp;get(long&nbsp;timeout,
    java.util.concurrent.TimeUnit&nbsp;unit)
      throws java.lang.InterruptedException,
             java.util.concurrent.ExecutionException,
             java.util.concurrent.TimeoutException</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>get</code>&nbsp;in interface&nbsp;<code>java.util.concurrent.Future&lt;<a href="../../../../microsoft/aspnet/signalr/client/SignalRFuture.html" title="type parameter in SignalRFuture">V</a>&gt;</code></dd>
<dt><span class="strong">Throws:</span></dt>
<dd><code>java.lang.InterruptedException</code></dd>
<dd><code>java.util.concurrent.ExecutionException</code></dd>
<dd><code>java.util.concurrent.TimeoutException</code></dd></dl>
</li>
</ul>
<a name="isCancelled()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isCancelled</h4>
<pre>public&nbsp;boolean&nbsp;isCancelled()</pre>
<div class="block">Indicates if the operation is cancelled</div>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>isCancelled</code>&nbsp;in interface&nbsp;<code>java.util.concurrent.Future&lt;<a href="../../../../microsoft/aspnet/signalr/client/SignalRFuture.html" title="type parameter in SignalRFuture">V</a>&gt;</code></dd>
<dt><span class="strong">Returns:</span></dt><dd>True if the operation is cancelled</dd></dl>
</li>
</ul>
<a name="isDone()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDone</h4>
<pre>public&nbsp;boolean&nbsp;isDone()</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>isDone</code>&nbsp;in interface&nbsp;<code>java.util.concurrent.Future&lt;<a href="../../../../microsoft/aspnet/signalr/client/SignalRFuture.html" title="type parameter in SignalRFuture">V</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="onCancelled(java.lang.Runnable)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onCancelled</h4>
<pre>public&nbsp;void&nbsp;onCancelled(java.lang.Runnable&nbsp;onCancelled)</pre>
<div class="block">Handles the cancellation event</div>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>onCancelled</code> - The handler</dd></dl>
</li>
</ul>
<a name="onError(microsoft.aspnet.signalr.client.ErrorCallback)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onError</h4>
<pre>public&nbsp;<a href="../../../../microsoft/aspnet/signalr/client/SignalRFuture.html" title="class in microsoft.aspnet.signalr.client">SignalRFuture</a>&lt;<a href="../../../../microsoft/aspnet/signalr/client/SignalRFuture.html" title="type parameter in SignalRFuture">V</a>&gt;&nbsp;onError(<a href="../../../../microsoft/aspnet/signalr/client/ErrorCallback.html" title="interface in microsoft.aspnet.signalr.client">ErrorCallback</a>&nbsp;errorCallback)</pre>
<div class="block">Handles error during the execution of the Future. If it's the first time
 the method is invoked on the object and errors were already triggered,
 the handler will be called once per error, right away.</div>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>errorCallback</code> - The handler</dd></dl>
</li>
</ul>
<a name="setResult(java.lang.Object)">
<!--   -->
</a><a name="setResult(V)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setResult</h4>
<pre>public&nbsp;void&nbsp;setResult(<a href="../../../../microsoft/aspnet/signalr/client/SignalRFuture.html" title="type parameter in SignalRFuture">V</a>&nbsp;result)</pre>
<div class="block">Sets a result to the future and finishes its execution</div>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>result</code> - The future result</dd></dl>
</li>
</ul>
<a name="triggerError(java.lang.Throwable)">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>triggerError</h4>
<pre>public&nbsp;void&nbsp;triggerError(java.lang.Throwable&nbsp;error)</pre>
<div class="block">Triggers an error for the Future</div>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>error</code> - The error</dd></dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../microsoft/aspnet/signalr/client/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/SignalRFuture.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../microsoft/aspnet/signalr/client/PlatformComponent.html" title="interface in microsoft.aspnet.signalr.client"><span class="strong">Prev Class</span></a></li>
<li><a href="../../../../microsoft/aspnet/signalr/client/SimpleEntry.html" title="class in microsoft.aspnet.signalr.client"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?microsoft/aspnet/signalr/client/SignalRFuture.html" target="_top">Frames</a></li>
<li><a href="SignalRFuture.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
