//
//  TimeConstraintModel.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 4/5/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation
import ObjectMapper

class TimeConstraintModel : Mappable {
     var TimeConstraintId : String?
     var Name : String?
     var UniqueName : String?
     var IsEnabled : Bool?


    required init?(map: Map) {

    }

    func mapping(map: Map) {
        TimeConstraintId     <- map["TimeConstraintId"]
        Name                 <- map["Name"]
        UniqueName           <- map["UniqueName"]
        IsEnabled            <- map["IsEnabled"]
    }
}
