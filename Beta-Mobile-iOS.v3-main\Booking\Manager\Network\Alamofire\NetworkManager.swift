//
//  TestNetwork.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 4/10/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation
import Alamofire
import ObjectMapper
typealias NetworkHandler<T: BaseMappable> = (_ response: DDKCResult<T>?, _ error: Error?) -> Void
class NetworkManager: NSObject {
    
    static let shared = NetworkManager()
    
    var alamofireManager: SessionManager!
    
    private override init() {
        let configuration = URLSessionConfiguration.default
        configuration.timeoutIntervalForRequest = 10 //seconds
        configuration.timeoutIntervalForResource = 10
        alamofireManager = Alamofire.SessionManager(configuration:configuration)
    }
    
    func getRequest<T>(path: String, params: [String: Any]? = nil, completionHandler:@escaping NetworkHandler<T>){
        let url = Config.BaseURL + path
        print("URL: \(url)")
        alamofireManager.request(url, parameters: params).responseJSON { (response) in
            print("Response: \(response)")
            guard response.error == nil else{
                completionHandler(nil, response.error)
                return
            }
            
            guard let resJson = response.result.value as? [String: Any] else {
                    completionHandler(nil, nil)
                    return
            }
            
            let result = DDKCResult<T>(dict: resJson)
            completionHandler(result, nil)
        }
    }
    
//    func getFullRequest<T>(path: String, params: [String: Any], completionHandler:@escaping NetworkHandler<T>){
//        let url = Config.BaseURL + path
//        print("URL: \(url)")
//        alamofireManager.request(url, method: HTTPMethod.get, parameters: params, encoding: ParameterEncoding.default, headers: nil).responseJSON { (response) in
//            print("Response: \(response)")
//            guard response.error == nil else{
//                completionHandler(nil, response.error)
//                return
//            }
//
//            guard let resJson = response.result.value as? [String: Any] else {
//                completionHandler(nil, nil)
//                return
//            }
//
//            let result = DDKCResult<T>(dict: resJson)
//            completionHandler(result, nil)
//        }
//    }
}
