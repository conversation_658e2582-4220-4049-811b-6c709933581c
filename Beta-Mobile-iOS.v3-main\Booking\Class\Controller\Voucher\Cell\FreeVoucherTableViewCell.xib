<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14490.70" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14490.49"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="Oswald-Bold.ttf">
            <string>Oswald-Bold</string>
        </array>
        <array key="SourceSansPro-Bold.ttf">
            <string>SourceSansPro-Bold</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" rowHeight="286" id="KGk-i7-Jjw" customClass="FreeVoucherTableViewCell" customModule="Booking_dev" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="320" height="286"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="285.5"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="dqs-Hb-0dV">
                        <rect key="frame" x="8" y="8" width="304" height="269.5"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ufV-V1-jIj">
                                <rect key="frame" x="0.0" y="0.0" width="304" height="204.5"/>
                                <subviews>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="nHN-Fg-AX0">
                                        <rect key="frame" x="0.0" y="0.0" width="304" height="204.5"/>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    </imageView>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="nHN-Fg-AX0" firstAttribute="top" secondItem="ufV-V1-jIj" secondAttribute="top" id="S32-XR-QnX"/>
                                    <constraint firstItem="nHN-Fg-AX0" firstAttribute="leading" secondItem="ufV-V1-jIj" secondAttribute="leading" id="daL-di-kZF"/>
                                    <constraint firstAttribute="bottom" secondItem="nHN-Fg-AX0" secondAttribute="bottom" id="esv-Q2-Dc3"/>
                                    <constraint firstAttribute="trailing" secondItem="nHN-Fg-AX0" secondAttribute="trailing" id="jsO-3r-XGw"/>
                                </constraints>
                            </view>
                            <view alpha="0.75" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="AEh-4R-NMQ">
                                <rect key="frame" x="0.0" y="0.0" width="304" height="204.5"/>
                                <color key="backgroundColor" white="0.33333333333333331" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </view>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Hết mã Voucher" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="qFb-EI-93f">
                                <rect key="frame" x="74" y="84.5" width="156.5" height="36"/>
                                <fontDescription key="fontDescription" name="Oswald-Bold" family="Oswald" pointSize="24"/>
                                <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="q8D-mv-HCX">
                                <rect key="frame" x="0.0" y="204.5" width="304" height="65"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Mvv-hg-H5M">
                                        <rect key="frame" x="16" y="22.5" width="272" height="20.5"/>
                                        <fontDescription key="fontDescription" name="SourceSansPro-Bold" family="Source Sans Pro" pointSize="16"/>
                                        <color key="textColor" red="0.11764705882352941" green="0.12156862745098039" blue="0.15686274509803921" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="Mvv-hg-H5M" firstAttribute="leading" secondItem="q8D-mv-HCX" secondAttribute="leading" constant="16" id="Dfn-dx-eG1"/>
                                    <constraint firstAttribute="height" constant="65" id="Gxu-bo-er8"/>
                                    <constraint firstItem="Mvv-hg-H5M" firstAttribute="centerY" secondItem="q8D-mv-HCX" secondAttribute="centerY" id="cO2-dE-Nd4"/>
                                    <constraint firstAttribute="trailing" secondItem="Mvv-hg-H5M" secondAttribute="trailing" constant="16" id="qCK-cm-WuA"/>
                                </constraints>
                            </view>
                        </subviews>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="q8D-mv-HCX" firstAttribute="top" secondItem="ufV-V1-jIj" secondAttribute="bottom" id="Ij4-tj-ae5"/>
                            <constraint firstAttribute="bottom" secondItem="q8D-mv-HCX" secondAttribute="bottom" id="T09-X2-QSY"/>
                            <constraint firstAttribute="trailing" secondItem="ufV-V1-jIj" secondAttribute="trailing" id="Wyz-Ts-7cl"/>
                            <constraint firstItem="AEh-4R-NMQ" firstAttribute="bottom" secondItem="ufV-V1-jIj" secondAttribute="bottom" id="XbK-rQ-08u"/>
                            <constraint firstItem="AEh-4R-NMQ" firstAttribute="trailing" secondItem="ufV-V1-jIj" secondAttribute="trailing" id="Zka-dj-iGt"/>
                            <constraint firstItem="q8D-mv-HCX" firstAttribute="leading" secondItem="dqs-Hb-0dV" secondAttribute="leading" id="ai1-pi-NaH"/>
                            <constraint firstItem="AEh-4R-NMQ" firstAttribute="leading" secondItem="ufV-V1-jIj" secondAttribute="leading" id="dtN-DR-MIW"/>
                            <constraint firstItem="ufV-V1-jIj" firstAttribute="top" secondItem="dqs-Hb-0dV" secondAttribute="top" id="iCR-eM-ure"/>
                            <constraint firstItem="ufV-V1-jIj" firstAttribute="leading" secondItem="dqs-Hb-0dV" secondAttribute="leading" id="lkR-9Y-Mkg"/>
                            <constraint firstItem="AEh-4R-NMQ" firstAttribute="top" secondItem="ufV-V1-jIj" secondAttribute="top" id="ltf-3V-qxK"/>
                            <constraint firstAttribute="trailing" secondItem="q8D-mv-HCX" secondAttribute="trailing" id="pqh-t8-HEz"/>
                            <constraint firstItem="qFb-EI-93f" firstAttribute="centerY" secondItem="AEh-4R-NMQ" secondAttribute="centerY" id="v5h-as-RZ2"/>
                            <constraint firstItem="qFb-EI-93f" firstAttribute="centerX" secondItem="AEh-4R-NMQ" secondAttribute="centerX" id="w8B-7Y-j5b"/>
                        </constraints>
                    </view>
                </subviews>
                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                <constraints>
                    <constraint firstAttribute="trailing" secondItem="dqs-Hb-0dV" secondAttribute="trailing" constant="8" id="F80-LO-Pr5"/>
                    <constraint firstItem="dqs-Hb-0dV" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="8" id="IyD-9o-Wq1"/>
                    <constraint firstItem="dqs-Hb-0dV" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" constant="8" id="Pg1-aL-05U"/>
                    <constraint firstAttribute="bottom" secondItem="dqs-Hb-0dV" secondAttribute="bottom" constant="8" id="pFj-da-v28"/>
                </constraints>
            </tableViewCellContentView>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <viewLayoutGuide key="safeArea" id="njF-e1-oar"/>
            <connections>
                <outlet property="alphaView" destination="AEh-4R-NMQ" id="1S8-iO-HWd"/>
                <outlet property="lostCodeLabel" destination="qFb-EI-93f" id="x48-5R-1Qf"/>
                <outlet property="nameLabel" destination="Mvv-hg-H5M" id="1iJ-N6-Sq0"/>
                <outlet property="parentView" destination="dqs-Hb-0dV" id="LyN-5d-avw"/>
                <outlet property="previewImageView" destination="nHN-Fg-AX0" id="2Ml-56-jLi"/>
            </connections>
            <point key="canvasLocation" x="137.68115942028987" y="205.58035714285714"/>
        </tableViewCell>
    </objects>
</document>
