
import Foundation

// MARK: - DataBookingModel
struct DataBookingModel: Codable {
    let showID: String?
    let seats: [Seat]?
    let countDown: String?

    enum CodingKeys: String, CodingKey {
        case showID = "ShowId"
        case seats = "Seats"
        case countDown = "CountDown"
    }
}

// MARK: - Seat
struct Seat: Codable {
    let seatIndex: Int?
    let seatName, seatType, ticketTypeID: String?
    let price: Int?

    enum CodingKeys: String, CodingKey {
        case seatIndex = "SeatIndex"
        case seatName = "SeatName"
        case seatType = "SeatType"
        case ticketTypeID = "TicketTypeId"
        case price = "Price"
    }
    
    var isVip: Bool {
        return ticketTypeID == SeatType.Catalog.vip.rawValue
    }

    var isCouple: Bool {
        return ticketTypeID == SeatType.Catalog.couple.rawValue
    }

    var isNormal: Bool {
        return ticketTypeID == SeatType.Catalog.normal.rawValue
    }
}
