package vn.zenity.betacineplex.model

import android.location.Location
import android.os.Parcel
import android.os.Parcelable
import com.thoughtbot.expandablerecyclerview.models.ExpandableGroup
import vn.zenity.betacineplex.app.App
import vn.zenity.betacineplex.helper.extension.getDistance

class FilmBooking(var Name: String = "Cinema",
                  var Shows: List<ShowModel>? = null,
                  var ListShow: List<ShowModel>? = null,
                  var Address: String? = null,
                  var NameF: String? = null,
                  var CinemaId: String = "",
                  var Code: String? = null,
                  var Order: String? = "1",
                  var FilmFormatName_F: String? = "",
                  var Latitude: String? = null,
                  var Longtitude: String? = null,
                  var type: Int = 1): ExpandableGroup<FilmBookingContent>(Name, listOf(FilmBookingContent())){
    fun getDistanceToCurrentLocation(lct: Location?): Float {
        lct ?: return 10000f
        return lct.getDistance(Latitude, Longtitude)
    }
    var FilmFormatName: String? = ""
    get() {
        if (!App.shared().isLangVi() && FilmFormatName_F != null) {
            return FilmFormatName_F
        }
        return field
    }
}
class FilmBookingContent() : Parcelable {
    constructor(parcel: Parcel) : this() {
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {

    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<FilmBookingContent> {
        override fun createFromParcel(parcel: Parcel): FilmBookingContent {
            return FilmBookingContent(parcel)
        }

        override fun newArray(size: Int): Array<FilmBookingContent?> {
            return arrayOfNulls(size)
        }
    }
}