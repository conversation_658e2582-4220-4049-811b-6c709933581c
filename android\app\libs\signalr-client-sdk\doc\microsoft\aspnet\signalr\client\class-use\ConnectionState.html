<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<title>Uses of Class microsoft.aspnet.signalr.client.ConnectionState</title>
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="Uses of Class microsoft.aspnet.signalr.client.ConnectionState";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../microsoft/aspnet/signalr/client/package-summary.html">Package</a></li>
<li><a href="../../../../../microsoft/aspnet/signalr/client/ConnectionState.html" title="enum in microsoft.aspnet.signalr.client">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?microsoft/aspnet/signalr/client/class-use/ConnectionState.html" target="_top">Frames</a></li>
<li><a href="ConnectionState.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class microsoft.aspnet.signalr.client.ConnectionState" class="title">Uses of Class<br>microsoft.aspnet.signalr.client.ConnectionState</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="microsoft.aspnet.signalr.client">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../microsoft/aspnet/signalr/client/ConnectionState.html" title="enum in microsoft.aspnet.signalr.client">ConnectionState</a> in <a href="../../../../../microsoft/aspnet/signalr/client/package-summary.html">microsoft.aspnet.signalr.client</a></h3>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../microsoft/aspnet/signalr/client/package-summary.html">microsoft.aspnet.signalr.client</a> that return <a href="../../../../../microsoft/aspnet/signalr/client/ConnectionState.html" title="enum in microsoft.aspnet.signalr.client">ConnectionState</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../microsoft/aspnet/signalr/client/ConnectionState.html" title="enum in microsoft.aspnet.signalr.client">ConnectionState</a></code></td>
<td class="colLast"><span class="strong">ConnectionBase.</span><code><strong><a href="../../../../../microsoft/aspnet/signalr/client/ConnectionBase.html#getState()">getState</a></strong>()</code>
<div class="block">Returns the connection state</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../microsoft/aspnet/signalr/client/ConnectionState.html" title="enum in microsoft.aspnet.signalr.client">ConnectionState</a></code></td>
<td class="colLast"><span class="strong">Connection.</span><code><strong><a href="../../../../../microsoft/aspnet/signalr/client/Connection.html#getState()">getState</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../microsoft/aspnet/signalr/client/ConnectionState.html" title="enum in microsoft.aspnet.signalr.client">ConnectionState</a></code></td>
<td class="colLast"><span class="strong">ConnectionState.</span><code><strong><a href="../../../../../microsoft/aspnet/signalr/client/ConnectionState.html#valueOf(java.lang.String)">valueOf</a></strong>(java.lang.String&nbsp;name)</code>
<div class="block">Returns the enum constant of this type with the specified name.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../microsoft/aspnet/signalr/client/ConnectionState.html" title="enum in microsoft.aspnet.signalr.client">ConnectionState</a>[]</code></td>
<td class="colLast"><span class="strong">ConnectionState.</span><code><strong><a href="../../../../../microsoft/aspnet/signalr/client/ConnectionState.html#values()">values</a></strong>()</code>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../microsoft/aspnet/signalr/client/package-summary.html">microsoft.aspnet.signalr.client</a> with parameters of type <a href="../../../../../microsoft/aspnet/signalr/client/ConnectionState.html" title="enum in microsoft.aspnet.signalr.client">ConnectionState</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">StateChangedCallback.</span><code><strong><a href="../../../../../microsoft/aspnet/signalr/client/StateChangedCallback.html#stateChanged(microsoft.aspnet.signalr.client.ConnectionState, microsoft.aspnet.signalr.client.ConnectionState)">stateChanged</a></strong>(<a href="../../../../../microsoft/aspnet/signalr/client/ConnectionState.html" title="enum in microsoft.aspnet.signalr.client">ConnectionState</a>&nbsp;oldState,
            <a href="../../../../../microsoft/aspnet/signalr/client/ConnectionState.html" title="enum in microsoft.aspnet.signalr.client">ConnectionState</a>&nbsp;newState)</code>&nbsp;</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing constructors, and an explanation">
<caption><span>Constructors in <a href="../../../../../microsoft/aspnet/signalr/client/package-summary.html">microsoft.aspnet.signalr.client</a> with parameters of type <a href="../../../../../microsoft/aspnet/signalr/client/ConnectionState.html" title="enum in microsoft.aspnet.signalr.client">ConnectionState</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><strong><a href="../../../../../microsoft/aspnet/signalr/client/InvalidStateException.html#InvalidStateException(microsoft.aspnet.signalr.client.ConnectionState)">InvalidStateException</a></strong>(<a href="../../../../../microsoft/aspnet/signalr/client/ConnectionState.html" title="enum in microsoft.aspnet.signalr.client">ConnectionState</a>&nbsp;connectionState)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../microsoft/aspnet/signalr/client/package-summary.html">Package</a></li>
<li><a href="../../../../../microsoft/aspnet/signalr/client/ConnectionState.html" title="enum in microsoft.aspnet.signalr.client">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?microsoft/aspnet/signalr/client/class-use/ConnectionState.html" target="_top">Frames</a></li>
<li><a href="ConnectionState.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
