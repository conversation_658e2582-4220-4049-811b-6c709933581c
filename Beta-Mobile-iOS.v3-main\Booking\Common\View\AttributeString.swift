//
//  AttributeString.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 8/4/19.
//  Copyright © 2019 ddkc. All rights reserved.
//

import Foundation

extension NSMutableAttributedString {
    @discardableResult func string(_ text: String, font: UIFont) -> NSMutableAttributedString {
        let attrs: [NSAttributedStringKey: Any] = [.font: font]
        let boldString = NSMutableAttributedString(string:text, attributes: attrs)
        append(boldString)

        return self
    }

    @discardableResult func normal(_ text: String) -> NSMutableAttributedString {
        let normal = NSAttributedString(string: text)
        append(normal)

        return self
    }
}
