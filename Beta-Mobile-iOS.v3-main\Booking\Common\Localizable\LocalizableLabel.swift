//
//  LocalizableLabel.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 4/14/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit

@IBDesignable
class LocalizableLabel: UILabel, Localizable {
    @IBInspectable var localizableString: String? {
        didSet { updateLocalizable() }
    }

    func updateLocalizable() {
        if let text = localizableString {
            self.text = text.localized
        }
    }
}
