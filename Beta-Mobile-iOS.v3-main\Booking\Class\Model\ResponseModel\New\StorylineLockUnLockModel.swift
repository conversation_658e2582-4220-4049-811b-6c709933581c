//
//  StorylineLockUnLockModel.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 4/5/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation
import ObjectMapper

class StorylineLockUnLockModel : Mappable {
     var StorylineID : String?
     var LockStatusWithUser : Int?
     var LastLockedByUserID : String?
     var LastLockedByUserName : String?
     var LastLockedOnDate : String?


    required init?(map: Map) {

    }

    func mapping(map: Map) {
        StorylineID          <- map["StorylineID"]
        LockStatusWithUser   <- map["LockStatusWithUser"]
        LastLockedByUserID   <- map["LastLockedByUserID"]
        LastLockedByUserName <- map["LastLockedByUserName"]
        LastLockedOnDate     <- map["LastLockedOnDate"]
    }
}
