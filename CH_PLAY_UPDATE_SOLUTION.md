# 🚀 CH Play Update Solution - Android to Flutter

## 🎯 **VẤN ĐỀ ĐÃ ĐƯỢC GIẢI QUYẾT**

Người dùng đã cài **Android app từ CH Play** giờ có thể **cập nhật lên Flutter app** mà không gặp lỗi signing!

## ✅ **CÁC YẾU TỐ ĐÃ ĐƯỢC CHUẨN BỊ**

### **1. 📦 Package Name (Application ID)**
```gradle
// ✅ ĐÚNG - Giống với Android repo trên CH Play
applicationId "com.beta.betacineplex"
```

### **2. 🔑 Signing Certificate (Keystore)**
```gradle
// ✅ ĐÚNG - Sử dụng CHÍNH XÁC cùng production keystore
signingConfigs {
    release {
        keyAlias 'beta cineplex'
        keyPassword 'Betacorpvn@123'
        storeFile file('keystore/beta_cineplex_app_key.jks')
        storePassword 'Betacorpvn@123'
    }
}
```

### **3. 📊 Version Code & Version Name**
```gradle
// ✅ ĐÃ TĂNG - <PERSON> hơn Android repo hiện tại
// Android repo (CH Play): versionCode 48, versionName "2.7.6"
// Flutter repo (mới):     versionCode 61, versionName "2.8.1"
versionCode 61
versionName "2.8.1"
```

## 🛠️ **CÁCH SỬ DỤNG**

### **Bước 1: Verify Compatibility**
```batch
# Chạy script kiểm tra
scripts\verify_update_compatibility.bat
```

### **Bước 2: Build for CH Play**
```batch
# Build APK và AAB với production keystore
scripts\build_for_chplay_update.bat
```

### **Bước 3: Compare Signatures (Optional)**
```batch
# So sánh signature để đảm bảo
scripts\compare_signatures.bat
```

### **Bước 4: Upload to Google Play Console**
1. Đăng nhập **Google Play Console**
2. Chọn app **"Beta Cinemas"** (`com.beta.betacineplex`)
3. Vào **Production** → **Create new release**
4. Upload file: `build\app\outputs\bundle\release\app-release.aab`
5. Điền **Release notes** (changelog)
6. **Review** và **Start rollout to production**

## 🔍 **VERIFICATION CHECKLIST**

### ✅ **Trước khi upload:**
- [ ] Package name: `com.beta.betacineplex`
- [ ] Version code: `61` (cao hơn `48`)
- [ ] Version name: `2.8.1` (cao hơn `2.7.6`)
- [ ] Keystore: `beta_cineplex_app_key.jks`
- [ ] Key alias: `beta cineplex`
- [ ] Signature matches production keystore

### ✅ **Sau khi upload:**
- [ ] **Internal Testing** với internal testers
- [ ] **Staged Rollout**: 5% → 20% → 50% → 100%
- [ ] **Monitor**: Crash reports và user feedback

## 🚨 **TROUBLESHOOTING**

### **Lỗi "Certificate mismatch":**
```batch
# 1. Kiểm tra keystore đang dùng
scripts\compare_signatures.bat

# 2. Đảm bảo dùng đúng production keystore
# Kiểm tra: android\app\build.gradle → signingConfigs.release

# 3. Clean và rebuild
flutter clean
flutter build appbundle --release
```

### **Lỗi "Version code too low":**
```gradle
// Tăng version code trong android\app\build.gradle
flutterVersionCode = '62'  // Tăng lên cao hơn nữa

// Và trong pubspec.yaml
version: 2.8.2+62
```

### **Lỗi "Package name mismatch":**
```gradle
// Kiểm tra applicationId trong android\app\build.gradle
applicationId "com.beta.betacineplex"  // PHẢI ĐÚNG
```

## 📋 **TECHNICAL DETAILS**

### **Android Repo (Current on CH Play):**
- **Package:** `com.beta.betacineplex`
- **Version Code:** `48`
- **Version Name:** `2.7.6`
- **Keystore:** `beta_cineplex_app_key.jks`
- **Key Alias:** `beta cineplex`

### **Flutter Repo (Update):**
- **Package:** `com.beta.betacineplex` ✅
- **Version Code:** `61` ✅ (higher)
- **Version Name:** `2.8.1` ✅ (higher)
- **Keystore:** `beta_cineplex_app_key.jks` ✅ (same)
- **Key Alias:** `beta cineplex` ✅ (same)

## 🎉 **KẾT QUẢ**

**✅ Flutter app CÓ THỂ update Android app trên CH Play!**

Người dùng đã cài bản Android sẽ nhận được notification update và có thể cập nhật lên bản Flutter mà không gặp lỗi signing certificate.

## 📞 **HỖ TRỢ**

Nếu gặp vấn đề:
1. Chạy `scripts\verify_update_compatibility.bat` để kiểm tra
2. Chạy `scripts\compare_signatures.bat` để so sánh signature
3. Kiểm tra Google Play Console error messages
4. Đảm bảo đang dùng đúng production keystore
