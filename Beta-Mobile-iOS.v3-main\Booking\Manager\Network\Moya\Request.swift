//
//  Request.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 8/7/19.
//  Copyright © 2019 ddkc. All rights reserved.
//

import Foundation
import Moya
import RxSwift

enum StatusCode: Int {
    case success = 200
    case tokenExpired = 401
}

extension Reactive where Base: MoyaProviderType {
    func request(_ target: Base.Target) -> Observable<Response> {
        return Observable.create({ [weak base] observer -> Disposable in
            let cancellableToken = base?.request(target, callbackQueue: nil, progress: nil) { result in
                switch result {
                case let .success(response):
                    if let code = StatusCode(rawValue: response.statusCode), code == .tokenExpired {
                        guard let vc = AppDelegate.shared.topVC else {
                            print("Missing top vc")
                            return
                        }
                        let alert = UIAlertController(title: nil, message: "Message_401".localized, preferredStyle: .alert)
                        let okButton = UIAlertAction(title: "OK", style: .default, handler: { (_) in
                            Global.shared.logout()
                            AppDelegate.shared.gotoLogin()
                        })
                        alert.addAction(okButton)
                        vc.present(alert, animated: true, completion: nil)

                    }
                    observer.onNext(response)
                case let .failure(error):
                    observer.onError(error)
                }
            }

            return Disposables.create {
                cancellableToken?.cancel()
            }
        })
    }
}
