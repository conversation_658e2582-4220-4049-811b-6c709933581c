//
//  ListMetadataModel.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 4/5/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation
import ObjectMapper

class ListMetadataModel : Mappable {
     var Name : String?
     var Value : String?
     var IsShowOut : Bool?


    required init?(map: Map) {

    }

    func mapping(map: Map) {
        Name                 <- map["Name"]
        Value                <- map["Value"]
        IsShowOut            <- map["IsShowOut"]
    }
}
