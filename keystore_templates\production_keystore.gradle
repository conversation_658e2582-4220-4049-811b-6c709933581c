    signingConfigs {
        // 🔑 ===== KEYSTORE SELECTION =====
        // Uncomment ONE of the following configs to select keystore:
        
        // 🏭 PRODUCTION KEYSTORE (for release builds) - ACTIVE
        release {
            keyAlias 'beta cineplex'
            keyPassword 'Betacorpvn@123'
            storeFile file('keystore/beta_cineplex_app_key.jks')
            storePassword 'Betacorpvn@123'
        }
        
        // 🔧 DEBUG KEYSTORE (for development) - COMMENTED
        // release {
        //     keyAlias 'debug'
        //     keyPassword 'sdfoafojasdfji'
        //     storeFile file('keystore/debug.keystore')
        //     storePassword 'sdfoafojasdfji'
        // }
        
        // 👥 CUSTOMER KEYSTORE (for customer testing) - COMMENTED
        // release {
        //     keyAlias 'customer'
        //     keyPassword 'sdiidfjieiurier'
        //     storeFile file('keystore/customer.keystore')
        //     storePassword 'sdiidfjieiurier'
        // }
    }
