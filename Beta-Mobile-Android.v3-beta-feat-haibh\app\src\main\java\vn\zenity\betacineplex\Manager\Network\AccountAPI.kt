package vn.zenity.betacineplex.Manager.Network

import io.reactivex.Observable
import io.reactivex.Single
import retrofit2.http.*
import vn.zenity.betacineplex.model.*
import vn.zenity.betacineplex.model.RequestModel.LoginModel
import vn.zenity.betacineplex.model.RequestModel.RegisterModel
import vn.zenity.betacineplex.model.response.ResultSuccesModel

/**
 * Created by tinhvv on 4/8/18.
 */
interface AccountAPI {
    @POST("api/v1/erp/accounts")// Register
    fun register(@Body param: RegisterModel): Single<DDKCReponse<UserModel>>

    @PUT("api/v1/erp/accounts/{userId}")
    fun updateProfile(@Path("userId") userId: String, @Body param: RegisterModel): Observable<DDKCReponse<UserModel>>

    @GET("api/v1/erp/accounts/{userId}")
    fun getProfile(@Path("userId") userId: String): Observable<DDKCReponse<UserModel>>

    @GET("api/v1/erp/accounts/info-hompage")
    fun getTopInfo(): Observable<DDKCReponse<UserModel>>

    @POST("api/v1/erp/accounts/login")
    fun login(@Body param: LoginModel): Observable<DDKCReponse<UserModel>>

    @POST("api/v1/erp/accounts/confirm-password")
    fun confirmPassword(@Body param: LoginModel): Observable<DDKCReponse<ResultSuccesModel>>

    @PUT("api/v1/erp/accounts/change-password")
    fun changePassword(@Body mapData: Map<String, String>): Observable<DDKCReponse<ResultSuccesModel>>

    @POST("api/v1/erp/accounts/recovery-password")
    fun forgotPassword(@Body mapData: Map<String, String>): Observable<DDKCReponse<ResultSuccesModel>>

    @PUT("api/v1/erp/accounts/{id}/avatar")
    fun uploadAvatar(@Body mapData: Map<String, String>, @Path("id") accountId: String): Observable<DDKCReponse<ResultUploadAvatar>>

    @GET("api/v1/erp/accounts/{id}/points")
    fun getBetaPoint(@Path("id") accountId: String): Single<DDKCReponse<PointModel>>

    @GET("api/v1/erp/voucher")
    fun getListVoucher(@Query("customerId") customerId: String, @Query("cardTypeName") cardTypeName: String): Observable<DDKCReponse<List<VoucherModel>>>

    @GET("api/v2/erp/voucher")
    fun getListVoucher(): Observable<DDKCReponse<List<VoucherModel>>>

    @PUT("api/v2/erp/voucher/assign")
    fun registerVoucher(@Body data: Map<String, String>): Single<DDKCReponse<VoucherModel>>

    @POST("api/v1/erp/accounts/authentication-referral-code")
    fun registerReferCode(@Query("refCode") code: String): Single<DDKCReponse<Any>>

    @GET("api/v1/erp/accounts/{id}/cards")
    fun getListCardMember(@Path("id") accountId: String): Single<DDKCReponse<List<CardModel>>>

    @POST("api/v1/erp/accounts/login-facebook")
    fun loginFacebook(@Body data: Map<String, String>): Single<DDKCReponse<UserModel>>

    @GET("api/v1/ecm/categories/count/notifications/{lang}") //Time = 2018-04-01T15:28:21.453
    fun getNumberNotificationUnread(@Path("lang") lang: String = "", @Query("dt") dataTime: String): Single<DDKCReponse<HashMap<String, Int>>>

    @GET("/api/v1/erp/card-class")
    fun getCardClass(): Single<DDKCReponse<List<CardClass>>>

    @POST("/api/v1/erp/notifications/register-device-token")
    fun registerFCMToken(@Body data: Map<String, String>): Single<DDKCReponse<UserModel>>

    @PUT("/api/v1/erp/notifications/unregister-device-token")
    fun unregisterFCMToken(@Body data: Map<String, String>): Single<DDKCReponse<UserModel>>

    @PUT("api/v1/erp/accounts/update-password-facebook")
    fun updateFacebookPassword(@Body mapData: Map<String, String>): Observable<DDKCReponse<ResultSuccesModel>>

    @GET("/api/v1/erp/banner-slider")
    fun getBanner(): Observable<DDKCReponse<List<BannerModel>>>

    @GET("api/v1/erp/app-params?appType=android")
    fun getAppParams(): Observable<DDKCReponse<List<AppParamsModel>>>

    @GET("api/v1/erp/accounts/get-list-account")
    fun searchUser(@Query("textSearch") keyword: String): Observable<DDKCReponse<List<UserModel>>>

    @POST("api/v2/erp/voucher/donate")
    fun giveVoucher(@Body data: Map<String, String>): Observable<DDKCReponse<String>>

    @GET("api/v2/erp/voucher/history")
    fun getUsedVoucherHistories(): Single<DDKCReponse<List<VoucherHistoryModel>>>

    @GET("api/v2/erp/point/history")
    fun getUsedPointHistories(): Single<DDKCReponse<List<PointHistoryModel>>>

    @POST("api/v2/erp/point/donate")
    fun givePoint(@Body data: Map<String, String>): Single<DDKCReponse<String>>

}