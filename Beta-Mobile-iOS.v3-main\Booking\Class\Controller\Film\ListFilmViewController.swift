//
//  ListFilmViewController.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 3/14/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit
import ObjectMapper
import RxSwift

class ListFilmViewController: BaseViewController {
    @IBOutlet weak var tableView: UITableView!
    lazy var refreshControl: UIRefreshControl = {
        let refreshControl = UIRefreshControl()
        refreshControl.addTarget(self, action: #selector(self.refresh), for: .valueChanged)
        return refreshControl
    }()

    let dataSource = SimpleTableViewDataSource()
    let cellId = "FilmItemTableCell"
    var type: FilmType = .nowShowing

    override func viewDidLoad() {
        super.viewDidLoad()

        // Do any additional setup after loading the view.
        tableView.dataSource = dataSource
        tableView.register(UINib(nibName: cellId, bundle: nil), forCellReuseIdentifier: cellId)
        tableView.addSubview(refreshControl)

        // test
//        let items = (0...10).map { TableItem(title: "\($0)", cellId:cellId) }
//        dataSource.addRows(items)

        refreshControl.beginRefreshing()
        refresh()
    }

    func didLoadData(_ list: [FilmModel]) {
        let items = list.map { TableItem(data: $0, cellId:cellId) }
        if type == .nowShowing {
            items.first?.isOpen = true
        }
        dataSource.removeAll()
        dataSource.addRows(items)
        tableView.reloadData()
    }

    @objc func refresh() {
        if type == .nowShowing {
            getListFilm()
        } else {
            getListFilm(isShowing: true, isSpecial: true)
        }
    }

    override func showLoading() {
        refreshControl.beginRefreshing()
    }

    override func dismissLoading() {
        refreshControl.endRefreshing()
    }

    enum FilmType: Int {
        case nowShowing, special
    }
}

extension ListFilmViewController {
    private func getListFilm(isShowing: Bool = true, isSpecial: Bool = false){
        FilmProvider.rx.request(.listFilm(isShowing)).mapObject(DDKCResponse<FilmModel>.self)
            .subscribeOn(MainScheduler.instance)
            .subscribe(onNext:{ response in
                self.handlerResponse(response, success: {
                    guard let objects = response.ListObject else{
                        print("Data wrong")
                        return
                    }
                    if isSpecial {
                        self.didLoadData(objects.filter{ $0.HasSneakShow == true })
                    } else {
                        self.didLoadData(objects)
                    }

                    self.dismissLoading()
                })

            }, onError:{[weak self] error in
                guard let `self` = self else { return }
                self.dismissLoading()
//                UIAlertController.showAlert(self, message: error.localizedDescription)
            }).disposed(by: disposeBag)
    }

}

extension ListFilmViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        let vc = UIStoryboard.film[.filmChooseTime] as! FilmChooseTimeViewController
        vc.film = dataSource[indexPath].data as? FilmModel
        Tracking.shared.selectMovie(movieId: vc.film?.FilmId,movieName: vc.film?.getName())
        show(vc)
    }

    func tableView(_ tableView: UITableView, willDisplay cell: UITableViewCell, forRowAt indexPath: IndexPath) {
        let filmCell = cell as? FilmItemTableCell
        let film = dataSource[indexPath].data as? FilmModel
        filmCell?.onPlayFilmTrailer = { [weak self] in
            let vc = UIStoryboard.home[.youtube] as! YoutubeViewController
            vc.videoURL = film?.TrailerURL
            self?.present(vc, animated: true, completion: nil)
        }
    }
}
