<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14113" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14088"/>
        <capability name="Aspect ratio constraints" minToolsVersion="5.1"/>
        <capability name="Constraints with non-1.0 multipliers" minToolsVersion="5.1"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="SourceSansPro-Bold.ttf">
            <string>SourceSansPro-Bold</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="default" indentationWidth="10" rowHeight="131" id="KwW-1I-eNF" customClass="NewsTableViewCell" customModule="Booking_dev" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="816" height="131"/>
            <autoresizingMask key="autoresizingMask"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="KwW-1I-eNF" id="LzX-Cv-d5d">
                <rect key="frame" x="0.0" y="0.0" width="816" height="130.5"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="7e0-ES-lKG">
                        <rect key="frame" x="8" y="7" width="800" height="117"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="K5k-1u-DiQ" customClass="RoundView" customModule="Booking_dev" customModuleProvider="target">
                                <rect key="frame" x="20" y="16" width="772" height="97"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Thu 5 thoa suc Mam Mam Mam" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="zWV-ny-QF7">
                                        <rect key="frame" x="386" y="8" width="374" height="81"/>
                                        <fontDescription key="fontDescription" name="SourceSansPro-Bold" family="Source Sans Pro" pointSize="16"/>
                                        <color key="textColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="calibratedRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="7vO-a2-XYL">
                                        <rect key="frame" x="0.0" y="35" width="386" height="21"/>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                        <nil key="textColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="zWV-ny-QF7" firstAttribute="top" secondItem="K5k-1u-DiQ" secondAttribute="top" constant="8" id="0Rm-6M-ZKx"/>
                                    <constraint firstAttribute="trailing" secondItem="zWV-ny-QF7" secondAttribute="trailing" constant="12" id="36G-dH-5QT"/>
                                    <constraint firstAttribute="bottom" secondItem="zWV-ny-QF7" secondAttribute="bottom" constant="8" id="BVN-lG-9up"/>
                                    <constraint firstItem="7vO-a2-XYL" firstAttribute="top" secondItem="K5k-1u-DiQ" secondAttribute="top" constant="35" id="D8k-sn-J1X"/>
                                    <constraint firstItem="7vO-a2-XYL" firstAttribute="leading" secondItem="K5k-1u-DiQ" secondAttribute="leading" id="MaV-wb-faq"/>
                                    <constraint firstAttribute="bottom" secondItem="7vO-a2-XYL" secondAttribute="bottom" constant="41" id="Ocx-QY-m2d"/>
                                    <constraint firstAttribute="height" priority="750" constant="90" id="ihX-kV-Bkg"/>
                                    <constraint firstItem="zWV-ny-QF7" firstAttribute="leading" secondItem="7vO-a2-XYL" secondAttribute="trailing" id="kBo-cU-Jub"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                        <real key="value" value="4"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </view>
                            <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="s3X-gQ-qqC" userLabel="Image View" customClass="RoundView" customModule="Booking_dev" customModuleProvider="target">
                                <rect key="frame" x="8" y="4" width="390" height="97"/>
                                <subviews>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="bg1.png" translatesAutoresizingMaskIntoConstraints="NO" id="2S8-9Z-NiE">
                                        <rect key="frame" x="0.0" y="0.0" width="390" height="97"/>
                                        <constraints>
                                            <constraint firstAttribute="height" priority="800" constant="90" id="PcW-Y5-HQx"/>
                                            <constraint firstAttribute="width" secondItem="2S8-9Z-NiE" secondAttribute="height" multiplier="175:90" priority="900" id="Uhp-Xa-15a"/>
                                        </constraints>
                                    </imageView>
                                </subviews>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="2S8-9Z-NiE" firstAttribute="leading" secondItem="s3X-gQ-qqC" secondAttribute="leading" id="7T4-0D-SRu"/>
                                    <constraint firstAttribute="trailing" secondItem="2S8-9Z-NiE" secondAttribute="trailing" id="CML-Uy-37y"/>
                                    <constraint firstItem="2S8-9Z-NiE" firstAttribute="top" secondItem="s3X-gQ-qqC" secondAttribute="top" id="Czr-Sg-OuD"/>
                                    <constraint firstAttribute="bottom" secondItem="2S8-9Z-NiE" secondAttribute="bottom" id="mFm-zX-o3h"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                        <real key="value" value="4"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="point" keyPath="shadowOffset">
                                        <point key="value" x="0.0" y="2"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="number" keyPath="shadowRadius">
                                        <real key="value" value="4"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="color" keyPath="shadowColor">
                                        <color key="value" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </view>
                        </subviews>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="s3X-gQ-qqC" firstAttribute="width" secondItem="7e0-ES-lKG" secondAttribute="width" multiplier="175:359" id="0Sj-dA-KyX"/>
                            <constraint firstItem="7vO-a2-XYL" firstAttribute="width" secondItem="s3X-gQ-qqC" secondAttribute="width" constant="-4" id="242-Bu-Sko"/>
                            <constraint firstItem="s3X-gQ-qqC" firstAttribute="leading" secondItem="7e0-ES-lKG" secondAttribute="leading" constant="8" id="30K-Ze-GNo"/>
                            <constraint firstItem="s3X-gQ-qqC" firstAttribute="top" secondItem="7e0-ES-lKG" secondAttribute="top" constant="4" id="O0B-qi-sPe"/>
                            <constraint firstAttribute="bottom" secondItem="s3X-gQ-qqC" secondAttribute="bottom" constant="16" id="Qkr-rP-QCi"/>
                            <constraint firstAttribute="bottom" secondItem="K5k-1u-DiQ" secondAttribute="bottom" constant="4" id="TX6-SR-I60"/>
                            <constraint firstAttribute="trailing" secondItem="K5k-1u-DiQ" secondAttribute="trailing" constant="8" id="Ti4-ws-gSQ"/>
                            <constraint firstItem="K5k-1u-DiQ" firstAttribute="top" secondItem="7e0-ES-lKG" secondAttribute="top" constant="16" id="ktq-t6-uBj"/>
                            <constraint firstItem="K5k-1u-DiQ" firstAttribute="leading" secondItem="7e0-ES-lKG" secondAttribute="leading" constant="20" id="vig-kp-3QP"/>
                        </constraints>
                    </view>
                </subviews>
                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                <constraints>
                    <constraint firstAttribute="bottom" secondItem="7e0-ES-lKG" secondAttribute="bottom" constant="7" id="ARR-Ws-6ET"/>
                    <constraint firstAttribute="trailing" secondItem="7e0-ES-lKG" secondAttribute="trailing" constant="8" id="FIV-dh-ys0"/>
                    <constraint firstItem="7e0-ES-lKG" firstAttribute="top" secondItem="LzX-Cv-d5d" secondAttribute="top" constant="7" id="XKN-ob-fta"/>
                    <constraint firstItem="7e0-ES-lKG" firstAttribute="leading" secondItem="LzX-Cv-d5d" secondAttribute="leading" constant="8" id="Z8R-hg-AsS"/>
                </constraints>
            </tableViewCellContentView>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <connections>
                <outlet property="imvPromotion" destination="2S8-9Z-NiE" id="Y77-zC-wSn"/>
                <outlet property="lbTitle" destination="zWV-ny-QF7" id="wsQ-kj-NDc"/>
            </connections>
            <point key="canvasLocation" x="-105" y="223"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="bg1.png" width="320" height="568"/>
    </resources>
</document>
