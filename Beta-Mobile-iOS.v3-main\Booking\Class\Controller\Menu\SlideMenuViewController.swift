//
//  SlideMenuViewController.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 3/10/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit

class SlideMenuViewController: BaseViewController {
    @IBOutlet weak var tableView: UITableView!
    @IBOutlet weak var btRegisterByMovie: UIButton!
    @IBOutlet weak var btRegisterByTheater: UIButton!

    let menuDataSource = SimpleTableViewDataSource()
    let menuCellId = "MenuItemCell"
    var homeNavigationController: UINavigationController!
    var notificationCount: Int = 0

    override func viewDidLoad() {
        super.viewDidLoad()

        btRegisterByMovie.setBackgroundImage(UIColor.white.image(), for: .normal)
        btRegisterByTheater.setBackgroundImage(UIColor.white.image(), for: .normal)
        btRegisterByMovie.setBackgroundImage(UIColor.lightGray.withAlphaComponent(0.4).image(), for: .highlighted)
        btRegisterByTheater.setBackgroundImage(UIColor.lightGray.withAlphaComponent(0.4).image(), for: .highlighted)
        tableView.dataSource = menuDataSource
        loadMenu()
        getUnreadNotification()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)

        guard let viewController = homeNavigationController.visibleViewController,
            let item = tableItem(for: viewController),
            let indexPath = menuDataSource[item] else {
            return
        }

        tableView.selectRow(at: indexPath, animated: true, scrollPosition: .none)
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
    }

    func loadMenu() {
        var items = [TableItem]()

        items.append(TableItem(title: "Menu.Home".localized, cellId: menuCellId, icon: "ic_home", tag: .home))
        items.append(TableItem(title: "Menu.Member".localized, cellId: menuCellId, icon: "ic_account", tag: .member))
        items.append(TableItem(title: "Menu.Cenima".localized, cellId: menuCellId, icon: "ic_cenima", tag: .cinema))
//        items.append(TableItem(title: "Menu.Price".localized, cellId: menuCellId, icon: "ticketpriceUnselect", tag: .price))
        items.append(TableItem(title: "Menu.NewsAndHotDeals".localized, cellId: menuCellId, icon: "ic_news_hot_deals", tag: .newsAndHotDeal))
        items.append(TableItem(title: "Menu.Recruitment".localized, cellId: menuCellId, icon: "ic_carreer_unselect", tag: .recruitment))
        items.append(TableItem(title: "Menu.Notification".localized, cellId: menuCellId, icon: notificationCount > 0 ? "notificationUnselectRed" : "ic_notification", selectedIcon: "ic_notification", tag: .notification))
        items.append(TableItem(title: "Menu.Setting".localized, cellId: menuCellId, icon: "ic_setting", tag: .setting))

        menuDataSource.addRows(items)

        tableView.selectRow(at: IndexPath(row: 0, section: 0), animated: false, scrollPosition: .none)
    }

    func getUnreadNotification() {
//       let date = UserDefaults.standard.string(forKey: DefaultKey.lastNotificationDate.rawValue) ?? Date().toServerString()
//        EcmProvider.rx.request(.getNotificationCount(date)).mapObject(DDKCResponse<NotificationCount>.self).subscribe(onNext: { response in
//            guard let object = response.Object else {
//                return
//            }
//            self.notificationCount = object.NumberUnread ?? 0
//            self.updateNotifications()
//        }).disposed(by: disposeBag)
        guard let userId = Global.shared.user?.UserId else {
            return
        }
        EcmProvider.rx.request(.getNumberUnread(userId)).mapObject(DDKCResponse<NotificationCount>.self)
            .subscribe(onNext: { response in
                guard let object = response.Object else {
                    return
                }
                self.notificationCount = object.TotalUnread ?? 0
                self.updateNotifications()
            }).disposed(by: disposeBag)
    }

    override func updateNotifications(_ needReset: Bool = false) {
        let navigation = sideMenuViewController?.contentViewController as? UINavigationController
        let currentVC = navigation?.visibleViewController ?? sideMenuViewController?.contentViewController
        if let vc = currentVC as? BaseViewController {
            vc.updateNotifications()
        }

        let selectedIndexPath = tableView.indexPathForSelectedRow
        menuDataSource.removeAll()
        loadMenu()
        tableView.reloadData()
        if let indexPath = selectedIndexPath {
            tableView.selectRow(at: indexPath, animated: true, scrollPosition: .none)
        }
    }

    override func localizationDidChange() {
        super.localizationDidChange()
        let selectedIndexPath = tableView.indexPathForSelectedRow
        menuDataSource.removeAll()
        loadMenu()
        tableView.reloadData()
        tableView.selectRow(at: selectedIndexPath, animated: true, scrollPosition: .none)
    }

    @IBAction func closeButtonPressed(_ sender: UIButton) {
        sideMenuViewController?.hideMenuViewController()
    }

    @IBAction func bookingByCinemaPressed(_ sender: UIButton) {
        CATransaction.begin()
        CATransaction.setCompletionBlock {
            let vc = UIStoryboard.cinema[.listAllCinema] as! ListAllCinemasViewController
            vc.isBooking = true
            let navi = self.sideMenuViewController?.contentViewController as? UINavigationController
            navi?.pushViewController(vc, animated: true)
        }
        sideMenuViewController?.hideMenuViewController()
        CATransaction.commit()
    }

    @IBAction func bookingByMoviePressed(_ sender: UIButton) {

        CATransaction.begin()
        CATransaction.setCompletionBlock {
            let vc = UIStoryboard.film[.filmBooking]
            let navi = self.sideMenuViewController?.contentViewController as? UINavigationController
            navi?.pushViewController(vc, animated: true)
        }
        sideMenuViewController?.hideMenuViewController()
        CATransaction.commit()
//        let duration = sideMenuViewController?.animationDuration ?? 0
    }

    func closeMenu(_ completion: @escaping () -> Void) {
        CATransaction.begin()
        CATransaction.setCompletionBlock {
            completion()
        }
        CATransaction.commit()
    }

    func openView(_ tag: TableItemTag, shouldSelect: Bool = false) {
        if shouldSelect, let row = menuDataSource.sections.first?.items.index(where: { $0.tag == tag }) {
            let indexPath = IndexPath(row: row, section: 0)
            tableView.selectRow(at: indexPath, animated: true, scrollPosition: .none)
        }
        let navigation = sideMenuViewController?.contentViewController as? UINavigationController
        let currentVC = navigation?.visibleViewController ?? sideMenuViewController?.contentViewController

        if tag == .home && !(currentVC is HomeViewController) /* && sideMenuViewController?.contentViewController != homeNavigationController */{
            homeNavigationController.viewControllers.forEach {
                if let vc = $0 as? ChooseSeatViewController {
                    vc.removeAllSeatsAndStop()
                }
            }
            homeNavigationController.popToRootViewController(animated: true)
//            sideMenuViewController?.contentViewController = homeNavigationController
        } else if tag == .member {
            if Global.shared.isLogined && !(currentVC is MemberViewController) {
//                sideMenuViewController?.contentViewController = BaseNavigationViewController(rootViewController: UIStoryboard.member[.member])
                closeMenu {
                    navigation?.pushViewController(UIStoryboard.member[.member], animated: true)
                }
            } else if !Global.shared.isLogined && !(currentVC is LoginViewController) {
                let vc = UIStoryboard.authen[.login] as! LoginViewController
                vc.showMemberVC = true
//                sideMenuViewController?.contentViewController = BaseNavigationViewController(rootViewController: vc)
                closeMenu {
                    navigation?.pushViewController(vc, animated: true)
                }
            }
        } else if tag == .cinema && !(currentVC is ListAllCinemasViewController) {
//            sideMenuViewController?.contentViewController = BaseNavigationViewController(rootViewController: UIStoryboard.cinema[.listAllCinema])
            closeMenu {
                navigation?.pushViewController(UIStoryboard.cinema[.listAllCinema], animated: true)
            }
        } else if tag == .newsAndHotDeal && !(currentVC is NewsAndDealsViewController) {
//            sideMenuViewController?.contentViewController = BaseNavigationViewController(rootViewController: UIStoryboard.home[.newsAndDeals])
            closeMenu {
                navigation?.pushViewController(UIStoryboard.home[.newsAndDeals], animated: true)
            }
        } else if tag == .recruitment && !(currentVC is RecruitmentViewController) {
//                sideMenuViewController?.contentViewController = BaseNavigationViewController(rootViewController: UIStoryboard.home[.recruitment])
            closeMenu {
                navigation?.pushViewController(UIStoryboard.home[.recruitment], animated: true)
            }
        } else if tag == .notification && !(currentVC is NotificationViewController) {
//            sideMenuViewController?.contentViewController = BaseNavigationViewController(rootViewController: UIStoryboard.home[.notification])
            if let _ = Global.shared.user {
                closeMenu {
                    navigation?.pushViewController(UIStoryboard.home[.notification], animated: true)
                }
            } else {
                self.showAlert(title: nil, message: "AlertWarningLogin".localized, action: nil)
            }
        } else if tag == .setting && !(currentVC is SettingViewController) {
//            sideMenuViewController?.contentViewController = BaseNavigationViewController(rootViewController: UIStoryboard.setting[.setting])
            closeMenu {
                navigation?.pushViewController(UIStoryboard.setting[.setting], animated: true)
            }
        } else if tag == .price && !(currentVC is CinemaPriceViewController) {
            CATransaction.begin()
            CATransaction.setCompletionBlock {
                let vc = UIStoryboard.cinema[.price]
                let navi = self.sideMenuViewController?.contentViewController as? UINavigationController
                navi?.visibleViewController?.showPopup(vc, height: self.view.frame.height - 100)
            }
            sideMenuViewController?.hideMenuViewController()
            CATransaction.commit()
            return
        }

        sideMenuViewController?.hideMenuViewController()
    }

    func isOpenView(_ tag: TableItemTag) -> Bool {
        if let indexPath = tableView.indexPathForSelectedRow {
            return menuDataSource[indexPath].tag == tag
        }
        return false
    }
}

extension SlideMenuViewController: UITableViewDelegate{
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        guard let item = menuDataSource.itemAt(indexPath) else {
            return
        }

        openView(item.tag)
    }

    func tableView(_ tableView: UITableView, willSelectRowAt indexPath: IndexPath) -> IndexPath? {
        if menuDataSource[indexPath].tag == .price {
            openView(.price)
            return nil
        } else {
            return indexPath
        }
    }
}

extension SlideMenuViewController {
    func navigationController(_ navigationController: UINavigationController, didShow viewController: UIViewController, animated: Bool) {
        guard let item = tableItem(for: viewController), let indexPath = menuDataSource[item] else {
            return
        }

        tableView.selectRow(at: indexPath, animated: true, scrollPosition: .none)
    }

    func tableItem(for viewController: UIViewController) -> TableItemTag? {
        let types: [TableItemTag: UIViewController.Type] = [
            .home: HomeViewController.self,
            .member: MemberViewController.self,
            .cinema: ListAllCinemasViewController.self,
            .newsAndHotDeal: NewsAndDealsViewController.self,
            .recruitment: RecruitmentViewController.self,
            .notification: NotificationViewController.self,
            .setting: SettingViewController.self,
            .price: CinemaPriceViewController.self
        ]
        return types.first { viewController.isKind(of: $0.value) }?.key
    }
}

extension UIViewController {
    var slideMenuVC: SlideMenuViewController? {
        return sideMenuViewController?.rightMenuViewController as? SlideMenuViewController
    }
}

extension TableItemTag {
    static let home = TableItemTag(value: 0)
    static let member = TableItemTag(value: 1)
    static let cinema = TableItemTag(value: 2)
    static let newsAndHotDeal = TableItemTag(value: 3)
    static let recruitment = TableItemTag(value: 4)
    static let notification = TableItemTag(value: 5)
    static let setting = TableItemTag(value: 6)
    static let price = TableItemTag(value: 7)
}
