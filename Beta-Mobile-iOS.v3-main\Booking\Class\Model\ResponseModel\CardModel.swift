//
//  CardModel.swift
//  Booking
//
//  Created by <PERSON>h Vu on 4/4/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation
import ObjectMapper

class CardModel : Mappable {
    var CardId : String?
    var CardNumber : String?
    var CardTypeId : String?
    var Status : Int?
    var IsSuspendCard : Bool?
    var DateEntered : String?
    var ExpirationDate : String?
    var AllocationReasonId : String?
    var IsCreateOnline : Bool?
    var Description : String?
    var CreatedByUserId : String?
    var CreatedOnDate : String?
    var LastModifiedByUserId : String?
    var LastModifiedOnDate : String?
    var ApplicationId : String?
    var ClassName: String?
    var ClassNameF: String?


    required init?(map: Map) {

    }

    func mapping(map: Map) {
        CardId               <- map["CardId"]
        CardNumber           <- map["CardNumber"]
        CardTypeId           <- map["CardTypeId"]
        Status               <- map["Status"]
        IsSuspendCard        <- map["IsSuspendCard"]
        DateEntered          <- map["DateEntered"]
        ExpirationDate       <- map["ExpirationDate"]
        AllocationReasonId   <- map["AllocationReasonId"]
        IsCreateOnline       <- map["IsCreateOnline"]
        Description          <- map["Description"]
        CreatedByUserId      <- map["CreatedByUserId"]
        CreatedOnDate        <- map["CreatedOnDate"]
        LastModifiedByUserId <- map["LastModifiedByUserId"]
        LastModifiedOnDate   <- map["LastModifiedOnDate"]
        ApplicationId        <- map["ApplicationId"]
        ClassName <- map["ClassName"]
        ClassNameF <- map["ClassNameF"]

        if let count = DateEntered?.count, count > 19 {
            let range = DateEntered!.index(DateEntered!.startIndex, offsetBy: 19)..<DateEntered!.endIndex
            DateEntered?.removeSubrange(range)
        }
    }
}
