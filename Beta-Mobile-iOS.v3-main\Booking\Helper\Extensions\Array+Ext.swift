//
//  Array+Ext.swift
//  Booking-dev
//
//  Created by <PERSON><PERSON> on 5/30/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation

extension Array {
    subscript(safe index: Int) -> Element? {
        if index >= 0 && index < count {
            return self[index]
        }
        return nil
    }
}

extension Array where Element: Equatable {
    @discardableResult mutating func remove(element: Element) -> <PERSON><PERSON> {
        if let index = self.index(of: element) {
            self.remove(at: index)
            return true
        }
        return false
    }
    
    @discardableResult mutating func remove(where predicate: (Array.Iterator.Element) -> Bool) -> <PERSON>ol {
        if let index = self.index(where: { (element) -> <PERSON><PERSON> in
            return predicate(element)
        }) {
            self.remove(at: index)
            return true
        }
        return false
    }
}

extension Sequence {
    func group<GroupingType: Hashable>(by key: (Iterator.Element) -> GroupingType) -> [[Iterator.Element]] {
        var groups: [GroupingType: [Iterator.Element]] = [:]
        var groupsOrder: [GroupingType] = []
        forEach { element in
            let key = key(element)
            if case nil = groups[key]?.append(element) {
                groups[key] = [element]
                groupsOrder.append(key)
            }
        }
        return groupsOrder.map { groups[$0]! }
    }
}
