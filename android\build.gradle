buildscript {
    ext.kotlin_version = '2.0.21'
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.1.0'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }
  subprojects {
    afterEvaluate { project ->
      // Apply to Android application and library projects
      if (project.plugins.hasPlugin("com.android.application") ||
          project.plugins.hasPlugin("com.android.library")) {
        project.android {
          compileSdkVersion 34
          buildToolsVersion "34.0.0"

          if (namespace == null) {
            namespace project.group
          }

          // Apply JVM 17 compatibility to all Android projects
          compileOptions {
            sourceCompatibility JavaVersion.VERSION_17
            targetCompatibility JavaVersion.VERSION_17
          }
        }
      }

      // Fallback for projects with android property but no plugin detection
      if (project.hasProperty('android') &&
          !project.plugins.hasPlugin("com.android.application") &&
          !project.plugins.hasPlugin("com.android.library")) {
        project.android {
          if (namespace == null) {
            namespace project.group
          }

          // Apply JVM 17 compatibility
          compileOptions {
            sourceCompatibility JavaVersion.VERSION_17
            targetCompatibility JavaVersion.VERSION_17
          }
        }
      }

      // Apply Kotlin JVM target to all Kotlin projects
      project.tasks.withType(org.jetbrains.kotlin.gradle.tasks.KotlinCompile).configureEach {
        kotlinOptions {
          jvmTarget = '17'
        }
      }
    }
  }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(':app')
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}

subprojects {
  project.configurations.all {
    resolutionStrategy.eachDependency { details ->
      if (details.requested.group == 'com.android.support'
        && !details.requested.name.contains('multidex') ) {
        details.useVersion "27.1.1"
      }
    }
  }
}
configurations.all {
  resolutionStrategy {
    force 'androidx.core:core-ktx:1.6.0'
  }
}
