import 'package:flutter/material.dart';

/// Point History Model - matches iOS PointHistory.swift and Android PointHistoryModel.kt
class PointHistoryModel {
  final String? date;
  final int? statusType;
  final String? statusName;
  final double? point;
  final String? accountName;

  PointHistoryModel({
    this.date,
    this.statusType,
    this.statusName,
    this.point,
    this.accountName,
  });

  factory PointHistoryModel.fromJson(Map<String, dynamic> json) {
    return PointHistoryModel(
      date: json['Date'] as String?,
      statusType: json['StatusType'] as int?,
      statusName: json['StatusName'] as String?,
      point: (json['Point'] as num?) as double?,
      accountName: json['AccountName'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'Date': date,
      'StatusType': statusType,
      'StatusName': statusName,
      'Point': point,
      'AccountName': accountName,
    };
  }

  /// Get point status type - matches iOS PointStatusType enum
  PointStatusType get pointStatus {
    switch (statusType) {
      case 1:
        return PointStatusType.save;
      case 2:
        return PointStatusType.expense;
      case 3:
      case 4:
        return PointStatusType.cancel;
      case 5:
        return PointStatusType.donate;
      case 6:
        return PointStatusType.receive;
      default:
        return PointStatusType.save;
    }
  }

  /// Format date string - matches iOS dateString computed property
  String get dateString {
    if (date == null || date!.isEmpty) return '-';

    try {
      // Parse server date format and convert to display format
      final dateTime = DateTime.parse(date!);
      return '${dateTime.day.toString().padLeft(2, '0')}/${dateTime.month.toString().padLeft(2, '0')}/${dateTime.year}, ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return date ?? '-';
    }
  }

  /// Check if should show account name - matches iOS showName computed property
  bool get showName {
    return pointStatus == PointStatusType.receive || pointStatus == PointStatusType.donate;
  }
}

/// Point Status Type Enum - matches iOS PointStatusType
enum PointStatusType {
  save,     // 1: Tích điểm
  expense,  // 2: Tiêu điểm
  cancel,   // 3,4: Giao dịch hủy
  donate,   // 5: Tặng điểm
  receive,  // 6: Nhận điểm
}

extension PointStatusTypeExtension on PointStatusType {
  /// Get description - matches iOS description computed property
  String get description {
    switch (this) {
      case PointStatusType.save:
        return 'Tích điểm';
      case PointStatusType.expense:
        return 'Tiêu điểm';
      case PointStatusType.cancel:
        return 'Giao dịch hủy';
      case PointStatusType.donate:
        return 'Tặng điểm';
      case PointStatusType.receive:
        return 'Nhận điểm';
    }
  }

  /// Get color - matches iOS color computed property
  Color get color {
    switch (this) {
      case PointStatusType.save:
        return const Color(0xFF3fb7f9); // Blue
      case PointStatusType.expense:
        return const Color(0xFFfd2802); // Red
      case PointStatusType.donate:
        return const Color(0xFFfd7c02); // Orange
      case PointStatusType.receive:
        return const Color(0xFF7ed321); // Green
      case PointStatusType.cancel:
        return const Color(0xFF494c62); // Gray
    }
  }

  /// Get icon - additional for Flutter UI
  IconData get icon {
    switch (this) {
      case PointStatusType.save:
        return Icons.add_circle;
      case PointStatusType.expense:
        return Icons.remove_circle;
      case PointStatusType.donate:
        return Icons.card_giftcard;
      case PointStatusType.receive:
        return Icons.redeem;
      case PointStatusType.cancel:
        return Icons.cancel;
    }
  }
}
