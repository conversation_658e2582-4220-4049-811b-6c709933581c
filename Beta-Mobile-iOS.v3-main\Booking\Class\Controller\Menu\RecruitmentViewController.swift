//
//  RecruitmentViewController.swift
//  Booking-dev
//
//  Created by <PERSON><PERSON> on 5/24/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit

class RecruitmentViewController: BaseViewController {

    @IBOutlet weak var tableView: UITableView!

    let cellId = "NotificationTableViewCell"
    private var items: [PolicyContentModel] = []
    private var categories: [NewsModel] = []

    fileprivate let dataSource = SimpleTableViewDataSource()

    override func viewDidLoad() {
        super.viewDidLoad()

        self.tableView.dataSource = dataSource
        self.tableView.register(UINib.init(nibName: cellId, bundle: nil), forCellReuseIdentifier: cellId)
        self.getCategories()
        // Do any additional setup after loading the view.

        localizableTitle = "Recruitment.Title"
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        self.navigationController?.setTransparent(false)
    }

    override func didReceiveMemoryWarning() {
        super.didReceiveMemoryWarning()
        // Dispose of any resources that can be recreated.
    }

    private func getCategories(){
        self.showLoading()
        EcmProvider.rx.request(.getRecruitment(nil, nil)).mapObject(DDKCResponse<NewsModel>.self)
            
            .subscribe(onNext:{[weak self] response in
                guard let items = response.ListObject else{
                    self?.dismissLoading()
                    print("Data wrong")
                    return
                }
                self?.categories = items
//                self?.getRecruitment()
                let listItem = items.sorted(by: {$0.getStartDate() > $1.getStartDate()}).map { object -> TableItem in

                    return TableItem(title: object.Tieu_de, content: nil, data: object, cellId: self?.cellId, isOpen: false) }
                self?.dataSource.removeAll()
                self?.dataSource.addRows(listItem)
                self?.tableView.reloadData()
                self?.dismissLoading()
            }).disposed(by: disposeBag)
    }

    private func getRecruitment(){
        guard categories.count > 0 else {
            self.dismissLoading()
            return
        }
        let requestGroup = DispatchGroup()
        let _ = DispatchQueue.global(qos: .userInitiated)
        DispatchQueue.concurrentPerform(iterations: categories.count) { (i) in
            let categoryId = categories[i].StorylineID
            requestGroup.enter()
            self.getListNotification(categoryId, completionHander: {
                requestGroup.leave()
            })
        }

        requestGroup.notify(queue: DispatchQueue.main){
            print("Get data completion")
            self.dismissLoading()
            let items = self.items.sorted(by: {$0.getStartDate() > $1.getStartDate()}).map { object -> TableItem in
                let content = object.Noi_dung_chi_tiet?.first?.ParagraphData?.ParagraphContent?.htmlToAttributedString()?.string
                
                return TableItem(title: object.Tieu_de, content: content, data: object, cellId: self.cellId, isOpen: false) }
            self.dataSource.removeAll()
            self.dataSource.addRows(items)
            self.tableView.reloadData()
        }
    }

    private func getListNotification(_ categoryId: String?, completionHander: @escaping()->Void){
        guard let id = categoryId else {
            return
        }
        EcmProvider.rx.request(.getNewForCategory(id, 99, 99)).mapObject(DDKCResponse<PolicyContentModel>.self)
            
            .subscribe(onNext:{[weak self] response in
                completionHander()
                guard response.isSuccess(), let item = response.Object else {return}
                self?.items.append(item)
            }).disposed(by: disposeBag)
    }
}

extension RecruitmentViewController: UITableViewDelegate {

    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        let item = dataSource[indexPath]
        guard let news = item.data as? NewsModel else {
            return
        }
        let vc = UIStoryboard.home[.newsDetail] as! NewsDetailViewController
        vc.type = NewType.recruitment(news)
        show(vc)
    }
}

