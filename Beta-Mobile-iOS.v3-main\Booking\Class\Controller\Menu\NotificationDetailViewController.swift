//
//  NotificationDetailViewController.swift
//  Booking-dev
//
//  Created by <PERSON><PERSON> on 6/13/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit
import WebKit

class NotificationDetailViewController: BaseViewController {
    var news: NewsModel?

    @IBOutlet weak var tvContent: UITextView!
    fileprivate var webView: WKWebView!

    override func viewDidLoad() {
        super.viewDidLoad()

//        let configuration = WKWebViewConfiguration()
//        webView = WKWebView(frame: view.bounds, configuration: configuration)
//        webView.autoresizingMask = [.flexibleHeight, .flexibleWidth]
//        webView.navigationDelegate = self
//        view.addSubview(webView)

        let inset = UIEdgeInsets(top: 10, left: 10, bottom: 10, right: 10)
        tvContent.contentInset = inset

        fillData()

        getContent(newId: news?.StorylineID)
    }

    override func didReceiveMemoryWarning() {
        super.didReceiveMemoryWarning()
        // Dispose of any resources that can be recreated.
    }
    

    func fillData() {
        guard let news = self.news else { return }
        setTitle(news.Tieu_de)

        if let html = news.Noi_dung_chi_tiet?.first?.ParagraphData?.ParagraphContent {
//            let customHeader = "<link rel='stylesheet' href='style.css' type='text/css'><meta name='viewport' content='initial-scale=1.0'/>"
//            let newHtml = html.replacingOccurrences(of: "<head>", with: "<head>" + customHeader)
//            webView.loadHTMLString(newHtml, baseURL: URL(string: Config.BaseURL))
            tvContent.attributedText = html.htmlToAttributedString(14)
        } else {
            tvContent.text = news.Tom_tat_noi_dung
        }
    }

    private func getContent(newId: String?){
        guard let id = newId else {
            return
        }
        self.showLoading()
        EcmProvider.rx.request(.getNewWithId(id, nil, nil)).mapObject(DDKCResponse<NewsModel>.self)
            
            .subscribe(onNext:{[weak self] response in
                guard let `self` = self else {return}
                self.handlerResponse(response, success: {
                    guard let object = response.Object else{
                        print("Wrong data")
                        self.dismissLoading()
                        return
                    }
                    self.news = object
                    self.fillData()
                    self.dismissLoading()
                }, error: {
                    self.dismissLoading()
                })
            }).disposed(by: disposeBag)
    }
}

extension NotificationDetailViewController: WKNavigationDelegate {
    func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
//        let textSize: Int = 300
//
//        webView.evaluateJavaScript("document.getElementsByTagName('body')[0].style.webkitTextSizeAdjust= '\(textSize)%%'") { res, error in
//            print("res: \(res) error: \(error)")
//        }
    }
}
