//
//  TabOtherViewController.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 8/2/19.
//  Copyright © 2019 ddkc. All rights reserved.
//

import UIKit

struct OtherItem {
    var color: Int
    var icon: String
    var text: String
}

class TabOtherViewController: BaseViewController {

    @IBOutlet weak var collectionView: UICollectionView!
    @IBOutlet weak var customizeTitleLabel: UILabel!

    let items = [OtherItem(color: 0x0093ee, icon: "ic_free_voucher", text: "free_voucher"),
    OtherItem(color: 0x26c1c9, icon: "ic_other_cinema", text: "CinemaDetail.Title"),
    OtherItem(color: 0x81c926, icon: "ic_other_member", text: "Member.Title"),
    OtherItem(color: 0xfd7b1f, icon: "ic_other_notification", text: "Notification.Title"),
    OtherItem(color: 0xd81b7b, icon: "ic_other_recruiqment", text: "Recruitment.Title"),
    OtherItem(color: 0xab7df6, icon: "ic_other_setting", text: "Menu.Setting")]

    override func viewDidLoad() {
        super.viewDidLoad()

        self.customizeTitleLabel.text = "Tab5".localized

        collectionView.register(UINib(nibName: "OthersCollectionViewCell", bundle: nil), forCellWithReuseIdentifier: "OthersCollectionViewCell")
        collectionView.contentInset = UIEdgeInsets(top: 0, left: 0, bottom: 70, right: 0)
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        self.navigationController?.isNavigationBarHidden = true
        reloadData()
    }

    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        self.navigationController?.isNavigationBarHidden = false
    }

    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
    }

    private func reloadData() {
        self.collectionView.reloadData()
        self.customizeTitleLabel.text = "Tab5".localized
    }

}

extension TabOtherViewController: UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return items.count
    }

    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        guard let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "OthersCollectionViewCell", for: indexPath) as? OthersCollectionViewCell else {
            return OthersCollectionViewCell()
        }
        cell.item = items[indexPath.row]
        return cell
    }

    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        let width = (collectionView.frame.size.width - 10) / 2
        return CGSize(width: width, height: width)
    }

    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        let item = items[indexPath.row]
        switch item.icon {
        case "ic_free_voucher":
            let freeVoucherVC = FreeVoucherViewController()
            freeVoucherVC.hidesBottomBarWhenPushed = true
            self.navigationController?.pushViewController(freeVoucherVC, animated: true)
            break
        case "ic_other_cinema":
            let allCinemaVC = CinemasViewController()
            allCinemaVC.hidesBottomBarWhenPushed = true
            self.navigationController?.pushViewController(allCinemaVC, animated: true)
        case "ic_other_member":
            if Global.shared.user == nil {
                let loginVC = UIStoryboard.authen[.login]
                loginVC.hidesBottomBarWhenPushed = true
                self.navigationController?.pushViewController(loginVC, animated: true)
                return
            }
            let memberVC = UIStoryboard.member[.member]
            memberVC.hidesBottomBarWhenPushed = true
            self.navigationController?.pushViewController(memberVC, animated: true)
        case "ic_other_price":
            let vc = UIStoryboard.cinema[.price]
            present(vc, animated: true, completion: nil)
        case "ic_other_notification":
            let vc = UIStoryboard.home[.notification]
            vc.hidesBottomBarWhenPushed = true
            self.navigationController?.pushViewController(vc, animated: true)
        case "ic_other_recruiqment":
            let vc = UIStoryboard.home[.recruitment]
            vc.hidesBottomBarWhenPushed = true
            self.navigationController?.pushViewController(vc, animated: true)
        case "ic_other_setting":
            let vc = UIStoryboard.setting[.setting]
            vc.hidesBottomBarWhenPushed = true
            self.navigationController?.pushViewController(vc, animated: true)
        default:
            break
        }
    }
}


