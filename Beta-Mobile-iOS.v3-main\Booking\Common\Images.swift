//
//  Images.swift
//  Booking-dev
//
//  Created by <PERSON><PERSON> on 4/5/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit

extension CAGradientLayer {
    static var defaultGradient: CAGradientLayer {
        let layer = CAGradientLayer()
        layer.startPoint = CGPoint(x: 0, y: 0.5)
        layer.endPoint = CGPoint(x: 1, y: 0.5)
        layer.colors = [UIColor.gradientBg1, UIColor.gradientBg2].map { $0.cgColor }
        return layer
    }

    var image: UIImage? {
        var image: UIImage? = nil
        UIGraphicsBeginImageContext(CGSize(width: bounds.size.width, height: bounds.size.height))
        if let context = UIGraphicsGetCurrentContext() {
            render(in: context)
            image = UIGraphicsGetImageFromCurrentImageContext()
        }
        UIGraphicsEndImageContext()
        return image
    }
}
