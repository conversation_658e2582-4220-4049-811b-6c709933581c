//
//  TableViewHelper.swift
//  Booking-dev
//
//  Created by <PERSON><PERSON> on 4/6/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit

struct TableItemTag: Hashable {
    var value: Int = 0
    static let none = TableItemTag(value: -1)

    static func == (lTag: TableItemTag, rTag: TableItemTag) -> Bool {
        return lTag.value == rTag.value
    }

    var hashValue: Int {
        return value.hashValue
    }
}

struct TableSectionTag {
    var value: Int = 0
    static let none = TableSectionTag(value: -1)

    static func == (lTag: TableSectionTag, rTag: TableSectionTag) -> Bool {
        return lTag.value == rTag.value
    }
}

//MARK: - TableView item
class TableItem: NSObject {
    var title: String?
    var content: String?
    var data: Any?
    var cellId: String?
    var tag: TableItemTag
    var icon: String?
    var selectedIcon: String?
    var cell: UITableViewCell?
    var accessoryType: UITableViewCellAccessoryType

    var detail: String?
    var titleAttributed: NSAttributedString?
    var detailAttributed: NSAttributedString?
    var isOpen: Bool = false

    init(title: String? = nil, content: String? = nil, titleAttributed: NSAttributedString? = nil, detailAttributed: NSAttributedString? = nil, data: Any? = nil, cell: UITableViewCell? = nil, cellId: String? = nil, icon: String? = nil, selectedIcon: String? = nil, accessoryType: UITableViewCellAccessoryType = .none, tag: TableItemTag = .none, isOpen: Bool = false) {
        self.title = title
        self.content = content
        self.titleAttributed = titleAttributed
        self.detailAttributed = detailAttributed
        self.data = data
        self.cell = cell
        self.cellId = cellId
        self.icon = icon
        self.selectedIcon = selectedIcon
        self.tag = tag
        self.accessoryType = accessoryType
        self.isOpen = isOpen
    }
    
    func setAccessoryType(_ type: UITableViewCellAccessoryType){
        self.accessoryType = type
    }
}

//MARK: - TableSection
class TableSection: NSObject {
    var title: String?
    var subTitle: String?
    var isOpen: Bool
    var items: [TableItem]
    var tag: TableSectionTag

    init(title: String? = nil, subTitle: String? = nil, items: [TableItem] = [], tag: TableSectionTag = .none, isOpen: Bool = true) {
        self.title = title
        self.subTitle = subTitle
        self.items = items
        self.tag = tag
        self.isOpen = isOpen
    }

    var count: Int {
        return items.count
    }

    subscript(index: Int) -> TableItem {
        return items[index]
    }

    func addRows( _ rows: [TableItem]) {
        items.append(contentsOf: rows)
    }
    
    func updateTitle(title: String){
        self.title = title
    }
}

class SimpleTableViewDataSource: NSObject, UITableViewDataSource {
    var sections: [TableSection] = []

    override init() {

    }

    func removeAll() {
        sections.removeAll()
    }
    func removeRowAt(_ indexPath: IndexPath, table: UITableView, with animation: UITableViewRowAnimation = .fade) {
        if sections.count <= indexPath.section {
            return
        }
        let section = sections[indexPath.section]
        if section.items.count <= indexPath.row {
            return
        }
        table.beginUpdates()
        section.items.remove(at: indexPath.row)
        table.deleteRows(at: [indexPath], with: animation)
        if section.items.count == 0 {
            table.deleteSections(IndexSet(integer: indexPath.section), with: animation)
            sections.remove(at: indexPath.section)
        }
        table.endUpdates()
    }
    func appendRows(_ rows: [TableItem], table: UITableView,  with animation: UITableViewRowAnimation = .fade) {
        if rows.count == 0 {
            return
        }
        table.beginUpdates()
        var section: TableSection!
        var sec = 0
        var indexPaths: [IndexPath] = []

        if sections.count == 0 {
            section = TableSection(title: "", items: [], isOpen: true)
            sections.append(section)
            table.insertSections(IndexSet(integer: 0), with: animation)
        } else {
            sec = sections.count - 1
            section = sections[sec]
        }

        let start = section.count
        for i in 0..<rows.count {
            indexPaths.append(IndexPath(row: start+i, section: sec))
        }
        section.addRows(rows)

        table.insertRows(at: indexPaths, with: animation)
        table.endUpdates()
//        table.reloadEmptyDataSet()
        table.reloadData()
    }
    func addRows( _ rows: [TableItem]) {
        var section: TableSection!
        if sections.count == 0 {
            section = TableSection(title: "", items: [], isOpen: true)
            sections.append(section)
        } else {
            section = sections[0]
        }
        section.addRows(rows)
    }

    func setNewRows(_ rows: [TableItem]) {
        removeAll()
        addRows(rows)
    }

    func addSection(_ section: [TableSection]) {
        sections.append(contentsOf: section)
    }

    func itemAt(_ indexPath: IndexPath) -> TableItem? {
        guard indexPath.section < sections.count else {
            return nil
        }
        let items = sections[indexPath.section].items
        guard indexPath.row < items.count else {
            return nil
        }
        let tbItem = items[indexPath.row]
        return tbItem
    }

    func register(tableView: UITableView, cellId: String) {
        tableView.register(UINib(nibName: cellId, bundle: nil), forCellReuseIdentifier: cellId)
    }

    func table(_ tableView: UITableView, sectionAt section: Int) -> TableSection {
        return sections[section]
    }

    func table(_ tableView: UITableView, itemAt indexPath: IndexPath) -> TableItem {
        let items = sections[indexPath.section].items
        let tbItem = items[indexPath.row]
        return tbItem
    }
    func numberOfSections(in tableView: UITableView) -> Int {
        return sections.count
    }

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        let sec = sections[section]
        guard sec.isOpen else {
            return 0
        }
        let items = sec.items
        return items.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let tbItem = table(tableView, itemAt: indexPath)
        if let cell = tbItem.cell {
            cell.updateViewWithItem(tbItem, indexPath: indexPath)
            updateCell(cell, tableView: tableView, at: indexPath)
            return cell
        }
        let cell = tableView.dequeueReusableCell(withIdentifier: tbItem.cellId!, for: indexPath)
        cell.updateViewWithItem(tbItem, indexPath: indexPath)
        updateCell(cell, tableView: tableView, at: indexPath)
        return cell
    }

    func tableView(_ tableView: UITableView, titleForHeaderInSection section: Int) -> String? {
        return sections[section].title
    }

    fileprivate func updateCell(_ cell: UITableViewCell, tableView: UITableView, at indexPath: IndexPath) {
        let tbItem = table(tableView, itemAt: indexPath)
        if indexPath.row == 0 {
            cell.updateViewAtTopOfSection(tbItem, section: indexPath.section)
        }
        if indexPath.row == tableView.numberOfRows(inSection: indexPath.section) - 1 {
            cell.updateViewAtEndOfSection(tbItem, section: indexPath.section)
        }
    }

    subscript(_ index: Int) -> TableSection {
        return sections[index]
    }

    subscript(_ tag: TableItemTag) -> IndexPath? {
        var row: Int = 0
        if let section = sections.index(where: { sec in
            if let index = sec.items.index(where: { $0.tag == tag }) {
                row = index; return true
            }
            return false
        }) {
            return IndexPath(row: row, section: section)
        }
        return nil
    }

    subscript(_ indexPath: IndexPath) -> TableItem {
        return sections[indexPath.section][indexPath.row]
    }

    subscript(_ tag: TableSectionTag) -> TableSection? {
        return sections.first(where: { $0.tag == tag })
    }
}

//MARK: - Tableview cell
extension UITableViewCell {
    @objc func updateViewWithItem(_ item: TableItem, indexPath: IndexPath) {
        textLabel?.text = item.title
        detailTextLabel?.text = item.content
    }

    @objc func updateViewAtTopOfSection(_ item: TableItem, section: Int) {

    }

    @objc func updateViewAtEndOfSection(_ item: TableItem, section: Int) {

    }
}

//MARK: - Table Section
extension UITableViewHeaderFooterView {
    @objc func updateViewWithSection(_ tbSection: TableSection) {

    }
}
