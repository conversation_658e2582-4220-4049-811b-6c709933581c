//
//  UserDefault+Quick.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 3/10/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation

extension UserDefaults{
    
    static func setBool(value: Bool, forKey key: Default<PERSON>ey){
        self.standard.set(value, forKey: key.rawValue)
    }
    
    static func getBool(forKey key: Default<PERSON>ey) -> Bool?{
        return self.standard.value(forKey: key.rawValue) as? Bool
    }
}
