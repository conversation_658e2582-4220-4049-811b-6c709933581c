//
//  ListAttachmentFileModel.swift
//  Booking
//
//  Created by <PERSON><PERSON> Vu on 4/5/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation
import ObjectMapper

class ListAttachmentFileModel : Mappable {
     var FileId : String?
     var NodeId : String?
     var ObjId : String?
     var NodeType : String?
     var FileName : String?
     var FileSize : String?
     var FileType : String?
     var AbsolutePath : String?
     var RelativePath : String?
     var MetadataList : [MetadataListModel]?
     var Position : Int?
     var Title : String?
     var Description : String?
     var Author : String?
     var Url : String?
     var Date : String?
     var StreamingPath : String?
     var PreviewNodePath : String?
     var Duong_dan_anh_dai_dien_video : String?


    required init?(map: Map) {

    }

    func mapping(map: Map) {
        FileId               <- map["FileId"]
        NodeId               <- map["NodeId"]
        ObjId                <- map["ObjId"]
        NodeType             <- map["NodeType"]
        FileName             <- map["FileName"]
        FileSize             <- map["FileSize"]
        FileType             <- map["FileType"]
        AbsolutePath         <- map["AbsolutePath"]
        RelativePath         <- map["RelativePath"]
        MetadataList         <- map["MetadataList"]
        Position             <- map["Position"]
        Title                <- map["Title"]
        Description          <- map["Description"]
        Author               <- map["Author"]
        Url                  <- map["Url"]
        Date                 <- map["Date"]
        StreamingPath        <- map["StreamingPath"]
        PreviewNodePath      <- map["PreviewNodePath"]
        Duong_dan_anh_dai_dien_video <- map["Duong_dan_anh_dai_dien_video"]
    }
}
