//
//  CinemaModel.swift
//  Booking
//
//  Created by <PERSON>h Vu on 4/4/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation
import ObjectMapper

class CinemaModel: Mappable {
     var CinemaId : String?
     var CinemaTypeId : String?
     var Name : String?
     var Code : String?
     var Address : String?
     var Name_F : String?
     var Address_F : String?
     var StartDate : String?
     var EndDate : String?
     var Status : Bool?
     var Order : Int?
     var PhoneNumber : String?
     var CityId : String?
     var CityName : String?
     var Latitude : String?
     var Longtitude : String?
    var Picture: String?
    var NewsId: String?


    required init?(map: Map) {

    }

    func mapping(map: Map) {
        CinemaId             <- map["CinemaId"]
        CinemaTypeId         <- map["CinemaTypeId"]
        Name                 <- map["Name"]
        Code                 <- map["Code"]
        Address              <- map["Address"]
        Name_F               <- map["Name_F"]
        Address_F            <- map["Address_F"]
        StartDate            <- map["StartDate"]
        EndDate              <- map["EndDate"]
        Status               <- map["Status"]
        Order                <- map["Order"]
        PhoneNumber          <- map["PhoneNumber"]
        CityId               <- map["CityId"]
        CityName             <- map["CityName"]
        Latitude             <- map["Latitude"]
        Longtitude           <- map["Longtitude"]
        Picture             <- map["Picture"]
        NewsId              <- map["NewsId"]
    }

    var pictureURL: URL? {
        let urlStr = Config.BaseURLResource + (Picture ?? "")
        return URL(string: urlStr)
    }
    
    func getName() -> String?{
        return Utils.shared.isEng() ? (Name_F ?? Name) : Name
    }
    
    func getAddress() -> String?{
        return Utils.shared.isEng() ? Address_F : Address
    }
    
    func getDistanceDouble() -> Double{
        return Utils.shared.distanceFrom(latitude: Latitude, longitude: Longtitude)
    }
    
    func getDistance() -> String{
        return NSString(format: "%.1f", getDistanceDouble()) as String
    }
}
