//
//  LanguageManager.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 4/13/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation

enum LanguageType: String{
    case English = "en"
    case Vietnam = "vi"
}

class LanguageManager {
    static let shared = LanguageManager()
    
    func getLanguage() -> LanguageType{
        guard let lang = UserDefaults.standard.string(forKey: Constant.CurrentLocalization) else{
            UserDefaults.standard.set(LanguageType.Vietnam.rawValue, forKey: Constant.CurrentLocalization)
            UserDefaults.standard.synchronize()
            return .Vietnam
        }
        return LanguageType(rawValue: lang) ?? .Vietnam
    }
    
    func setLanguage(_ language: LanguageType){
        UserDefaults.standard.set(language.rawValue, forKey: Constant.CurrentLocalization)
        UserDefaults.standard.synchronize()
    }
}
