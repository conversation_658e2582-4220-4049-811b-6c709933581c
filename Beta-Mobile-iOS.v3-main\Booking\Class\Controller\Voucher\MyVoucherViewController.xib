<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14490.70" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14490.49"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="Oswald-Regular.ttf">
            <string>Oswald-Regular</string>
        </array>
        <array key="SourceSansPro-Regular.ttf">
            <string>SourceSansPro-Regular</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="MyVoucherViewController" customModule="Booking_dev" customModuleProvider="target">
            <connections>
                <outlet property="emptyDetailLabel" destination="a3e-bP-GRl" id="Oid-lX-fa7"/>
                <outlet property="emptyTitleLabel" destination="XMh-II-SlH" id="EgO-4D-oKz"/>
                <outlet property="emptyView" destination="t5O-mi-02F" id="YhR-0r-RRR"/>
                <outlet property="freeButton" destination="6ah-OF-CyJ" id="PNd-WA-qHb"/>
                <outlet property="tableView" destination="YbD-0K-oCF" id="axh-nL-vKH"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" style="plain" separatorStyle="default" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="YbD-0K-oCF">
                    <rect key="frame" x="0.0" y="52" width="414" height="810"/>
                    <color key="backgroundColor" red="0.95294117647058818" green="0.95294117647058818" blue="0.95294117647058818" alpha="1" colorSpace="calibratedRGB"/>
                    <connections>
                        <outlet property="dataSource" destination="-1" id="0hG-VS-xFm"/>
                        <outlet property="delegate" destination="-1" id="6pp-68-kZz"/>
                    </connections>
                </tableView>
                <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="t5O-mi-02F">
                    <rect key="frame" x="0.0" y="44" width="414" height="818"/>
                    <subviews>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Bạn không có voucher nào!" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="XMh-II-SlH">
                            <rect key="frame" x="20" y="314" width="374" height="30"/>
                            <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="20"/>
                            <color key="textColor" red="0.011764705882352941" green="0.34901960784313724" blue="0.61568627450980395" alpha="1" colorSpace="calibratedRGB"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Bạn có thể tìm kiếm thêm voucher miễn phí hoặc thêm mới voucher của bạn!" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="a3e-bP-GRl">
                            <rect key="frame" x="20" y="352" width="374" height="40.5"/>
                            <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                            <color key="textColor" red="0.011764705882352941" green="0.011764705882352941" blue="0.011764705882352941" alpha="1" colorSpace="calibratedRGB"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="6ah-OF-CyJ" customClass="GradientButton" customModule="Booking_dev" customModuleProvider="target">
                            <rect key="frame" x="20" y="432.5" width="374" height="40"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="40" id="tXt-qY-j75"/>
                            </constraints>
                            <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="16"/>
                            <state key="normal">
                                <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </state>
                            <userDefinedRuntimeAttributes>
                                <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                    <real key="value" value="3"/>
                                </userDefinedRuntimeAttribute>
                                <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="free_voucher"/>
                            </userDefinedRuntimeAttributes>
                            <connections>
                                <action selector="freeVoucherTapped:" destination="-1" eventType="touchUpInside" id="yCG-SZ-uAq"/>
                            </connections>
                        </button>
                    </subviews>
                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstItem="XMh-II-SlH" firstAttribute="centerY" secondItem="t5O-mi-02F" secondAttribute="centerY" constant="-80" id="3m1-Om-3yX"/>
                        <constraint firstItem="6ah-OF-CyJ" firstAttribute="leading" secondItem="t5O-mi-02F" secondAttribute="leading" constant="20" id="Fgf-Kk-BB0"/>
                        <constraint firstItem="a3e-bP-GRl" firstAttribute="leading" secondItem="t5O-mi-02F" secondAttribute="leading" constant="20" id="Pfz-B3-O7U"/>
                        <constraint firstItem="XMh-II-SlH" firstAttribute="leading" secondItem="t5O-mi-02F" secondAttribute="leading" constant="20" id="Wjg-PN-zDf"/>
                        <constraint firstItem="a3e-bP-GRl" firstAttribute="top" secondItem="XMh-II-SlH" secondAttribute="bottom" constant="8" id="Xk6-zD-94B"/>
                        <constraint firstAttribute="trailing" secondItem="XMh-II-SlH" secondAttribute="trailing" constant="20" id="iQr-hv-VCK"/>
                        <constraint firstAttribute="trailing" secondItem="a3e-bP-GRl" secondAttribute="trailing" constant="20" id="iTQ-ls-I4s"/>
                        <constraint firstItem="6ah-OF-CyJ" firstAttribute="top" secondItem="a3e-bP-GRl" secondAttribute="bottom" constant="40" id="rbw-Gw-3eO"/>
                        <constraint firstAttribute="trailing" secondItem="6ah-OF-CyJ" secondAttribute="trailing" constant="20" id="sR8-61-529"/>
                    </constraints>
                </view>
            </subviews>
            <color key="backgroundColor" red="0.95294117649999999" green="0.95294117649999999" blue="0.95294117649999999" alpha="1" colorSpace="calibratedRGB"/>
            <constraints>
                <constraint firstItem="t5O-mi-02F" firstAttribute="top" secondItem="fnl-2z-Ty3" secondAttribute="top" id="FkT-9N-AGn"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="YbD-0K-oCF" secondAttribute="trailing" id="OTC-4c-vSa"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="t5O-mi-02F" secondAttribute="trailing" id="U4n-dY-83c"/>
                <constraint firstItem="YbD-0K-oCF" firstAttribute="top" secondItem="fnl-2z-Ty3" secondAttribute="top" constant="8" id="bv6-gz-vT9"/>
                <constraint firstItem="t5O-mi-02F" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" id="dRv-jq-hIR"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="bottom" secondItem="YbD-0K-oCF" secondAttribute="bottom" id="t8g-vg-L0r"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="bottom" secondItem="t5O-mi-02F" secondAttribute="bottom" id="zNj-Pd-ixL"/>
                <constraint firstItem="YbD-0K-oCF" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" id="znD-lY-Qoz"/>
            </constraints>
            <viewLayoutGuide key="safeArea" id="fnl-2z-Ty3"/>
        </view>
    </objects>
</document>
