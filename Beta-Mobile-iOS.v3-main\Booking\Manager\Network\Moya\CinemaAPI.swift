//
//  CinemaAPI.swift
//  Booking-dev
//
//  Created by <PERSON><PERSON> on 4/4/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation
import Moya


public enum Cinema{
    case listCinema
    case listCinemaByProvince
    case cinemaDetail(String)
    case cinemaShowDate(String)
    case cinemaFilmShow(String, String)
}

let CinemaProvider = MoyaProvider<Cinema>(plugins: [NetworkLoggerPlugin(verbose: true, responseDataFormatter: nil)])

extension Cinema: TargetType {
    
    public var baseURL: URL { return URL(string: Config.BaseURL)! }
    
    
    public var path: String {
        switch self {
        case .listCinema:
            return "api/v1/erp/cinemas"
        case .listCinemaByProvince:
            return "api/v1/erp/cites/cinemas"
        case .cinemaDetail(let id):
            return "api/v1/erp/cinemas/{\(id)}"
        case .cinemaShowDate(let id):
            return "api/v1/erp/cinemas/{\(id)}/show-dates"
        case .cinemaFilmShow(let id, _):
            return "api/v2/erp/cinemas/{\(id)}/shows"
        }
    }
    public var method: Moya.Method {
        return .get
    }
    public var parameters: [String: Any]? {
        switch self {
        case .cinemaFilmShow(_, let date):
            return ["dateShow": date]
        default:
            return [:]
        }
    }
    public var task: Task {
        return Task.requestParameters(parameters: self.parameters!, encoding: URLEncoding.default)
    }
    public var headers: [String : String]? {
        return Global.shared.headers()
    }
    
    public var sampleData: Data{
        return "".data(using: String.Encoding.utf8)!
    }
}
