//
//  DashView.swift
//  Booking-dev
//
//  Created by <PERSON><PERSON> on 4/19/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit

@IBDesignable
class DashView: UIView {
    @IBInspectable var dashNumber: Int = 0
    @IBInspectable var dashWidth: CGFloat = 0
    @IBInspectable var dashColor: UIColor = .white

    @IBInspectable var horizontal: Bool = true

    override func draw(_ rect: CGRect) {
        super.draw(rect)

        if dashWidth == 0 && dashNumber == 0 {
            return
        }

        var space = dashWidth
        let size = horizontal ? rect.width : rect.height
        let lineWidth = horizontal ? rect.height : rect.width
        if dashNumber == 0 {
            dashNumber = Int(size / dashWidth)
        } else if dashWidth == 0 {
            dashWidth = size / CGFloat(dashNumber)
            space = dashWidth
        } else if dashNumber > 1 {
            space = (size - dashWidth * CGFloat(dashNumber)) / CGFloat(dashNumber - 1)
        }

        let context = UIGraphicsGetCurrentContext()
        context?.beginPath()
        context?.setLineWidth(lineWidth)
        context?.setLineDash(phase: 0, lengths: [dashWidth, space])
        context?.setStrokeColor(dashColor.cgColor)
        if horizontal {
            context?.move(to: CGPoint(x: 0, y: rect.midY))
            context?.addLine(to: CGPoint(x: rect.maxX, y: rect.midY))
        } else {
            context?.move(to: CGPoint(x: rect.midX, y: 0))
            context?.addLine(to: CGPoint(x: rect.midX, y: rect.maxY))
        }

        context?.strokePath()
        context?.closePath()
    }
}
