<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="19529" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="19519"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="Oswald-Regular.ttf">
            <string>Oswald-Regular</string>
        </array>
        <array key="SourceSansPro-Regular.ttf">
            <string>SourceSansPro-Regular</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="NewAndDealsView" customModule="Beta_Cinemas" customModuleProvider="target">
            <connections>
                <outlet property="tableView" destination="evz-pg-J3R" id="9Fb-n9-hyX"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="OAx-0v-xVZ">
            <rect key="frame" x="0.0" y="0.0" width="375" height="437"/>
            <subviews>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="KHUYẾN MÃI MỚI" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="YO4-za-A4I" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                    <rect key="frame" x="8" y="8" width="170.5" height="23.5"/>
                    <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="20"/>
                    <color key="textColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="calibratedRGB"/>
                    <nil key="highlightedColor"/>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Home.BigDeals"/>
                    </userDefinedRuntimeAttributes>
                </label>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="s2L-ZC-JjV" customClass="RoundButton" customModule="Beta_Cinemas" customModuleProvider="target">
                    <rect key="frame" x="299" y="8" width="68" height="24"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="68" id="RnF-0x-R87"/>
                        <constraint firstAttribute="height" constant="24" id="fDJ-GN-f3K"/>
                    </constraints>
                    <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                    <state key="normal" title="Tất cả">
                        <color key="titleColor" red="0.01176470588" green="0.34901960780000002" blue="0.61568627450000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                            <real key="value" value="1"/>
                        </userDefinedRuntimeAttribute>
                        <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                            <real key="value" value="13"/>
                        </userDefinedRuntimeAttribute>
                        <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                            <color key="value" red="0.01176470588" green="0.34901960780000002" blue="0.61568627450000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        </userDefinedRuntimeAttribute>
                        <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Home.All"/>
                    </userDefinedRuntimeAttributes>
                    <connections>
                        <action selector="showAllButtonPress:" destination="-1" eventType="touchUpInside" id="nag-fZ-NjG"/>
                    </connections>
                </button>
                <view alpha="0.30000001192092896" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="6W9-PX-7nT" userLabel="lineView">
                    <rect key="frame" x="178.5" y="31" width="133.5" height="1"/>
                    <color key="backgroundColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="calibratedRGB"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="1" id="WGL-HV-peM"/>
                    </constraints>
                </view>
                <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" scrollEnabled="NO" dataMode="prototypes" style="plain" separatorStyle="none" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="evz-pg-J3R">
                    <rect key="frame" x="0.0" y="42" width="375" height="385"/>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <connections>
                        <outlet property="dataSource" destination="-1" id="Frn-mR-EVV"/>
                        <outlet property="delegate" destination="-1" id="726-uC-uA1"/>
                    </connections>
                </tableView>
            </subviews>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="evz-pg-J3R" firstAttribute="top" secondItem="6W9-PX-7nT" secondAttribute="bottom" constant="10" id="6nw-FX-0tX"/>
                <constraint firstItem="s2L-ZC-JjV" firstAttribute="bottom" secondItem="6W9-PX-7nT" secondAttribute="bottom" id="8dm-bZ-R2w"/>
                <constraint firstAttribute="trailing" secondItem="evz-pg-J3R" secondAttribute="trailing" id="ABG-7q-coi"/>
                <constraint firstItem="evz-pg-J3R" firstAttribute="leading" secondItem="OAx-0v-xVZ" secondAttribute="leading" id="R8B-eZ-nbt"/>
                <constraint firstAttribute="trailing" secondItem="s2L-ZC-JjV" secondAttribute="trailing" constant="8" id="c8B-l9-ZXh"/>
                <constraint firstItem="YO4-za-A4I" firstAttribute="top" secondItem="OAx-0v-xVZ" secondAttribute="top" constant="8" id="fDM-UC-svv"/>
                <constraint firstItem="YO4-za-A4I" firstAttribute="top" secondItem="OAx-0v-xVZ" secondAttribute="top" constant="8" id="hY6-oH-Up1"/>
                <constraint firstAttribute="bottom" secondItem="evz-pg-J3R" secondAttribute="bottom" constant="10" id="qwg-k6-aq0"/>
                <constraint firstItem="s2L-ZC-JjV" firstAttribute="centerY" secondItem="YO4-za-A4I" secondAttribute="centerY" id="rku-ed-VS8"/>
                <constraint firstItem="s2L-ZC-JjV" firstAttribute="leading" secondItem="6W9-PX-7nT" secondAttribute="trailing" constant="-13" id="trq-dx-nHH"/>
                <constraint firstItem="6W9-PX-7nT" firstAttribute="leading" secondItem="YO4-za-A4I" secondAttribute="trailing" id="vuS-KN-ucU"/>
                <constraint firstItem="YO4-za-A4I" firstAttribute="leading" secondItem="OAx-0v-xVZ" secondAttribute="leading" constant="8" id="yi6-i0-iZz"/>
            </constraints>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <point key="canvasLocation" x="-405.5" y="186.5"/>
        </view>
    </objects>
    <designables>
        <designable name="YO4-za-A4I">
            <size key="intrinsicContentSize" width="170.5" height="23.5"/>
        </designable>
        <designable name="s2L-ZC-JjV">
            <size key="intrinsicContentSize" width="46" height="31"/>
        </designable>
    </designables>
</document>
