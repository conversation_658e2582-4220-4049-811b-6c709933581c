//
//  LoginModel.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 4/6/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation

class LoginModel: BaseRequestModel {
    var userName: String
    var password: String
    var deviceId: String?
    var token: String
    
    init(userName: String, password: String, token: String) {
        self.userName = userName
        self.password = password
        self.deviceId = UIDevice.current.identifierForVendor?.uuidString
        self.token = token
    }
    
    override func toJSON() -> [String : Any] {
        return ["UserName": userName,
                "PassWord": password,
                "DeviceId": deviceId ?? "",
                "ReCaptchaToken": token]
    }
}
