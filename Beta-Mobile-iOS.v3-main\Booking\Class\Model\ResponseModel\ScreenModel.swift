//
//  ScreenModel.swift
//  Booking-dev
//
//  Created by <PERSON><PERSON> on 5/27/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation
import ObjectMapper

class ScreenModel: Mappable {
    var ScreenId: String?
    var Code: String?
    var CinemaId: String?
    var SeatPosition: [[SeatModel]]?
    var Name: String?
    var Description: String?
    var Status: Bool?
    var StartDate: String?
    var EndDate: String?
    var NumberRow: Int?
    var NumberCol: Int?
    var TotalSeat: Int?
    var CreatedOnDate: String?
    var CreatedByUserId: String?
    var LastModifiedOnDate: String?
    var LastModifiedByUserId: String?
    var ApplicationId: String?
    var ScreenTypeId: String?
    var Area: String?
    var UserManagerId: String?
    var ProjectorDrescription: String?
    var Order: String?
    var CinemaName: String?

    required init?(map: Map) {

    }

    func mapping(map: Map) {
        ScreenId <- map["ScreenId"]
        Code <- map["Code"]
        CinemaId <- map["CinemaId"]
        SeatPosition <- map["SeatPosition"]
        Name <- map["Name"]
        Description <- map["Description"]
        Status <- map["Status"]
        StartDate <- map["StartDate"]
        EndDate <- map["EndDate"]
        NumberRow <- map["NumberRow"]
        NumberCol <- map["NumberCol"]
        TotalSeat <- map["TotalSeat"]
        CreatedOnDate <- map["CreatedOnDate"]
        CreatedByUserId <- map["CreatedByUserId"]
        LastModifiedOnDate <- map["LastModifiedOnDate"]
        LastModifiedByUserId <- map["LastModifiedByUserId"]
        ApplicationId <- map["ApplicationId"]
        ScreenTypeId <- map["ScreenTypeId"]
        Area <- map["Area"]
        UserManagerId <- map["UserManagerId"]
        ProjectorDrescription <- map["ProjectorDrescription"]
        Order <- map["Order"]
        CinemaName <- map["CinemaName"]
    }
}
