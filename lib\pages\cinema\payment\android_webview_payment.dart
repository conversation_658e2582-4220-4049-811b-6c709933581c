// import 'dart:io';
// import 'package:flutter/material.dart';
// import 'package:flutter_inappwebview/flutter_inappwebview.dart';
// import 'package:intl/intl.dart';
// import 'package:provider/provider.dart';
//
// import '../../../cubit/index.dart';
// import '../../Movie_schedule/model/Film_model.dart';
// import '../../voucher/api/api_test.dart';
// import '../model/cinema_model.dart';
// import '../model/list_seat_model.dart';
// import '../model/seat_model.dart';
//
// class AndroidPaymentWebView extends StatefulWidget {
//   final String htmlData; // HTML data từ API booking
//   final String baseUrl; // Base URL cho WebView
//   final FilmModel? film; // Thông tin phim
//   final String? combo; // Thông tin combo ghế
//   final ListSeatModel? listSeat; // Thông tin ghế và suất chiếu
//   final String? cinemaId; // ID rạp chiếu phim
//   final String? cinemaName; // Tên rạp chiếu phim
//   final Function(String?) onPaymentSuccess; // Callback khi thanh toán thành công
//   final Function(String?) onPaymentFailed; // Callback khi thanh toán thất bại
//   final Function() onPaymentWaiting; // Callback khi đang chờ thanh toán
//   final Function(String) onPaymentMethodSelected; // Callback khi chọn phương thức thanh toán
//   final int? totalPrice; // Tổng giá tiền
//
//   // Missing data that should be passed from payment_screen
//   final List<SeatModel>? selectedSeats; // Danh sách ghế đã chọn
//   final ShowModel? showTime; // Thông tin suất chiếu
//   final String? showId; // ID suất chiếu
//
//   const AndroidPaymentWebView({
//     super.key,
//     required this.htmlData,
//     required this.baseUrl,
//     this.film,
//     this.combo,
//     this.listSeat,
//     this.cinemaId,
//     this.totalPrice,
//     this.cinemaName,
//     required this.onPaymentSuccess,
//     required this.onPaymentFailed,
//     required this.onPaymentWaiting,
//     required this.onPaymentMethodSelected,
//     // Add missing data parameters
//     this.selectedSeats,
//     this.showTime,
//     this.showId,
//   });
//
//   @override
//   State<AndroidPaymentWebView> createState() => _AndroidPaymentWebViewState();
// }
//
// class _AndroidPaymentWebViewState extends State<AndroidPaymentWebView> {
//    late InAppWebViewController webViewController;
//
//   @override
//   void initState() {
//     super.initState();
//
//     // Validate customer data and log debug info
//     _validateAndLogData();
//
//     // Android webview needs this for debugging
//     if (Platform.isAndroid) {
//       InAppWebViewController.setWebContentsDebuggingEnabled(true);
//     }
//   }
//
//   /// Validate customer data and log all payment data for debugging
//   void _validateAndLogData() {
//     print('🔧 ===== ANDROID WEBVIEW PAYMENT DEBUG =====');
//
//     // Validate and log customer data
//     final user = context.read<AuthC>().state.user;
//     print('👤 Customer Data Validation:');
//     print('   - User object: ${user != null ? "✅ Available" : "❌ NULL"}');
//     print('   - Account ID: ${user?.accountId ?? "❌ EMPTY"}');
//     print('   - Card Number: ${user?.cardNumber ?? "❌ EMPTY"}');
//     print('   - User Name: ${user?.name ?? "❌ EMPTY"}');
//     print('   - Phone: ${user?.phoneNumber ?? "❌ EMPTY"}');
//     print('   - Email: ${user?.email ?? "❌ EMPTY"}');
//
//     // Critical validation
//     if (user?.accountId?.isEmpty ?? true) {
//       print('❌ CRITICAL: Customer ID is empty - Payment will fail!');
//     }
//     if (user?.cardNumber?.isEmpty ?? true) {
//       print('❌ CRITICAL: Customer card number is empty - Payment will fail!');
//     }
//
//     // Log booking data
//     print('🎬 Booking Data:');
//     print('   - Film: ${widget.film?.getName() ?? "❌ EMPTY"}');
//     print('   - Cinema: ${widget.listSeat?.tenRap ?? "❌ EMPTY"}');
//     print('   - Show ID: ${widget.showId ?? widget.showTime?.showId ?? "❌ EMPTY"}');
//     print('   - Total Price: ${widget.totalPrice ?? "❌ EMPTY"}');
//     print('   - Selected Seats: ${widget.selectedSeats?.length ?? 0}');
//
//     // Log seat details
//     if (widget.selectedSeats?.isNotEmpty ?? false) {
//       print('🪑 Selected Seats Details:');
//       for (var seat in widget.selectedSeats!) {
//         print('   - Seat ${seat.seatNumber}: Index=${seat.seatIndex}, Type=${seat.seatTypeEnum}');
//       }
//     }
//
//     // Log show time data
//     print('⏰ Show Time Data:');
//     print('   - Date: ${widget.listSeat?.ngayChieu ?? "❌ EMPTY"}');
//     print('   - Time: ${widget.listSeat?.gioChieu ?? "❌ EMPTY"}');
//     print('   - Screen: ${widget.listSeat?.phongChieu ?? "❌ EMPTY"}');
//     print('   - Format: ${widget.listSeat?.filmFormatCode ?? "❌ EMPTY"}');
//
//     print('🔧 ===== END ANDROID WEBVIEW DEBUG =====');
//   }
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: const Text("Thanh toán"),
//       ),
//       body: InAppWebView(
//         // Load HTML data directly from widget
//         initialData: InAppWebViewInitialData(
//           data: widget.htmlData,
//           baseUrl: WebUri(widget.baseUrl),
//         ),
//         initialSettings: InAppWebViewSettings(
//           // Cross-platform settings
//           javaScriptEnabled: true,
//           useShouldOverrideUrlLoading: true,
//           mediaPlaybackRequiresUserGesture: false,
//           allowsInlineMediaPlayback: true,
//
//           // Android-specific settings to match Android repo
//           domStorageEnabled: true,
//           databaseEnabled: true,
//           useHybridComposition: true,
//
//           // Security and compatibility settings
//           mixedContentMode: MixedContentMode.MIXED_CONTENT_ALWAYS_ALLOW,
//           allowsBackForwardNavigationGestures: false,
//
//           // Disable problematic features for older Android versions
//           supportZoom: true,
//           builtInZoomControls: false,
//           displayZoomControls: false,
//
//           // Performance settings
//           cacheEnabled: true,
//           clearCache: false,
//
//           // User agent - let WebView use default
//           userAgent: null,
//         ),
//
//         onWebViewCreated: (controller) async {
//           print('🔗 Android InAppWebView created, loading HTML data');
//           webViewController = controller;
//
//           // Add JavaScript handler for Android repo compatibility
//           controller.addJavaScriptHandler(
//             handlerName: "androidkit",
//             callback: (args) {
//               if (!mounted) return;
//               final message = args.isNotEmpty ? args[0].toString() : '';
//               print('📱 Android InAppWebView received message: $message');
//               _handleJavaScriptMessage(message);
//             }
//           );
//
//           print('✅ Android InAppWebView JavaScript handler "androidkit" added');
//         },
//         onConsoleMessage: (controller, consoleMessage) {
//           // Enhanced console message handling for debugging
//           final level = consoleMessage.messageLevel;
//           final message = consoleMessage.message;
//           final source = consoleMessage ?? 'unknown';
//           final line = consoleMessage ?? 0;
//
//           // Format console output with emojis for easy identification
//           String emoji = '📱';
//           switch (consoleMessage.messageLevel) {
//             case ConsoleMessageLevel.ERROR:
//               emoji = '❌';
//               break;
//             case ConsoleMessageLevel.WARNING:
//               emoji = '⚠️';
//               break;
//             case ConsoleMessageLevel.LOG:
//               emoji = 'ℹ️';
//               break;
//             case ConsoleMessageLevel.DEBUG:
//               emoji = '🐛';
//               break;
//             default:
//               emoji = '📱';
//           }
//
//           print('$emoji JS Console [$level]');
//
//           // Handle specific patterns for payment debugging
//           if (message.contains('[object Object]')) {
//             print('⚠️ Detected [object Object] - object not properly stringified');
//           }
//           if (message.contains('Value cannot be null')) {
//             print('⚠️ Null value error - check variable initialization');
//           }
//           if (message.contains('Xảy ra lỗi trong quá trình thực hiện')) {
//             print('❌ Payment process error: $message');
//           }
//           if (message.contains('thanhtoan')) {
//             print('💳 Payment function activity: $message');
//           }
//           if (message.contains('dataBooking')) {
//             print('📊 Booking data activity: $message');
//           }
//           if (message.contains('authoToken')) {
//             print('🔐 Auth token activity: $message');
//           }
//           if (message.contains('🔧') || message.contains('✅') || message.contains('📱')) {
//             print('🔍 Debug message from Flutter injection: $message');
//           }
//         },
//         onLoadStop: (controller, url) async {
//           print('✅ Android InAppWebView HTML data loaded: $url');
//           // Send booking info after HTML loads
//           await _sendBookingInfoToWebView();
//         },
//         onLoadError: (controller, url, code, message) {
//           print("WebView load error: $message");
//         },
//         onLoadHttpError: (controller, url, statusCode, description) {
//           print("HTTP error: $statusCode $description");
//         },
//         onJsAlert: (controller, jsAlertRequest) async {
//           final message = jsAlertRequest.message ?? '';
//           final urrl = jsAlertRequest.url ?? 'khong cos';
//           print('🚨 JS Alert: $message \n$urrl');
//
//           // Handle payment-related alerts
//           if (message.contains('Có lỗi xảy ra')) {
//             print('❌ Payment error alert detected');
//           }
//
//           return JsAlertResponse(
//             handledByClient: false,
//             action: JsAlertResponseAction.CONFIRM,
//           );
//         },
//         onJsConfirm: (controller, jsConfirmRequest) async {
//           final message = jsConfirmRequest.message ?? '';
//           print('❓ JS Confirm: $message');
//
//           return JsConfirmResponse(
//             handledByClient: true,
//             action: JsConfirmResponseAction.CONFIRM,
//           );
//         },
//         onReceivedServerTrustAuthRequest: (controller, challenge) async {
//           return ServerTrustAuthResponse(
//             action: ServerTrustAuthResponseAction.PROCEED,
//           );
//         },
//         onPermissionRequest: (controller, request) async {
//           return PermissionResponse(
//             resources: request.resources,
//             action: PermissionResponseAction.GRANT,
//           );
//         },
//         // onConsoleMessageAndroid: (controller, message) {
//         //   print("Console (Android): ${message.message}");
//         // },
//         onLoadStart: (controller, url) async {
//           print('🔄 Android WebView started loading: $url');
//         },
//       ),
//     );
//   }
//
//   /// Handle JavaScript messages from WebView - match Android repo behavior
//   void _handleJavaScriptMessage(String message) {
//     print('📱 Android WebView handling message: $message');
//
//     // Clean message (remove quotes like Android repo)
//     final cleanMessage = message.replaceAll('"', '');
//
//     switch (cleanMessage) {
//       case 'policy':
//         // Open policy fragment - for now just log
//         print('📄 Policy requested');
//         break;
//       case 'payment_success':
//         print('✅ Payment success');
//         widget.onPaymentSuccess(null);
//         break;
//       case 'awaiting_payment':
//         print('⏳ Payment awaiting');
//         widget.onPaymentWaiting();
//         break;
//       case 'payment_failed':
//         print('❌ Payment failed');
//         widget.onPaymentFailed('Thanh toán thất bại');
//         break;
//       case 'booking_seat_failed':
//         print('❌ Booking seat failed');
//         widget.onPaymentFailed('Đặt ghế thất bại');
//         break;
//       case 'timeout':
//         print('⏰ Payment timeout');
//         // Handle timeout like Android repo
//         widget.onPaymentFailed('Hết thời gian thanh toán');
//         break;
//       default:
//         print('🔍 Unknown message: $cleanMessage');
//         break;
//     }
//   }
//
//
//
//   Future<void> _sendBookingInfoToWebView() async {
//     final film = widget.film;
//     final listSeat = widget.listSeat;
//
//     if (film == null || listSeat == null) {
//       print('⚠️ Missing film or listSeat data');
//       return;
//     }
//
//     try {
//       // Wait for HTML to fully load and scripts to initialize
//       await Future.delayed(const Duration(milliseconds: 1500));
//
//       // First, initialize all required global variables to prevent "Value cannot be null" errors
//       await _initializeJavaScriptVariables();
//
//       // Since we're loading HTML data, we need to inject real booking data
//       await _injectRealBookingData();
//
//       // Inject debug helpers for easier debugging
//       await _injectDebugHelpers();
//
//       final dateFormat = DateFormat('dd/MM/yyyy');
//       final timeFormat = DateFormat('HH:mm');
//
//       DateTime? date;
//       DateTime? time;
//
//       try {
//         date = listSeat.ngayChieu != null ? DateTime.parse(listSeat.ngayChieu!) : null;
//         time = listSeat.gioChieu != null ? DateTime.parse(listSeat.gioChieu!) : null;
//       } catch (e) {
//         print('Error parsing date/time: $e');
//       }
//
//       final dateStr = date != null ? dateFormat.format(date) : '';
//       final timeStr = time != null ? timeFormat.format(time) : '';
//
//       // Call getBookingInfo with proper parameter like the JavaScript expects
//       final jsBookingInfo = '''
//         console.log("🔧 Calling getBookingInfo with proper data...");
//
//         // Create bookingInfo object that getBookingInfo function expects
//         var bookingIf = {};
//         bookingIf.FilmName = "${_escapeJavaScript(film.getName() ?? "")}";
//         bookingIf.FilmInfo = "${_escapeJavaScript(film.getFinalOptions())}";
//         bookingIf.CinemaName = "${_escapeJavaScript(listSeat.tenRap ?? "")}";
//         bookingIf.DateShow = "$dateStr";
//         bookingIf.ShowTime = "$timeStr";
//         bookingIf.Combo = "${_escapeJavaScript(widget.combo ?? "")}";
//         bookingIf.TotalMoney = ${widget.totalPrice ?? 0};
//         bookingIf.Screen = "${_escapeJavaScript(listSeat.phongChieu ?? "")}";
//         bookingIf.FilmPoster = "${ApiService.baseUrlImage}${film.MainPosterUrl ?? ""}";
//         bookingIf.FilmFormatCode = "${listSeat.filmFormatCode ?? "2d"}";
//
//         console.log("📱 Calling getBookingInfo with:", bookingIf);
//
//         // Call the function with proper parameter (this is what was missing!)
//         if (typeof getBookingInfo === 'function') {
//           getBookingInfo(bookingIf);
//         } else {
//           console.warn("getBookingInfo function not found");
//         }
//
//         // Update global bookingInfor object with real data
//         if (typeof bookingInfor !== 'undefined') {
//           bookingInfor.ShowId = "${listSeat.filmId ?? ""}";
//           bookingInfor.TotalMoney = ${widget.totalPrice ?? 0};
//           bookingInfor.FilmFormatCode = "${listSeat.filmFormatCode ?? "2d"}";
//         }
//
//         // Update paymentInfor
//         if (typeof paymentInfor !== 'undefined') {
//           paymentInfor.TotalMoneyNeedPay = ${widget.totalPrice ?? 0};
//         }
//         console.log();
//         console.log("✅ getBookingInfo called successfully");
//       ''';
//
//       print('🔧 Android InAppWebView calling getBookingInfo');
//       await webViewController.evaluateJavascript(source: jsBookingInfo).catchError(
//           (val) => print('đã  ỗi : $val')
//       );
//
//       // Update seat information display
//       await _updateSeatDisplay();
//
//       // Send customer info if available - Android repo format (like webview_payment.dart)
//       final user = context.read<AuthC>().state.user;
//       if (user != null) {
//         // Validate customer data before sending to WebView
//         final customerId = user.accountId ?? "";
//         final customerCard = user.cardNumber ?? "";
//
//         print('🔧 Sending customer info to WebView:');
//         print('   - Customer ID: ${customerId.isEmpty ? "❌ EMPTY" : "✅ $customerId"}');
//         print('   - Customer Card: ${customerCard.isEmpty ? "❌ EMPTY" : "✅ $customerCard"}');
//
//         if (customerId.isEmpty) {
//           print('❌ WARNING: Customer ID is empty - Payment may fail!');
//         }
//         if (customerCard.isEmpty) {
//           print('❌ WARNING: Customer card is empty - Payment may fail!');
//         }
//
//         final jsCustomerInfo = '''
//           console.log("🔧 Setting customer info...");
//           console.log("👤 Customer ID: $customerId");
//           console.log("💳 Customer Card: $customerCard");
//
//           var cusI = {};
//           cusI.customerId = '$customerId';
//           cusI.customerCard = '$customerCard';
//
//           console.log("📱 Calling getCustomerInfo with:", cusI);
//           if (typeof getCustomerInfo === 'function') {
//             getCustomerInfo(cusI);
//             console.log("✅ getCustomerInfo called successfully");
//           } else {
//             console.error("❌ getCustomerInfo function not found");
//           }
//         ''';
//
//         print('🔧 Android InAppWebView executing customer info JS');
//         print('👤 Customer ID: ${user.accountId ?? "empty"}');
//         print('💳 Card Number: ${user.cardNumber ?? "empty"}');
//         await webViewController.evaluateJavascript(source: jsCustomerInfo);
//       } else {
//         print('⚠️ No user logged in - using empty customer info');
//         const jsCustomerInfo = '''
//           console.log("🔧 Setting empty customer info (no user logged in)...");
//           var cusI = {};
//           cusI.customerId = "";
//           cusI.customerCard = "";
//
//           console.log("📱 Calling getCustomerInfo with:", cusI);
//           if (typeof getCustomerInfo === 'function') {
//             getCustomerInfo(cusI);
//           } else {
//             console.warn("getCustomerInfo function not found");
//           }
//         ''';
//
//         await webViewController.evaluateJavascript(source: jsCustomerInfo);
//       }
//
//     } catch (e) {
//       print('❌ Error sending booking info to WebView: $e');
//     }
//   }
//
//   /// Initialize JavaScript variables to prevent "Value cannot be null" errors
//   Future<void> _initializeJavaScriptVariables() async {
//     try {
//       // Get real customer data like webview_payment.dart
//       final user = context.read<AuthC>().state.user;
//       final customerId = user?.accountId ?? '';
//       final customerCard = user?.cardNumber ?? '';
//
//       final jsInit = '''
//         console.log("🔧 Initializing JavaScript variables...");
//
//         // Initialize global variables with real customer data
//         if (typeof customerId === 'undefined') {
//           window.customerId = '$customerId';
//         }
//         if (typeof customerCard === 'undefined') {
//           window.customerCard = '$customerCard';
//         }
//         if (typeof airpayOrderNo === 'undefined') {
//           window.airpayOrderNo = '';
//         }
//
//         console.log("👤 Initialized customerId:", window.customerId);
//         console.log("💳 Initialized customerCard:", window.customerCard ? "exists" : "empty");
//
//         // Initialize bookingInfor object
//         if (typeof bookingInfor === 'undefined') {
//           window.bookingInfor = {};
//         }
//         if (!bookingInfor.FilmFormatCode) {
//           bookingInfor.FilmFormatCode = '2d';
//         }
//         if (!bookingInfor.seats) {
//           bookingInfor.seats = [];
//         }
//         if (!bookingInfor.ComboSelected) {
//           bookingInfor.ComboSelected = [];
//         }
//
//         // Initialize paymentInfor object
//         if (typeof paymentInfor === 'undefined') {
//           window.paymentInfor = {};
//         }
//         if (!paymentInfor.BetaPoint) {
//           paymentInfor.BetaPoint = { money: 0, point: 0 };
//         }
//         if (!paymentInfor.Vouchers) {
//           paymentInfor.Vouchers = {};
//         }
//         if (!paymentInfor.Coupons) {
//           paymentInfor.Coupons = {};
//         }
//         if (!paymentInfor.PaymentCardType) {
//           paymentInfor.PaymentCardType = 'vn';
//         }
//         if (typeof paymentInfor.TotalDiscount === 'undefined') {
//           paymentInfor.TotalDiscount = 0;
//         }
//         if (typeof paymentInfor.VoucherDiscountMoney === 'undefined') {
//           paymentInfor.VoucherDiscountMoney = 0;
//         }
//         if (!paymentInfor.VoucherPaymentValidate) {
//           paymentInfor.VoucherPaymentValidate = {};
//         }
//         if (!paymentInfor.VoucherPaymentDetail) {
//           paymentInfor.VoucherPaymentDetail = {};
//         }
//         if (typeof paymentInfor.TotalMoneyNeedPay === 'undefined') {
//           paymentInfor.TotalMoneyNeedPay = 0;
//         }
//
//         // Initialize customerInfor object with real data
//         if (typeof customerInfor === 'undefined') {
//           window.customerInfor = {};
//         }
//         window.customerInfor.customerId = '$customerId';
//         window.customerInfor.customerCard = '$customerCard';
//
//         // Initialize other required variables
//         if (typeof FirstLoad === 'undefined') {
//           window.FirstLoad = { Coupon: false, Voucher: false, Point: false };
//         }
//
//         // Override getBookingInfo to prevent error when called without parameter
//         if (typeof getBookingInfo === 'function') {
//           window.originalGetBookingInfo = getBookingInfo;
//           window.getBookingInfo = function(bookingInfo) {
//             if (bookingInfo) {
//               console.log("📱 getBookingInfo called with parameter:", bookingInfo);
//               return originalGetBookingInfo(bookingInfo);
//             } else {
//               console.log("⚠️ getBookingInfo called without parameter - ignoring");
//               return;
//             }
//           };
//         }
//         if (typeof screenType === 'undefined') {
//           window.screenType = "payment";
//         }
//         if (typeof isBooking === 'undefined') {
//           window.isBooking = false;
//         }
//         if (typeof listCombo === 'undefined') {
//           window.listCombo = [];
//         }
//
//         // Override console.log to prevent [object Object] errors
//         const originalLog = console.log;
//         console.log = function(...args) {
//           const processedArgs = args.map(arg => {
//             if (typeof arg === 'object' && arg !== null) {
//               try {
//                 return JSON.stringify(arg);
//               } catch (e) {
//                 return '[Object]';
//               }
//             }
//             return arg;
//           });
//           originalLog.apply(console, processedArgs);
//         };
//
//         console.log("✅ JavaScript variables initialized successfully");
//       ''';
//
//       await webViewController.evaluateJavascript(source: jsInit);
//       print('✅ JavaScript variables initialized');
//     } catch (e) {
//       print('❌ Error initializing JavaScript variables: $e');
//     }
//   }
//
//   /// Inject real booking data into the HTML page (HTML data approach)
//   Future<void> _injectRealBookingData() async {
//     try {
//       final film = widget.film;
//       final listSeat = widget.listSeat;
//       final selectedSeats = widget.selectedSeats;
//
//       // Create real seat data from selected seats
//       String seatDataArray = '[]';
//       if (selectedSeats != null && selectedSeats.isNotEmpty) {
//         final seatDataList = selectedSeats.map((seat) {
//           // Determine seat type and ticket type
//           String seatType = "STARDAR"; // Default
//           String ticketTypeId = "";
//           int price = 0;
//
//           if (seat.seatType?.isVip == true || seat.seatTypeEnum == SeatType.VIP) {
//             seatType = "VIP";
//             ticketTypeId = listSeat?.ticketTypes?.firstWhere((t) => t.isVip == true, orElse: () => listSeat.ticketTypes!.first).ticketTypeId ?? "";
//             price = listSeat?.ticketTypes?.firstWhere((t) => t.isVip == true, orElse: () => listSeat.ticketTypes!.first).price ?? 0;
//           } else if (seat.seatType?.isCouple == true || seat.seatTypeEnum == SeatType.COUPLE) {
//             seatType = "DOUBLE";
//             ticketTypeId = listSeat?.ticketTypes?.firstWhere((t) => t.isCouple == true, orElse: () => listSeat.ticketTypes!.first).ticketTypeId ?? "";
//             price = listSeat?.ticketTypes?.firstWhere((t) => t.isCouple == true, orElse: () => listSeat.ticketTypes!.first).price ?? 0;
//           } else {
//             seatType = "STARDAR";
//             ticketTypeId = listSeat?.ticketTypes?.firstWhere((t) => t.isNormal == true, orElse: () => listSeat.ticketTypes!.first).ticketTypeId ?? "";
//             price = listSeat?.ticketTypes?.firstWhere((t) => t.isNormal == true, orElse: () => listSeat.ticketTypes!.first).price ?? 0;
//           }
//
//           return '''
//             {
//               "SeatIndex": ${seat.seatIndex ?? 0},
//               "SeatName": "${seat.seatNumber ?? ""}",
//               "SeatType": "$seatType",
//               "TicketTypeId": "$ticketTypeId",
//               "Price": $price
//             }
//           ''';
//         }).toList();
//
//         seatDataArray = '[${seatDataList.join(',')}]';
//       }
//
//       final jsInjectData = '''
//         console.log("🔧 Injecting real booking data into HTML page...");
//
//         // Update the existing dataBooking with real seat data
//         if (typeof dataBooking !== 'undefined') {
//           dataBooking.ShowId = "${widget.showId ?? widget.showTime?.showId ?? ""}";
//           dataBooking.Seats = $seatDataArray;
//           dataBooking.ImageHost = "${ApiService.baseUrlImage}";
//           console.log("📱 Updated existing dataBooking with real seats:", dataBooking);
//         } else {
//           window.dataBooking = {
//             "ShowId": "${widget.showId ?? widget.showTime?.showId ?? ""}",
//             "Seats": $seatDataArray,
//             "CountDown": "/Date(${DateTime.now().add(const Duration(minutes: 10)).millisecondsSinceEpoch})/",
//             "ImageHost": "${ApiService.baseUrlImage}"
//           };
//           console.log("📱 Created new dataBooking with real seats:", window.dataBooking);
//         }
//
//         // Update bookingInfor with real data
//         if (typeof bookingInfor !== 'undefined') {
//           bookingInfor.ShowId = "${ widget.showId ?? ""}";
//           bookingInfor.TotalMoney = ${widget.totalPrice ?? 0};
//           bookingInfor.FilmFormatCode = "${listSeat?.filmFormatCode ?? "2d"}";
//           bookingInfor.seats = dataBooking.Seats;
//         }
//
//         // Update paymentInfor
//         if (typeof paymentInfor !== 'undefined') {
//           paymentInfor.TotalMoneyNeedPay = ${widget.totalPrice ?? 0};
//         }
//
//         // Call getTicketTypeInBooking if it exists (like in original HTML)
//         if (typeof getTicketTypeInBooking === 'function') {
//           getTicketTypeInBooking();
//         }
//
//         console.log("✅ Real booking data injected successfully");
//         console.log("dataBooking:", dataBooking);
//         console.log("bookingInfor:", bookingInfor);
//         console.log("paymentInfor:", paymentInfor);
//       ''';
//
//       await webViewController.evaluateJavascript(source: jsInjectData);
//       print('✅ Real booking data injected into HTML page');
//     } catch (e) {
//       print('❌ Error injecting booking data into loaded page: $e');
//     }
//   }
//
//   /// Inject debug helpers for easier debugging in mobile app
//   Future<void> _injectDebugHelpers() async {
//     try {
//       const jsDebugHelpers = '''
//         console.log("🔧 Injecting debug helpers...");
//
//         // Create debug object for easy access
//         window.debugPayment = {
//           // Show all payment-related variables
//           showAll: function() {
//             console.log("=== PAYMENT DEBUG INFO ===");
//             console.log("📊 dataBooking:", typeof dataBooking !== 'undefined' ? dataBooking : 'undefined');
//             console.log("📋 bookingInfor:", typeof bookingInfor !== 'undefined' ? bookingInfor : 'undefined');
//             console.log("💳 paymentInfor:", typeof paymentInfor !== 'undefined' ? paymentInfor : 'undefined');
//             console.log("🔐 authoToken:", typeof authoToken !== 'undefined' ? (authoToken ? 'exists' : 'empty') : 'undefined');
//             console.log("🎬 listCombo:", typeof listCombo !== 'undefined' ? listCombo : 'undefined');
//             console.log("👤 customerInfor:", typeof customerInfor !== 'undefined' ? customerInfor : 'undefined');
//             console.log("========================");
//           },
//
//           // Show seat information
//           showSeats: function() {
//             console.log("=== SEAT DEBUG INFO ===");
//             if (typeof dataBooking !== 'undefined' && dataBooking.Seats) {
//               console.log("📍 Total seats:", dataBooking.Seats.length);
//               dataBooking.Seats.forEach((seat, index) => {
//                 console.log(`Seat index + 1}:`, seat);
//               });
//             } else {
//               console.log("❌ No seat data found");
//             }
//             console.log("=====================");
//           },
//
//           // Test payment function
//           testPayment: function() {
//             console.log("🧪 Testing payment function...");
//             if (typeof thanhtoan === 'function') {
//               console.log("✅ thanhtoan function exists");
//               console.log("📋 Function signature:", thanhtoan.toString().substring(0, 100) + "...");
//             } else {
//               console.log("❌ thanhtoan function not found");
//             }
//           },
//
//           // Show all available functions
//           showFunctions: function() {
//             console.log("=== AVAILABLE FUNCTIONS ===");
//             const functions = [];
//             for (let prop in window) {
//               if (typeof window[prop] === 'function' && !prop.startsWith('webkit')) {
//                 functions.push(prop);
//               }
//             }
//             console.log("🔧 Functions:", functions.sort());
//             console.log("==========================");
//           },
//
//           // Monitor payment button clicks
//           monitorPaymentButton: function() {
//             console.log("👀 Setting up payment button monitoring...");
//             const buttons = document.querySelectorAll('button, .btn, [onclick*="thanhtoan"]');
//             buttons.forEach((btn, index) => {
//               btn.addEventListener('click', function(e) {
//                 console.log(`🖱️ Button index + 1} clicked:`, btn.textContent?.trim() || btn.innerHTML);
//                 console.log("🎯 Button element:", btn);
//               });
//             });
//             console.log(`✅ Monitoring buttons`);
//           }
//         };
//
//         // Auto-run some debug info
//         console.log("🔍 Debug helpers loaded. Use debugPayment.showAll() to see all data");
//
//         // Monitor for payment errors
//         window.addEventListener('error', function(e) {
//           console.error("❌ JavaScript Error:", e.error);
//           console.error("📍 Error location:", e.filename + ":" + e.lineno);
//         });
//
//         // Monitor unhandled promise rejections
//         window.addEventListener('unhandledrejection', function(e) {
//           console.error("❌ Unhandled Promise Rejection:", e.reason);
//         });
//
//         console.log("✅ Debug helpers injected successfully");
//       ''';
//
//       await webViewController.evaluateJavascript(source: jsDebugHelpers);
//       print('✅ Debug helpers injected');
//     } catch (e) {
//       print('❌ Error injecting debug helpers: $e');
//     }
//   }
//
//   /// Update seat display with selected seats information
//   Future<void> _updateSeatDisplay() async {
//     try {
//       final selectedSeats = widget.selectedSeats;
//       if (selectedSeats == null || selectedSeats.isEmpty) {
//         print('⚠️ No selected seats to display');
//         return;
//       }
//
//       // Create seat display information
//       final seatNames = selectedSeats.map((seat) => seat.seatNumber ?? "").join(", ");
//       final totalSeats = selectedSeats.length;
//
//       // Calculate individual prices and total
//       int totalTicketPrice = 0;
//       final seatPriceDetails = selectedSeats.map((seat) {
//         int price = 0;
//         String seatType = "Standard";
//
//         if (seat.seatType?.isVip == true || seat.seatTypeEnum == SeatType.VIP) {
//           seatType = "VIP";
//           price = widget.listSeat?.ticketTypes?.firstWhere((t) => t.isVip == true, orElse: () => widget.listSeat!.ticketTypes!.first)?.price ?? 0;
//         } else if (seat.seatType?.isCouple == true || seat.seatTypeEnum == SeatType.COUPLE) {
//           seatType = "Couple";
//           price = widget.listSeat?.ticketTypes?.firstWhere((t) => t.isCouple == true, orElse: () => widget.listSeat!.ticketTypes!.first)?.price ?? 0;
//         } else {
//           seatType = "Standard";
//           price = widget.listSeat?.ticketTypes?.firstWhere((t) => t.isNormal == true, orElse: () => widget.listSeat!.ticketTypes!.first)?.price ?? 0;
//         }
//
//         totalTicketPrice += price;
//         return {
//           'name': seat.seatNumber ?? "",
//           'type': seatType,
//           'price': price
//         };
//       }).toList();
//
//       final jsUpdateSeatDisplay = '''
//         console.log("🔧 Updating seat display...");
//
//         // Update seat information in UI
//         var seatInfoHtml = "";
//         var seatDetails = [${seatPriceDetails.map((seat) => '''
//           {
//             "name": "${seat['name']}",
//             "type": "${seat['type']}",
//             "price": ${seat['price']}
//           }
//         ''').join(',')}];
//
//         // Create seat display HTML
//         seatDetails.forEach(function(seat, index) {
//           var formattedPrice = seat.price.toString().replace(/(\\d)(?=(\\d{3})+(?!\\d))/g, "\$1,") + "đ";
//           seatInfoHtml += '<div class="seat-item">';
//           seatInfoHtml += '<span class="seat-name">' + seat.name + '</span>';
//           seatInfoHtml += '<span class="seat-type">(' + seat.type + ')</span>';
//           seatInfoHtml += '<span class="seat-price">' + formattedPrice + '</span>';
//           seatInfoHtml += '</div>';
//         });
//
//         // Update seat display elements
//         if (\$('.selected-seats').length > 0) {
//           \$('.selected-seats').html(seatInfoHtml);
//         }
//
//         // Update seat count
//         if (\$('.seat-count').length > 0) {
//           \$('.seat-count').text('$totalSeats ghế');
//         }
//
//         // Update seat names
//         if (\$('.seat-names').length > 0) {
//           \$('.seat-names').text('$seatNames');
//         }
//
//         // Update ticket total
//         var ticketTotal = $totalTicketPrice;
//         var formattedTicketTotal = ticketTotal.toString().replace(/(\\d)(?=(\\d{3})+(?!\\d))/g, "\$1,") + "đ";
//         if (\$('.ticket-total').length > 0) {
//           \$('.ticket-total').text(formattedTicketTotal);
//         }
//
//         // Update grand total
//         var grandTotal = ${widget.totalPrice ?? 0};
//         var formattedGrandTotal = grandTotal.toString().replace(/(\\d)(?=(\\d{3})+(?!\\d))/g, "\$1,") + "đ";
//         if (\$('.grand-total').length > 0) {
//           \$('.grand-total').text(formattedGrandTotal);
//         }
//         if (\$('.total-amount').length > 0) {
//           \$('.total-amount').text(formattedGrandTotal);
//         }
//
//         console.log("✅ Seat display updated");
//         console.log("📍 Seats: $seatNames");
//         console.log("💰 Ticket total: " + formattedTicketTotal);
//         console.log("💳 Grand total: " + formattedGrandTotal);
//       ''';
//
//       await webViewController.evaluateJavascript(source: jsUpdateSeatDisplay);
//       print('✅ Seat display updated - Seats: $seatNames, Total: ${_formatCurrency(widget.totalPrice ?? 0)}đ');
//     } catch (e) {
//       print('❌ Error updating seat display: $e');
//     }
//   }
//
//   /// Escape JavaScript strings to prevent injection and syntax errors
//   String _escapeJavaScript(String input) {
//     return input
//         .replaceAll('\\', '\\\\')  // Escape backslashes
//         .replaceAll('"', '\\"')    // Escape double quotes
//         .replaceAll("'", "\\'")    // Escape single quotes
//         .replaceAll('\n', '\\n')   // Escape newlines
//         .replaceAll('\r', '\\r')   // Escape carriage returns
//         .replaceAll('\t', '\\t');  // Escape tabs
//   }
//
//   /// Helper to convert Map<String, dynamic> to JS object string
//   String _jsonToJSObject(Map<String, dynamic> map) {
//     final entries = map.entries.map((e) {
//       final key = e.key;
//       final value = e.value;
//       if (value is String) {
//         return "$key: '${value.replaceAll("'", "\\'")}'";
//       } else {
//         return "$key: $value";
//       }
//     }).join(", ");
//     return "{ $entries }";
//   }
//   String _formatCurrency(int amount) {
//     final formatter = NumberFormat("#,###", "vi_VN");
//     return formatter.format(amount);
//   }
// }
