//
//  String+Ext.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 4/4/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation

// MARK: - Localized
public extension String {
    public var localized: String {
        if let _ = UserDefaults.standard.string(forKey: Constant.CurrentLocalization) {} else {
            // we set a default, just in case
            UserDefaults.standard.set("vi", forKey: Constant.CurrentLocalization)
            UserDefaults.standard.synchronize()
        }
        
        let lang = UserDefaults.standard.string(forKey: Constant.CurrentLocalization)
        
        let path = Bundle.main.path(forResource: lang, ofType: "lproj")
        let bundle = Bundle(path: path!)
        
        return NSLocalizedString(self, tableName: nil, bundle: bundle!, value: "", comment: "")
    }
    
    func lang(_ dict: [String: String]? ) -> String {
        var s = localized
        if let dict = dict {
            for (key, val) in dict {
                s = s.replacingOccurrences(of: "{" + key + "}", with: val)
            }
        }
        return s
    }
    
    func isValidEmail() -> Bool {
        // here, `try!` will always succeed because the pattern is valid
        let regex = try! NSRegularExpression(pattern: "^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$", options: .caseInsensitive)
        return regex.firstMatch(in: self, options: [], range: NSRange(location: 0, length: self.count)) != nil
    }
    
    func isPhone() -> Bool {
        do {
            let detector = try NSDataDetector(types: NSTextCheckingResult.CheckingType.phoneNumber.rawValue)
            let matches = detector.matches(in: self, options: [], range: NSMakeRange(0, self.count))
            if let res = matches.first {
                print("res type: \(res.resultType == .phoneNumber) ")
                return res.resultType == .phoneNumber && res.range.location == 0 && res.range.length == self.count
            } else {
                return false
            }
        } catch {
            return false
        }
    }

    func isValidPersonalId() -> Bool {
        return self.count > 8 && self.count < 17
    }

    func isValidPassword() -> Bool {
        return self.count >= 6
    }
    
    func htmlToAttributedString(_ fontSize: CGFloat? = nil) -> NSAttributedString? {
        guard let data = self.data(using: .utf8) else { return nil }
        do {
            let attributeStr =  try NSMutableAttributedString(data: data, options: [NSAttributedString.DocumentReadingOptionKey.documentType:  NSAttributedString.DocumentType.html, NSAttributedString.DocumentReadingOptionKey.characterEncoding: String.Encoding.utf8.rawValue], documentAttributes: nil)
            if let fontSize = fontSize {
                attributeStr.convertFontTo(font: UIFont.systemFont(ofSize: fontSize))
            }
            attributeStr.enumerateAttribute(NSAttributedStringKey.attachment, in: NSMakeRange(0, attributeStr.length), options: .init(rawValue: 0), using: { (value, range, stop) in
                if let attachement = value as? NSTextAttachment {
                    let image = attachement.image(forBounds: attachement.bounds, textContainer: NSTextContainer(), characterIndex: range.location)!
                    let screenSize: CGRect = UIScreen.main.bounds
                    if image.size.width > screenSize.width-20 {
                        let newImage = image.resize((screenSize.width-2)/image.size.width)
                        let newAttribut = NSTextAttachment()
                        newAttribut.image = newImage
                        attributeStr.addAttribute(NSAttributedStringKey.attachment, value: newAttribut, range: range)
                    }
                }
            })
            return attributeStr
        } catch {
            return nil
        }
    }
    var htmlToString: String {
        return htmlToAttributedString()?.string ?? ""
    }

    func removeAllSpaces() -> String {
        return self.components(separatedBy: .whitespaces).joined()
    }
    
    func removeAllSpacesAndLines() -> String {
        return self.components(separatedBy: .whitespacesAndNewlines).joined()
    }

    var url: URL? {
        return URL(string: self)
    }

    func url(baseUrl: String) -> URL? {
        if self.hasPrefix("http") {
            return self.url
        }
        return (baseUrl + self).url
    }
}

extension Int {
    var toString: String? {
        return String(self)
    }
    
//    func toCurrency() -> String{
//        let currencyFormatter = NumberFormatter()
//        currencyFormatter.numberStyle = .currency
//        currencyFormatter.currencyCode = "đ"
//        currencyFormatter.locale = Locale(identifier: "vi")
//
//        let currency = NSNumber(value: self)
//        guard let priceString = currencyFormatter.string(from:currency) else{
//            return ""
//        }
//        return priceString
//    }

}

extension String {
    func toCurrency(_ currency: String = "đ") -> String {
        return Float(self)?.toCurrency(currency) ?? ""
    }
}


extension Float {
    func toCurrency(_ currency: String = "đ") -> String {
        return NSNumber(value: self).toCurrency(currency)
    }
}

extension Int {
    func toCurrency(_ currency: String = "đ") -> String {
        return NSNumber(value: self).toCurrency(currency)
    }
}

extension Int64 {
    func toCurrency(_ currency: String = "đ") -> String {
        return NSNumber(value: self).toCurrency(currency)
    }
}

extension NSNumber {
    func toCurrency(_ currency: String = "đ") -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .decimal
        formatter.groupingSeparator = "."
        formatter.usesGroupingSeparator = true
        return (formatter.string(from: self) ?? "") + currency
    }
}

extension String {
    var isYoutubeURL: Bool {
        return self.lowercased().contains("youtube") || self.lowercased().contains("youtu.be")
    }
}

extension URL {
    var isYoutubeURL: Bool {
        return self.absoluteString.isYoutubeURL
    }
}

extension URL {
    func valueOf(_ queryParamaterName: String) -> String? {
        guard let url = URLComponents(string: self.absoluteString) else { return nil }
        return url.queryItems?.first(where: { $0.name == queryParamaterName })?.value
    }
}

extension NSMutableAttributedString
{
    func convertFontTo(font: UIFont)
    {
        var range = NSMakeRange(0, 0)

        while (NSMaxRange(range) < length)
        {
            let attributes = self.attributes(at: NSMaxRange(range), effectiveRange: &range)
            if let oldFont = attributes[NSAttributedStringKey.font] as? UIFont
            {
                let newFont = UIFont(descriptor: font.fontDescriptor.withSymbolicTraits(oldFont.fontDescriptor.symbolicTraits)!, size: font.pointSize)
                addAttribute(NSAttributedStringKey.font, value: newFont, range: range)
            }
        }
    }
}
