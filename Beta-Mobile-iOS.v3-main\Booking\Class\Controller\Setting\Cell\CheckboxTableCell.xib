<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14109" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14088"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="SourceSansPro-Regular.ttf">
            <string>SourceSansPro-Regular</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="default" indentationWidth="10" rowHeight="109" id="6Kz-GU-UoR" customClass="CheckboxTableCell" customModule="Booking_dev" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="375" height="109"/>
            <autoresizingMask key="autoresizingMask"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="6Kz-GU-UoR" id="uJD-Nm-YYv">
                <rect key="frame" x="0.0" y="0.0" width="375" height="108.5"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Dp8-aA-Ny0" customClass="RoundView" customModule="Booking_dev" customModuleProvider="target">
                        <rect key="frame" x="8" y="0.0" width="359" height="108.5"/>
                        <subviews>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Tiếng Việt" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="uAa-8U-pi8">
                                <rect key="frame" x="12" y="15" width="295" height="78.5"/>
                                <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                <color key="textColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <imageView userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_check_done" translatesAutoresizingMaskIntoConstraints="NO" id="Ldl-Pw-PXn">
                                <rect key="frame" x="319" y="42.5" width="24" height="24"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="24" id="JBp-mK-6NR"/>
                                    <constraint firstAttribute="height" constant="24" id="mHe-v1-3Ux"/>
                                </constraints>
                            </imageView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="zcO-ep-ofp">
                                <rect key="frame" x="0.0" y="107.5" width="359" height="1"/>
                                <color key="backgroundColor" red="0.73725490196078436" green="0.73333333333333328" blue="0.75686274509803919" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="1" id="hul-4g-80v"/>
                                </constraints>
                            </view>
                        </subviews>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstAttribute="trailing" secondItem="zcO-ep-ofp" secondAttribute="trailing" id="05N-KC-0Aw"/>
                            <constraint firstItem="zcO-ep-ofp" firstAttribute="leading" secondItem="Dp8-aA-Ny0" secondAttribute="leading" id="0Jm-dB-sWU"/>
                            <constraint firstAttribute="bottom" secondItem="uAa-8U-pi8" secondAttribute="bottom" constant="15" id="5ZQ-fG-KOh"/>
                            <constraint firstAttribute="trailing" secondItem="Ldl-Pw-PXn" secondAttribute="trailing" constant="16" id="7ph-Sq-FdD"/>
                            <constraint firstItem="Ldl-Pw-PXn" firstAttribute="leading" secondItem="uAa-8U-pi8" secondAttribute="trailing" constant="12" id="8qJ-Qr-tqp"/>
                            <constraint firstItem="uAa-8U-pi8" firstAttribute="leading" secondItem="Dp8-aA-Ny0" secondAttribute="leading" constant="12" id="AGX-tB-xhy"/>
                            <constraint firstItem="uAa-8U-pi8" firstAttribute="top" secondItem="Dp8-aA-Ny0" secondAttribute="top" constant="15" id="NIH-7A-7YW"/>
                            <constraint firstAttribute="bottom" secondItem="zcO-ep-ofp" secondAttribute="bottom" id="pS2-2N-zpB"/>
                            <constraint firstItem="Ldl-Pw-PXn" firstAttribute="centerY" secondItem="Dp8-aA-Ny0" secondAttribute="centerY" id="tWJ-AC-Qql"/>
                        </constraints>
                    </view>
                </subviews>
                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                <constraints>
                    <constraint firstAttribute="bottom" secondItem="Dp8-aA-Ny0" secondAttribute="bottom" id="362-5f-NKu"/>
                    <constraint firstItem="Dp8-aA-Ny0" firstAttribute="top" secondItem="uJD-Nm-YYv" secondAttribute="top" id="Dai-zF-EhD"/>
                    <constraint firstAttribute="trailing" secondItem="Dp8-aA-Ny0" secondAttribute="trailing" constant="8" id="VDO-n9-V6K"/>
                    <constraint firstItem="Dp8-aA-Ny0" firstAttribute="leading" secondItem="uJD-Nm-YYv" secondAttribute="leading" constant="8" id="tU6-tH-Kek"/>
                </constraints>
            </tableViewCellContentView>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <connections>
                <outlet property="ivCheck" destination="Ldl-Pw-PXn" id="8TI-f9-zAC"/>
                <outlet property="lbTitle" destination="uAa-8U-pi8" id="JjK-a9-t8M"/>
                <outlet property="mainView" destination="Dp8-aA-Ny0" id="uQE-EU-pMX"/>
                <outlet property="vBottomLine" destination="zcO-ep-ofp" id="WJY-IX-6Ou"/>
            </connections>
            <point key="canvasLocation" x="33.5" y="-14.5"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="ic_check_done" width="24" height="24"/>
    </resources>
</document>
