//
//  ShowFilmModel.swift
//  Booking
//
//  Created by <PERSON><PERSON>u on 4/5/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation
import ObjectMapper

class ShowFilmModel : Mappable, FilmInformation {
    var formatName: String {
        return (getFormatName()?.replacingOccurrences(of: "(", with: "").replacingOccurrences(of: ")", with: "") ?? "")
    }

    var filmGroupId : String?
    var code : String?
    var name : String?
    var name_F : String?
    var shortDescription : String?
    var shortDescription_F : String?
    var openingDate : String?
    var rate : String?
    var duration : Int?
    var filmGenreName : String?
    var filmGenreName_F : String?
    var filmRestrictAgeName : String?
    var subtitleName : String?
    var subtitleName_F : String?
    var trailerURL : String?
    var order : Int?
    var status : Bool?
    var nowShowing : Bool?
    var hasShow : Bool?
    var director : String?
    var mainPosterUrl : String?
    var sumOfShow : Int?
    var hasSneakShow : Bo<PERSON>?
    var listFilm : [ListFilm]?
    var indexPath: IndexPath?
    var IsHot: Bool = false

    var height: CGFloat {
        return (listFilm ?? []).map{ $0.height + 44.0 }.reduce(0, +)
    }

    required init?(map: Map) {

    }

    func mapping(map: Map) {

        filmGroupId <- map["FilmGroupId"]
        code <- map["Code"]
        name <- map["Name"]
        name_F <- map["Name_F"]
        shortDescription <- map["ShortDescription"]
        shortDescription_F <- map["ShortDescription_F"]
        openingDate <- map["OpeningDate"]
        rate <- map["Rate"]
        duration <- map["Duration"]
        filmGenreName <- map["FilmGenreName"]
        filmGenreName_F <- map["FilmGenreName_F"]
        filmRestrictAgeName <- map["FilmRestrictAgeName"]
        subtitleName <- map["SubtitleName"]
        subtitleName_F <- map["SubtitleName_F"]
        trailerURL <- map["TrailerURL"]
        order <- map["Order"]
        status <- map["Status"]
        nowShowing <- map["NowShowing"]
        hasShow <- map["HasShow"]
        director <- map["Director"]
        mainPosterUrl <- map["MainPosterUrl"]
        sumOfShow <- map["SumOfShow"]
        hasSneakShow <- map["HasSneakShow"]
        listFilm <- map["ListFilm"]
        IsHot <- map["IsHot"]
    }

    func getFilmRestrictAgeName() -> String? {
        return filmRestrictAgeName
    }

    func getName() -> String?{
        return Utils.shared.isEng() ? name_F : name
    }

    func getFilmGenre() -> String?{
        return Utils.shared.isEng() ? filmGenreName_F : filmGenreName
    }

    func getFormatName() -> String?{
        let isEn = Utils.shared.isEng()
        let film = listFilm?[indexPath?.row ?? 0]
        guard let formatName = isEn ? film?.filmFormatName_F : film?.filmFormatName else {
            return ""
        }

        return "(" + formatName + ")"
    }

    func getFilmPoster() -> String {
        let poster = mainPosterUrl
        return Config.BaseURLResource + (poster ?? "")
    }

    func getFullOptions() -> String? {
        var option = (getFormatName()?.replacingOccurrences(of: "(", with: "").replacingOccurrences(of: ")", with: "") ?? "") + " | "
        option += (getFilmGenre() ?? "") + " | "
        option += "\(duration ?? 0) " + "Home.Minute".localized

        return option
    }

  func getFinalOptions(_ format: String?) -> String? {
        var option = ""
        option += (format ?? getFormatName()?.replacingOccurrences(of: "(", with: "").replacingOccurrences(of: ")", with: "") ?? "") + " | "
        option += (getFilmGenre() ?? "") + " | "
        option += "\(duration ?? 0) " + "Home.Minute".localized

        return option
    }
}
