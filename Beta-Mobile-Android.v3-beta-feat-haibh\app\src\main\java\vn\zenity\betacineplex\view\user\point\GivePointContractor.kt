package vn.zenity.betacineplex.view.user.point

import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.model.UserModel

/**
 * Created by Zenity.
 */

interface GivePointContractor {
    interface View : IBaseView {
        fun showPoints(accumulation: Int, used: Int, available: Int)
        fun showUserSearched(users: List<UserModel>)
        fun giveVoucherFinished(isSuccess: <PERSON><PERSON><PERSON>, message: String)
        fun givePointFinished(isSuccess: <PERSON>ole<PERSON>, message: String)
    }

    interface Presenter : IBasePresenter<View> {
        fun getBetaPoint(accountId: String)
        fun searchUser(keyword: String)
        fun giveVoucher(id: String, userId: String)
        fun givePoint(point: Int, userId: String)
    }
}
