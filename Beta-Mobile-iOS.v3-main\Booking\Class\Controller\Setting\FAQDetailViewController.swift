//
//  FAQDetailViewController.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 4/13/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit

class FAQDetailViewController: BaseViewController {
    @IBOutlet weak var tableView: UITableView!
    var topicId: String?
    var topicTitle: String?
    let cellId = "NotificationTableViewCell"
    private var items: [TopicDetailModel] = []

    fileprivate let dataSource = SimpleTableViewDataSource()

    override func viewDidLoad() {
        super.viewDidLoad()

        localizableTitle = "FAQ.Title"
        setTitle("FAQ.Title".localized + " - " + (topicTitle ?? ""))

        self.tableView.dataSource = dataSource
        self.tableView.register(UINib.init(nibName: cellId, bundle: nil), forCellReuseIdentifier: cellId)
        getTopicDetail()
    }

    override func localizationDidChange() {
        super.localizationDidChange()
        setTitle("FAQ.Title".localized + " - " + (topicTitle ?? ""))
    }

    override func didReceiveMemoryWarning() {
        super.didReceiveMemoryWarning()
        // Dispose of any resources that can be recreated.
    }
}

extension FAQDetailViewController{
    private func getTopicDetail(){
        guard let id = topicId else {return}
        self.showLoading()
        EcmProvider.rx.request(.getFAQs(id)).mapObject(DDKCResponse<TopicDetailModel>.self)
            
            .subscribe(onNext:{[weak self] response in
                guard let `self` = self else {return}
                self.dismissLoading()
                self.handlerResponse(response, success: {
                    guard let items = response.ListObject else {return}
                    self.items = items
                    let tbItems = items.map { TableItem(title: $0.Title, content: $0.AnswerContent?.htmlToString, cellId: self.cellId, isOpen: false) }
                    self.dataSource.removeAll()
                    self.dataSource.addRows(tbItems)
                    self.tableView.reloadData()
                })
            }).disposed(by: disposeBag)
    }
}

extension FAQDetailViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, willSelectRowAt indexPath: IndexPath) -> IndexPath? {
        if let currentIdx = tableView.indexPathForSelectedRow, indexPath.row != currentIdx.row {
            let item = dataSource[currentIdx]
            tableView.deselectRow(at: currentIdx, animated: true)
            let cell = tableView.cellForRow(at: currentIdx) as? NotificationTableViewCell
            tableView.beginUpdates()
            item.isOpen = false//!item.isOpen
            cell?.isOpen = item.isOpen
            tableView.endUpdates()
        }
        return indexPath
    }
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
//        tableView.deselectRow(at: indexPath, animated: true)
        let item = dataSource[indexPath]
        print("select indexpath: \(indexPath) item: \(item.isOpen)")
        let cell = tableView.cellForRow(at: indexPath) as? NotificationTableViewCell
        CATransaction.begin()
        CATransaction.setCompletionBlock {
            let cellRect = tableView.rectForRow(at: indexPath)
            if !tableView.frame.contains(cellRect) {
                tableView.scrollToRow(at: indexPath, at: .top, animated: true)
            }
        }
//        tableView.layer.removeAllAnimations()
        tableView.beginUpdates()
        item.isOpen = !item.isOpen
        cell?.isOpen = item.isOpen
        tableView.endUpdates()
        CATransaction.commit()
    }
}
