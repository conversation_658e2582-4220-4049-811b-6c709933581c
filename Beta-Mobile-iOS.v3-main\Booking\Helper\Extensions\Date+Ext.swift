//
//  Date+Ext.swift
//  Booking
//
//  Created by <PERSON><PERSON>u on 4/10/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation
import SwiftDate

extension Date {
    func dateMorning() -> Bool {
        let hour = Calendar.current.component(.hour, from: self)
        return hour < 12
    }
}

extension Date
{
    static func dateFormatter(_ format: String = "dd/MM/yyyy") -> DateFormatter{
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = format
        dateFormatter.locale = Locale(identifier: "vi_VN")
        return dateFormatter
    }
    
    static func serverFormatter() -> DateFormatter{
        return dateFormatter("yyyy-MM-dd'T'HH:mm:ss")
    }
    
    func toString( dateFormat format: String ) -> String
    {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = format
        if Utils.shared.isEng() {
            dateFormatter.locale = Locale(identifier: "en_US")
        } else {
            dateFormatter.locale = Locale(identifier: "vi_VN")
        }
        return dateFormatter.string(from: self)
    }
    
    func toStringCalendar() -> String{
        return "\(toStringDay()), ngày \(toStringStandard())"
    }
    
    func toStringDay() -> String{
        return toString(dateFormat: "EEEE")
    }
    
    func toStringDateTime() -> String{
        return toString(dateFormat: "HH:mm dd/MM/yyyy")
    }
    
    
    func toStringStandard() -> String{
        return toString(dateFormat: "dd/MM/yyyy")
    }
    
    func timeFromDate() -> String{
        return toString(dateFormat: "HH:mm")
    }

    func toServerString() -> String {
        return toString(dateFormat: "yyyy-MM-dd'T'HH:mm:ss")
    }
    
    func years(from date: Date) -> Int {
        return Calendar.current.dateComponents([.year], from: date, to: self).year ?? 0
    }
    func months(from date: Date) -> Int {
        return Calendar.current.dateComponents([.month], from: date, to: self).month ?? 0
    }
    func weeks(from date: Date) -> Int {
        return Calendar.current.dateComponents([.weekOfYear], from: date, to: self).weekOfYear ?? 0
    }
    func days(from date: Date) -> Int {
        return Calendar.current.dateComponents([.day], from: date, to: self).day ?? 0
    }
    func hours(from date: Date) -> Int {
        return Calendar.current.dateComponents([.hour], from: date, to: self).hour ?? 0
    }
    func minutes(from date: Date) -> Int {
        return Calendar.current.dateComponents([.minute], from: date, to: self).minute ?? 0
    }
    func seconds(from date: Date) -> Int {
        return Calendar.current.dateComponents([.second], from: date, to: self).second ?? 0
    }

    var relativeTime: String {
        let now = Date()
        let ago = "ago".localized
        if now.years(from: self)   > 0 {
            return now.years(from: self).description  + " \("year".localized)"  + { return now.years(from: self)   > 1 ? "s".localized : "" }() + " \(ago)"
        }
        if now.months(from: self)  > 0 {
            return now.months(from: self).description + " \("month".localized)" + { return now.months(from: self)  > 1 ? "s".localized : "" }() + " \(ago)"
        }
        if now.weeks(from:self)   > 0 {
            return now.weeks(from: self).description  + " \("week".localized)"  + { return now.weeks(from: self)   > 1 ? "s".localized : "" }() + " \(ago)"
        }
        if now.days(from: self)    > 0 {
            if now.days(from:self) == 1 { return "Yesterday".localized }
            return now.days(from: self).description + " \("days ago".localized)"
        }
        if now.hours(from: self)   > 0 {
            return "\(now.hours(from: self)) \("hour".localized)"     + { return now.hours(from: self)   > 1 ? "s".localized : "" }() + " \(ago)"
        }
        if now.minutes(from: self) > 0 {
            return "\(now.minutes(from: self)) \("minute".localized)" + { return now.minutes(from: self) > 1 ? "s".localized : "" }() + " \(ago)"
        }
        if now.seconds(from: self) >= 0 {
            if now.seconds(from: self) < 30 { return "Just now".localized  }
            return "\(now.seconds(from: self)) \("second".localized)" + { return now.seconds(from: self) > 1 ? "s".localized : "" }() + " \(ago)"
        }
        return ""
    }
    
    static func fromString(_ value: String, dateFormat: String = "dd/MM/yyyy HH:mm:ss") -> Date? {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = dateFormat
        return dateFormatter.date(from: value)
    }
    
    func dateAppendWeek(number: Int, fromDate: Date = Date()) -> Date{
        let theSecondsOfWeek = 7 * 24 * 60 * 60
        return fromDate.addingTimeInterval(Double(number * theSecondsOfWeek))
    }
    
    static func dateFromServer(_ dateString: String?) -> Date?{
        guard let date = dateString else {
            return Date()
        }
        return serverFormatter().date(from: date)
    }
    
    static func dateFromServerSavis(_ dateStringFull: String) -> Date{
        var dateString = dateStringFull
        if dateStringFull.contains(".") {
            dateString = dateStringFull.components(separatedBy: ".")[0]
        }
        if let date = dateFormatter("yyyy-MM-dd'T'HH:mm:ss.ZZZ").date(from: dateString){
            return date
        }else if let date = dateFormatter("yyyy-MM-dd'T'HH:mm:ss.ZZ").date(from: dateString){
            return date
        }else if let date = dateFormatter("yyyy-MM-dd'T'HH:mm:ss.Z").date(from: dateString){
            return date
        }else if let date = dateFormatter("yyyy-MM-dd'T'HH:mm:ss").date(from: dateString){
            return date
        }else{
            return Date()
        }
    }
    
    func from(year: Int, month: Int, day: Int) -> Date {
        let gregorianCalendar = NSCalendar(calendarIdentifier: .gregorian)!

        var dateComponents = DateComponents()
        dateComponents.year = year
        dateComponents.month = month
        dateComponents.day = day

        let date = gregorianCalendar.date(from: dateComponents)!
        return date
    }
}

extension String {
    func usesAMPM() -> Bool {
        let locale = Locale.current
        let dateFormat = DateFormatter.dateFormat(fromTemplate: "j", options: 0, locale: locale)!
        if dateFormat.range(of:("a")) != nil {
            return true
        }
        else {
            return false
        }
    }
    
    func toDate(_ formatString: String) -> Date? {
        var fmtString = formatString
        if (fmtString.contains( "HH") && usesAMPM()){
            fmtString = fmtString.replacingOccurrences(of: "HH", with: "hh")
        } else if (fmtString.contains( "hh") && !usesAMPM()){
            fmtString = fmtString.replacingOccurrences(of: "hh", with: "HH")
        }
        return self.toDate(fmtString, region: .current)?.date
    }

    func dateFromServer() -> Date? {
        return toDate("yyyy-MM-dd'T'HH:mm:ss")
    }
}
