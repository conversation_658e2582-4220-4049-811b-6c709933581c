package vn.zenity.betacineplex.view.event

import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.model.NewModel

/**
 * Created by Zenity.
 */

interface EventContractor {
    interface View : IBaseView {
        fun showListNewsCategories(list: List<NewModel>)
    }

    interface Presenter : IBasePresenter<View> {
        fun getListNewsCategories()
    }
}
