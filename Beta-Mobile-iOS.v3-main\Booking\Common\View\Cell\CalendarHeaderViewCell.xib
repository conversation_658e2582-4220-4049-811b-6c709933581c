<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14109" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14088"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="Oswald-Regular.ttf">
            <string>Oswald-Regular</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" id="gTV-IL-0wX" customClass="CalendarHeaderViewCell" customModule="Booking_dev" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="178" height="78"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="178" height="78"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="8jt-JN-hYg">
                        <rect key="frame" x="0.0" y="0.0" width="178" height="78"/>
                        <subviews>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="249" verticalCompressionResistancePriority="751" text="05" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ESu-zQ-Gj3">
                                <rect key="frame" x="0.0" y="8" width="178" height="41.5"/>
                                <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="28"/>
                                <color key="textColor" red="0.72549019607843135" green="0.72549019607843135" blue="0.72549019607843135" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Hôm nay" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="isq-iE-DV1">
                                <rect key="frame" x="0.0" y="49.5" width="178" height="20.5"/>
                                <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="18"/>
                                <color key="textColor" red="0.72549019609999998" green="0.72549019609999998" blue="0.72549019609999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstAttribute="bottom" relation="greaterThanOrEqual" secondItem="isq-iE-DV1" secondAttribute="bottom" constant="8" id="Ldl-1J-EFs"/>
                            <constraint firstItem="isq-iE-DV1" firstAttribute="leading" secondItem="8jt-JN-hYg" secondAttribute="leading" id="XKz-Bu-w5s"/>
                            <constraint firstAttribute="trailing" secondItem="ESu-zQ-Gj3" secondAttribute="trailing" id="jZL-Cz-1Ny"/>
                            <constraint firstAttribute="trailing" secondItem="isq-iE-DV1" secondAttribute="trailing" id="jcz-Mk-Rcp"/>
                            <constraint firstItem="ESu-zQ-Gj3" firstAttribute="leading" secondItem="8jt-JN-hYg" secondAttribute="leading" id="nAY-aF-Oq5"/>
                            <constraint firstItem="isq-iE-DV1" firstAttribute="top" secondItem="ESu-zQ-Gj3" secondAttribute="bottom" id="pT0-ij-k2s"/>
                            <constraint firstItem="ESu-zQ-Gj3" firstAttribute="top" secondItem="8jt-JN-hYg" secondAttribute="top" constant="8" id="szp-Zh-hTK"/>
                        </constraints>
                    </view>
                </subviews>
            </view>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstAttribute="bottom" secondItem="8jt-JN-hYg" secondAttribute="bottom" id="0fw-iX-dCe"/>
                <constraint firstItem="8jt-JN-hYg" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="SCN-dq-vUN"/>
                <constraint firstAttribute="trailing" secondItem="8jt-JN-hYg" secondAttribute="trailing" id="d4u-x4-bGr"/>
                <constraint firstItem="8jt-JN-hYg" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" id="h6l-Dc-PPt"/>
            </constraints>
            <viewLayoutGuide key="safeArea" id="ZTg-uK-7eu"/>
            <size key="customSize" width="178" height="78"/>
            <connections>
                <outlet property="lbContent" destination="isq-iE-DV1" id="ZdR-En-bxz"/>
                <outlet property="lbDay" destination="ESu-zQ-Gj3" id="vmb-DE-FRI"/>
            </connections>
            <point key="canvasLocation" x="89" y="66"/>
        </collectionViewCell>
    </objects>
</document>
