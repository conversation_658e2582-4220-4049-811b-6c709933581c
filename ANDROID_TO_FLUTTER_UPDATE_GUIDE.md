# 🔄 Android to Flutter Update Guide

## 🎯 **MỤC TIÊU: <PERSON> phép người dùng đã cài app Android từ CH Play cập nhật lên bản Flutter**

## ✅ **CÁC YẾU TỐ ĐÃ ĐƯỢC ĐẢM BẢO:**

### 1. **📦 Package Name (Application ID)**
```gradle
// ✅ ĐÚNG - Giống với Android repo
applicationId "com.beta.betacineplex"
```

### 2. **🔑 Signing Certificate (Keystore)**
```gradle
// ✅ ĐÚNG - Sử dụng cùng production keystore
release {
    keyAlias 'beta cineplex'
    keyPassword 'Betacorpvn@123'
    storeFile file('keystore/beta_cineplex_app_key.jks')
    storePassword 'Betacorpvn@123'
}
```

### 3. **📊 Version Code & Version Name**
```gradle
// ✅ ĐÃ CẬP NHẬT - Cao h<PERSON> cả Android và iOS hiện tại
// Android repo: versionCode 48, versionName "2.7.6"
// iOS repo: version 38, versionName "2.7.1"
// Flutter repo: versionCode 50, versionName "2.8.0"
versionCode 50
versionName "2.8.0"
```

## 📋 **CHECKLIST HOÀN CHỈNH:**

### ✅ **Đã hoàn thành:**
- [x] **Package Name:** `com.beta.betacineplex` (giống Android & iOS)
- [x] **Keystore:** Sử dụng cùng production keystore
- [x] **Version Code:** 50 (cao hơn Android: 48, iOS: 38)
- [x] **Version Name:** 2.8.0 (cao hơn Android: 2.7.6, iOS: 2.7.1)
- [x] **Target SDK:** 35 (tương thích)
- [x] **Min SDK:** 21 (tương thích)

### 🔄 **Cần kiểm tra thêm:**
- [ ] **Firebase Project:** Cùng project với Android app
- [ ] **Google Services:** Cùng configuration
- [ ] **Facebook App ID:** Cùng với Android app
- [ ] **Permissions:** Đầy đủ permissions cần thiết

## 🚀 **QUY TRÌNH DEPLOY LÊN CH PLAY:**

### **Bước 1: Build Production APK/AAB**
```bash
# Đảm bảo đang dùng production keystore
# Kiểm tra trong android/app/build.gradle - uncomment production keystore

# Build App Bundle cho CH Play
flutter build appbundle --release

# Hoặc build APK
flutter build apk --release
```

### **Bước 2: Kiểm tra thông tin APK/AAB**
```bash
# Kiểm tra package name
aapt dump badging build/app/outputs/bundle/release/app-release.aab | grep package

# Kết quả mong đợi:
# package: name='com.beta.betacineplex' versionCode='49' versionName='2.8.0'
```

### **Bước 3: Upload lên Google Play Console**
1. Đăng nhập Google Play Console
2. Chọn app "Beta Cinemas" (com.beta.betacineplex)
3. Vào **Production** → **Create new release**
4. Upload file `app-release.aab`
5. Điền **Release notes** (changelog)
6. **Review** và **Start rollout to production**

## ⚠️ **LƯU Ý QUAN TRỌNG:**

### **1. Version Code phải cao hơn:**
- **Android hiện tại:** 48
- **Flutter mới:** 49 ✅
- **Lần sau:** 50, 51, 52...

### **2. Signing Certificate phải giống:**
- ✅ Đang dùng cùng production keystore
- ✅ Cùng alias: "beta cineplex"
- ✅ Cùng password: "Betacorpvn@123"

### **3. Package Name phải giống:**
- ✅ `com.beta.betacineplex`

### **4. Permissions:**
- Đảm bảo Flutter app có đủ permissions như Android app
- Kiểm tra `android/app/src/main/AndroidManifest.xml`

## 🔍 **VERIFICATION STEPS:**

### **Trước khi upload:**
```bash
# 1. Kiểm tra package name
flutter build apk --release
aapt dump badging build/app/outputs/flutter-apk/app-release.apk | grep package

# 2. Kiểm tra signing certificate
keytool -printcert -jarfile build/app/outputs/flutter-apk/app-release.apk

# 3. Kiểm tra version
aapt dump badging build/app/outputs/flutter-apk/app-release.apk | grep version
```

### **Sau khi upload:**
1. **Internal Testing:** Test với internal testers trước
2. **Staged Rollout:** Rollout từng phần (5% → 20% → 50% → 100%)
3. **Monitor:** Theo dõi crash reports và user feedback

## 📱 **TRẢI NGHIỆM NGƯỜI DÙNG:**

### **Người dùng đã cài Android app:**
1. Nhận thông báo update từ CH Play
2. Tap "Update" 
3. App Flutter sẽ được cài đè lên Android app
4. Dữ liệu user được giữ nguyên (nếu cùng package name)
5. User tiếp tục sử dụng bình thường

### **Người dùng mới:**
1. Tìm "Beta Cinemas" trên CH Play
2. Cài đặt bản Flutter mới
3. Sử dụng bình thường

## 🎯 **EXPECTED RESULTS:**

✅ **Seamless Update:** Người dùng cập nhật bình thường  
✅ **Data Preservation:** Dữ liệu user được giữ nguyên  
✅ **Same App Listing:** Cùng listing trên CH Play  
✅ **No Conflicts:** Không có xung đột giữa 2 bản  

## 🚨 **TROUBLESHOOTING:**

### **Nếu upload bị lỗi "Certificate mismatch":**
- Kiểm tra lại keystore đang dùng
- Đảm bảo dùng đúng production keystore
- Kiểm tra alias và password

### **Nếu upload bị lỗi "Version code too low":**
- Tăng version code lên cao hơn bản hiện tại
- Update trong `android/app/build.gradle`

### **Nếu upload bị lỗi "Package name mismatch":**
- Kiểm tra `applicationId` trong `build.gradle`
- Đảm bảo đúng `com.beta.betacineplex`

## 🚀 **QUICK BUILD COMMANDS:**

### **🖥️ Windows (Recommended):**
```batch
# 1. Verify keystore configuration
verify_keystore.bat

# 2. Build release APK with production keystore
build_release_apk.bat

# 3. Compare signatures (optional)
compare_signatures.bat
```

### **⌨️ Manual Commands:**
```bash
# Build release APK with production keystore
flutter clean
flutter pub get
flutter build apk --release

# Check APK signature
keytool -printcert -jarfile build/app/outputs/flutter-apk/app-release.apk
```

### **🔧 Troubleshooting Install Issues:**

#### **❌ "Ứng dụng không cài đặt thành công":**

**Nguyên nhân:** Signing certificate mismatch

**Giải pháp:**
1. **Verify keystore:** Run `verify_keystore.bat`
2. **Check build.gradle:** Ensure production keystore is active
3. **Clean build:** `flutter clean` before building
4. **Compare signatures:** Use `compare_signatures.bat`

#### **✅ Success Criteria:**
- ✅ **Package Name:** `com.beta.betacineplex` (matches CH Play)
- ✅ **Version Code:** `50` (higher than CH Play: `48`)
- ✅ **Keystore:** `beta_cineplex_app_key.jks` (same as CH Play)
- ✅ **Alias:** `beta cineplex` (same as CH Play)

## 🎉 **READY FOR DEPLOYMENT!**

Tất cả yếu tố đã được chuẩn bị đúng để người dùng có thể cập nhật từ bản Android sang bản Flutter một cách seamless!

**Next step:** Build production AAB và upload lên Google Play Console.
