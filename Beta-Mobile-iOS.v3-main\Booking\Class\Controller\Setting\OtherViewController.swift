//
//  OtherViewController.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 3/14/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit

enum OtherType {
    case Term, PolicyPayment, Security, CompanyInfo
}

class OtherViewController: BaseViewController {
    @IBOutlet weak var textView: UITextView!
    var type: OtherType?
    override func viewDidLoad() {
        super.viewDidLoad()
        if type == .Term {
            localizableTitle = "TermOfUse.Title"
        } else if type == .PolicyPayment {
            localizableTitle = "PaymentPolicy.Title"
        } else if type == .Security {
            localizableTitle = "SecurePolicy.Title"
        } else if type == .CompanyInfo {
            localizableTitle = "CompanyInfo.Title"
        }
        getTypeId()

        var contentInset = textView.contentInset
        contentInset.left = 9
        contentInset.right = 9
        textView.contentInset = contentInset
        textView.isEditable = false

        if navigationController?.viewControllers.count == 1 {
            addRightButton(#imageLiteral(resourceName: "icClose"))
        }
    }

    override func didReceiveMemoryWarning() {
        super.didReceiveMemoryWarning()
        // Dispose of any resources that can be recreated.
    }

    override func updateNotifications(_ needReset: Bool = false) {
        if navigationController?.viewControllers.count == 1 {
//            navigationController?.dismiss(animated: true, completion: nil)
        } else {
            super.updateNotifications()
        }
    }

    override func rightButtonPressed(_ sender: UIBarButtonItem) {
        if navigationController?.viewControllers.count == 1 {
            navigationController?.dismiss(animated: true, completion: nil)
        } else {
            super.rightButtonPressed(sender)
        }
    }
}

extension OtherViewController{
    private func getTypeId(){
        guard let type = type else {return}
        self.showLoading()
        var ecmToken: Ecm = .getTermId
        switch type {
        case .PolicyPayment:
            ecmToken = .getPaymentPolicyId
        case .Security:
            ecmToken = .getSecurityId
        case .CompanyInfo:
            ecmToken = .getCompanyInfoId
        default:
            ecmToken = .getTermId
        }
        EcmProvider.rx.request(ecmToken).mapObject(DDKCResponse<PolicyModel>.self)
        
            .subscribe(onNext:{[weak self] response in
                guard let `self` = self else {return}
                self.handlerResponse(response, success: {
                    guard let object = response.Object else{
                        print("Wrong data")
                        return
                    }
                    self.getContent(newId: object.ParameterValue)
                })
            }).disposed(by: disposeBag)
//        NetworkManager.shared.getPolicyId(type: type) {[weak self] (response, error) in
//            guard let `self` = self else {return}
//            self.handlerNetworkResponse(response, error: error, success: { (result) in
//                guard let object = result.Object else {
//                    print("Wrong data")
//                    self.dismissLoading()
//                    return
//                }
//                self.getContent(newId: object.ParameterValue)
//            })
//        }
    }
    
    private func getContent(newId: String?){
        guard let id = newId else {
            self.dismissLoading()
            return
        }
        EcmProvider.rx.request(.getNewWithId(id, nil, nil)).mapObject(DDKCResponse<PolicyContentModel>.self)
        
            .subscribe(onNext:{[weak self] response in
                guard let `self` = self else {return}
                self.dismissLoading()
                self.handlerResponse(response, success: {
                    guard let object = response.Object else{
                        print("Wrong data")
                        return
                    }
                    if let content = object.Noi_dung_chi_tiet?.first?.ParagraphData?.ParagraphContent{
                        self.textView.attributedText = content.htmlToAttributedString(14)
                    }
                })
            }).disposed(by: disposeBag)
    }
}
