package vn.zenity.betacineplex.model

import android.os.Parcel
import android.os.Parcelable
import com.thoughtbot.expandablerecyclerview.models.ExpandableGroup
import java.util.*

class Notification(title: String, var content: String, var date: String? = null, var isRead: Boolean = true, var newsModel: NewsModel? = null,
                   var newNotification: NewNotification? = null,
                   var RefId: String? = null,
                   var Id: Int = 0,
                   var ScreenCode: Int = 0) : ExpandableGroup<NotificationContent>(title, listOf()) {
    var ReadStatus: Boolean = false

}
class NotificationContent() : Parcelable {
    constructor(parcel: Parcel) : this() {
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {

    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<NotificationContent> {
        override fun createFromParcel(parcel: Parcel): NotificationContent {
            return NotificationContent(parcel)
        }

        override fun newArray(size: Int): Array<NotificationContent?> {
            return arrayOfNulls(size)
        }
    }
}