package vn.zenity.betacineplex.helper.extension

import io.reactivex.Observable
import io.reactivex.Single
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.schedulers.Schedulers
import retrofit2.HttpException
import vn.zenity.betacineplex.app.App
import vn.zenity.betacineplex.global.Constant
import vn.zenity.betacineplex.global.Global
import java.util.concurrent.TimeUnit

fun <T> Observable<T>.applyOn(): Observable<T> {
    return this.subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .doOnError {
                if(it is HttpException && it.code() == 401) {
                    Global.share().user = null
                    App.shared().openLogin()
                }
            }
}

fun <T> Single<T>.applyOn(): Single<T> {
    return this.subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .doOnError {
                if(it is HttpException && it.code() == 401) {
                    Global.share().user = null
                    App.shared().openLogin()
                }
            }
}