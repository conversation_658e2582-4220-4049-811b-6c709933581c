{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9849adcd82354dfe612c11c0e00843044d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980d3926596ad3cd9c175f9e28b372b54b", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988817c5b08860e69fa82e342a8f815f12", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98288f36904c064e8ffee3307684f0adf4", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988817c5b08860e69fa82e342a8f815f12", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984ad21424d04da79e5b6b09fe13bb9d64", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982162d20dcce519a05a033e529cafba2b", "guid": "bfdfe7dc352907fc980b868725387e98c2ba2564738076858fe79d7046297cea", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc327999ea82a1c13068e6ce5f2f0509", "guid": "bfdfe7dc352907fc980b868725387e98853b0e87dffa71f5b58aa19e5d8456db", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b40ea6be1b4b5985a68a79cbf2b17a3", "guid": "bfdfe7dc352907fc980b868725387e98c0ffabfaa47270a182eb861dcb3c97ba", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db85ba963adfe416f60d19da18627314", "guid": "bfdfe7dc352907fc980b868725387e98e4280c00d9dacc0d029f2a76fae53742", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ffa4ac57bdb9051fb309675c78ff9e9", "guid": "bfdfe7dc352907fc980b868725387e984ea3eecb0cd4d74cb5250b05948b72c7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989db052358845b8eb890b92b864d2776b", "guid": "bfdfe7dc352907fc980b868725387e98b7ca25109c8d0cd0e6ec9cf85e326f07", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98272903566fc7d18b2e2024f546769bb4", "guid": "bfdfe7dc352907fc980b868725387e982b00b08c80673b7ac7763b5cca0c05c1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877147ef5e216d0d94ce955ed90497634", "guid": "bfdfe7dc352907fc980b868725387e9824f69690574f55583d0ec1eac6446085", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5618c541e36263ff5df980cf95eb2c2", "guid": "bfdfe7dc352907fc980b868725387e98a3d6ceca4889b1cc6dce1a9db4b8f3b2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880cd3837a1ca1eaa3a4b04e65617abbe", "guid": "bfdfe7dc352907fc980b868725387e980dcd742a45bc2b581179f4f31e84335f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809e488fd23aa56a052ee9a8b3eaed650", "guid": "bfdfe7dc352907fc980b868725387e988afd726ea2439bce89d32737dd29861e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833654be65f0fd943eb40e98f5f9d0bfa", "guid": "bfdfe7dc352907fc980b868725387e9837dc2cb26d47f125c3bfc8a42987c140", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983daa6180a1e38ae589e01b48184c7ef4", "guid": "bfdfe7dc352907fc980b868725387e984f13f038c098389b2cc3a7970f1ecf21", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b040f0ba3ba7b12cc983da682ad3bb8", "guid": "bfdfe7dc352907fc980b868725387e98f57fd05847d719d2fed620540c65bc5e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd4b130d66161279a40cad65c957ef58", "guid": "bfdfe7dc352907fc980b868725387e989d5a9b3c658ee5b10e84d06e2bf55ef9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98833813f666278e27a22125b24f76d7f5", "guid": "bfdfe7dc352907fc980b868725387e984df56e010584a7b7a75ec6e43e8e094b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d495387fe5a3e9b66ba00b7a4fb2cccf", "guid": "bfdfe7dc352907fc980b868725387e98a581ec0675bf26a52652879cab6ccb16", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891d0531f1d002be401a29a0c8cf7ead8", "guid": "bfdfe7dc352907fc980b868725387e987b9e081c1f47b9b5c71ff6b7a6741b5f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4306bbeb0d7d277c30822fe8fde7fc9", "guid": "bfdfe7dc352907fc980b868725387e98001b4d9e2a68f9da26c9df0f8dd22711", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984afb310f309019677a2f82baa4a939cb", "guid": "bfdfe7dc352907fc980b868725387e98fd50f07295abcf055b5e4b7b2cb093c2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6bad12ebed41c4327780d9378f8ff53", "guid": "bfdfe7dc352907fc980b868725387e98ee7c0655d6fdf9bb0b15a2564e3001b3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dddb3ed5d29151b5059b9454f8d53d87", "guid": "bfdfe7dc352907fc980b868725387e9879f560ef014af1116b607e66871f8a4c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3287f73bf06d9981437711561ee9c8b", "guid": "bfdfe7dc352907fc980b868725387e983f72bbb6c37b6e76a32b4a1b51eac00f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8d4b1dcfa49fb603172e757347d2941", "guid": "bfdfe7dc352907fc980b868725387e987a6b0f9f4cd20dab7e492387fd96c668", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e981664dfc7329363764fdacd5d07a0c8e8", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d0edb5c05cd27379e034717e6b5661de", "guid": "bfdfe7dc352907fc980b868725387e982d80efaa8cac804b67f5522a9e5f081e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b48200b697947d6d60b6bc357b8eec8", "guid": "bfdfe7dc352907fc980b868725387e98edb3b316fe792e12b5f399ed436ec453"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98203366f704a81fc116088ba54baff2c3", "guid": "bfdfe7dc352907fc980b868725387e98205a3f2f01abd99df7d670b1e4bc5b4f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981aaaf8c4e62d32b0d57c34d7506f5e6a", "guid": "bfdfe7dc352907fc980b868725387e98762ff973933a5ef20806850da244cc91"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98688ee67c350c18950104b595a27a33ea", "guid": "bfdfe7dc352907fc980b868725387e9870291f334a3b4193c45c3ec4143223a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e21eac8b2aa470455c763f112bd6508d", "guid": "bfdfe7dc352907fc980b868725387e98d0fad16d3d7fdee4d59393a157398d1e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0542ad07f49589803c22be143101ba3", "guid": "bfdfe7dc352907fc980b868725387e98e0db079f9fbde5fd4c4b8540dc0da824"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985facc067f15c33f055eb46cfb8735242", "guid": "bfdfe7dc352907fc980b868725387e98c8ce60cef79a7ce69fc2662a318d67f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da21a1b831168bc1b944d3313da81454", "guid": "bfdfe7dc352907fc980b868725387e9812895a73a0e03eaf508ddd3fb0d0c01f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2e05407d50630d8a1c851de02192185", "guid": "bfdfe7dc352907fc980b868725387e989c97da061777c23cf2387296b895a4e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6ee80c1163e489e5e4df78a07e3d732", "guid": "bfdfe7dc352907fc980b868725387e98f02f600f64d58e0f7d4eecbc25f04c7b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801228aae3c1fd53e7c57a25c277144a6", "guid": "bfdfe7dc352907fc980b868725387e984c6a003f82bd71c2daa58df7da0f74fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824c97421e120388285995d5b162d81e7", "guid": "bfdfe7dc352907fc980b868725387e9820a1666d38ed470527e26d3b96a3fec9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb299cb9382f45424d85bc183ad7c2b2", "guid": "bfdfe7dc352907fc980b868725387e98cd466a315222b40faa33fc6bdc1fae43"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98303ecfd17d2a9f33402ca89d0f95106e", "guid": "bfdfe7dc352907fc980b868725387e98950a4366924646dd7ea62e7a738fc23f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864889cf8b2dbc9bc8651b177730f403d", "guid": "bfdfe7dc352907fc980b868725387e9830f1cedde3230afa0f52f0983ff82505"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898a183f81d6d684320188ead0abc21e1", "guid": "bfdfe7dc352907fc980b868725387e988b75dd9cae97808bca73ac3a51cbcd92"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ccde41f91c63b4e55e36cfa02daee19", "guid": "bfdfe7dc352907fc980b868725387e98bc69122c6991dc655225143252842d7e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0d07914efd1fe5b9dff5ea51c93e61d", "guid": "bfdfe7dc352907fc980b868725387e985ad6ea797f8a9f84792fc3790ec5062f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c75d82503a0b1bf423ecb557930cd574", "guid": "bfdfe7dc352907fc980b868725387e98895c6bd97422a8fbaa2f027ff69d57eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b4e1b4103511137bef1219c67191e7c", "guid": "bfdfe7dc352907fc980b868725387e98634b2be6a9361bf9e9f27f008ca68dd7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808ff7c172b50a0158ed8802dfbc9f031", "guid": "bfdfe7dc352907fc980b868725387e9836e395285a4b0a2a22da5efef5807372"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988592a6c5fb0a662a9728e8f3372782d4", "guid": "bfdfe7dc352907fc980b868725387e9830d50d3b8dada408fbd5ac3ca1c576d1"}], "guid": "bfdfe7dc352907fc980b868725387e98339f3c5c18cf261c37731f1ff9cd3c6b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e98f76f428393514513ee220ee3cd45b4b7"}], "guid": "bfdfe7dc352907fc980b868725387e98cf9e1d75d6fd474380329cbf47d24d45", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98911b19d9ae38fc82e6a73bfd4cf29ba7", "targetReference": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1"}], "guid": "bfdfe7dc352907fc980b868725387e98f5ee52bb1ad32275d40a0a37ff2d7e9c", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1", "name": "webview_flutter_wkwebview-webview_flutter_wkwebview_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988efdc4dd0ac29b43123295eca853f4ed", "name": "webview_flutter_wkwebview", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e980823710353e0487822d6da09bf8d6254", "name": "webview_flutter_wkwebview.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}