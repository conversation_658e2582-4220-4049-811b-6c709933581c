//
//  NetworkManager+Ecm.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 4/10/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation
typealias EcmHandler = (_ response: DDKCResult<NewModel>?, _ error: Error?) -> Void
typealias NewHandler = (_ response: DDKCResult<NewsModel>?, _ error: Error?) -> Void
typealias PolicyHandler = (_ response: DDKCResult<PolicyModel>?, _ error: Error?) -> Void
extension NetworkManager{
    func getPromotionCategory(completionHandler: @escaping EcmHandler){
        var path = "api/v1/ecm/categories/news-promotion"
        if Utils.shared.isEng() {
            path += "/en"
        }
        self.getRequest(path: path, completionHandler: completionHandler)
    }
    
    func getNewByCategoryId(_ id: String, pageSize: Int? = nil, pageNumber: Int? = nil, completionHandler: @escaping NewHandler){
        var size = Config.PageSize
        if let pageSize = pageSize{
            size = pageSize
        }
        var num = 1
        if let pageNumber = pageNumber{
            num = pageNumber
        }
        let params = ["pageSize": size,
                      "pageNumber": num]
        let path = "api/v1/ecm/{\(id)}/news"
        self.getRequest(path: path, params: params, completionHandler: completionHandler)
    }
    
    func getPolicyId(type: OtherType, completionHandler: @escaping PolicyHandler){
        var path = "api/v1/ecm/parameter?code="
        switch type {
        case .Term:
            path = path + "mobile:app:dieukhoan:"
        case .PolicyPayment:
            path = path + "mobile:app:dieukhoan-thanhtoan:"
        case .Security:
            path = path + "mobile:app:dieukhoan-baomat:"
        case .CompanyInfo:
            path = path + "mobile:app:thongtin-congty:"
        }
        path = path + (Utils.shared.isEng() ? "en" : "vi")
        self.getRequest(path: path, completionHandler: completionHandler)
    }
}
