//
//  CardClassModel.swift
//  Booking-dev
//
//  Created by <PERSON><PERSON> on 6/9/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation
import ObjectMapper

class CardClassModel : Mappable {
    var CardId : String?
    var Name : String?
    var Code : String?
    var TotalPointCondition : Int64?
    var TotalPaymentCondition : Int64?
    var Order : Int?
    var IsDeleted : Bool?
    var Status : Int?
    var MoneyAccumulateUnit : Int?
    var Description : String?
    var SpendingPointUnit : Int?
    var UrlIcon: String?
    var ClassId: String?


    required init?(map: Map) {

    }

    func mapping(map: Map) {
        CardId               <- map["CardId"]
        Name           <- map["Name"]
        Code           <- map["Code"]
        Status               <- map["Status"]
        TotalPointCondition        <- map["TotalPointCondition"]
        TotalPaymentCondition          <- map["TotalPaymentCondition"]
        Order       <- map["Order"]
        IsDeleted   <- map["IsDeleted"]
        Status       <- map["Status"]
        Description          <- map["Description"]
        MoneyAccumulateUnit      <- map["MoneyAccumulateUnit"]
        SpendingPointUnit        <- map["SpendingPointUnit"]
        UrlIcon         <-      map["UrlIcon"]
        ClassId         <-      map["ClassId"]
    }

    enum CardCode: String {
        case VIP, KHTT

        static func ==(_ left: CardCode, right: String?) -> Bool {
            return left.rawValue == right
        }

        static func ==(_ left: String?, right: CardCode) -> Bool {
            return left == right.rawValue
        }
    }
}

