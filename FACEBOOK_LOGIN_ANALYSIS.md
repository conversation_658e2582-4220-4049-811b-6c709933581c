# 🔍 Facebook Login Configuration Analysis

## 🚨 **Vấn đề hiện tại**

**Error:** `Invalid key hash. The key hash M/51 ..... does not match any stored key hashes`

## 📊 **So sánh cấu hình giữa các repos**

### **✅ Facebook App ID & Client Token - ĐÚNG**

| Repo | Facebook App ID | Client Token | Status |
|------|----------------|--------------|---------|
| **Flutter** | `367174740769877` | `********************************` | ✅ MATCH |
| **Android** | `367174740769877` | `********************************` | ✅ MATCH |
| **iOS Dev** | `367174740769877` | `********************************` | ✅ MATCH |

### **✅ Bundle ID / Package Name - ĐÚNG**

| Repo | Package/Bundle ID | Status |
|------|------------------|---------|
| **Flutter Android** | `com.beta.betacineplex` | ✅ MATCH |
| **Flutter iOS** | `com.beta.betacineplex` | ✅ MATCH |
| **Android Repo** | `vn.zenity.betacineplex` | ❌ KHÁC |

### **❌ VẤN ĐỀ CHÍNH: KEY HASH**

**Root Cause:** Flutter app đang sử dụng **debug key hash** khác với key hash đã đăng ký trên Facebook Developer Console.

## 🔧 **Cách fix Key Hash Issue**

### **Step 1: Generate Key Hash cho Flutter App**

#### **Method 1: Sử dụng keytool (Recommended)**

```bash
# For Debug Key Hash
keytool -exportcert -alias androiddebugkey -keystore ~/.android/debug.keystore | openssl sha1 -binary | openssl base64

# For Release Key Hash (nếu có release keystore)
keytool -exportcert -alias your_release_alias -keystore path/to/your/release.keystore | openssl sha1 -binary | openssl base64
```

#### **Method 2: Programmatically trong Flutter**

```dart
// Thêm vào main.dart để get key hash
import 'dart:convert';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';
import 'package:flutter/services.dart';

Future<void> getKeyHash() async {
  try {
    final Uint8List signature = await const MethodChannel('flutter.dev/key_hash')
        .invokeMethod('getKeyHash');
    final String keyHash = base64.encode(sha1.convert(signature).bytes);
    print('🔑 Key Hash: $keyHash');
  } catch (e) {
    print('❌ Error getting key hash: $e');
  }
}
```

#### **Method 3: Sử dụng Android Code**

```kotlin
// Thêm vào MainActivity.kt
import android.content.pm.PackageManager
import android.content.pm.Signature
import android.util.Base64
import android.util.Log
import java.security.MessageDigest

private fun printKeyHash() {
    try {
        val info = packageManager.getPackageInfo(packageName, PackageManager.GET_SIGNATURES)
        for (signature: Signature in info.signatures) {
            val md = MessageDigest.getInstance("SHA")
            md.update(signature.toByteArray())
            val keyHash = Base64.encodeToString(md.digest(), Base64.DEFAULT)
            Log.d("KeyHash", "Key Hash: $keyHash")
            println("🔑 Key Hash: $keyHash")
        }
    } catch (e: Exception) {
        Log.e("KeyHash", "Error: ${e.message}")
    }
}

// Gọi trong onCreate()
override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)
    printKeyHash() // Add this line
}
```

### **Step 2: Update Facebook Developer Console**

1. **Truy cập Facebook Developer Console:**
   - Đi đến https://developers.facebook.com/
   - Chọn app `367174740769877`

2. **Thêm Key Hash:**
   - Settings → Basic
   - Scroll xuống "Android" section
   - Thêm key hash vừa generate vào "Key Hashes" field
   - Save changes

3. **Verify Package Name:**
   - Đảm bảo "Package Name" là `com.beta.betacineplex`
   - Đảm bảo "Class Name" là `com.beta.betacineplex.MainActivity`

### **Step 3: Update Flutter Configuration**

#### **Android Manifest - Đã đúng ✅**
```xml
<!-- android/app/src/main/AndroidManifest.xml -->
<meta-data
    android:name="com.facebook.sdk.ApplicationId"
    android:value="@string/facebook_app_id" />

<meta-data
    android:name="com.facebook.sdk.ClientToken"
    android:value="@string/facebook_client_token" />
```

#### **Strings.xml - Đã đúng ✅**
```xml
<!-- android/app/src/main/res/values/strings.xml -->
<string name="facebook_app_id">367174740769877</string>
<string name="facebook_client_token">********************************</string>
<string name="fb_login_protocol_scheme">fb367174740769877</string>
```

#### **iOS Info.plist - Đã đúng ✅**
```xml
<!-- ios/Runner/Info.plist -->
<key>FacebookAppID</key>
<string>367174740769877</string>
<key>FacebookClientToken</key>
<string>********************************</string>
<key>FacebookDisplayName</key>
<string>Betacineplex</string>

<!-- URL Schemes -->
<key>CFBundleURLSchemes</key>
<array>
    <string>fb367174740769877</string>
</array>
```

## 🛠️ **Quick Fix Implementation**

### **Tạo Key Hash Generator**

```dart
// lib/utils/key_hash_generator.dart
import 'dart:convert';
import 'dart:io';
import 'package:flutter/services.dart';

class KeyHashGenerator {
  static const MethodChannel _channel = MethodChannel('key_hash_generator');
  
  static Future<String?> getKeyHash() async {
    try {
      if (Platform.isAndroid) {
        final String? keyHash = await _channel.invokeMethod('getKeyHash');
        print('🔑 Generated Key Hash: $keyHash');
        return keyHash;
      }
      return null;
    } catch (e) {
      print('❌ Error generating key hash: $e');
      return null;
    }
  }
  
  static Future<void> printKeyHashForDebugging() async {
    final keyHash = await getKeyHash();
    if (keyHash != null) {
      print('');
      print('🔑 =================================');
      print('🔑 FACEBOOK KEY HASH FOR DEBUGGING');
      print('🔑 =================================');
      print('🔑 Key Hash: $keyHash');
      print('🔑 =================================');
      print('🔑 Copy this key hash to Facebook Developer Console');
      print('🔑 Settings → Basic → Android → Key Hashes');
      print('🔑 =================================');
      print('');
    }
  }
}
```

### **Android Implementation**

```kotlin
// android/app/src/main/kotlin/com/beta/betacineplex/MainActivity.kt
import android.content.pm.PackageManager
import android.content.pm.Signature
import android.util.Base64
import android.util.Log
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import java.security.MessageDigest

class MainActivity: FlutterActivity() {
    private val KEY_HASH_CHANNEL = "key_hash_generator"

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, KEY_HASH_CHANNEL)
            .setMethodCallHandler { call, result ->
                when (call.method) {
                    "getKeyHash" -> {
                        val keyHash = getKeyHash()
                        result.success(keyHash)
                    }
                    else -> result.notImplemented()
                }
            }
    }

    private fun getKeyHash(): String? {
        try {
            val info = packageManager.getPackageInfo(packageName, PackageManager.GET_SIGNATURES)
            for (signature: Signature in info.signatures) {
                val md = MessageDigest.getInstance("SHA")
                md.update(signature.toByteArray())
                val keyHash = Base64.encodeToString(md.digest(), Base64.DEFAULT).trim()
                Log.d("KeyHash", "🔑 Key Hash: $keyHash")
                return keyHash
            }
        } catch (e: Exception) {
            Log.e("KeyHash", "❌ Error generating key hash: ${e.message}")
        }
        return null
    }
}
```

### **Usage trong App**

```dart
// lib/main.dart hoặc login page
import 'utils/key_hash_generator.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Print key hash for debugging (chỉ trong debug mode)
  if (kDebugMode) {
    await KeyHashGenerator.printKeyHashForDebugging();
  }
  
  runApp(MyApp());
}
```

## 🧪 **Testing Steps**

### **1. Generate Key Hash:**
```bash
# Run app và check console logs
flutter run --debug

# Tìm log:
# 🔑 Key Hash: [YOUR_KEY_HASH_HERE]
```

### **2. Add to Facebook Console:**
- Copy key hash từ console
- Paste vào Facebook Developer Console
- Save settings

### **3. Test Facebook Login:**
```dart
// Test login
await FacebookAuth.instance.login(permissions: ['public_profile', 'email']);
```

## 📋 **Checklist**

- [ ] ✅ Facebook App ID match: `367174740769877`
- [ ] ✅ Client Token match: `********************************`
- [ ] ✅ Bundle ID match: `com.beta.betacineplex`
- [ ] ❌ **Key Hash cần update**
- [ ] ✅ URL Schemes match: `fb367174740769877`
- [ ] ✅ AndroidManifest.xml configured
- [ ] ✅ Info.plist configured

## 🎯 **Expected Result**

Sau khi fix key hash:
```
✅ Facebook login successful
✅ User data retrieved
✅ No "Invalid key hash" error
```

**Root cause: Key hash mismatch. Fix bằng cách generate đúng key hash và update Facebook Developer Console.** 🔑
