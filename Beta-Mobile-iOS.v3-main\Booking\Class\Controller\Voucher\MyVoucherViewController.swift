//
//  MyVoucherViewController.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 8/3/19.
//  Copyright © 2019 ddkc. All rights reserved.
//

import UIKit

class MyVoucherViewController: BaseViewController {

    @IBOutlet weak var tableView: UITableView!
    @IBOutlet weak var emptyView: UIView!
    @IBOutlet weak var emptyTitleLabel: UILabel!
    @IBOutlet weak var emptyDetailLabel: UILabel!
    @IBOutlet weak var freeButton: GradientButton!

    private var items: [VoucherModel] = []
    override func viewDidLoad() {
        super.viewDidLoad()

        localizableTitle = "your_voucher"

        tableView.registerCell(id: MyVoucherTableViewCell.id)
        tableView.separatorStyle = .none
        tableView.contentInset = UIEdgeInsets(top: 0, left: 0, bottom: 70, right: 0)

        let addButton = UIBarButtonItem(barButtonSystemItem: .add, target: self, action: #selector(addVoucher))
        let historyButton = UIBarButtonItem(image: UIImage(named: "ic_history_used"), style: .plain, target: self, action: #selector(historyVoucher))
        navigationItem.rightBarButtonItems = [historyButton, addButton]
        updateFreeVoucher()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        updateFreeVoucher()
        getVouchers()
    }

    private func updateFreeVoucher() {
        emptyTitleLabel.text = "voucher_empty_title".localized.uppercased()
        emptyDetailLabel.text = "voucher_empty_detail".localized
        freeButton.setTitle(freeButton.title(for: .normal)?.uppercased(), for: .normal)
    }

    private func getVouchers(){
      if !Global.shared.isLogined {
        self.emptyView.isHidden = false
        self.tableView.isHidden = true

        freeButton.isHidden = true
        emptyTitleLabel.text = "not_login_yet".localized.uppercased()
        emptyDetailLabel.text = "must_login_to_get_voucher".localized

        return
      }
        self.showLoading()
        VoucherProvider.rx.request(.getVoucher).mapObject(DDKCResponse<VoucherModel>.self)
            .subscribe(onNext: {[weak self] response in
                guard let `self` = self else {return}
                self.dismissLoading()
                guard response.isSuccess() else{
                    return
                }

                self.items = response.ListObject?.sorted(by: {$0.getDateStatus() > $1.getDateStatus()}) ?? []
                self.emptyView.isHidden = !self.items.isEmpty
                self.tableView.isHidden = !self.emptyView.isHidden
              self.updateFreeVoucher()
                self.tableView.reloadData()
            }).disposed(by: disposeBag)
    }

    @objc private func addVoucher() {
        let newVC = AddVoucherViewController()
        newVC.hidesBottomBarWhenPushed = true
        self.navigationController?.pushViewController(newVC, animated: true)
    }

    @objc private func historyVoucher() {
        let historyVC = HistoryVoucherViewController()
        historyVC.hidesBottomBarWhenPushed = true
        self.navigationController?.pushViewController(historyVC, animated: true)
    }

    private func gotoDonate(_ index: IndexPath) {
        let donateVC = DonateVoucherViewController()
        donateVC.hidesBottomBarWhenPushed = true
        donateVC.voucher = items[index.row]
        self.navigationController?.pushViewController(donateVC, animated: true)
    }

    private func gotoUse(_ index: IndexPath) {
        let useVC = UseVoucherViewController()
        useVC.voucher = items[index.row]
        self.navigationController?.pushViewController(useVC, animated: true)
    }

    private func gotoDetail(_ voucher: VoucherModel) {
        guard let id = voucher.StorylineId else {
            return
        }
        let vc = UIStoryboard.home[.newsDetail] as! NewsDetailViewController
        vc.type = NewType.voucherFromDirectLink(id, true)
        vc.hidesBottomBarWhenPushed = true
        vc.isFromRouter = true
        show(vc)
    }
    
    @IBAction func freeVoucherTapped(_ sender: Any) {
        let freeVoucherVC = FreeVoucherViewController()
        freeVoucherVC.hidesBottomBarWhenPushed = true
        self.navigationController?.pushViewController(freeVoucherVC, animated: true)
    }

}

extension MyVoucherViewController: UITableViewDelegate, UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return items.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let cell = tableView.dequeueReusableCell(withIdentifier: MyVoucherTableViewCell.id) as? MyVoucherTableViewCell else {
            return MyVoucherTableViewCell()
        }

        cell.configure(items[indexPath.row], index: indexPath)

        cell.useHandler = { [weak self] index in
            self?.gotoUse(index)
        }

        cell.donateHandler = { [weak self] index in
            self?.gotoDonate(index)
        }
        return cell
    }

    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        if items[indexPath.row].Description == nil {
            return 200
        }
        return 234
    }

    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        gotoDetail(items[indexPath.row])
    }
}
