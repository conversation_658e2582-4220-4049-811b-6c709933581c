package vn.zenity.betacineplex.view.event

import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.model.Event
import vn.zenity.betacineplex.model.NewsModel

/**
 * Created by Zenity.
 */

interface ListEventContractor {
    interface View : IBaseView {
        fun showListEvents(listEvents: List<NewsModel>)
        fun openEvent(event: NewsModel)
    }

    interface Presenter : IBasePresenter<View> {
        fun getListEvent(id: String, page: Int)
    }
}
