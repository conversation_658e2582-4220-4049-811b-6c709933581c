<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14109" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14088"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="SourceSansPro-Regular.ttf">
            <string>SourceSansPro-Regular</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="default" indentationWidth="10" rowHeight="156" id="9K2-2A-5BR" customClass="SettingTableCell" customModule="Booking_dev" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="375" height="156"/>
            <autoresizingMask key="autoresizingMask"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="9K2-2A-5BR" id="hYh-hy-4Gt">
                <rect key="frame" x="0.0" y="0.0" width="375" height="155.5"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="5q8-sd-0Qn" customClass="RoundView" customModule="Booking_dev" customModuleProvider="target">
                        <rect key="frame" x="8" y="3" width="359" height="149.5"/>
                        <subviews>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="4TN-XM-ow2">
                                <rect key="frame" x="12" y="15" width="252" height="119.5"/>
                                <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                <color key="textColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="251" text="Label" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="klo-db-HIy">
                                <rect key="frame" x="276" y="65" width="37" height="20.5"/>
                                <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                <color key="textColor" red="0.01176470588" green="0.34901960780000002" blue="0.61568627450000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <imageView userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_arrow_right" translatesAutoresizingMaskIntoConstraints="NO" id="eHQ-b7-pbO">
                                <rect key="frame" x="323" y="62" width="26" height="26"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="26" id="8sr-3J-MHh"/>
                                    <constraint firstAttribute="height" constant="26" id="DtF-k5-kLH"/>
                                </constraints>
                            </imageView>
                        </subviews>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="eHQ-b7-pbO" firstAttribute="centerY" secondItem="5q8-sd-0Qn" secondAttribute="centerY" id="2N5-dK-6cy"/>
                            <constraint firstAttribute="bottom" secondItem="4TN-XM-ow2" secondAttribute="bottom" constant="15" id="2Pb-ae-pyR"/>
                            <constraint firstAttribute="trailing" secondItem="eHQ-b7-pbO" secondAttribute="trailing" constant="10" id="5wJ-gR-Ctm"/>
                            <constraint firstItem="klo-db-HIy" firstAttribute="leading" secondItem="4TN-XM-ow2" secondAttribute="trailing" constant="12" id="cl2-Vi-hNn"/>
                            <constraint firstItem="4TN-XM-ow2" firstAttribute="top" secondItem="5q8-sd-0Qn" secondAttribute="top" constant="15" id="kKC-Bp-uMH"/>
                            <constraint firstItem="4TN-XM-ow2" firstAttribute="leading" secondItem="5q8-sd-0Qn" secondAttribute="leading" constant="12" id="mRX-ZY-mb8"/>
                            <constraint firstItem="klo-db-HIy" firstAttribute="centerY" secondItem="5q8-sd-0Qn" secondAttribute="centerY" id="urm-1p-xuu"/>
                            <constraint firstItem="eHQ-b7-pbO" firstAttribute="leading" secondItem="klo-db-HIy" secondAttribute="trailing" constant="10" id="xdM-G5-Edw"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                <real key="value" value="2"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </view>
                </subviews>
                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                <constraints>
                    <constraint firstItem="5q8-sd-0Qn" firstAttribute="leading" secondItem="hYh-hy-4Gt" secondAttribute="leading" constant="8" id="Vz6-Bg-Bnu"/>
                    <constraint firstAttribute="bottom" secondItem="5q8-sd-0Qn" secondAttribute="bottom" constant="3" id="b5V-dw-8ta"/>
                    <constraint firstAttribute="trailing" secondItem="5q8-sd-0Qn" secondAttribute="trailing" constant="8" id="oR4-uA-UhF"/>
                    <constraint firstItem="5q8-sd-0Qn" firstAttribute="top" secondItem="hYh-hy-4Gt" secondAttribute="top" constant="3" id="qNw-4p-8cf"/>
                </constraints>
            </tableViewCellContentView>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <connections>
                <outlet property="lbContent" destination="klo-db-HIy" id="rqY-dS-GCm"/>
                <outlet property="lbTitle" destination="4TN-XM-ow2" id="xLC-av-whl"/>
            </connections>
            <point key="canvasLocation" x="-32.5" y="163"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="ic_arrow_right" width="26" height="26"/>
    </resources>
</document>
