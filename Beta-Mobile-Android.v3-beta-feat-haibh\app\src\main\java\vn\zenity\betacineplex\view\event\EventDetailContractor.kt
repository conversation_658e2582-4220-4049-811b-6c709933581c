package vn.zenity.betacineplex.view.event

import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.model.NewsModel
import vn.zenity.betacineplex.model.NotificationDetail

/**
 * Created by Zenity.
 */

interface EventDetailContractor {
    interface View : IBaseView {
        fun showEventDetail(event: NewsModel)
        fun showNotification(noti: NotificationDetail)
    }

    interface Presenter : IBasePresenter<View> {
        fun getEventDetails(id: String)
        fun getNotificationDetails(id: Int)
        fun readNotification(id: Int)
    }
}
