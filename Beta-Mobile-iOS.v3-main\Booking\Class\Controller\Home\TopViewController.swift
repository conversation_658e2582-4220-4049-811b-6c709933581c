//
//  TopViewController.swift
//  Booking-dev
//
//  Created by <PERSON><PERSON> on 4/14/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit

class TopViewController: BaseViewController {

    override func viewDidLoad() {
        super.viewDidLoad()

        let vc = UIStoryboard.home[.homeNav]
        vc.view.frame = view.bounds
        view.addSubview(vc.view)
        vc.willMove(toParentViewController: self)
        addChildViewController(vc)
        vc.didMove(toParentViewController: self)
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
    }

    override func didReceiveMemoryWarning() {
        super.didReceiveMemoryWarning()
        // Dispose of any resources that can be recreated.
    }

}
