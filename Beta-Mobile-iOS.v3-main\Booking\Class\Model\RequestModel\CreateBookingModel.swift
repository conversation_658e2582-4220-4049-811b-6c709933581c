//
//  CreateBookingModel.swift
//  Booking-dev
//
//  Created by <PERSON><PERSON> on 6/1/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation

public class CreateBookingModel: BaseRequestModel {
    var showId: String
    var list: [SeatBookingModel]
    var expiredTime: String

    init(showId: String, list: [SeatBookingModel], expiredTime: String) {
        self.showId = showId
        self.list = list
        self.expiredTime = expiredTime        
    }

    override func toJSON() -> [String : Any] {
        return ["ShowId": showId,
                "Seats": list.map{ $0.toJSON() },
                "CountDown": expiredTime]
    }
}
