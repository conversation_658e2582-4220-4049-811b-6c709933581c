package vn.zenity.betacineplex.app

import android.content.Context
import android.content.Intent
import android.content.res.Configuration
import androidx.multidex.MultiDex
import androidx.multidex.MultiDexApplication
import androidx.appcompat.app.AppCompatDelegate
import com.google.android.gms.maps.MapsInitializer
import com.google.firebase.messaging.FirebaseMessaging
import vn.zenity.betacineplex.BuildConfig
import vn.zenity.betacineplex.Manager.Network.APIClient
import vn.zenity.betacineplex.Manager.local.BetaDB
import vn.zenity.betacineplex.global.Constant
import vn.zenity.betacineplex.global.Global
import vn.zenity.betacineplex.global.Tracking
import vn.zenity.betacineplex.helper.extension.*
import vn.zenity.betacineplex.helper.thirtypart.LocaleHelper
import vn.zenity.betacineplex.view.HomeActivity
import vn.zenity.betacineplex.view.auth.LoginActivity
import java.util.*
import kotlin.system.exitProcess

/**
 * Created by vinh on 4/2/18.
 */
class App : MultiDexApplication() {

    override fun onCreate() {
        super.onCreate()
        instance = this
        AppCompatDelegate.setCompatVectorFromResourcesEnabled(true)
        MapsInitializer.initialize(this)
        Tracking.share().setToken(this, BuildConfig.MIXPANEL_TOKEN)
        Global.share().user = BetaDB.getInstance().userDao().getCurrentUser()
        if (Global.share().isLogin && !PreferencesHelper.shared.getBooleanValue(Constant.Key.isRegisterToken, false)) {
            registerFCMDevice()
        }
    }

    fun changeLanguage(lang: String = Constant.Lang.vi) {
        val locale = Locale(lang)
        Locale.setDefault(locale)
        val config = Configuration()
        config.setLocale(locale)
        baseContext.resources.updateConfiguration(config, baseContext.resources.displayMetrics)
        instance.baseContext.resources.updateConfiguration(config, baseContext.resources.displayMetrics)
        instance.resources.updateConfiguration(config, instance.resources.displayMetrics)
        PreferencesHelper.shared.putValue(Constant.Key.language, lang)
    }

    fun getCurrentLang(): String {
//        return PreferencesHelper.shared.getStringValue(Constant.Key.language, Locale.getDefault().language) ?: Locale.getDefault().language
        return LocaleHelper.getLanguage(this)
    }

    fun isLangVi(): Boolean {
        return getCurrentLang() != "en"
    }

    fun restartApp() {
        val intent = Intent(this, HomeActivity::class.java)
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK)
        startActivity(intent)
        exitProcess(0)
    }

    fun updateUserInfo(accountId: String){
        val disposable = APIClient.shared.accountAPI.getProfile(accountId).applyOn().subscribe(
                { responseProfile ->
                    if (responseProfile.isSuccess) {
                        responseProfile.Data?.Token = Global.share().token
                        Global.share().user = responseProfile.Data
                        App.shared().registerFCMDevice()
                    }
                }, {
        })
    }

    fun updateTopInfo(){
        if (!Global.share().isLogin) return
        val disposable = APIClient.shared.accountAPI.getTopInfo().applyOn().subscribe(
                { responseProfile ->
                    if (responseProfile.isSuccess) {
                        val user = Global.share().user ?: return@subscribe
                        user.AccountId = responseProfile.Data?.AccountId
                        user.FullName = responseProfile.Data?.FullName
                        user.Picture = responseProfile.Data?.Picture
                        user.QuantityOfVoucher = responseProfile.Data?.QuantityOfVoucher ?: user.QuantityOfVoucher
                        user.AvailablePoint = responseProfile.Data?.AvailablePoint ?: user.AvailablePoint
                        Global.share().user = user
                        App.shared().registerFCMDevice()
                    }
                }, {
        })
    }

    fun openLogin() {
        val intent = Intent(this, LoginActivity::class.java)
        intent.putExtra(Constant.Key.isReLogin, true)
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK)
        startActivity(intent)
        System.exit(0)
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        changeLanguage(getCurrentLang())
    }

    override fun attachBaseContext(base: Context?) {
        super.attachBaseContext(LocaleHelper.onAttach(base, LocaleHelper.getLanguage(base)))
        MultiDex.install(this)
    }

    fun registerFCMDevice() {
        FirebaseMessaging.getInstance().token.addOnSuccessListener { token ->
            val gcmToken = token
            val deviceId = DeviceHelper.shared.deviceId()
            if (gcmToken.isNotEmpty()) {
                if (!Global.share().isLogin && PreferencesHelper.shared.getBooleanValue(Constant.Key.isRegisterToken, false)) {
                    val dis = APIClient.shared.accountAPI.unregisterFCMToken(mapOf("DeviceId" to deviceId,
                            "AccountId" to (Global.share().user?.AccountId ?: ""),
                            "DeviceToken" to token,
                            "DeviceType" to "android")).applyOn().subscribe({
                        PreferencesHelper.shared.putValue(Constant.Key.isRegisterToken, false)
                    }, {
                    })
                    return@addOnSuccessListener
                }
                logD("GCM TOKEN $token")
                val dis = APIClient.shared.accountAPI.registerFCMToken(mapOf("DeviceId" to deviceId,
                        "AccountId" to (Global.share().user?.AccountId ?: ""),
                        "DeviceToken" to token,
                        "DeviceType" to "android")).applyOn().subscribe({
                    if (it.isSuccess) {
                        PreferencesHelper.shared.putValue(Constant.Key.isRegisterToken, true)
                    }
                }, {

                })
            }
        }
    }

    companion object {

        private lateinit var instance: App

        fun shared(): App {
            return instance
        }
    }
}