<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14490.70" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14490.49"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="Oswald-Regular.ttf">
            <string>Oswald-Regular</string>
        </array>
        <array key="SourceSansPro-Bold.ttf">
            <string>SourceSansPro-Bold</string>
        </array>
        <array key="SourceSansPro-Regular.ttf">
            <string>SourceSansPro-Regular</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" rowHeight="93" id="KGk-i7-Jjw" customClass="DonateVoucherTableViewCell" customModule="Booking_dev" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="320" height="93"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="92.5"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="MNb-P4-GwC" customClass="RoundView" customModule="Booking_dev" customModuleProvider="target">
                        <rect key="frame" x="8" y="4" width="304" height="84.5"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_account" translatesAutoresizingMaskIntoConstraints="NO" id="LCk-N2-YpD" customClass="RoundImageView" customModule="Booking_dev" customModuleProvider="target">
                                <rect key="frame" x="16" y="22.5" width="40" height="40"/>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="40" id="64r-1w-2G3"/>
                                    <constraint firstAttribute="width" secondItem="LCk-N2-YpD" secondAttribute="height" id="Z55-pX-8vU"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                        <real key="value" value="20"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </imageView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Fatima Delgadillo" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="5T7-me-ATe">
                                <rect key="frame" x="64" y="22.5" width="146" height="23"/>
                                <fontDescription key="fontDescription" name="SourceSansPro-Bold" family="Source Sans Pro" pointSize="18"/>
                                <color key="textColor" red="0.070588235289999995" green="0.**********" blue="0.15686274510000001" alpha="1" colorSpace="calibratedRGB"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="zOR-1p-Z9P" customClass="RoundButton" customModule="Booking_dev" customModuleProvider="target">
                                <rect key="frame" x="218" y="22.5" width="70" height="40"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="40" id="ahW-PN-ggg"/>
                                    <constraint firstAttribute="width" constant="70" id="lef-t9-1sO"/>
                                </constraints>
                                <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="16"/>
                                <state key="normal" title="TẶNG">
                                    <color key="titleColor" red="0.01176470588" green="0.34901960780000002" blue="0.61568627450000002" alpha="1" colorSpace="calibratedRGB"/>
                                </state>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                        <color key="value" red="0.01176470588" green="0.34901960780000002" blue="0.61568627450000002" alpha="1" colorSpace="calibratedRGB"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                        <real key="value" value="1"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                        <real key="value" value="3"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                                <connections>
                                    <action selector="didTapDonate:" destination="KGk-i7-Jjw" eventType="touchUpInside" id="n0S-zU-EtJ"/>
                                </connections>
                            </button>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="<EMAIL>" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="oVi-Fm-tjX">
                                <rect key="frame" x="64" y="44.5" width="146" height="18"/>
                                <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="14"/>
                                <color key="textColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="calibratedRGB"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="zOR-1p-Z9P" firstAttribute="leading" secondItem="oVi-Fm-tjX" secondAttribute="trailing" constant="8" id="1ot-ls-pK3"/>
                            <constraint firstItem="oVi-Fm-tjX" firstAttribute="leading" secondItem="LCk-N2-YpD" secondAttribute="trailing" constant="8" id="Eh7-Oh-UYR"/>
                            <constraint firstItem="5T7-me-ATe" firstAttribute="top" secondItem="LCk-N2-YpD" secondAttribute="top" id="FfY-V0-P7B"/>
                            <constraint firstAttribute="trailing" secondItem="zOR-1p-Z9P" secondAttribute="trailing" constant="16" id="G4w-6p-1km"/>
                            <constraint firstItem="5T7-me-ATe" firstAttribute="leading" secondItem="LCk-N2-YpD" secondAttribute="trailing" constant="8" id="Pch-pi-NHO"/>
                            <constraint firstItem="LCk-N2-YpD" firstAttribute="centerY" secondItem="MNb-P4-GwC" secondAttribute="centerY" id="Sd8-3f-xqI"/>
                            <constraint firstItem="zOR-1p-Z9P" firstAttribute="leading" secondItem="5T7-me-ATe" secondAttribute="trailing" constant="8" id="Uh3-R0-X3L"/>
                            <constraint firstItem="zOR-1p-Z9P" firstAttribute="centerY" secondItem="MNb-P4-GwC" secondAttribute="centerY" id="ZUJ-Qf-WO0"/>
                            <constraint firstItem="oVi-Fm-tjX" firstAttribute="bottom" secondItem="LCk-N2-YpD" secondAttribute="bottom" id="dTp-zN-2kU"/>
                            <constraint firstItem="LCk-N2-YpD" firstAttribute="leading" secondItem="MNb-P4-GwC" secondAttribute="leading" constant="16" id="zqQ-AD-flu"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                <real key="value" value="5"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </view>
                </subviews>
                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                <constraints>
                    <constraint firstItem="MNb-P4-GwC" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="8" id="5Ro-vw-lEF"/>
                    <constraint firstAttribute="trailing" secondItem="MNb-P4-GwC" secondAttribute="trailing" constant="8" id="bIT-Hf-8KE"/>
                    <constraint firstAttribute="bottom" secondItem="MNb-P4-GwC" secondAttribute="bottom" constant="4" id="rrA-TE-zYq"/>
                    <constraint firstItem="MNb-P4-GwC" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" constant="4" id="yvx-3y-6dB"/>
                </constraints>
            </tableViewCellContentView>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <viewLayoutGuide key="safeArea" id="njF-e1-oar"/>
            <connections>
                <outlet property="avatarImageView" destination="LCk-N2-YpD" id="g4P-iS-T7M"/>
                <outlet property="donateButton" destination="zOR-1p-Z9P" id="kZn-GV-sdT"/>
                <outlet property="emailLabel" destination="oVi-Fm-tjX" id="sHW-Y1-Oe7"/>
                <outlet property="nameLabel" destination="5T7-me-ATe" id="saZ-cf-mAN"/>
            </connections>
            <point key="canvasLocation" x="131.*************" y="169.**************"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="ic_account" width="23" height="23"/>
    </resources>
</document>
