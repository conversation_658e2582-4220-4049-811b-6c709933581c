//
//  Button.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 8/4/19.
//  Copyright © 2019 ddkc. All rights reserved.
//

import Foundation
import UIKit

public enum UIButtonBorderSide {
    case Top, Bottom, Left, Right
}

extension UIButton {

    public func addBorder(side: UIButtonBorderSide, color: UIColor, width: CGFloat) {
        let border = CALayer()
        border.backgroundColor = color.cgColor

        switch side {
        case .Top:
            border.frame = CGRect(x: 0, y: 0, width: frame.size.width, height: width)
        case .Bottom:
            border.frame = CGRect(x: 0, y: self.frame.size.height - width, width: self.frame.size.width, height: width)
        case .Left:
            border.frame = CGRect(x: 0, y: 0, width: width, height: self.frame.size.height)
        case .Right:
            border.frame = CGRect(x: self.frame.size.width - width, y: 0, width: width, height: self.frame.size.height)
        }

        self.layer.addSublayer(border)
    }

    func setLocalizableImage(_ name: String) {
        var _name = name
        if Utils.shared.isEng() {
            _name = _name + "-1"
        }
        self.setImage(UIImage(named: _name), for: .normal)
    }
}
