//
//  ColumnLayout.swift
//  Booking-dev
//
//  Created by <PERSON><PERSON> on 5/27/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit

protocol ColumnLayoutDelegate: class {
    func collectionView(_ collectionView: UICollectionView, widthAt indexPath: IndexPath) -> CGFloat
    func collectionView(_ collectionView: UICollectionView, heightAt column: Int) -> CGFloat
//    func collectionView(_ collectionView: UICollectionView, numberOfColumnAt indexPath: IndexPath) -> Int
//    func collectionView(_ collectionView: UICollectionView, numberOfRowAt indexPath: IndexPath) -> Int
}

class ColumnLayout: UICollectionViewLayout {
    enum Element: String {
        case header
        case menu
        case sectionHeader
        case sectionFooter
        case cell

        var id: String {
            return self.rawValue
        }

        var kind: String {
            return "Kind\(self.rawValue.capitalized)"
        }
    }

    weak var delegate: ColumnLayoutDelegate!

    private var cache = [Element: [IndexPath: UICollectionViewLayoutAttributes]]()
    private var visibleLayoutAttributes = [UICollectionViewLayoutAttributes]()
    var contentHeight: CGFloat = 0

    fileprivate var contentWidth: CGFloat = 0
    fileprivate var cellPadding: CGFloat = 4

    override var collectionViewContentSize: CGSize {
        return CGSize(width: contentWidth, height: contentHeight)
    }

    override func prepare() {
        guard cache[.cell]?.isEmpty != false, let collectionView = collectionView else {
            return
        }

        let columnCount = collectionView.numberOfSections
        let listWidths = (0..<columnCount).map { x in
            (0..<collectionView.numberOfItems(inSection: x)).map { y in
                IndexPath(row: y, section: x)
                }.map { delegate.collectionView(collectionView, widthAt: $0) }.reduce(0, { $0 + cellPadding + $1 })
            }
        contentWidth = (listWidths.max() ?? 0) + 80 // padding

        var yOffset = [CGFloat](repeating: 0, count: columnCount)

        var cellAttributes = [IndexPath: UICollectionViewLayoutAttributes]()
        var headerAttributes = [IndexPath: UICollectionViewLayoutAttributes]()
        for col in (0..<columnCount) {
            let height = delegate.collectionView(collectionView, heightAt: col) + cellPadding * 2
            let rowCount = collectionView.numberOfItems(inSection: col)
            var offsetX = (contentWidth - listWidths[col]) / 2 // center align
//            var offsetX: CGFloat = 40 // left align
            if col > 0 {
                yOffset[col] = yOffset[col - 1] + height
            }

            for row in (0..<rowCount) {
                let indexPath = IndexPath(row: row, section: col)
                let width = delegate.collectionView(collectionView, widthAt: indexPath)
                let frame = CGRect(x: offsetX, y: yOffset[col], width: width, height: height)

                let attributes = UICollectionViewLayoutAttributes(forCellWith: indexPath)
                attributes.frame = frame
                cellAttributes.updateValue(attributes, forKey: indexPath)
                offsetX += width + cellPadding
                contentHeight = max(contentHeight, frame.maxY)
            }
            let headerAttr = UICollectionViewLayoutAttributes(forSupplementaryViewOfKind: UICollectionElementKindSectionHeader, with: IndexPath(row: 0, section: col))
            headerAttr.frame = CGRect(x: 0, y: yOffset[col], width: 30, height: height)
            headerAttributes.updateValue(headerAttr, forKey: IndexPath(row: 0, section: col))
        }
        if !cellAttributes.isEmpty {
            cache.updateValue(cellAttributes, forKey: .cell)
        }
        headerAttributes.forEach { $0.value.zIndex = 1000 }
        cache.updateValue(headerAttributes, forKey: .sectionHeader)
        contentHeight += 40
    }

    override func layoutAttributesForElements(in rect: CGRect) -> [UICollectionViewLayoutAttributes]? {

//        var visibleLayoutAttributes = [UICollectionViewLayoutAttributes]()
        visibleLayoutAttributes.removeAll(keepingCapacity: true)

        // Loop through the cache and look for items in the rect
        for (type, elementInfos) in cache {
            for (_, attributes) in elementInfos {
                if type == .sectionHeader {
                    var frame = attributes.frame
                    frame.origin.x = collectionView!.contentOffset.x
                    attributes.frame = frame
                }
                if attributes.frame.intersects(rect) {
                    visibleLayoutAttributes.append(attributes)
                }
            }
        }
        return visibleLayoutAttributes
    }

    override func layoutAttributesForItem(at indexPath: IndexPath) -> UICollectionViewLayoutAttributes? {
        return cache[.cell]?[indexPath]
    }

    override func layoutAttributesForSupplementaryView(ofKind elementKind: String, at indexPath: IndexPath) -> UICollectionViewLayoutAttributes? {
        return cache[.sectionHeader]?[indexPath]
    }

    override func shouldInvalidateLayout(forBoundsChange newBounds: CGRect) -> Bool {
        return false
    }
}
