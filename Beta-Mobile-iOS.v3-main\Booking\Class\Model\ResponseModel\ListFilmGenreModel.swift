//
//  ListFilmGenreModel.swift
//  Booking
//
//  Created by <PERSON><PERSON>u on 4/3/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation
import ObjectMapper

class ListFilmGenreModel : Mappable {
    var GenreId : String?
    var Code : String?
    var Name : String?
    var Name_F : String?
    var Order : Int?


    


    required init?(map: Map) {

    }

    func mapping(map: Map) {
        GenreId              <- map["GenreId"]
        Code                 <- map["Code"]
        Name                 <- map["Name"]
        Name_F               <- map["Name_F"]
        Order                <- map["Order"]
    }
}
