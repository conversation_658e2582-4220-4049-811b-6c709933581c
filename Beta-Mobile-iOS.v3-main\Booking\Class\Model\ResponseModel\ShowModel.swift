//
//  ShowModel.swift
//  Booking
//
//  Created by <PERSON><PERSON>u on 4/5/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation
import ObjectMapper
import SwiftDate

class ShowModel : Mappable {
    var startTime : String?
    var seatSolded : Int?
    var showId : String?
    var timeToLock : Int?
    var totalSeat : Int?

    required init?(map: Map) {

    }

    func mapping(map: Map) {

        startTime <- map["StartTime"]
        seatSolded <- map["SeatSolded"]
        showId <- map["ShowId"]
        timeToLock <- map["TimeToLock"]
        totalSeat <- map["TotalSeat"]
    }


    func getStartDate() -> Date? {
        guard let dateStr = self.startTime else { return nil }
        return dateStr.toDate("yyyy-MM-dd'T'HH:mm:ss")
    }

    func getTimeLockDate() -> Date? {
        guard let date = getStartDate(), let timeLock = self.timeToLock else {
            return nil
        }
        return date - timeLock.minutes
    }

    var emptySeat: Int {
        return (totalSeat ?? 0) - (seatSolded ?? 0)
    }
}
