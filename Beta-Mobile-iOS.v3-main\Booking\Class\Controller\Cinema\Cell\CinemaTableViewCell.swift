//
//  CinemaTableViewCell.swift
//  Booking-dev
//
//  Created by <PERSON><PERSON> on 4/9/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit

class CinemaTableViewCell: UITableViewCell {
    @IBOutlet weak var lbTitle: UILabel!
    @IBOutlet weak var bottomLine: UIView!

    override func awakeFromNib() {
        super.awakeFromNib()
        // Initialization code
    }

    override func updateViewWithItem(_ item: TableItem, indexPath: IndexPath) {
        bottomLine.isHidden = false
        lbTitle.text = item.title
    }

    override func updateViewAtEndOfSection(_ item: TableItem, section: Int) {
        bottomLine.isHidden = true
    }
}
