//
//  TransactionHistoryModel.swift
//  Booking-dev
//
//  Created by <PERSON><PERSON> Vu on 5/29/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation
import ObjectMapper
class TransactionHistoryModel : Mappable {
    public var Invoice_Id : String?
    public var FilmName : String?
    public var CinemaName : String?
    public var CinemaId : String?
    public var ShowId : String?
    public var DateShow : String?
    public var ShowTime : String?
    public var DateEntered : String?
    public var QuantityPoint : Int?
    public var DateExpiredPoint : String?
    public var AccountId : String?
    public var SalesChannelId : String?
    public var SalesChannelCode : String?
    public var CardId : String?
    public var CardNumber : String?
    public var TotalPayment : Int?
    public var AirpayLandingUrl: URL?


    required init?(map: Map) {

    }

    func mapping(map: Map) {
        Invoice_Id           <- map["Invoice_Id"]
        FilmName             <- map["FilmName"]
        CinemaName           <- map["CinemaName"]
        CinemaId             <- map["CinemaId"]
        ShowId               <- map["ShowId"]
        DateShow             <- map["DateShow"]
        ShowTime             <- map["ShowTime"]
        DateEntered          <- map["DateEntered"]
        QuantityPoint        <- map["QuantityPoint"]
        AccountId            <- map["AccountId"]
        SalesChannelId       <- map["SalesChannelId"]
        SalesChannelCode     <- map["SalesChannelCode"]
        CardId               <- map["CardId"]
        CardNumber           <- map["CardNumber"]
        TotalPayment         <- map["TotalPayment"]
        DateExpiredPoint     <- map["DateExpiredPoint"]
        AirpayLandingUrl <- (map["AirpayLandingUrl"], URLTransform())
    }
}
