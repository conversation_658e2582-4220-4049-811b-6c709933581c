
import Foundation

// MARK: - ComboListModelElement
struct ComboListModelElement: Codable {
    let itemInCombos: [ItemInCombo]?
    let comboPackageID: String?
    let combo: ComboData?
    let totalPriceAfterVAT, totalPriceInCombo, totalPriceBeforeVAT: Int?
    let isTicketPackage: Bool?

    enum CodingKeys: String, CodingKey {
        case itemInCombos = "ItemInCombos"
        case comboPackageID = "ComboPackageId"
        case combo = "Combo"
        case totalPriceAfterVAT = "TotalPriceAfterVAT"
        case totalPriceInCombo = "TotalPriceInCombo"
        case totalPriceBeforeVAT = "TotalPriceBeforeVAT"
        case isTicketPackage = "IsTicketPackage"
    }
}

// MARK: - Combo
struct ComboData: Codable {
    let id, code, name: String?
    let shortName: JSONNull?
    let startDate, endDate: String?
    let barCode: JSONNull?
    let applicationID: String?
    let comboDescription: JSONNull?
    let status: Bool?
    let comboPacketType, order: Int?
    let isHot: Bool?
    let priceBeforeVAT: Int?
    let discountPercent, amountBeforeVAT, vatPercent: JSONNull?
    let priceAfterVAT: Int?
    let totalAmountAfterVAT: JSONNull?
    let createdOnDate, createdByUser, lastModifiedOnDate, lastModifiedByUser: String?
    let groupID: String?
    let version: Int?

    enum CodingKeys: String, CodingKey {
        case id = "Id"
        case code = "Code"
        case name = "Name"
        case shortName = "ShortName"
        case startDate = "StartDate"
        case endDate = "EndDate"
        case barCode = "BarCode"
        case applicationID = "ApplicationId"
        case comboDescription = "Description"
        case status = "Status"
        case comboPacketType = "ComboPacketType"
        case order = "Order"
        case isHot = "IsHot"
        case priceBeforeVAT = "PriceBeforeVAT"
        case discountPercent = "DiscountPercent"
        case amountBeforeVAT = "AmountBeforeVAT"
        case vatPercent = "VATPercent"
        case priceAfterVAT = "PriceAfterVAT"
        case totalAmountAfterVAT = "TotalAmountAfterVAT"
        case createdOnDate = "CreatedOnDate"
        case createdByUser = "CreatedByUser"
        case lastModifiedOnDate = "LastModifiedOnDate"
        case lastModifiedByUser = "LastModifiedByUser"
        case groupID = "GroupId"
        case version = "Version"
    }
}

// MARK: - ItemInCombo
struct ItemInCombo: Codable {
    let id, comboPacketID, itemID: String?
    let quantity: Int?
    let isItem: Bool?
    let itemInComboDescription: JSONNull?
    let priceInCombo, order, priceBeforeVAT: Int?
    let discountPercent, amountBeforeVAT: JSONNull?
    let vatPercent, priceAfterVAT: Int?
    let totalAmountAfterVAT: JSONNull?
    let isTicketType: Bool?

    enum CodingKeys: String, CodingKey {
        case id = "Id"
        case comboPacketID = "Combo_Packet_Id"
        case itemID = "Item_Id"
        case quantity = "Quantity"
        case isItem = "IsItem"
        case itemInComboDescription = "Description"
        case priceInCombo = "PriceInCombo"
        case order = "Order"
        case priceBeforeVAT = "PriceBeforeVAT"
        case discountPercent = "DiscountPercent"
        case amountBeforeVAT = "AmountBeforeVAT"
        case vatPercent = "VATPercent"
        case priceAfterVAT = "PriceAfterVAT"
        case totalAmountAfterVAT = "TotalAmountAfterVAT"
        case isTicketType = "IsTicketType"
    }
}

typealias ComboListModel = [ComboListModelElement]

// MARK: - Encode/decode helpers

class JSONNull: Codable, Hashable {

    public static func == (lhs: JSONNull, rhs: JSONNull) -> Bool {
        return true
    }

    public var hashValue: Int {
        return 0
    }

    public func hash(into hasher: inout Hasher) {
        // No-op
    }

    public init() {}

    public required init(from decoder: Decoder) throws {
        let container = try decoder.singleValueContainer()
        if !container.decodeNil() {
            throw DecodingError.typeMismatch(JSONNull.self, DecodingError.Context(codingPath: decoder.codingPath, debugDescription: "Wrong type for JSONNull"))
        }
    }

    public func encode(to encoder: Encoder) throws {
        var container = encoder.singleValueContainer()
        try container.encodeNil()
    }
}
