<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<title>microsoft.aspnet.signalr.client</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
</head>
<body>
<h1 class="bar"><a href="../../../../microsoft/aspnet/signalr/client/package-summary.html" target="classFrame">microsoft.aspnet.signalr.client</a></h1>
<div class="indexContainer">
<h2 title="Interfaces">Interfaces</h2>
<ul title="Interfaces">
<li><a href="Action.html" title="interface in microsoft.aspnet.signalr.client" target="classFrame"><i>Action</i></a></li>
<li><a href="ConnectionBase.html" title="interface in microsoft.aspnet.signalr.client" target="classFrame"><i>ConnectionBase</i></a></li>
<li><a href="Credentials.html" title="interface in microsoft.aspnet.signalr.client" target="classFrame"><i>Credentials</i></a></li>
<li><a href="ErrorCallback.html" title="interface in microsoft.aspnet.signalr.client" target="classFrame"><i>ErrorCallback</i></a></li>
<li><a href="Logger.html" title="interface in microsoft.aspnet.signalr.client" target="classFrame"><i>Logger</i></a></li>
<li><a href="MessageReceivedHandler.html" title="interface in microsoft.aspnet.signalr.client" target="classFrame"><i>MessageReceivedHandler</i></a></li>
<li><a href="PlatformComponent.html" title="interface in microsoft.aspnet.signalr.client" target="classFrame"><i>PlatformComponent</i></a></li>
<li><a href="StateChangedCallback.html" title="interface in microsoft.aspnet.signalr.client" target="classFrame"><i>StateChangedCallback</i></a></li>
</ul>
<h2 title="Classes">Classes</h2>
<ul title="Classes">
<li><a href="CalendarSerializer.html" title="class in microsoft.aspnet.signalr.client" target="classFrame">CalendarSerializer</a></li>
<li><a href="Connection.html" title="class in microsoft.aspnet.signalr.client" target="classFrame">Connection</a></li>
<li><a href="Constants.html" title="class in microsoft.aspnet.signalr.client" target="classFrame">Constants</a></li>
<li><a href="DateSerializer.html" title="class in microsoft.aspnet.signalr.client" target="classFrame">DateSerializer</a></li>
<li><a href="FutureHelper.html" title="class in microsoft.aspnet.signalr.client" target="classFrame">FutureHelper</a></li>
<li><a href="HeartbeatMonitor.html" title="class in microsoft.aspnet.signalr.client" target="classFrame">HeartbeatMonitor</a></li>
<li><a href="MessageResult.html" title="class in microsoft.aspnet.signalr.client" target="classFrame">MessageResult</a></li>
<li><a href="NullLogger.html" title="class in microsoft.aspnet.signalr.client" target="classFrame">NullLogger</a></li>
<li><a href="Platform.html" title="class in microsoft.aspnet.signalr.client" target="classFrame">Platform</a></li>
<li><a href="SignalRFuture.html" title="class in microsoft.aspnet.signalr.client" target="classFrame">SignalRFuture</a></li>
<li><a href="SimpleEntry.html" title="class in microsoft.aspnet.signalr.client" target="classFrame">SimpleEntry</a></li>
<li><a href="UpdateableCancellableFuture.html" title="class in microsoft.aspnet.signalr.client" target="classFrame">UpdateableCancellableFuture</a></li>
<li><a href="Version.html" title="class in microsoft.aspnet.signalr.client" target="classFrame">Version</a></li>
</ul>
<h2 title="Enums">Enums</h2>
<ul title="Enums">
<li><a href="ConnectionState.html" title="enum in microsoft.aspnet.signalr.client" target="classFrame">ConnectionState</a></li>
<li><a href="LogLevel.html" title="enum in microsoft.aspnet.signalr.client" target="classFrame">LogLevel</a></li>
</ul>
<h2 title="Exceptions">Exceptions</h2>
<ul title="Exceptions">
<li><a href="InvalidProtocolVersionException.html" title="class in microsoft.aspnet.signalr.client" target="classFrame">InvalidProtocolVersionException</a></li>
<li><a href="InvalidStateException.html" title="class in microsoft.aspnet.signalr.client" target="classFrame">InvalidStateException</a></li>
</ul>
</div>
</body>
</html>
