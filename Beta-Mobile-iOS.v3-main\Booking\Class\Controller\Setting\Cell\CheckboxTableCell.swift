//
//  CheckboxTableCell.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 4/12/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit

class CheckboxTableCell: UITableViewCell {
    @IBOutlet weak var lbTitle: UILabel!
    @IBOutlet weak var ivCheck: UIImageView!
    @IBOutlet weak var vBottomLine: UIView!
    @IBOutlet weak var mainView: RoundView!

    fileprivate var isTop: Bool = false
    fileprivate var isBottom: Bool = false

    override func awakeFromNib() {
        super.awakeFromNib()
    }

    override func updateViewWithItem(_ item: TableItem, indexPath: IndexPath) {
        lbTitle.text = item.title
        ivCheck.isHidden = item.accessoryType != .checkmark
        vBottomLine.isHidden = false
    }

    override func updateViewAtTopOfSection(_ item: TableItem, section: Int) {
        mainView.onLayoutSubView = { [weak self] in
            self?.mainView.maskCornerRadius(2, corners: [.topLeft, .topRight])
        }
    }

    override func updateViewAtEndOfSection(_ item: TableItem, section: Int) {
        vBottomLine.isHidden = true

        mainView.onLayoutSubView = { [weak self] in
            self?.mainView.maskCornerRadius(2, corners: [.bottomLeft, .bottomRight])
        }
    }
}
