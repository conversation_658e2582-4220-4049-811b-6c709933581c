//
//  MemberCardViewController.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 3/14/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit

class MemberCardViewController: BaseViewController {
    @IBOutlet var tableView: UITableView!
    @IBOutlet weak var headerView: UIView!


    fileprivate let cellId = "MemberCardCell"
    fileprivate var dataSource = SimpleTableViewDataSource()

    override func viewDidLoad() {
        super.viewDidLoad()

        localizableTitle = "MemberCard.Title"

        // Do any additional setup after loading the view.
        tableView.dataSource = self.dataSource

        updateData()
        
        self.getUserCard()
    }

    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        headerView.autoFitSize()
        tableView.tableHeaderView = headerView
        tableView.allowsSelection = false
    }

    func updateData() {
//        guard let user = Global.shared.user else { return }
//
//        self.dataSource.removeAll()
//        self.dataSource.addRows([TableItem(title: user.CardNumber, content: user.CardNumber, cellId: cellId)])
    }
    
    private func getUserCard(){
        guard let user = Global.shared.user, let userId = user.UserId else {
            return
        }
        self.showLoading()
        AccountProvider.rx.request(.getListCard(userId))
            .mapObject(DDKCResponse<CardModel>.self)
            .subscribe(onNext: { (response) in
                self.dismissLoading()
                self.handlerResponse(response, success: { [weak self] in
                    if let listCard = response.ListObject {
                        self?.dataSource.removeAll()
                        self?.dataSource.addRows(listCard.map { TableItem(data: $0, cellId: self?.cellId ) })
                        self?.tableView.reloadData()
                    }
                })
            }).disposed(by: disposeBag)
        
    }

}
