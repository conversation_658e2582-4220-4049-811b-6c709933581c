//
//  AccountAPI.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 4/4/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation
import Moya


enum Account{
    case register(RegisterModel)
    case getProfileById(String)
    case getProfileByCardNumber(String)
    case updateProfile(String,RegisterModel)
    case getAvaliblePoints(String)
    case getPoints(String)
    case getListCard(String)
    case login(LoginModel)
    case loginFB(String, String)
    case loginApple(String, String, String, String)
    case confirmPass(String, String)
    case changePass(String, String, String)
    case forgotPass(String)
    case uploadAvatar(String, String)
    case getTransactionHistory(String)
    case getTransactionDetails(String, String)
    case getCardClass
    
    case registerDeviceToken(String, String, String)
    case unRegisterDeviceToken(String, String, String)
    case updatePassword(String, String)
    case updateApplePassword(String, String)
    case searchUser(String)

    case appParams
    case intro(String)
    case homeInfo
}

let AccountProvider = MoyaProvider<Account>(plugins: [NetworkLoggerPlugin(verbose: true, responseDataFormatter: nil)])

extension Account: TargetType {
    
    public var baseURL: URL {

        switch self {
        case .intro(let code):
            if let url = URL(string: Config.BaseURL + "?refCode=\(code)") {
                return url
            }
             return URL(string: Config.BaseURL)!
        default:
             return URL(string: Config.BaseURL)!
        }
    }

    public var path: String {
        switch self {
        case .register(_), .getProfileByCardNumber(_):
            return "api/v1/erp/accounts"
        case .getProfileById(let id), .updateProfile(let id, _):
            return "api/v1/erp/accounts/{\(id)}"
        case .getAvaliblePoints(let id):
            return "api/v1/erp/accounts/{\(id)}/availablepoint"
        case .getPoints(let id):
            return "api/v1/erp/accounts/{\(id)}/points"
        case .getListCard(let id):
            return "api/v1/erp/accounts/{\(id)}/cards"
        case .login(_):
            return "api/v1/erp/accounts/login"
        case .loginFB:
            return "api/v1/erp/accounts/login-facebook"
        case .loginApple:
            return "api/v1/erp/accounts/login-apple"
        case .confirmPass(_, _):
            return "api/v1/erp/accounts/confirm-password"
        case .changePass(_, _, _):
            return "api/v1/erp/accounts/change-password"
        case .forgotPass(_):
            return "api/v1/erp/accounts/recovery-password"
        case .uploadAvatar(let id, _):
            return "api/v1/erp/accounts/{\(id)}/avatar"
        case .getTransactionHistory(let id):
            return "api/v1/erp/transaction-history/\(id)"
        case .getTransactionDetails(let userId, let tranId):
            return "api/v2/erp/transaction-history/\(userId)/\(tranId)"
        case .getCardClass:
            return "api/v1/erp/card-class"
        case .registerDeviceToken(_, _, _):
            return "api/v1/erp/notifications/register-device-token"
        case .unRegisterDeviceToken(_, _, _):
            return "api/v1/erp/notifications/unregister-device-token"
        case .updatePassword(_, _):
            return "api/v1/erp/accounts/update-password-facebook"
        case .updateApplePassword(_, _):
            return "api/v1/erp/accounts/update-password-apple"
        case .searchUser:
            return "api/v1/erp/accounts/get-list-account"
        case .appParams:
            return "api/v1/erp/app-params"
        case .intro:
            return "api/v1/erp/accounts/authentication-referral-code"
        case .homeInfo:
            return "api/v1/erp/accounts/info-hompage"
        }
    }
    public var method: Moya.Method {
        switch self {
        case .register(_), .login( _), .confirmPass(_, _), .forgotPass(_), .loginFB(_), .loginApple(_), .registerDeviceToken, .intro:
            return .post
        case .updateProfile(_, _), .changePass(_, _, _), .uploadAvatar(_, _), .unRegisterDeviceToken(_), .updatePassword(_, _), .updateApplePassword(_, _):
            return .put
        default:
            return .get
        }
        
    }
    public var parameters: [String: Any]? {
        switch self {
        case .register(let model):
            return model.toJSON()
        case .login(let model):
            return model.toJSON()
        case .loginFB(let token, let captchaToken):
            return ["Token": token,
                    "ReCaptchaToken": captchaToken]
        case .loginApple(let token, let fullName, let email, let captchaToken):
            return ["Token": token, "Name" : fullName, "Email" : email,
                    "ReCaptchaToken": captchaToken]
        case .updateProfile(_, let model):
            return model.toJSON()
        case .confirmPass(let email, let password):
            return ["UserName": email,
                    "PassWord": password,
                    "DeviceId": UIDevice.current.identifierForVendor?.uuidString ?? ""]
        case .changePass(let email, let password, let oldPassword):
            return ["UserName": email,
                    "OldPassWord": oldPassword,
                    "PassWord": password,
                    "DeviceId": UIDevice.current.identifierForVendor?.uuidString ?? ""]
        case .forgotPass(let email):
            return ["UserName": email,
                    "DeviceId": UIDevice.current.identifierForVendor?.uuidString ?? ""]
        case .uploadAvatar(_, let base64):
            return ["ImageBase64": base64,
                    "DeviceId": UIDevice.current.identifierForVendor?.uuidString ?? "",
                    "Extension": ".jpg"]
        case .getTransactionHistory(_):
            return ["stringFilter":""]
        case .registerDeviceToken(let deviceId, let accountId, let deviceToken), .unRegisterDeviceToken(let deviceId, let accountId, let deviceToken):
            return ["DeviceId": deviceId,
                    "AccountId": accountId,
                    "DeviceToken": deviceToken,
                    "DeviceType": "ios"]
        case .updatePassword(let email, let password):
            return ["UserName": email,
                "PassWord": password,
                    "DeviceId": UIDevice.current.identifierForVendor?.uuidString ?? ""]
        case .updateApplePassword(let email, let password):
            return ["UserName": email,
                "PassWord": password,
                    "DeviceId": UIDevice.current.identifierForVendor?.uuidString ?? ""]
        case .searchUser(let text):
            return ["textSearch": text]
        case .appParams:
            return ["appType": "ios"]
        default:
            return [:]
        }
    }
    public var task: Task {
        switch self {
        case .register(_), .updateProfile(_, _), .login(_), .confirmPass(_, _), .forgotPass(_), .uploadAvatar(_, _), .changePass(_, _, _), .loginFB(_), .loginApple(_), .registerDeviceToken(_, _, _), .unRegisterDeviceToken(_, _, _), .updatePassword(_, _), .updateApplePassword(_, _):
            return Task.requestParameters(parameters: self.parameters!, encoding: JSONEncoding.default)
        default:
            return Task.requestParameters(parameters: self.parameters!, encoding: URLEncoding.default)
        }
        
    }
    public var headers: [String : String]? {
        return Global.shared.headers()
    }
    
    public var sampleData: Data{
        return "".data(using: String.Encoding.utf8)!
    }
}
