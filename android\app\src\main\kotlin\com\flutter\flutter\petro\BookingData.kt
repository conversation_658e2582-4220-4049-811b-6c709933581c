package com.flutter.flutter.petro

/**
 * Data models for native Android WebView payment
 * These mirror the Flutter models but in Kotlin format
 */

data class BookingData(
    val filmName: String? = null,
    val filmInfo: String? = null,
    val cinemaName: String? = null,
    val dateShow: String? = null,
    val showTime: String? = null,
    val combo: String? = null,
    val totalPrice: Int? = null,
    val screen: String? = null,
    val filmPoster: String? = null,
    val filmFormatCode: String? = null,
    val showId: String? = null,
    val customerId: String? = null,
    val customerCard: String? = null,
    val selectedSeats: List<SeatData>? = null
)

data class SeatData(
    val seatIndex: Int? = null,
    val seatNumber: String? = null,
    val isVip: Boolean? = null,
    val isCouple: Boolean? = null,
    val isNormal: Boolean? = null,
    val ticketTypeId: String? = null,
    val price: Int? = null
)
