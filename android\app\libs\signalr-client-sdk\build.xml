<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project default="library">
    <target name="library">
        <javadoc access="public" author="false" classpath="lib/gson-2.2.2.jar" destdir="doc" nodeprecated="true" nodeprecatedlist="true" noindex="false" nonavbar="false" notree="false" packagenames="microsoft.aspnet.signalr.client" source="1.6" sourcepath="src:gen" splitindex="true" use="true" version="false">
        	<arg value="-notimestamp" />    
        </javadoc>
		<jar destfile="bin/signalr-client-sdk.jar">
			<fileset dir="src"/>
			<fileset dir="bin" includes="**/*.class" />
		</jar>
	</target>
</project>
