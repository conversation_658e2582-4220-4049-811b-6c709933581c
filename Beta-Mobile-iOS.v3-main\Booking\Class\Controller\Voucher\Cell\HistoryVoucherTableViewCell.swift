//
//  HistoryVoucherTableViewCell.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 8/3/19.
//  Copyright © 2019 ddkc. All rights reserved.
//

import UIKit

class HistoryVoucherTableViewCell: UITableViewCell {

    @IBOutlet weak var dateLabel: UILabel!
    
    @IBOutlet weak var stateLabel: UILabel!
    @IBOutlet weak var nameLabel: UILabel!
    @IBOutlet weak var codeLabel: UILabel!
    @IBOutlet weak var titleLabel: UILabel!
    
    override func awakeFromNib() {
        super.awakeFromNib()
        // Initialization code
    }

    func configure(_ voucher: VoucherHistory) {
        dateLabel.text = voucher.dateString
        stateLabel.text = voucher.voucherStatus.description.localized.uppercased()
        stateLabel.textColor = voucher.voucherStatus.color
        codeLabel.text = voucher.code
        titleLabel.text = voucher.description

        nameLabel.isHidden = !voucher.voucherStatus.showName
        nameLabel.text = "(\(voucher.accountName ?? ""))"
    }
    
}
