//
//  SettingTableCell.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 4/12/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit

class SettingTableCell: UITableViewCell {
    @IBOutlet weak var lbTitle: UILabel!
    @IBOutlet weak var lbContent: UILabel!

    override func awakeFromNib() {
        super.awakeFromNib()
    }

    override func updateViewWithItem(_ item: TableItem, indexPath: IndexPath) {
        lbTitle.text = item.title
        lbContent.text = item.content

        if item.tag == .intro {
            guard let appIntro = Global.shared.appIntro?.value else {
                return
            }

            lbTitle.isEnabled = appIntro == "1"
            lbTitle.textColor = appIntro == "1" ? 0x494c62.toColor : .lightGray
        } else {
            lbTitle.isEnabled = true
            lbTitle.textColor = 0x494c62.toColor
        }
    }
}

