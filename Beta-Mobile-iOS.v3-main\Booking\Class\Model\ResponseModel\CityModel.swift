//
//  CityModel.swift
//  Booking
//
//  Created by <PERSON><PERSON> Vu on 4/7/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation
import ObjectMapper

class CityModel: Mappable {
    var Id: String?
    var Name: String?
    var Code: String?


    required init?(map: Map) {

    }

    func mapping(map: Map) {
        Id                   <- map["Id"]
        Name                 <- map["Name"]
        Code                 <- map["Code"]
    }
}
