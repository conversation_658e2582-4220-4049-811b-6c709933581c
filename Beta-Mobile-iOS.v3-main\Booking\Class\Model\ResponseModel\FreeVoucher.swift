//
//  FreeVoucher.swift
//  Booking
//
//  Created by <PERSON><PERSON> Vu on 8/12/19.
//  Copyright © 2019 ddkc. All rights reserved.
//

import Foundation
import ObjectMapper

class FreeVoucher: Mappable {
    var storylineID : String?
    var tieu_de : String?
    var duong_dan_anh_dai_dien : String?
    var publishOnDate : String?
    var newsURI : String?
    var isExistVoucherCode : Bool?
    var Noi_dung_chi_tiet: [Content]?

    var contents: String {
        return (Noi_dung_chi_tiet?.map({ (content) -> String in
            return content.ParagraphData?.ParagraphContent ?? ""
        }) ?? []).joined(separator: "\n")
    }

    required init?(map: Map) {

    }

    func mapping(map: Map) {

        storylineID <- map["StorylineID"]
        tieu_de <- map["Tieu_de"]
        duong_dan_anh_dai_dien <- map["Duong_dan_anh_dai_dien"]
        publishOnDate <- map["PublishOnDate"]
        newsURI <- map["NewsURI"]
        isExistVoucherCode <- map["IsExistVoucherCode"]
        Noi_dung_chi_tiet <- map["Noi_dung_chi_tiet"]
    }
}
