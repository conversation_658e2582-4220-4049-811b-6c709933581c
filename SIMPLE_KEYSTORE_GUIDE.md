# 🔑 Simple Keystore Selection Guide

## 🎯 **CÁCH SỬ DỤNG ĐỠN GIẢN NHẤT**

Chỉ cần **comment/uncomment** trong file `android/app/build.gradle` để chọn keystore!

## 📝 **Cách thay đổi keystore:**

### **Bước 1: Mở file `android/app/build.gradle`**

### **Bước 2: Tì<PERSON> phần `signingConfigs`** (khoảng dòng 73-100)

### **Bước 3: Comment/Uncomment keystore muốn dùng:**

## 🏭 **SỬ DỤNG PRODUCTION KEYSTORE:**
```gradle
signingConfigs {
    // 🏭 PRODUCTION KEYSTORE (for release builds) - ACTIVE
    release {
        keyAlias 'beta cineplex'
        keyPassword 'Betacorpvn@123'
        storeFile file('keystore/beta_cineplex_app_key.jks')
        storePassword 'Betacorpvn@123'
    }
    
    // 🔧 DEBUG KEYSTORE (for development) - COMMENTED
    // release {
    //     keyAlias 'debug'
    //     keyPassword 'sdfoafojasdfji'
    //     storeFile file('keystore/debug.keystore')
    //     storePassword 'sdfoafojasdfji'
    // }
    
    // 👥 CUSTOMER KEYSTORE (for customer testing) - COMMENTED
    // release {
    //     keyAlias 'customer'
    //     keyPassword 'sdiidfjieiurier'
    //     storeFile file('keystore/customer.keystore')
    //     storePassword 'sdiidfjieiurier'
    // }
}
```

## 🔧 **SỬ DỤNG DEBUG KEYSTORE:**
```gradle
signingConfigs {
    // 🏭 PRODUCTION KEYSTORE (for release builds) - COMMENTED
    // release {
    //     keyAlias 'beta cineplex'
    //     keyPassword 'Betacorpvn@123'
    //     storeFile file('keystore/beta_cineplex_app_key.jks')
    //     storePassword 'Betacorpvn@123'
    // }
    
    // 🔧 DEBUG KEYSTORE (for development) - ACTIVE
    release {
        keyAlias 'debug'
        keyPassword 'sdfoafojasdfji'
        storeFile file('keystore/debug.keystore')
        storePassword 'sdfoafojasdfji'
    }
    
    // 👥 CUSTOMER KEYSTORE (for customer testing) - COMMENTED
    // release {
    //     keyAlias 'customer'
    //     keyPassword 'sdiidfjieiurier'
    //     storeFile file('keystore/customer.keystore')
    //     storePassword 'sdiidfjieiurier'
    // }
}
```

## 👥 **SỬ DỤNG CUSTOMER KEYSTORE:**
```gradle
signingConfigs {
    // 🏭 PRODUCTION KEYSTORE (for release builds) - COMMENTED
    // release {
    //     keyAlias 'beta cineplex'
    //     keyPassword 'Betacorpvn@123'
    //     storeFile file('keystore/beta_cineplex_app_key.jks')
    //     storePassword 'Betacorpvn@123'
    // }
    
    // 🔧 DEBUG KEYSTORE (for development) - COMMENTED
    // release {
    //     keyAlias 'debug'
    //     keyPassword 'sdfoafojasdfji'
    //     storeFile file('keystore/debug.keystore')
    //     storePassword 'sdfoafojasdfji'
    // }
    
    // 👥 CUSTOMER KEYSTORE (for customer testing) - ACTIVE
    release {
        keyAlias 'customer'
        keyPassword 'sdiidfjieiurier'
        storeFile file('keystore/customer.keystore')
        storePassword 'sdiidfjieiurier'
    }
}
```

## 🚀 **Sau khi chọn keystore:**

### **Build commands:**
```bash
# Debug build
flutter build apk --debug

# Release build  
flutter build apk --release

# App Bundle for Play Store
flutter build appbundle --release
```

## 📋 **Quy trình làm việc:**

### **🔧 Development:**
1. Uncomment **DEBUG KEYSTORE** trong build.gradle
2. Comment các keystore khác
3. `flutter build apk --debug`

### **👥 Customer Testing:**
1. Uncomment **CUSTOMER KEYSTORE** trong build.gradle
2. Comment các keystore khác
3. `flutter build apk --release`

### **🏭 Production Release:**
1. Uncomment **PRODUCTION KEYSTORE** trong build.gradle
2. Comment các keystore khác
3. `flutter build appbundle --release`

## 💡 **Lưu ý quan trọng:**

1. **CHỈ ĐƯỢC UNCOMMENT 1 KEYSTORE** tại một thời điểm
2. **COMMENT TẤT CẢ KEYSTORE KHÁC** để tránh conflict
3. **Kiểm tra file keystore tồn tại** trong thư mục `android/keystore/`
4. **Clean build** sau khi thay đổi keystore: `flutter clean`

## 🔍 **Kiểm tra keystore hiện tại:**

Xem phần `signingConfigs` trong `android/app/build.gradle` để biết keystore nào đang active (không có comment).

## 🎉 **Ưu điểm của cách này:**

✅ **Cực kỳ đơn giản** - Chỉ cần comment/uncomment  
✅ **Không cần script** - Trực tiếp trong build.gradle  
✅ **Rõ ràng** - Thấy ngay keystore nào đang dùng  
✅ **Không lỗi** - Không có conflict giữa các config  
✅ **Linh hoạt** - Dễ dàng switch giữa các keystore  

## 🔑 **Keystore Information:**

- **🏭 Production:** `beta_cineplex_app_key.jks` (Betacorpvn@123) - Cho production
- **🔧 Debug:** `debug.keystore` (sdfoafojasdfji) - Cho development  
- **👥 Customer:** `customer.keystore` (sdiidfjieiurier) - Cho customer testing

**Hiện tại đang dùng: PRODUCTION KEYSTORE** ✅
