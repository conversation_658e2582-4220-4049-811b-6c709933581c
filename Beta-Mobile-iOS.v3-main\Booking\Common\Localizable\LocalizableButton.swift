//
//  LocalizableButton.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 4/14/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit

@IBDesignable
public class LocalizableButton: UIButton , Localizable {
    @IBInspectable var localizableString: String? {
        didSet { updateLocalizable() }
    }

    @IBInspectable var selectedLocalizableString: String? {
        didSet { updateLocalizable() }
    }

    func updateLocalizable() {
        if let text = localizableString {
            self.setTitle(text.localized, for: .normal)
        }

        if let text = selectedLocalizableString {
            self.setTitle(text.localized, for: .selected)
        }
    }
}
