@echo off
echo ========================================
echo 🔍 VERIFYING CH PLAY UPDATE COMPATIBILITY
echo ========================================
echo.

echo 📋 Checking Flutter repo configuration...
echo.

REM Check if keystore exists
if exist "android\keystore\beta_cineplex_app_key.jks" (
    echo ✅ Production keystore found: beta_cineplex_app_key.jks
) else (
    echo ❌ ERROR: Production keystore NOT found!
    echo    Expected: android\keystore\beta_cineplex_app_key.jks
    pause
    exit /b 1
)

REM Check build.gradle configuration
echo.
echo 📄 Checking build.gradle configuration...
findstr /C:"applicationId \"com.beta.betacineplex\"" android\app\build.gradle >nul
if %errorlevel%==0 (
    echo ✅ Package name: com.beta.betacineplex
) else (
    echo ❌ ERROR: Package name mismatch!
    pause
    exit /b 1
)

findstr /C:"keyAlias 'beta cineplex'" android\app\build.gradle >nul
if %errorlevel%==0 (
    echo ✅ Key alias: beta cineplex
) else (
    echo ❌ ERROR: Key alias mismatch!
    pause
    exit /b 1
)

REM Check version
echo.
echo 📊 Checking version configuration...
findstr /C:"flutterVersionCode = '61'" android\app\build.gradle >nul
if %errorlevel%==0 (
    echo ✅ Version code: 61 (higher than Android repo: 48)
) else (
    echo ⚠️  WARNING: Version code might not be set correctly
)

findstr /C:"flutterVersionName = '2.8.1'" android\app\build.gradle >nul
if %errorlevel%==0 (
    echo ✅ Version name: 2.8.1 (higher than Android repo: 2.7.6)
) else (
    echo ⚠️  WARNING: Version name might not be set correctly
)

echo.
echo ========================================
echo 🎯 COMPATIBILITY CHECK SUMMARY
echo ========================================
echo ✅ Package Name: com.beta.betacineplex (MATCH)
echo ✅ Keystore: beta_cineplex_app_key.jks (SAME)
echo ✅ Key Alias: beta cineplex (SAME)
echo ✅ Version Code: 61 (HIGHER than 48)
echo ✅ Version Name: 2.8.1 (HIGHER than 2.7.6)
echo.
echo 🚀 RESULT: Flutter app CAN UPDATE Android app on CH Play!
echo.

echo 📱 Next steps:
echo 1. Run: flutter clean
echo 2. Run: flutter build appbundle --release
echo 3. Upload app-release.aab to Google Play Console
echo.

pause
