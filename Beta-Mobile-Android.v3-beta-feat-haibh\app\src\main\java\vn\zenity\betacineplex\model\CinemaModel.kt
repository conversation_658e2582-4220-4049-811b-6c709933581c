package vn.zenity.betacineplex.model

import android.location.Location
import android.os.Parcel
import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import vn.zenity.betacineplex.app.App
import vn.zenity.betacineplex.helper.extension.getDistance

/**
 * Created by tinhvv on 4/14/18.
 */
data class CinemaModel(var CinemaId: String, var CinemaTypeId: String? = null
                       ): Parcelable {
    var Name: String? = null
        get() {
            if (!App.shared().isLangVi() && Name_F != null) {
                return Name_F
            }
            return field
        }
    var Name_F : String? = null
    var Code : String? = null
    var Address : String? = null
        get() {
            if (!App.shared().isLangVi() && Address_F != null) {
                return Address_F
            }
            return field
        }
    var Address_F : String? = null
    var StartDate : String? = null
    var EndDate : String? = null
    var Status : Boolean? = null
    var Order : Int? = null
    var PhoneNumber : String? = null
    var CityId : String? = null
    var CityName : String? = null
    var Latitude : String? = null
    var Longtitude : String? = null
    @SerializedName("Picture")
    var Duong_dan_anh_dai_dien: String? = null
    var NewsId: String? = null

    constructor(parcel: Parcel) : this(
            parcel.readString() ?: "",
            parcel.readString()) {
        Name = parcel.readString()
        Code = parcel.readString()
        Address = parcel.readString()
        Name_F = parcel.readString()
        Address_F = parcel.readString()
        StartDate = parcel.readString()
        EndDate = parcel.readString()
        Status = parcel.readValue(Boolean::class.java.classLoader) as? Boolean
        Order = parcel.readValue(Int::class.java.classLoader) as? Int
        PhoneNumber = parcel.readString()
        CityId = parcel.readString()
        CityName = parcel.readString()
        Latitude = parcel.readString()
        Longtitude = parcel.readString()
        Duong_dan_anh_dai_dien = parcel.readString()
        NewsId = parcel.readString()
    }

    fun getDistanceToCurrentLocation(lct: Location?): Float {
        lct ?: return 10000f
        return lct.getDistance(Latitude, Longtitude)
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(CinemaId)
        parcel.writeString(CinemaTypeId)
        parcel.writeString(Name)
        parcel.writeString(Code)
        parcel.writeString(Address)
        parcel.writeString(Name_F)
        parcel.writeString(Address_F)
        parcel.writeString(StartDate)
        parcel.writeString(EndDate)
        parcel.writeValue(Status)
        parcel.writeValue(Order)
        parcel.writeString(PhoneNumber)
        parcel.writeString(CityId)
        parcel.writeString(CityName)
        parcel.writeString(Latitude)
        parcel.writeString(Longtitude)
        parcel.writeString(Duong_dan_anh_dai_dien)
        parcel.writeString(NewsId)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<CinemaModel> {
        override fun createFromParcel(parcel: Parcel): CinemaModel {
            return CinemaModel(parcel)
        }

        override fun newArray(size: Int): Array<CinemaModel?> {
            return arrayOfNulls(size)
        }
    }
}