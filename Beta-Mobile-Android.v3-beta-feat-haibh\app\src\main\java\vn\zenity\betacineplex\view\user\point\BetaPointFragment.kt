package vn.zenity.betacineplex.view.user.point

import android.os.Bundle
import android.view.View
import android.view.View.GONE
import kotlinx.android.synthetic.main.fragment_betapoint.*
import kotlinx.android.synthetic.main.fragment_betapoint.tvRewardPoint
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.base.BaseFragment
import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.global.Global
import vn.zenity.betacineplex.helper.extension.click
import vn.zenity.betacineplex.helper.extension.rewardPoint

/**
 * Created by Zenity.
 */

class BetaPointFragment : BaseFragment(), BetaPointContractor.View {
    override fun showPoints(accumulation: Int, used: Int, available: Int, remain: Int, expiredOn: String) {
        activity?.runOnUiThread {
            tvAccumulationPoint?.text = "$accumulation"
            tvUsedPoint?.text = "$used"
            tvAvailablePoint?.text = "$available"
            tvRewardPoint?.rewardPoint(remain, expiredOn)
        }
    }

    private val presenter = BetaPointPresenter()

    override fun getPresenter(): IBasePresenter<IBaseView>? {
        return presenter as? IBasePresenter<IBaseView>
    }

    override fun isShowToolbar(): Boolean {
        return false
    }

    override fun getLayoutRes(): Int {
        return R.layout.fragment_betapoint
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val user = Global.share().user
        presenter.getBetaPoint(user?.AccountId ?: return)
        btnGivePoint.click {
            openFragment(GivePointFragment.getInstance {
                presenter.getBetaPoint(user.AccountId ?: return@getInstance)
            })
        }
        btnHistories.click {
            openFragment(PointHistoryFragment())
        }
        btnGivePoint.visibility = GONE
    }
}
