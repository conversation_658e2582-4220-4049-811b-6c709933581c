//
//  SeatBookingModel.swift
//  Booking-dev
//
//  Created by <PERSON><PERSON> on 6/1/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation

public class SeatBookingModel: BaseRequestModel {
    var seatIndex: Int
    var seatName: String
    var seatType: String
    var ticketTypeId: String
    var price: Int

    init(seatIndex: Int, seatName: String, seatType: String, ticketTypeId: String, price: Int) {
        self.seatIndex = seatIndex
        self.seatName = seatName
        self.seatType = seatType
        self.ticketTypeId = ticketTypeId
        self.price = price
    }

    init(seat: SeatModel, ticketType: TicketType) {
        self.seatIndex = seat.SeatIndex ?? 0
        self.seatName = seat.SeatName ?? ""
        self.seatType = seat.seatType?.isVip == true ? "VIP" : (seat.seatType?.isCouple == true ? "DOUBLE" : "STARDAR")
        self.ticketTypeId = ticketType.TicketTypeId ?? ""
        self.price = ticketType.Price ?? 0
    }

    override func toJSON() -> [String : Any] {
        return [
            "SeatIndex": seatIndex,
            "SeatName": seatName,
            "SeatType": seatType,
            "TicketTypeId": ticketTypeId,
            "Price": price
        ]
    }
}
