package vn.zenity.betacineplex.view.event

import vn.zenity.betacineplex.Manager.Network.APIClient
import vn.zenity.betacineplex.helper.extension.applyOn
import java.lang.ref.WeakReference

/**
 * Created by Zenity.
 */

class ListEventPresenter : ListEventContractor.Presenter {
    override fun getListEvent(id: String, page: Int) {
        this.view?.get()?.showLoading()
        APIClient.shared.ecmAPI.getNewForCategory(id, 1000, page).applyOn()
                .subscribe({
                    if(it.Data?.size ?: 0 > 0) {
                        view?.get()?.showListEvents(it.Data!!)
                    }
                    this.view?.get()?.hideLoading()
                }, {
                    this.view?.get()?.hideLoading()
                })
    }

    private var view: WeakReference<ListEventContractor.View?>? = null
    override fun attachView(view: ListEventContractor.View) {
        this.view = WeakReference(view)
    }

    override fun detachView() {
        this.view?.clear()
        this.view = null
    }
}
