//
//  Notification.swift
//  Booking-dev
//
//  Created by <PERSON><PERSON> on 7/9/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation
import ObjectMapper

/*
 {
 "Id": 0,
 "NotificationCampaignId": 0,
 "AccountId": "string",
 "ReadStatus": true,
 "LastModifiedOnDate": "2018-07-09T13:02:43.203Z",
 "Title": "string"
 }
 */
class NewNotification: Mappable {
    var Id: Int?
    var NotificationCampaignId: Int?
    var AccountId: String?
    var ReadStatus: Bool?
    var LastModifiedOnDate: String?
    var Title: String?
    var ScreenCode: Int?
    var RefId: String?
    
    init(_ campaignId: Int) {
        self.NotificationCampaignId = campaignId
    }
    
    required init?(map: Map) {
        
    }
    
    func mapping(map: Map) {
        Id               <- map["Id"]
        NotificationCampaignId           <- map["NotificationCampaignId"]
        AccountId           <- map["AccountId"]
        ReadStatus               <- map["ReadStatus"]
        LastModifiedOnDate        <- map["CreatedOnDate"]
        Title          <- map["Title"]
        ScreenCode <- map["ScreenCode"]
        RefId <- map["RefId"]
    }

    var date: String? {
        guard let last = LastModifiedOnDate else {
            return nil
        }
        return Date.dateFromServerSavis(last).toString(dateFormat: "dd/MM/yyyy, HH:mm")
    }
}

class NotificationDetail: Mappable {
    var ImageThumb: String?
    var Title: String?
    var Content: String?
    
    required init?(map: Map) {
        
    }
    
    func mapping(map: Map) {
        ImageThumb               <- map["ImageThumb"]
        Title           <- map["Title"]
        Content           <- map["Content"]
    }
}
