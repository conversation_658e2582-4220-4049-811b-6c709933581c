@echo off
echo ========================================
echo 🚀 BUILDING FLUTTER APP FOR CH PLAY UPDATE
echo ========================================
echo.

echo 🧹 Cleaning previous builds...
flutter clean
if %errorlevel% neq 0 (
    echo ❌ Flutter clean failed!
    pause
    exit /b 1
)

echo.
echo 📦 Getting dependencies...
flutter pub get
if %errorlevel% neq 0 (
    echo ❌ Flutter pub get failed!
    pause
    exit /b 1
)

echo.
echo 🔍 Verifying keystore configuration...
if not exist "android\keystore\beta_cineplex_app_key.jks" (
    echo ❌ ERROR: Production keystore not found!
    echo    Expected: android\keystore\beta_cineplex_app_key.jks
    echo    This keystore MUST be the same as Android repo for CH Play update!
    pause
    exit /b 1
)

echo ✅ Production keystore found: beta_cineplex_app_key.jks
echo.

echo 🏗️  Building release APK...
flutter build apk --release
if %errorlevel% neq 0 (
    echo ❌ APK build failed!
    pause
    exit /b 1
)

echo.
echo 🏗️  Building release App Bundle (AAB) for CH Play...
flutter build appbundle --release
if %errorlevel% neq 0 (
    echo ❌ App Bundle build failed!
    pause
    exit /b 1
)

echo.
echo ========================================
echo ✅ BUILD COMPLETED SUCCESSFULLY!
echo ========================================
echo.

echo 📱 Generated files:
echo   APK: build\app\outputs\flutter-apk\app-release.apk
echo   AAB: build\app\outputs\bundle\release\app-release.aab
echo.

echo 🔍 Verifying build output...
if exist "build\app\outputs\bundle\release\app-release.aab" (
    echo ✅ App Bundle (AAB) created successfully
) else (
    echo ❌ App Bundle (AAB) not found!
    pause
    exit /b 1
)

if exist "build\app\outputs\flutter-apk\app-release.apk" (
    echo ✅ APK created successfully
) else (
    echo ❌ APK not found!
    pause
    exit /b 1
)

echo.
echo 📊 Checking package info...
echo.

REM Use aapt to check package info if available
where aapt >nul 2>&1
if %errorlevel%==0 (
    echo 📋 Package information:
    aapt dump badging "build\app\outputs\flutter-apk\app-release.apk" | findstr "package\|version"
    echo.
) else (
    echo ⚠️  aapt not found in PATH - cannot verify package info
    echo    You can manually verify using Android SDK tools
    echo.
)

echo ========================================
echo 🎯 NEXT STEPS FOR CH PLAY UPDATE:
echo ========================================
echo.
echo 1. 📤 Upload to Google Play Console:
echo    - Go to Google Play Console
echo    - Select "Beta Cinemas" app
echo    - Create new release in Production
echo    - Upload: build\app\outputs\bundle\release\app-release.aab
echo.
echo 2. ✅ Verify before publishing:
echo    - Package name: com.beta.betacineplex
echo    - Version code: 61 (higher than current: 48)
echo    - Version name: 2.8.1 (higher than current: 2.7.6)
echo.
echo 3. 🚀 Release strategy:
echo    - Start with staged rollout (5%% → 20%% → 50%% → 100%%)
echo    - Monitor crash reports and user feedback
echo.
echo 🎉 Flutter app is ready to UPDATE Android app on CH Play!
echo.

pause
