//
//  FreeVoucherViewController.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 8/10/19.
//  Copyright © 2019 ddkc. All rights reserved.
//

import UIKit
import RxSwift

class FreeVoucherViewController: BaseViewController {

    @IBOutlet weak var tableView: UITableView!
    private var vouchers: [FreeVoucher] = []
    override func viewDidLoad() {
        super.viewDidLoad()
        localizableTitle = "free_voucher".localized
        self.navigationController?.setTransparent(false)
        tableView.separatorStyle = .none
        tableView.registerCell(id: FreeVoucherTableViewCell.id)

        getBanners()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        self.navigationController?.setTransparent(false)
    }

    private func getBanners() {
        self.showLoading()
        EcmProvider.rx.request(.getFreeVoucher).mapObject(DDKCResponse<FreeVoucher>.self)
            .asObservable()
            .subscribeOn(MainScheduler.instance)
            .subscribe(onNext:{ response in
                self.handlerResponse(response, success: {
                    guard let objects = response.ListObject else{
                        print("Data wrong")
                        self.dismissLoading()
                        return
                    }
                    self.vouchers = objects
                    self.tableView.reloadData()
                    self.dismissLoading()
                }, error: {
                    self.dismissLoading()
                })

            }, onError:{[weak self] error in
                guard let `self` = self else { return }
                self.dismissLoading()
                UIAlertController.showAlert(self, message: error.localizedDescription)
            }).disposed(by: disposeBag)
    }

}

extension FreeVoucherViewController: UITableViewDataSource, UITableViewDelegate {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return vouchers.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let cell = tableView.dequeueReusableCell(withIdentifier: FreeVoucherTableViewCell.id) as? FreeVoucherTableViewCell else {
            return FreeVoucherTableViewCell()
        }
        cell.configure(vouchers[indexPath.row])
        return cell
    }

    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 300.0
    }

    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        let vc = UIStoryboard.home[.newsDetail] as! NewsDetailViewController
        vc.type = NewType.voucherFromList(vouchers[indexPath.row])
        show(vc, sender: nil)
    }
}
