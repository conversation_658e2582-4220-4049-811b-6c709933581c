{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984f18b1e230b0195ed69ce22594bef844", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989bf3213029e4e9a7dd0abbdacf630e22", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9895e428256ec1499087342c635a62fc7d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f47e55dcec3e36bc0b3cb20e5046cf6e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9895e428256ec1499087342c635a62fc7d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98148aa9936091540da8dfe1ade6998eee", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fe5bddc423ee9534b6a6c7c3661f6301", "guid": "bfdfe7dc352907fc980b868725387e98e46bc35165cc0e0c88d81761105d5da3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987055ce46f1d1286bf25198c30d750236", "guid": "bfdfe7dc352907fc980b868725387e98199995cbff49d1d0ec0d2b5b708720e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988eda7b0b8b637173fd00614a39f85a2d", "guid": "bfdfe7dc352907fc980b868725387e98cef3adf4ddf4d4a1ea1483de3181250d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839b7dd5abf71c5ab5f161b9083af1e7e", "guid": "bfdfe7dc352907fc980b868725387e98ce7cee74e03bf9ce479efcd52024aa2a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de51ffa78ec45d7efeb6728c98dd5f7e", "guid": "bfdfe7dc352907fc980b868725387e984852879e58f935547c7ed016a59648cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98624af64bbd1fd9ce6482ad543ec52328", "guid": "bfdfe7dc352907fc980b868725387e980f8be9bc5c3145cc95823426f50b5ab4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6f8f4e05ff7df7f628cb2af26b16b0d", "guid": "bfdfe7dc352907fc980b868725387e98d5aa2c1fe87c6f9579756ea0753725b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987872cdaff08c122c1d69e73e5ef5b967", "guid": "bfdfe7dc352907fc980b868725387e9862349b29efc459400bf6c83d5e04d13f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982170c4dd3ec297e05e5930e65010a277", "guid": "bfdfe7dc352907fc980b868725387e98c1d8462f55321efcbbc12eee313b37f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1f8cf48f1edf442840921e76165ec9b", "guid": "bfdfe7dc352907fc980b868725387e986db9291ff7aaffb144442a89df4bca4d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee3421d29c3c4d5a863c3c03593af525", "guid": "bfdfe7dc352907fc980b868725387e98125928c73d83d7f7685246413a529dac", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985cf2e12792e20c1dfc962e7fb408add6", "guid": "bfdfe7dc352907fc980b868725387e9854062f02364c484eea02f5b27dd5d61a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cf6c899a8b3819f6b9f59c5341834b3", "guid": "bfdfe7dc352907fc980b868725387e98afd9fef7f078d9bbdcaa79e9f7bb13c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813ebfde6922d8bed336968b1aea0392d", "guid": "bfdfe7dc352907fc980b868725387e98dedc01f7bb0306aa79416dd1f935321c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980dafc746c80282d8740168d0a40c87b9", "guid": "bfdfe7dc352907fc980b868725387e98775395534d8e215476e8f0bdd6baca57"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e80e3b3cfe9c6a845dbc97657c870fed", "guid": "bfdfe7dc352907fc980b868725387e98a8bc559d674a25c7e341ddf2023af2c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c597c915bff1fb9021ab9d9ddf70f6f5", "guid": "bfdfe7dc352907fc980b868725387e98e38a22d142f91185aff0ecfba72060ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d779da698ae61b0bfb51544831e40e6f", "guid": "bfdfe7dc352907fc980b868725387e982280c46b473c9cf2bbfb2532828f2f0a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b53e0e0f8a8af9008e67680cd208bd5", "guid": "bfdfe7dc352907fc980b868725387e98fb82b5ea392970805a09be7ecdeb5bad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ba9c9e95e9bec7c923642e49402265c", "guid": "bfdfe7dc352907fc980b868725387e988c9ebaa208eaff168faeabf769ee82e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985616bdaad4f2629e3694aec4770f6e18", "guid": "bfdfe7dc352907fc980b868725387e9869d7cedbdaadb46f7fe9debb94ab9ab4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae1b798b0b93a8be09619a29b1fd5dda", "guid": "bfdfe7dc352907fc980b868725387e98726c75a69ec1112044f2336f1ec5ea1a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985109c4e07e01cc74329b6563e82934f3", "guid": "bfdfe7dc352907fc980b868725387e98e37822402ff92e5a84616e0f9dfee719"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec29884dc0c613a4018e01bb9748f37a", "guid": "bfdfe7dc352907fc980b868725387e98a34ed50af754c01eace0a0b1184ef13a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5943f774874ea5ad8da9dc8e37a44c8", "guid": "bfdfe7dc352907fc980b868725387e986ebdd03b0e835f13d3ba14528d3145d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc3fd3a569ad974cdec11a9cf7c70d7c", "guid": "bfdfe7dc352907fc980b868725387e98cb6dd9a159fcdc7b49c42c674d3e4501"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822b24d36ff0e814ad681cc941ec8f588", "guid": "bfdfe7dc352907fc980b868725387e98aca29f11ec5a88ad861a8491b3f9e674"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d27911c4e867e09cf32dd69048faa7c6", "guid": "bfdfe7dc352907fc980b868725387e98d5c51020fbfc4d469ba883c35670024a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832b38e4b5ac0ed6ccf5a6c6fb26e0ad2", "guid": "bfdfe7dc352907fc980b868725387e9876e92054177ea16804aa21ef8aae594e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985bf0df905b5b9d014ad2a18c441938a2", "guid": "bfdfe7dc352907fc980b868725387e9819686259d24ebf4638e215bd943586c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af9886221714421a6047cad21d0533e6", "guid": "bfdfe7dc352907fc980b868725387e98fdeebe268a8b12fb0a12803dc45a9fd5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98661f3d3a7de36bb539913df5c49b78b8", "guid": "bfdfe7dc352907fc980b868725387e98f30c7906fe0d6f40e411d5d8cba69d9d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98813d6542e08439ba5ba84ab84f0a9a59", "guid": "bfdfe7dc352907fc980b868725387e98481f2b131bbeca8a796f28207c3dfae9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6f9de9418e92dfb616450337da795b2", "guid": "bfdfe7dc352907fc980b868725387e98c926727147c765663613ddc21f64ccc5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e0058c84f29c1ebff72a9a02eb626bc", "guid": "bfdfe7dc352907fc980b868725387e9869d652aa1bc1731fe0b2899d62e531e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852c27066414e139680f3c9736d4707be", "guid": "bfdfe7dc352907fc980b868725387e98e90ae0c9a9a11439178763c39c76758d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988230664110fb5eff250bf609dd4e97bf", "guid": "bfdfe7dc352907fc980b868725387e985fb2998faed41f5107d71bd0206c8d3e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98694f6f65b53787f986cbaeb5b163ec85", "guid": "bfdfe7dc352907fc980b868725387e98aa14f5b6a006329cec9e3c64a4106f7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c20b656a56cc92279d9beee4d4d925f0", "guid": "bfdfe7dc352907fc980b868725387e9899adcbf9070fdc3b3d9d3feed339a21f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d609f54306e8d8bad3b193c342b54c2", "guid": "bfdfe7dc352907fc980b868725387e98fcf3787b35f47cceb92166457a72b2ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aecc4b6e085f5ddeab2f8c9f2a04c594", "guid": "bfdfe7dc352907fc980b868725387e98930cdda9b4e8c861028b6460a462570a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7d0c0b3b96cef8898340bf776ea4b5d", "guid": "bfdfe7dc352907fc980b868725387e982825acacd4da7af2209811052ec67525"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98906a5f38951e5147674b9a7e2833ee15", "guid": "bfdfe7dc352907fc980b868725387e98058d0c7e9f3eacc9246dbdbbf6fab486"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984366985146e0ca21fef2a0948190c659", "guid": "bfdfe7dc352907fc980b868725387e989865040588e377d596dfbe590b3416e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f767bcee28cb4f1a41cdfcdd2f3f8252", "guid": "bfdfe7dc352907fc980b868725387e98bc472b49c2ec982eaadccd76f625aed6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825a0a135c572d20e39577c07cea399b6", "guid": "bfdfe7dc352907fc980b868725387e9883ffaf29a0457f98772e8272cfe2b48b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a657dfb46aa004e14a38734dac601c73", "guid": "bfdfe7dc352907fc980b868725387e9829d868343b621aeb05491b4b70905c0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809fa6ca8e59ef82daeb36d3b5994aa08", "guid": "bfdfe7dc352907fc980b868725387e98a1b62add890226c8552853fba61a52a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c757a2809139e75ecd1c8570134d849d", "guid": "bfdfe7dc352907fc980b868725387e984e36393cf7ad93f394a7dc1758df3073"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ecc7e05968d275689b51f7036aeed24d", "guid": "bfdfe7dc352907fc980b868725387e98232934199dcbdd00a37afc08fa784e34"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0ff973ebce25750196c92251f09f1e7", "guid": "bfdfe7dc352907fc980b868725387e985ab90e02499140290bca9fa9d0aebcf7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864ab88f164e5e36eddaa737214d1fcc3", "guid": "bfdfe7dc352907fc980b868725387e98923c665906c1744f78404ea3c4999a1f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f527b85e24aa80f3229bb89a218d3fe", "guid": "bfdfe7dc352907fc980b868725387e98719d978f7c9d5c3f63ad24116d0f9fbc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984bcfdd8adf34b9a31889b8351db062e0", "guid": "bfdfe7dc352907fc980b868725387e98005065e52737e51c0b5fa7d5f0c976e7"}], "guid": "bfdfe7dc352907fc980b868725387e9890c1479bbec1f670bb4fe9ccc96d182b", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980ee00532d79df158f0d142e2afc9813b", "guid": "bfdfe7dc352907fc980b868725387e980ea66fa91a3c60d374bf78bf6578b84c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98388b3dc7d9cf708f26546481af8c93a6", "guid": "bfdfe7dc352907fc980b868725387e98366f9fe5f9f3e09f5cae7b697f003b6f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98483fbe6c0f01dc37b81d066f695388a9", "guid": "bfdfe7dc352907fc980b868725387e98aeabad2c748ed978d4bdea0faeba8f24"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820d5deb02fc30a7f354e25b44479678b", "guid": "bfdfe7dc352907fc980b868725387e9872cd8d7693ce37f7634318b41e7b9d8e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dae1cb3d762831c0777fcfef1defd571", "guid": "bfdfe7dc352907fc980b868725387e98b06cb3024b73f9ba69e57205f8ca5ba6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bab71710997b1e0af9395b616ad47805", "guid": "bfdfe7dc352907fc980b868725387e9815ed44f50256c1348917fda3858d2403"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807ab112fbe424f267c7c88f25e93743a", "guid": "bfdfe7dc352907fc980b868725387e98f4734ac497f579b5b9b309e737679e40"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b09d117792ce2b2483b31db437f660a2", "guid": "bfdfe7dc352907fc980b868725387e98fe9b8887c3142b89b5e2e0e888b482c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3addc9ab8b5d1a9c55718437de1e043", "guid": "bfdfe7dc352907fc980b868725387e9827b4cffe63c6f1d9948ce21ba25df56a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889b2f94453ac3934d1068d4586432301", "guid": "bfdfe7dc352907fc980b868725387e98c752b711a58a523759e0cc4b54b20062"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987830fc20cce0c661a98a934d8724ef64", "guid": "bfdfe7dc352907fc980b868725387e981a6e5b4df2a04e59eccd45793f16c914"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1d7975dc46b28876b5debac04ff675d", "guid": "bfdfe7dc352907fc980b868725387e988cbf554d3618152ef63a60265b76c806"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1d86f418e7541024e504617c01be1df", "guid": "bfdfe7dc352907fc980b868725387e981725fd3a23788a4992b0223763609770"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b39ec0384cd79aee6c723b29340a1755", "guid": "bfdfe7dc352907fc980b868725387e9863b43d8de11853dfac912f906df0b41d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0eb0d855218e9a8d348570870aa9cea", "guid": "bfdfe7dc352907fc980b868725387e98f24be0c4f7a3fff26aa26fe7bcf30057"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc43dca1b0dcc3bc86ff0509d27428b8", "guid": "bfdfe7dc352907fc980b868725387e98b47d594ef3bb804e9c23d714c0d2c16c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fe6ece16ec3f915aa2af632c180ba30", "guid": "bfdfe7dc352907fc980b868725387e98ba6cc1e234e0c6668a570e01834d144d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fff31a4820aa9bfd6dd8fc375ddfd032", "guid": "bfdfe7dc352907fc980b868725387e98a8623405e30980ca2efba61d32f631b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea54fde367d5dd6db74deedd8cff415c", "guid": "bfdfe7dc352907fc980b868725387e98de883f3b68446ea3cd8bf51cf73a195b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c69f56854cc1f3566ea180ec89fe39b3", "guid": "bfdfe7dc352907fc980b868725387e98b8da88519d753c6b65006cea15174505"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7ef36c542b394f028ebbd6a19e51959", "guid": "bfdfe7dc352907fc980b868725387e986fca9dd47cce9730f9456057d31ed6b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8a32b991fc33c03da5da509aff419d4", "guid": "bfdfe7dc352907fc980b868725387e98dc8722ac25f6cac68695de837e4f1aef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb1190759729e8ab762e9125c1df8411", "guid": "bfdfe7dc352907fc980b868725387e9894b5a0ac283cf9af3433c7ad6966f67e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb012bce79be418b1c5f2552831344cd", "guid": "bfdfe7dc352907fc980b868725387e9827303c2e3f5ba034e9e3176793fa5fc8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98580fa90b5f3305752ce2e35a5d0e8e8e", "guid": "bfdfe7dc352907fc980b868725387e981bcf6f910d375790bea0e6f26d6decc7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985efae14fd67fda1755c5b1f2cc284f1e", "guid": "bfdfe7dc352907fc980b868725387e9818b337801bd6688c6d4f990af1046a7d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf0ad6aaed66e46d971d2a0e5fec84f0", "guid": "bfdfe7dc352907fc980b868725387e988b1b38a93c185b85d300fcee15306287"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811b69d377364bb630f2944c6278f686a", "guid": "bfdfe7dc352907fc980b868725387e98886a19f15f47e9ede466ec4ba13b1db6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862f0456d65a237488355fcf83ad00826", "guid": "bfdfe7dc352907fc980b868725387e9824be521b3cebb14edfd62feb6225c97c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c78901cae404bfea5210214f9e88a634", "guid": "bfdfe7dc352907fc980b868725387e987b96a447149b5c7eb77aa740fd66eb0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836f9c24097bb54ee0353117af389028a", "guid": "bfdfe7dc352907fc980b868725387e98c7c76e6030587b644d9d93f72cde635d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983153449e579f1948db1b9e14677180a2", "guid": "bfdfe7dc352907fc980b868725387e98656fcbb807db0bbeb2207e04fd13d01b"}], "guid": "bfdfe7dc352907fc980b868725387e9893e4db9bf404f1a77c059598f834dc71", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e98ebcc38ba05229ee3d2c10e72c77d1ad5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5f5979f0786f4a5ad9848f0349b1159", "guid": "bfdfe7dc352907fc980b868725387e981a044f8bde47f2ea48fcf68325fbe94e"}], "guid": "bfdfe7dc352907fc980b868725387e98ad9bba9bbb8e85ff61fd805a0d26ea8e", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98bf444f865084dea59eafad1a9036c74c", "targetReference": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab"}], "guid": "bfdfe7dc352907fc980b868725387e98eb13a7364a0446f674a05bb8d2740fa2", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations"}, {"guid": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab", "name": "FirebaseMessaging-FirebaseMessaging_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98d3c8dfff2c580c352f83d3850ad17775", "name": "GoogleDataTransport"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e980062393f91a1d2d94e3e5ed3a5aa5da9", "name": "nanopb"}], "guid": "bfdfe7dc352907fc980b868725387e983da17a3564c774dfaa331fa07754d2bc", "name": "FirebaseMessaging", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b3b0fadaedeb0138a07668440d83e3b3", "name": "FirebaseMessaging.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}