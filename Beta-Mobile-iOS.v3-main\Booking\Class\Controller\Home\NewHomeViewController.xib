<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="20037" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina3_5" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="20020"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="Stack View standard spacing" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="Oswald-Medium.ttf">
            <string>Oswald-Medium</string>
        </array>
        <array key="SourceSansPro-Bold.ttf">
            <string>SourceSansPro-Bold</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="NewHomeViewController" customModule="Beta_Cinemas" customModuleProvider="target">
            <connections>
                <outlet property="avatarImageView" destination="9F4-Ge-eOG" id="sfb-SD-HAt"/>
                <outlet property="collectionView" destination="sG4-zz-CqS" id="MHH-v4-7Tj"/>
                <outlet property="commingSoonButton" destination="zEy-6y-XdT" id="Okp-nC-H8l"/>
                <outlet property="contentViewHeight" destination="OCQ-Ar-yRE" id="0gh-3S-EOM"/>
                <outlet property="earlyShowButton" destination="8Wb-s3-VZA" id="UlO-W2-DkR"/>
                <outlet property="levelButton" destination="nC2-TK-WGF" id="SvD-wO-Nls"/>
                <outlet property="loginButton" destination="IqD-3u-dZG" id="ra3-b1-GgN"/>
                <outlet property="nameLabel" destination="EZH-xW-Ib9" id="f4R-sC-N7o"/>
                <outlet property="pageControl" destination="PKd-0t-cJZ" id="fuH-df-VLJ"/>
                <outlet property="pointButton" destination="54c-8U-Vom" id="xib-vi-8MV"/>
                <outlet property="profileView" destination="R8e-LF-mpn" id="gCv-HU-3R0"/>
                <outlet property="scrollView" destination="iDh-gn-L8X" id="5fY-y7-S1y"/>
                <outlet property="showingButton" destination="TGb-yx-R8B" id="bu0-lh-B18"/>
                <outlet property="slideShow" destination="qz5-Ld-FOx" id="gfa-BP-Xsp"/>
                <outlet property="stackViewWidth" destination="dhc-z1-LEi" id="SKB-Bt-YYZ"/>
                <outlet property="topViewTop" destination="8qj-da-rjB" id="n1H-12-YwZ"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
                <outlet property="voucherButton" destination="Yb1-Lc-8JD" id="03F-oq-NSx"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="320" height="480"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="qbW-yB-Ieh">
                    <rect key="frame" x="0.0" y="0.0" width="320" height="112"/>
                    <subviews>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="jd5-iJ-eef">
                            <rect key="frame" x="0.0" y="0.0" width="320" height="60"/>
                            <subviews>
                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="IqD-3u-dZG" customClass="RoundButton" customModule="Beta_Cinemas" customModuleProvider="target">
                                    <rect key="frame" x="20" y="14" width="105" height="32"/>
                                    <constraints>
                                        <constraint firstAttribute="width" constant="105" id="2h9-4O-AFg"/>
                                        <constraint firstAttribute="height" constant="32" id="x1R-l6-XNR"/>
                                    </constraints>
                                    <fontDescription key="fontDescription" name="Oswald-Medium" family="Oswald" pointSize="14"/>
                                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="8" maxY="0.0"/>
                                    <state key="normal" title="Đăng Nhập" image="ic_user">
                                        <color key="titleColor" red="0.24705882352941178" green="0.71764705882352942" blue="0.97647058823529409" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </state>
                                    <userDefinedRuntimeAttributes>
                                        <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                            <real key="value" value="5"/>
                                        </userDefinedRuntimeAttribute>
                                        <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                            <real key="value" value="1"/>
                                        </userDefinedRuntimeAttribute>
                                        <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                            <color key="value" red="0.2470588235" green="0.71764705880000002" blue="0.97647058819999999" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        </userDefinedRuntimeAttribute>
                                        <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Login.Title"/>
                                    </userDefinedRuntimeAttributes>
                                    <connections>
                                        <action selector="loginTapped:" destination="-1" eventType="touchUpInside" id="gYE-lF-7KZ"/>
                                    </connections>
                                </button>
                                <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="R8e-LF-mpn">
                                    <rect key="frame" x="8" y="10" width="226" height="40"/>
                                    <subviews>
                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_account" translatesAutoresizingMaskIntoConstraints="NO" id="9F4-Ge-eOG" customClass="RoundImageView" customModule="Beta_Cinemas" customModuleProvider="target">
                                            <rect key="frame" x="0.0" y="0.0" width="40" height="40"/>
                                            <constraints>
                                                <constraint firstAttribute="width" constant="40" id="El1-rG-VCO"/>
                                                <constraint firstAttribute="height" constant="40" id="EzL-LU-9IX"/>
                                            </constraints>
                                            <userDefinedRuntimeAttributes>
                                                <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                    <real key="value" value="20"/>
                                                </userDefinedRuntimeAttribute>
                                            </userDefinedRuntimeAttributes>
                                        </imageView>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="EZH-xW-Ib9">
                                            <rect key="frame" x="50" y="0.0" width="41.5" height="20.5"/>
                                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                            <nil key="textColor"/>
                                            <nil key="highlightedColor"/>
                                        </label>
                                        <stackView opaque="NO" contentMode="scaleToFill" distribution="fillProportionally" alignment="firstBaseline" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="C7z-av-nLV">
                                            <rect key="frame" x="48" y="24" width="200" height="16"/>
                                            <subviews>
                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="nC2-TK-WGF">
                                                    <rect key="frame" x="0.0" y="0.0" width="73.5" height="16"/>
                                                    <fontDescription key="fontDescription" name="SourceSansPro-Bold" family="Source Sans Pro" pointSize="12"/>
                                                    <inset key="titleEdgeInsets" minX="4" minY="0.0" maxX="0.0" maxY="0.0"/>
                                                    <state key="normal" title="Standard" image="ic_user">
                                                        <color key="titleColor" red="0.13333333333333333" green="0.2196078431372549" blue="0.28627450980392155" alpha="1" colorSpace="calibratedRGB"/>
                                                    </state>
                                                    <connections>
                                                        <action selector="levelTapped:" destination="-1" eventType="touchUpInside" id="hzt-Sr-lBZ"/>
                                                    </connections>
                                                </button>
                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="54c-8U-Vom">
                                                    <rect key="frame" x="89.5" y="0.0" width="57" height="16"/>
                                                    <fontDescription key="fontDescription" name="SourceSansPro-Bold" family="Source Sans Pro" pointSize="12"/>
                                                    <inset key="titleEdgeInsets" minX="4" minY="0.0" maxX="0.0" maxY="0.0"/>
                                                    <state key="normal" title="20600" image="ic_home_point">
                                                        <color key="titleColor" red="0.1333333333" green="0.21960784310000001" blue="0.28627450980000002" alpha="1" colorSpace="calibratedRGB"/>
                                                    </state>
                                                    <connections>
                                                        <action selector="pointTapped:" destination="-1" eventType="touchUpInside" id="bCg-hu-f2y"/>
                                                    </connections>
                                                </button>
                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Yb1-Lc-8JD">
                                                    <rect key="frame" x="162.5" y="0.0" width="37.5" height="16"/>
                                                    <fontDescription key="fontDescription" name="SourceSansPro-Bold" family="Source Sans Pro" pointSize="12"/>
                                                    <inset key="titleEdgeInsets" minX="4" minY="0.0" maxX="0.0" maxY="0.0"/>
                                                    <state key="normal" title="03" image="ic_home_voucher">
                                                        <color key="titleColor" red="0.1333333333" green="0.21960784310000001" blue="0.28627450980000002" alpha="1" colorSpace="calibratedRGB"/>
                                                    </state>
                                                    <connections>
                                                        <action selector="voucherTapped:" destination="-1" eventType="touchUpInside" id="GZ4-R8-HSr"/>
                                                    </connections>
                                                </button>
                                            </subviews>
                                            <constraints>
                                                <constraint firstAttribute="width" constant="200" id="dhc-z1-LEi"/>
                                            </constraints>
                                        </stackView>
                                    </subviews>
                                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <constraints>
                                        <constraint firstItem="C7z-av-nLV" firstAttribute="bottom" secondItem="9F4-Ge-eOG" secondAttribute="bottom" id="ABr-2K-KIQ"/>
                                        <constraint firstItem="9F4-Ge-eOG" firstAttribute="centerY" secondItem="R8e-LF-mpn" secondAttribute="centerY" id="Ho8-4i-Bhp"/>
                                        <constraint firstItem="9F4-Ge-eOG" firstAttribute="leading" secondItem="R8e-LF-mpn" secondAttribute="leading" id="dnr-od-xpS"/>
                                        <constraint firstItem="C7z-av-nLV" firstAttribute="leading" secondItem="9F4-Ge-eOG" secondAttribute="trailing" constant="8" id="fcj-Ar-EsB"/>
                                        <constraint firstItem="EZH-xW-Ib9" firstAttribute="top" secondItem="9F4-Ge-eOG" secondAttribute="top" id="lJf-nI-8h4"/>
                                        <constraint firstItem="EZH-xW-Ib9" firstAttribute="leading" secondItem="9F4-Ge-eOG" secondAttribute="trailing" constant="10" id="mmz-Kg-J7Z"/>
                                    </constraints>
                                </view>
                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_home_beta" translatesAutoresizingMaskIntoConstraints="NO" id="9iN-Pd-iQo">
                                    <rect key="frame" x="242" y="10" width="70" height="40"/>
                                    <constraints>
                                        <constraint firstAttribute="width" priority="999" constant="70" id="hLP-FV-jR8"/>
                                        <constraint firstAttribute="width" secondItem="9iN-Pd-iQo" secondAttribute="height" multiplier="97:56" id="jbh-mQ-DFS"/>
                                    </constraints>
                                </imageView>
                            </subviews>
                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <constraints>
                                <constraint firstItem="9iN-Pd-iQo" firstAttribute="centerY" secondItem="jd5-iJ-eef" secondAttribute="centerY" id="2JG-8X-fbp"/>
                                <constraint firstAttribute="height" constant="60" id="4rO-Yg-oTr"/>
                                <constraint firstItem="IqD-3u-dZG" firstAttribute="centerY" secondItem="jd5-iJ-eef" secondAttribute="centerY" id="6LZ-aP-Prn"/>
                                <constraint firstItem="R8e-LF-mpn" firstAttribute="leading" secondItem="jd5-iJ-eef" secondAttribute="leading" constant="8" id="VL3-Mt-NLK"/>
                                <constraint firstAttribute="trailing" secondItem="9iN-Pd-iQo" secondAttribute="trailing" constant="8" id="bgR-6H-fcR"/>
                                <constraint firstItem="9iN-Pd-iQo" firstAttribute="leading" secondItem="R8e-LF-mpn" secondAttribute="trailing" constant="8" id="d24-2J-YYf"/>
                                <constraint firstItem="IqD-3u-dZG" firstAttribute="leading" secondItem="jd5-iJ-eef" secondAttribute="leading" constant="20" id="hBS-xB-tua"/>
                                <constraint firstItem="R8e-LF-mpn" firstAttribute="top" secondItem="jd5-iJ-eef" secondAttribute="top" constant="10" id="qhb-EJ-QGN"/>
                                <constraint firstAttribute="bottom" secondItem="R8e-LF-mpn" secondAttribute="bottom" constant="10" id="wz9-TS-6wt"/>
                            </constraints>
                        </view>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Ce6-ie-ygi" customClass="RoundView" customModule="Beta_Cinemas" customModuleProvider="target">
                            <rect key="frame" x="0.0" y="60" width="320" height="52"/>
                            <subviews>
                                <view alpha="0.10000000000000001" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="vl5-2j-ohf">
                                    <rect key="frame" x="0.0" y="0.0" width="320" height="1"/>
                                    <color key="backgroundColor" red="0.13333333333333333" green="0.2196078431372549" blue="0.28627450980392155" alpha="1" colorSpace="calibratedRGB"/>
                                    <constraints>
                                        <constraint firstAttribute="height" constant="1" id="uU9-TL-Zue"/>
                                    </constraints>
                                </view>
                                <stackView opaque="NO" contentMode="scaleToFill" distribution="fillProportionally" spacingType="standard" translatesAutoresizingMaskIntoConstraints="NO" id="bdF-RA-97r">
                                    <rect key="frame" x="0.0" y="9" width="320" height="35"/>
                                    <subviews>
                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="zEy-6y-XdT">
                                            <rect key="frame" x="0.0" y="0.0" width="86" height="35"/>
                                            <state key="normal" image="tab_coming_vi"/>
                                            <connections>
                                                <action selector="commingTapped:" destination="-1" eventType="touchUpInside" id="dTs-Gv-hZe"/>
                                            </connections>
                                        </button>
                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="TGb-yx-R8B">
                                            <rect key="frame" x="94" y="0.0" width="99" height="35"/>
                                            <state key="normal" image="tab_nowshowing_vi_selected"/>
                                            <connections>
                                                <action selector="showingTapped:" destination="-1" eventType="touchUpInside" id="VqE-W3-xuj"/>
                                            </connections>
                                        </button>
                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="8Wb-s3-VZA">
                                            <rect key="frame" x="201" y="0.0" width="119" height="35"/>
                                            <state key="normal" image="tab_sneak_vi"/>
                                            <connections>
                                                <action selector="earlyTapped:" destination="-1" eventType="touchUpInside" id="1fG-ia-ZY1"/>
                                            </connections>
                                        </button>
                                    </subviews>
                                </stackView>
                            </subviews>
                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <constraints>
                                <constraint firstItem="bdF-RA-97r" firstAttribute="leading" secondItem="Ce6-ie-ygi" secondAttribute="leading" id="KOw-lt-HcW"/>
                                <constraint firstAttribute="bottom" secondItem="bdF-RA-97r" secondAttribute="bottom" constant="8" id="Ok7-x9-ZZh"/>
                                <constraint firstItem="bdF-RA-97r" firstAttribute="top" secondItem="vl5-2j-ohf" secondAttribute="bottom" constant="8" id="QSS-q7-zG1"/>
                                <constraint firstItem="vl5-2j-ohf" firstAttribute="leading" secondItem="Ce6-ie-ygi" secondAttribute="leading" id="YoL-QJ-RIq"/>
                                <constraint firstItem="vl5-2j-ohf" firstAttribute="top" secondItem="Ce6-ie-ygi" secondAttribute="top" id="gYb-xb-eZ1"/>
                                <constraint firstAttribute="trailing" secondItem="bdF-RA-97r" secondAttribute="trailing" id="nOH-xQ-ZdP"/>
                                <constraint firstAttribute="trailing" secondItem="vl5-2j-ohf" secondAttribute="trailing" id="wcz-U4-J83"/>
                            </constraints>
                            <userDefinedRuntimeAttributes>
                                <userDefinedRuntimeAttribute type="color" keyPath="shadowColor">
                                    <color key="value" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                </userDefinedRuntimeAttribute>
                                <userDefinedRuntimeAttribute type="number" keyPath="shadowOpacity">
                                    <real key="value" value="0.10000000000000001"/>
                                </userDefinedRuntimeAttribute>
                                <userDefinedRuntimeAttribute type="point" keyPath="shadowOffset">
                                    <point key="value" x="0.0" y="2"/>
                                </userDefinedRuntimeAttribute>
                                <userDefinedRuntimeAttribute type="number" keyPath="shadowRadius">
                                    <real key="value" value="1"/>
                                </userDefinedRuntimeAttribute>
                            </userDefinedRuntimeAttributes>
                        </view>
                    </subviews>
                    <color key="backgroundColor" red="0.95686274509803915" green="0.96078431372549022" blue="0.96470588235294119" alpha="1" colorSpace="calibratedRGB"/>
                    <constraints>
                        <constraint firstItem="Ce6-ie-ygi" firstAttribute="top" secondItem="jd5-iJ-eef" secondAttribute="bottom" id="B8A-rz-ZUJ"/>
                        <constraint firstItem="Ce6-ie-ygi" firstAttribute="leading" secondItem="qbW-yB-Ieh" secondAttribute="leading" id="Bmo-Xb-Jwv"/>
                        <constraint firstAttribute="height" constant="112" id="MB4-Fy-KyM"/>
                        <constraint firstItem="jd5-iJ-eef" firstAttribute="top" secondItem="qbW-yB-Ieh" secondAttribute="top" id="XRc-LT-UV1"/>
                        <constraint firstItem="jd5-iJ-eef" firstAttribute="leading" secondItem="qbW-yB-Ieh" secondAttribute="leading" id="YtY-ci-oo5"/>
                        <constraint firstAttribute="trailing" secondItem="jd5-iJ-eef" secondAttribute="trailing" id="mdB-f5-W8C"/>
                        <constraint firstAttribute="bottom" secondItem="Ce6-ie-ygi" secondAttribute="bottom" id="xdc-9G-y80"/>
                        <constraint firstAttribute="trailing" secondItem="Ce6-ie-ygi" secondAttribute="trailing" id="y0J-bY-ggT"/>
                    </constraints>
                </view>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="2lS-Sd-EaK">
                    <rect key="frame" x="0.0" y="112" width="320" height="368"/>
                    <subviews>
                        <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="iDh-gn-L8X">
                            <rect key="frame" x="0.0" y="0.0" width="320" height="368"/>
                            <subviews>
                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="RqO-Xn-3cn">
                                    <rect key="frame" x="0.0" y="0.0" width="320" height="706"/>
                                    <subviews>
                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="qz5-Ld-FOx" customClass="ImageSlideshow" customModule="ImageSlideshow">
                                            <rect key="frame" x="8" y="16" width="304" height="110"/>
                                            <subviews>
                                                <pageControl opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" numberOfPages="3" translatesAutoresizingMaskIntoConstraints="NO" id="PKd-0t-cJZ">
                                                    <rect key="frame" x="8" y="92" width="115.5" height="26"/>
                                                </pageControl>
                                            </subviews>
                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <constraints>
                                                <constraint firstAttribute="width" secondItem="qz5-Ld-FOx" secondAttribute="height" multiplier="359:130" id="9TJ-CL-Ggx"/>
                                                <constraint firstAttribute="bottom" secondItem="PKd-0t-cJZ" secondAttribute="bottom" constant="-8" id="qN3-A9-MuP"/>
                                                <constraint firstItem="PKd-0t-cJZ" firstAttribute="leading" secondItem="qz5-Ld-FOx" secondAttribute="leading" constant="8" id="v0u-ia-wua"/>
                                            </constraints>
                                        </view>
                                        <collectionView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" dataMode="none" translatesAutoresizingMaskIntoConstraints="NO" id="sG4-zz-CqS">
                                            <rect key="frame" x="0.0" y="142" width="320" height="564"/>
                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <collectionViewFlowLayout key="collectionViewLayout" minimumLineSpacing="0.0" minimumInteritemSpacing="8" id="MeC-Bq-chx">
                                                <size key="itemSize" width="50" height="50"/>
                                                <size key="headerReferenceSize" width="0.0" height="0.0"/>
                                                <size key="footerReferenceSize" width="0.0" height="0.0"/>
                                                <inset key="sectionInset" minX="8" minY="0.0" maxX="8" maxY="0.0"/>
                                            </collectionViewFlowLayout>
                                            <connections>
                                                <outlet property="dataSource" destination="-1" id="J5N-GB-zMv"/>
                                                <outlet property="delegate" destination="-1" id="dPJ-Yj-E31"/>
                                            </connections>
                                        </collectionView>
                                    </subviews>
                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <constraints>
                                        <constraint firstAttribute="bottom" secondItem="sG4-zz-CqS" secondAttribute="bottom" id="EUK-Is-tau"/>
                                        <constraint firstAttribute="height" constant="706" id="OCQ-Ar-yRE"/>
                                        <constraint firstItem="qz5-Ld-FOx" firstAttribute="top" secondItem="RqO-Xn-3cn" secondAttribute="top" constant="16" id="WNk-LM-Aqi"/>
                                        <constraint firstItem="sG4-zz-CqS" firstAttribute="top" secondItem="qz5-Ld-FOx" secondAttribute="bottom" constant="16" id="X9C-Xf-K8t"/>
                                        <constraint firstAttribute="trailing" secondItem="sG4-zz-CqS" secondAttribute="trailing" id="axr-Jn-bbg"/>
                                        <constraint firstItem="sG4-zz-CqS" firstAttribute="leading" secondItem="RqO-Xn-3cn" secondAttribute="leading" id="gIe-Gz-FxL"/>
                                        <constraint firstItem="qz5-Ld-FOx" firstAttribute="leading" secondItem="RqO-Xn-3cn" secondAttribute="leading" constant="8" id="v7L-wd-xHn"/>
                                        <constraint firstAttribute="trailing" secondItem="qz5-Ld-FOx" secondAttribute="trailing" constant="8" id="wng-rf-aC7"/>
                                    </constraints>
                                </view>
                            </subviews>
                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <constraints>
                                <constraint firstItem="RqO-Xn-3cn" firstAttribute="top" secondItem="iDh-gn-L8X" secondAttribute="top" id="7ho-aP-PTB"/>
                                <constraint firstItem="RqO-Xn-3cn" firstAttribute="leading" secondItem="iDh-gn-L8X" secondAttribute="leading" id="MW8-AF-cBF"/>
                                <constraint firstAttribute="bottom" secondItem="RqO-Xn-3cn" secondAttribute="bottom" id="Xfk-tZ-QFe"/>
                                <constraint firstAttribute="trailing" secondItem="RqO-Xn-3cn" secondAttribute="trailing" id="ZiO-LV-cmN"/>
                            </constraints>
                        </scrollView>
                    </subviews>
                    <color key="backgroundColor" red="0.95686274509803915" green="0.96078431372549022" blue="0.96470588235294119" alpha="1" colorSpace="calibratedRGB"/>
                    <constraints>
                        <constraint firstAttribute="trailing" secondItem="iDh-gn-L8X" secondAttribute="trailing" id="5yN-EN-CRn"/>
                        <constraint firstItem="RqO-Xn-3cn" firstAttribute="height" secondItem="2lS-Sd-EaK" secondAttribute="height" priority="750" id="Cyz-9f-GK7"/>
                        <constraint firstItem="iDh-gn-L8X" firstAttribute="top" secondItem="2lS-Sd-EaK" secondAttribute="top" id="GEx-u1-1Xa"/>
                        <constraint firstItem="iDh-gn-L8X" firstAttribute="leading" secondItem="2lS-Sd-EaK" secondAttribute="leading" id="Z2P-TE-jM8"/>
                        <constraint firstAttribute="bottom" secondItem="iDh-gn-L8X" secondAttribute="bottom" id="lWd-Br-3jJ"/>
                        <constraint firstItem="RqO-Xn-3cn" firstAttribute="width" secondItem="2lS-Sd-EaK" secondAttribute="width" id="zf3-ed-rM9"/>
                    </constraints>
                </view>
            </subviews>
            <viewLayoutGuide key="safeArea" id="fnl-2z-Ty3"/>
            <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
            <constraints>
                <constraint firstItem="qbW-yB-Ieh" firstAttribute="top" secondItem="fnl-2z-Ty3" secondAttribute="top" id="8qj-da-rjB"/>
                <constraint firstItem="2lS-Sd-EaK" firstAttribute="top" secondItem="qbW-yB-Ieh" secondAttribute="bottom" id="9Ce-8q-nUW"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="bottom" secondItem="2lS-Sd-EaK" secondAttribute="bottom" id="Rzo-QD-MK0"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="qbW-yB-Ieh" secondAttribute="trailing" id="Wb0-w7-w2r"/>
                <constraint firstItem="qbW-yB-Ieh" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" id="cHk-a8-aK3"/>
                <constraint firstAttribute="trailing" secondItem="2lS-Sd-EaK" secondAttribute="trailing" id="kip-vJ-fhK"/>
                <constraint firstItem="2lS-Sd-EaK" firstAttribute="leading" secondItem="i5M-Pr-FkT" secondAttribute="leading" id="zVE-s0-0hB"/>
            </constraints>
            <point key="canvasLocation" x="131.*************" y="127.**************"/>
        </view>
    </objects>
    <designables>
        <designable name="9F4-Ge-eOG">
            <size key="intrinsicContentSize" width="23" height="23"/>
        </designable>
        <designable name="IqD-3u-dZG">
            <size key="intrinsicContentSize" width="88" height="17"/>
        </designable>
    </designables>
    <resources>
        <image name="ic_account" width="23" height="23"/>
        <image name="ic_home_beta" width="73" height="42"/>
        <image name="ic_home_point" width="16" height="16"/>
        <image name="ic_home_voucher" width="16" height="16"/>
        <image name="ic_user" width="16" height="16"/>
        <image name="tab_coming_vi" width="70" height="20"/>
        <image name="tab_nowshowing_vi_selected" width="81" height="19"/>
        <image name="tab_sneak_vi" width="110" height="19"/>
    </resources>
</document>
