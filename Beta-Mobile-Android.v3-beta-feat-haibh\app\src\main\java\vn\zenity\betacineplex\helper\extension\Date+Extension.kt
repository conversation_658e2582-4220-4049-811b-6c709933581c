package vn.zenity.betacineplex.helper.extension

import vn.zenity.betacineplex.app.App
import vn.zenity.betacineplex.global.Constant
import java.text.SimpleDateFormat
import java.util.*

fun Calendar.toStringFormat(format: String = Constant.DateFormat.default): String? {
    return try {
        SimpleDateFormat(format, if (App.shared().getCurrentLang() == "en") Locale.ENGLISH else Locale("vi", "vn")).format(time)
    } catch (_: Exception) {
        null
    }
}

fun Date.toStringFormat(format: String = Constant.DateFormat.default): String? {
    return try {
        SimpleDateFormat(format, if (App.shared().getCurrentLang() == "en") Locale.ENGLISH else Locale("vi", "vn")).format(this)
    } catch (_: Exception) {
        null
    }
}