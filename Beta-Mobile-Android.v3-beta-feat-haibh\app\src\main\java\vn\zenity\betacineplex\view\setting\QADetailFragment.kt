package vn.zenity.betacineplex.view.setting

import android.annotation.SuppressLint
import android.os.Build
import android.os.Bundle
import androidx.recyclerview.widget.LinearLayoutManager
import android.text.Html
import android.view.View
import android.view.ViewGroup
import com.thoughtbot.expandablerecyclerview.ExpandableRecyclerViewAdapter
import com.thoughtbot.expandablerecyclerview.models.ExpandableGroup
import com.thoughtbot.expandablerecyclerview.viewholders.ChildViewHolder
import com.thoughtbot.expandablerecyclerview.viewholders.GroupViewHolder
import kotlinx.android.synthetic.main.fragment_qadetail.*
import kotlinx.android.synthetic.main.item_content_notifi_in_list.view.*
import kotlinx.android.synthetic.main.item_title_notifi_in_list.view.*
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.base.BaseFragment
import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.helper.extension.getString
import vn.zenity.betacineplex.helper.extension.gone
import vn.zenity.betacineplex.helper.extension.inflate
import vn.zenity.betacineplex.model.TopicDetailModel

/**
 * Created by Zenity.
 */

class QADetailFragment : BaseFragment(), QADetailContractor.View {

    companion object {
        fun getInstance(id: String, title: String?): QADetailFragment {
            val frag = QADetailFragment()
            frag.id = id
            frag.titleQA = title
            return frag
        }
    }

    private var id = ""
    private var titleQA: String? = ""

    override fun showListQADetail(listQA: List<TopicDetailModel>) {
        activity?.runOnUiThread {
            adapter = Adapter(listQA)
            recyclerView.adapter = adapter
            adapter?.notifyDataSetChanged()
        }
    }

    private val presenter = QADetailPresenter()
    private var adapter: Adapter? = null

    override fun getPresenter(): IBasePresenter<IBaseView>? {
        return presenter as? IBasePresenter<IBaseView>
    }

    override fun isShowToolbar(): Boolean {
        return false
    }

    override fun getLayoutRes(): Int {
        return R.layout.fragment_qadetail
    }

    @SuppressLint("SetTextI18n")
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        recyclerView.layoutManager = androidx.recyclerview.widget.LinearLayoutManager(this.context)
        adapter = Adapter(listOf())
        tvTitle.text = "${R.string.question_and_answer.getString()} - $titleQA"
        recyclerView.adapter = adapter
        presenter.getQADetail(id)
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        adapter?.onSaveInstanceState(outState)
    }

    override fun onViewStateRestored(savedInstanceState: Bundle?) {
        super.onViewStateRestored(savedInstanceState)
        adapter?.onRestoreInstanceState(savedInstanceState)
    }

    inner class Adapter(notifes: List<TopicDetailModel>?) : ExpandableRecyclerViewAdapter<TitleHolder, ContentHolder>(notifes) {

        override fun onCreateGroupViewHolder(parent: ViewGroup, viewType: Int): TitleHolder {
            val view = parent.inflate(R.layout.item_title_notifi_in_list)
            return TitleHolder(view)
        }

        override fun onCreateChildViewHolder(parent: ViewGroup, viewType: Int): ContentHolder {
            val view = parent.inflate(R.layout.item_content_notifi_in_list)
            return ContentHolder(view)
        }

        override fun onBindChildViewHolder(holder: ContentHolder, flatPosition: Int, group: ExpandableGroup<*>, childIndex: Int) {
            holder.itemView.tvContent.text =
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                        Html.fromHtml((group as TopicDetailModel).AnswerContent
                                ?: "", Html.FROM_HTML_MODE_COMPACT).toString()
                    } else
                        Html.fromHtml((group as TopicDetailModel).AnswerContent
                                ?: "").toString()
        }

        override fun onBindGroupViewHolder(holder: TitleHolder, flatPosition: Int, group: ExpandableGroup<*>) {
            holder.itemView.setOnClickListener {
                var lastExpanded = -1
                val lastStatus = if (flatPosition >= expandableList.expandedGroupIndexes.size) false else expandableList.expandedGroupIndexes[flatPosition]
                for (i in 0 until expandableList.expandedGroupIndexes.size) {
                    if (expandableList.expandedGroupIndexes[i]) {
                        lastExpanded = i
                    }
                    expandableList.expandedGroupIndexes[i] = false
                }
                expandableList.expandedGroupIndexes[if (lastExpanded in 0..(flatPosition - 1)) (flatPosition - 1) else flatPosition] = !lastStatus
                notifyDataSetChanged()
            }
            holder.itemView.tvTitle.text = ((group as TopicDetailModel)).Content
            holder.itemView.tvDate.gone()
        }
    }

    inner class TitleHolder(itemView: View) : GroupViewHolder(itemView) {

        override fun expand() {
            super.expand()
            itemView.ivDropdown?.setImageResource(R.drawable.ic_zipup)
        }

        override fun collapse() {
            super.collapse()
            itemView.ivDropdown?.setImageResource(R.drawable.ic_dropdown)
        }

    }

    inner class ContentHolder(itemView: View) : ChildViewHolder(itemView)
}
