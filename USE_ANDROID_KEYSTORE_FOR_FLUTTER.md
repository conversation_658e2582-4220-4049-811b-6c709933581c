# 🔑 Sử dụng Android Keystore cho Flutter

## 🎯 **Tìm thấy Keystore trong Android Repo!**

Trong Android repo có folder `keystore` với các file keystore production:

### **📁 Android Repo Keystore Files:**

```
Beta-Mobile-Android.v3-beta-feat-haibh/keystore/
├── android key store.txt          # Password: Betacorpvn@123, Alias: Beta Cineplex
├── customer.keystore.txt          # Password: sdiidfjieiurier, Alias: customer
├── debug.keystore.txt             # Password: sdfoafojasdfji, Alias: debug
├── beta_cineplex_app_key.jks      # Production keystore
├── customer.keystore              # Customer keystore
└── debug.keystore                 # Debug keystore
```

### **🔧 Android Build Config:**

```gradle
// Beta-Mobile-Android.v3-beta-feat-haibh/app/build.gradle
signingConfigs {
    devconfig {
        keyAlias 'debug'
        keyPassword 'sdfoafojasdfji'
        storeFile file('../keystore/debug.keystore')
        storePassword 'sdfoafojasdfji'
    }

    cusconfig {
        keyAlias 'customer'
        keyPassword 'sdiidfjieiurier'
        storeFile file('../keystore/customer.keystore')
        storePassword 'sdiidfjieiurier'
    }

    prodconfig {
        keyAlias 'beta cineplex'
        keyPassword 'Betacorpvn@123'
        storeFile file('../keystore/beta_cineplex_app_key.jks')
        storePassword 'Betacorpvn@123'
    }
}
```

## ✅ **CÓ THỂ SỬ DỤNG CHO FLUTTER!**

### **Step 1: Copy Keystore Files**

```bash
# Copy keystore files từ Android repo sang Flutter repo
cp Beta-Mobile-Android.v3-beta-feat-haibh/keystore/* /path/to/flutter/android/keystore/

# Hoặc tạo symlink
ln -s /path/to/Beta-Mobile-Android.v3-beta-feat-haibh/keystore /path/to/flutter/android/keystore
```

### **Step 2: Create key.properties**

```bash
# Tạo file android/key.properties
cat > android/key.properties << EOF
storePassword=Betacorpvn@123
keyPassword=Betacorpvn@123
keyAlias=beta cineplex
storeFile=keystore/beta_cineplex_app_key.jksaaa
EOF
```

### **Step 3: Update Flutter build.gradle**

```gradle
// android/app/build.gradle

// Load keystore properties
def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

android {
    // ... existing config

    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }
        
        // Add production config từ Android repo
        prodconfig {
            keyAlias 'beta cineplex'
            keyPassword 'Betacorpvn@123'
            storeFile file('keystore/beta_cineplex_app_key.jks')
            storePassword 'Betacorpvn@123'
        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.prodconfig  // Use production keystore
            minifyEnabled false
            shrinkResources false
        }
    }
}
```

## 🔑 **Generate Facebook Key Hash từ Production Keystore**

### **Method 1: Sử dụng Production Keystore**

```bash
# Generate key hash từ production keystore
keytool -exportcert -alias "beta cineplex" -keystore android/keystore/beta_cineplex_app_key.jks -storepass Betacorpvn@123 -keypass Betacorpvn@123 | openssl sha1 -binary | openssl base64
```

### **Method 2: Script với Production Keystore**

```bash
#!/bin/bash
# generate_production_key_hash.sh

KEYSTORE_PATH="android/keystore/beta_cineplex_app_key.jks"
KEYSTORE_PASSWORD="Betacorpvn@123"
KEY_ALIAS="beta cineplex"
KEY_PASSWORD="Betacorpvn@123"

echo "🔑 Generating Facebook key hash from PRODUCTION keystore..."
echo "📁 Keystore: $KEYSTORE_PATH"
echo "🔑 Alias: $KEY_ALIAS"
echo ""

if [ ! -f "$KEYSTORE_PATH" ]; then
    echo "❌ Production keystore not found!"
    echo "💡 Copy from Android repo: Beta-Mobile-Android.v3-beta-feat-haibh/keystore/"
    exit 1
fi

KEY_HASH=$(keytool -exportcert -alias "$KEY_ALIAS" -keystore "$KEYSTORE_PATH" -storepass "$KEYSTORE_PASSWORD" -keypass "$KEY_PASSWORD" 2>/dev/null | openssl sha1 -binary | openssl base64)

if [ $? -eq 0 ] && [ ! -z "$KEY_HASH" ]; then
    echo "✅ Production key hash generated!"
    echo ""
    echo "🔑 ====================================="
    echo "🔑 PRODUCTION FACEBOOK KEY HASH:"
    echo "🔑 ====================================="
    echo "🔑 $KEY_HASH"
    echo "🔑 ====================================="
    echo ""
    echo "📋 Add this to Facebook Developer Console:"
    echo "1. https://developers.facebook.com/"
    echo "2. App: 367174740769877"
    echo "3. Settings → Basic → Android"
    echo "4. Add to 'Key Hashes' field"
else
    echo "❌ Failed to generate key hash"
fi
```

### **Method 3: PowerShell cho Windows**

```powershell
# generate_production_key_hash.ps1

$keystorePath = "android\keystore\beta_cineplex_app_key.jks"
$keystorePassword = "Betacorpvn@123"
$keyAlias = "beta cineplex"
$keyPassword = "Betacorpvn@123"

Write-Host "🔑 Generating Facebook key hash from PRODUCTION keystore..." -ForegroundColor Cyan
Write-Host "📁 Keystore: $keystorePath" -ForegroundColor Blue
Write-Host "🔑 Alias: $keyAlias" -ForegroundColor Blue
Write-Host ""

if (-not (Test-Path $keystorePath)) {
    Write-Host "❌ Production keystore not found!" -ForegroundColor Red
    Write-Host "💡 Copy from Android repo: Beta-Mobile-Android.v3-beta-feat-haibh\keystore\" -ForegroundColor Yellow
    exit 1
}

# Generate key hash using keytool
try {
    $keyHash = keytool -exportcert -alias $keyAlias -keystore $keystorePath -storepass $keystorePassword -keypass $keyPassword 2>$null | openssl sha1 -binary | openssl base64
    
    if ($keyHash -and $keyHash.Trim() -ne "") {
        Write-Host "✅ Production key hash generated!" -ForegroundColor Green
        Write-Host ""
        Write-Host "🔑 =====================================" -ForegroundColor Cyan
        Write-Host "🔑 PRODUCTION FACEBOOK KEY HASH:" -ForegroundColor Cyan
        Write-Host "🔑 =====================================" -ForegroundColor Cyan
        Write-Host "🔑 $($keyHash.Trim())" -ForegroundColor Yellow
        Write-Host "🔑 =====================================" -ForegroundColor Cyan
        Write-Host ""
        Write-Host "📋 Add this to Facebook Developer Console:" -ForegroundColor Green
        Write-Host "1. https://developers.facebook.com/" -ForegroundColor White
        Write-Host "2. App: 367174740769877" -ForegroundColor White
        Write-Host "3. Settings → Basic → Android" -ForegroundColor White
        Write-Host "4. Add to 'Key Hashes' field" -ForegroundColor White
    }
} catch {
    Write-Host "❌ Failed to generate key hash: $($_.Exception.Message)" -ForegroundColor Red
}
```

## 🎯 **Benefits của việc sử dụng Production Keystore**

### **✅ Advantages:**

1. **Same Key Hash**: Flutter và Android app sẽ có cùng key hash
2. **Production Ready**: Sử dụng keystore đã được verify trong production
3. **Consistent Signing**: Cùng signing certificate cho cả 2 apps
4. **Facebook Login**: Key hash sẽ match với Android app đã hoạt động

### **⚠️ Considerations:**

1. **Security**: Cần bảo mật keystore files
2. **Backup**: Backup keystore files quan trọng
3. **Access Control**: Chỉ dev team có access
4. **CI/CD**: Setup signing cho automated builds

## 📋 **Implementation Checklist**

- [ ] ✅ Copy keystore files từ Android repo
- [ ] ✅ Create `android/key.properties`
- [ ] ✅ Update `android/app/build.gradle`
- [ ] ✅ Generate production key hash
- [ ] ✅ Add key hash to Facebook Console
- [ ] ✅ Test Facebook login
- [ ] ✅ Test release build signing

## 🧪 **Testing Steps**

### **1. Test Production Build:**

```bash
# Build release APK với production keystore
flutter build apk --release

# Verify signing
jarsigner -verify -verbose -certs build/app/outputs/flutter-apk/app-release.apk
```

### **2. Test Facebook Login:**

```bash
# Install release APK
flutter install --release

# Test Facebook login trong app
# Should work với production key hash
```

### **3. Verify Key Hash:**

```bash
# Extract key hash từ built APK
unzip -p build/app/outputs/flutter-apk/app-release.apk META-INF/CERT.RSA | keytool -printcert | grep SHA1
```

## 🎉 **Expected Result**

```
✅ Flutter app signed với production keystore
✅ Facebook login works với production key hash
✅ Same signing certificate như Android app
✅ Ready for production deployment
```

## 🚀 **Quick Setup Commands**

### **Automated Setup:**

```bash
# 1. Setup production keystore
chmod +x setup_production_keystore.sh
./setup_production_keystore.sh

# 2. Generate Facebook key hash
chmod +x generate_production_key_hash.sh
./generate_production_key_hash.sh

# 3. Build release APK
flutter build apk --release
```

### **Manual Setup:**

```bash
# 1. Copy keystore files
mkdir -p android/keystore
cp ../Beta-Mobile-Android.v3-beta-feat-haibh/keystore/* android/keystore/

# 2. Create key.properties
cat > android/key.properties << EOF
storePassword=Betacorpvn@123
keyPassword=Betacorpvn@123
keyAlias=beta cineplex
storeFile=keystore/beta_cineplex_app_key.jks
EOF

# 3. Generate key hash
keytool -exportcert -alias "beta cineplex" -keystore android/keystore/beta_cineplex_app_key.jks -storepass Betacorpvn@123 -keypass Betacorpvn@123 | openssl sha1 -binary | openssl base64
```

### **Expected Output:**

```
🔑 =====================================
🔑 PRODUCTION FACEBOOK KEY HASH:
🔑 =====================================
🔑 [YOUR_PRODUCTION_KEY_HASH]
🔑 =====================================

📋 Add this to Facebook Developer Console:
1. https://developers.facebook.com/
2. App: 367174740769877
3. Settings → Basic → Android
4. Paste into 'Key Hashes' field
```

**Sử dụng production keystore từ Android repo sẽ đảm bảo Facebook login hoạt động chính xác!** 🚀
