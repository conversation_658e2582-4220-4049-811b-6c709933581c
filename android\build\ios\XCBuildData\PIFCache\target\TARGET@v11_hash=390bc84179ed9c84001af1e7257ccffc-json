{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f01052db69729086837946776b677573", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/just_audio/just_audio-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/just_audio/just_audio-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/just_audio/just_audio.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "just_audio", "PRODUCT_NAME": "just_audio", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b2f4b25a8d90636f47ae32244238e77c", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987173d5e1a890485807e71e6395272bb0", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/just_audio/just_audio-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/just_audio/just_audio-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/just_audio/just_audio.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "just_audio", "PRODUCT_NAME": "just_audio", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984e4d86d70890c21ed16cf87923a2cf2b", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987173d5e1a890485807e71e6395272bb0", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.22.3/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/just_audio/just_audio-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/just_audio/just_audio-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/just_audio/just_audio.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "just_audio", "PRODUCT_NAME": "just_audio", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987b7fb43e5cb40c6c38d1a3e45c7031a6", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982c13b008425192b7ecfb00a469aa9967", "guid": "bfdfe7dc352907fc980b868725387e98f50432fb543da2cb681feadb7db53c45", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc01657ae28e78aad19b791ad1d0262d", "guid": "bfdfe7dc352907fc980b868725387e9832fc697faa56797f5b8afb29bc276ffd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c2c6674b8a51434df77c24faf14a751", "guid": "bfdfe7dc352907fc980b868725387e98fc20be4da2c440287bbf1cbedcf94fd6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3fff3a5a352c6ec3edee83c615a91fe", "guid": "bfdfe7dc352907fc980b868725387e9813feee1561a7cd950e5c73a4daf26f14", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a569375d9c1491cd9ca9cd8324d526b", "guid": "bfdfe7dc352907fc980b868725387e9815d54c329cf2f0b4bd5b36b521240522", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98045fd4970fbbbedd40fd29909119a743", "guid": "bfdfe7dc352907fc980b868725387e98bdfbfd044d2ec3ab88047adcbef24500", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eaf9a9833c0cfbcbbbad0199da52e5ca", "guid": "bfdfe7dc352907fc980b868725387e9810610d177662453151cdc4b63a7d85e5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e33ae8541c14ea9145cf990fb2c944f", "guid": "bfdfe7dc352907fc980b868725387e987a065af2610f73e182d8aef95321d80e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98529138e9e94f379db064d8c51b3240ed", "guid": "bfdfe7dc352907fc980b868725387e987ad27b82047bac70ac3bcebe4685132e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e79a963e3060896b5040cc4e59cadc6", "guid": "bfdfe7dc352907fc980b868725387e98bd0c5a36bba2ed80ad99f5425ed2dea2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982bbdcb4dfbd00e7074e951baf9a51dbd", "guid": "bfdfe7dc352907fc980b868725387e98b52e497e9af1d2da3bb49d00caf084b0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869a91b18789600d1c78ed1e9dcc5441f", "guid": "bfdfe7dc352907fc980b868725387e9820284b959847fac503baa71b6165fd42", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98b1c6e0dac1c0f3288cdfd038dfd59299", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f99666d017e71f12a7beb99fa5fe0af0", "guid": "bfdfe7dc352907fc980b868725387e98d1fe2b05cf8888e79702cb844a1a04be"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a264099beac54dbbd66e77020f8a794d", "guid": "bfdfe7dc352907fc980b868725387e980d8eacccc49d84483f216968ca7ba0bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870acf6a8e946a9d42736b8a2488e0b9c", "guid": "bfdfe7dc352907fc980b868725387e987cc96cda263366425e0b2e833ef263ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980bef45e2d6f107cb8cedeba8a3ba2285", "guid": "bfdfe7dc352907fc980b868725387e9868b72e7a6235f1c92f364b80486692c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c288af17a2aa2e1776a65f82616c4d3", "guid": "bfdfe7dc352907fc980b868725387e987a46065074241f2ab00863937b2975af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988963509a6a0a9025ce87f6af89c1b10b", "guid": "bfdfe7dc352907fc980b868725387e98e3054d8768ec0d1ac05682b8e956d31d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea066953f4dbadb1e1f0ae550faaa758", "guid": "bfdfe7dc352907fc980b868725387e98c245691c4fde8c2526c0a55da880353d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3ed8d1aa864f2e093e2fe355414a689", "guid": "bfdfe7dc352907fc980b868725387e98b48d635eb12a83e92dded002cc742c7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986124906cd3ff85652807e40fd012e034", "guid": "bfdfe7dc352907fc980b868725387e98f892d0f6f1575ca88d966310c64a1c4d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98447858091dd739969116859099b86c79", "guid": "bfdfe7dc352907fc980b868725387e98a1baa3d39093094b02632391ea7de55f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989be07fd480532cd637a915b2cf043443", "guid": "bfdfe7dc352907fc980b868725387e9837e5cbaf52d88d601f03525db1fc0734"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98804adb7023a1b2a58f1e02ec48e43dd7", "guid": "bfdfe7dc352907fc980b868725387e989e732f409a6b27f50dd1d5b98e3923e8"}], "guid": "bfdfe7dc352907fc980b868725387e984828d07df21b03575280a435412b2bd2", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e984dff9b9164ea7278ab55766135fcbf91"}], "guid": "bfdfe7dc352907fc980b868725387e98e02b84aa74e21e5d0824e135f776cc74", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9898f5fd0d63861b94769f137ac6982066", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}], "guid": "bfdfe7dc352907fc980b868725387e98f1ed40c0d488e7d17a2574221f5de571", "name": "just_audio", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f09d8f416b4b8830579237c0f3add196", "name": "just_audio.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}