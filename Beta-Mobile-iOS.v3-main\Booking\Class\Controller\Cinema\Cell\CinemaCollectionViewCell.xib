<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14490.70" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14490.49"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="SourceSansPro-Bold.ttf">
            <string>SourceSansPro-Bold</string>
        </array>
        <array key="SourceSansPro-Regular.ttf">
            <string>SourceSansPro-Regular</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" id="gTV-IL-0wX" customClass="CinemaCollectionViewCell" customModule="Booking_dev" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="149" height="134"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="149" height="134"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="wkh-ij-Kxl">
                        <rect key="frame" x="0.0" y="0.0" width="149" height="134"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="p9I-dO-aZm">
                                <rect key="frame" x="0.0" y="78" width="149" height="56"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ggr-3C-7dq">
                                        <rect key="frame" x="8" y="9.5" width="133" height="21"/>
                                        <fontDescription key="fontDescription" name="SourceSansPro-Bold" family="Source Sans Pro" pointSize="16"/>
                                        <color key="textColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="calibratedRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="NLZ-1j-3wD">
                                        <rect key="frame" x="8" y="30.5" width="133" height="18"/>
                                        <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="14"/>
                                        <color key="textColor" red="0.01176470588" green="0.34901960780000002" blue="0.61568627450000002" alpha="1" colorSpace="calibratedRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="NLZ-1j-3wD" firstAttribute="leading" secondItem="p9I-dO-aZm" secondAttribute="leading" constant="8" id="99Y-MJ-1xD"/>
                                    <constraint firstAttribute="trailing" secondItem="NLZ-1j-3wD" secondAttribute="trailing" constant="8" id="Abd-05-M16"/>
                                    <constraint firstAttribute="trailing" secondItem="ggr-3C-7dq" secondAttribute="trailing" constant="8" id="IWS-ez-cYO"/>
                                    <constraint firstItem="NLZ-1j-3wD" firstAttribute="top" secondItem="ggr-3C-7dq" secondAttribute="bottom" id="Qvc-pF-uit"/>
                                    <constraint firstItem="ggr-3C-7dq" firstAttribute="leading" secondItem="p9I-dO-aZm" secondAttribute="leading" constant="8" id="RAx-Jb-bdv"/>
                                    <constraint firstItem="ggr-3C-7dq" firstAttribute="centerY" secondItem="p9I-dO-aZm" secondAttribute="centerY" constant="-8" id="UW6-J7-j6x"/>
                                    <constraint firstAttribute="height" constant="56" id="tnq-Ve-glN"/>
                                </constraints>
                            </view>
                            <imageView clipsSubviews="YES" clearsContextBeforeDrawing="NO" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="DJm-PF-lpJ">
                                <rect key="frame" x="0.0" y="0.0" width="149" height="78"/>
                                <color key="backgroundColor" red="0.99215686270000003" green="0.48627450979999998" blue="0.0078431372550000003" alpha="1" colorSpace="calibratedRGB"/>
                            </imageView>
                        </subviews>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstAttribute="trailing" secondItem="DJm-PF-lpJ" secondAttribute="trailing" id="96S-Og-AmD"/>
                            <constraint firstItem="DJm-PF-lpJ" firstAttribute="leading" secondItem="wkh-ij-Kxl" secondAttribute="leading" id="BAr-cJ-vaJ"/>
                            <constraint firstAttribute="bottom" secondItem="p9I-dO-aZm" secondAttribute="bottom" id="SmZ-fJ-tK3"/>
                            <constraint firstItem="DJm-PF-lpJ" firstAttribute="top" secondItem="wkh-ij-Kxl" secondAttribute="top" id="WyZ-q3-j4U"/>
                            <constraint firstAttribute="trailing" secondItem="p9I-dO-aZm" secondAttribute="trailing" id="dfW-3t-NaW"/>
                            <constraint firstItem="p9I-dO-aZm" firstAttribute="leading" secondItem="wkh-ij-Kxl" secondAttribute="leading" id="kaz-da-tGy"/>
                            <constraint firstItem="p9I-dO-aZm" firstAttribute="top" secondItem="DJm-PF-lpJ" secondAttribute="bottom" id="w2k-be-NKr"/>
                        </constraints>
                    </view>
                </subviews>
            </view>
            <constraints>
                <constraint firstItem="wkh-ij-Kxl" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="5ug-wO-52h"/>
                <constraint firstAttribute="bottom" secondItem="wkh-ij-Kxl" secondAttribute="bottom" id="J7N-rN-SpV"/>
                <constraint firstAttribute="trailing" secondItem="wkh-ij-Kxl" secondAttribute="trailing" id="LiN-B7-Dz0"/>
                <constraint firstItem="wkh-ij-Kxl" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" id="xvK-el-TDj"/>
            </constraints>
            <viewLayoutGuide key="safeArea" id="ZTg-uK-7eu"/>
            <size key="customSize" width="149" height="140"/>
            <connections>
                <outlet property="cinemaNameLabel" destination="ggr-3C-7dq" id="8oF-et-MIr"/>
                <outlet property="parentView" destination="wkh-ij-Kxl" id="Bo4-eY-bTG"/>
                <outlet property="pictureImageView" destination="DJm-PF-lpJ" id="VSI-Ke-TdS"/>
                <outlet property="rangeLabel" destination="NLZ-1j-3wD" id="QRr-c2-nod"/>
            </connections>
            <point key="canvasLocation" x="203.62318840579712" y="182.8125"/>
        </collectionViewCell>
    </objects>
</document>
