<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14109" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14088"/>
        <capability name="Aspect ratio constraints" minToolsVersion="5.1"/>
        <capability name="Constraints with non-1.0 multipliers" minToolsVersion="5.1"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="Oswald-Bold.ttf">
            <string>Oswald-Bold</string>
        </array>
        <array key="Oswald-Regular.ttf">
            <string>Oswald-Regular</string>
        </array>
        <array key="SourceSansPro-Regular.ttf">
            <string>SourceSansPro-Regular</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" rowHeight="233" id="KGk-i7-Jjw" customClass="FilmItemTableCell" customModule="Booking_dev" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="2359" height="233"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="2359" height="232.5"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="J18-Wl-TVF">
                        <rect key="frame" x="0.0" y="-0.5" width="2359" height="1044"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="2Iu-XD-EEp" customClass="RoundView" customModule="Booking_dev" customModuleProvider="target">
                                <rect key="frame" x="8" y="11.5" width="2462" height="1028"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="gfi-eJ-sJv">
                                        <rect key="frame" x="0.0" y="-0.5" width="781" height="1028"/>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    </view>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="2T7-Ue-pJE" customClass="FilmSeperateView" customModule="Booking_dev" customModuleProvider="target">
                                        <rect key="frame" x="793" y="-0.5" width="20" height="1028"/>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="20" id="j6m-W5-fEi"/>
                                        </constraints>
                                    </view>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="407-oq-gXl">
                                        <rect key="frame" x="817" y="8" width="1605" height="27"/>
                                        <string key="text">Ông Ngoại Tuổi 30Ông Ngoại Tuổi 30Ông Ngoại Tuổi 30Ông Ngoại Tuổi 30Ông Ngoại Tuổi 30Ông Ngoại Tuổi 30Ông Ngoại Tuổi 30Ông Ngoại Tuổi 30Ông Ngoại Tuổi 30Ông Ngoại Tuổi 30Ông Ngoại Tuổi 30 Ông Ngoại Tuổi 30</string>
                                        <fontDescription key="fontDescription" name="Oswald-Bold" family="Oswald" pointSize="18"/>
                                        <color key="textColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="(2D - LT)" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="IgK-M0-qGe">
                                        <rect key="frame" x="817" y="41" width="50.5" height="24"/>
                                        <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="16"/>
                                        <color key="textColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Võ thuật, Viễn Tưởng" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="GN3-Q9-P07">
                                        <rect key="frame" x="817" y="71" width="1637" height="20.5"/>
                                        <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                        <color key="textColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="135 phút" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="vvb-2n-u7x">
                                        <rect key="frame" x="816.5" y="98" width="1637" height="20.5"/>
                                        <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                        <color key="textColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <imageView userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_c13" translatesAutoresizingMaskIntoConstraints="NO" id="dr5-ay-SSU">
                                        <rect key="frame" x="883.5" y="44" width="36" height="18"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="36" id="Ol4-HW-yLO"/>
                                            <constraint firstAttribute="height" constant="18" id="iv9-fe-qfl"/>
                                        </constraints>
                                    </imageView>
                                    <imageView userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_top_1" translatesAutoresizingMaskIntoConstraints="NO" id="D1l-l5-Pkq">
                                        <rect key="frame" x="2430" y="8" width="24" height="37"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="24" id="R5M-ep-SNT"/>
                                            <constraint firstAttribute="height" constant="37" id="ikR-5l-DuB"/>
                                        </constraints>
                                    </imageView>
                                </subviews>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="trailing" secondItem="GN3-Q9-P07" secondAttribute="trailing" constant="8" id="6Yb-aZ-vAi"/>
                                    <constraint firstItem="GN3-Q9-P07" firstAttribute="top" secondItem="IgK-M0-qGe" secondAttribute="bottom" constant="6" id="6uH-HX-Qlv"/>
                                    <constraint firstAttribute="bottom" secondItem="2T7-Ue-pJE" secondAttribute="bottom" id="AhW-s9-j5t"/>
                                    <constraint firstAttribute="bottom" secondItem="gfi-eJ-sJv" secondAttribute="bottom" id="GPl-Y8-1Jc"/>
                                    <constraint firstItem="dr5-ay-SSU" firstAttribute="leading" secondItem="IgK-M0-qGe" secondAttribute="trailing" constant="16" id="HOa-OF-rzB"/>
                                    <constraint firstAttribute="trailing" secondItem="D1l-l5-Pkq" secondAttribute="trailing" constant="8" id="LoG-4c-L4i"/>
                                    <constraint firstItem="GN3-Q9-P07" firstAttribute="leading" secondItem="2T7-Ue-pJE" secondAttribute="trailing" constant="4" id="Q7q-R0-Eps"/>
                                    <constraint firstItem="2T7-Ue-pJE" firstAttribute="leading" secondItem="gfi-eJ-sJv" secondAttribute="trailing" constant="12" id="QCi-xJ-bsJ"/>
                                    <constraint firstItem="D1l-l5-Pkq" firstAttribute="top" secondItem="2Iu-XD-EEp" secondAttribute="top" constant="8" id="RbV-3q-KyY"/>
                                    <constraint firstItem="IgK-M0-qGe" firstAttribute="top" secondItem="407-oq-gXl" secondAttribute="bottom" constant="6" id="S0l-cm-JnL"/>
                                    <constraint firstItem="vvb-2n-u7x" firstAttribute="top" secondItem="GN3-Q9-P07" secondAttribute="bottom" constant="6" id="Teu-zY-7od"/>
                                    <constraint firstItem="vvb-2n-u7x" firstAttribute="leading" secondItem="2T7-Ue-pJE" secondAttribute="trailing" constant="4" id="ifz-xj-8kj"/>
                                    <constraint firstAttribute="trailing" secondItem="vvb-2n-u7x" secondAttribute="trailing" constant="8" id="jQR-GV-ZJU"/>
                                    <constraint firstItem="IgK-M0-qGe" firstAttribute="leading" secondItem="2T7-Ue-pJE" secondAttribute="trailing" constant="4" id="k2V-lg-QW0"/>
                                    <constraint firstItem="gfi-eJ-sJv" firstAttribute="top" secondItem="2Iu-XD-EEp" secondAttribute="top" id="kT6-z5-EY8"/>
                                    <constraint firstItem="gfi-eJ-sJv" firstAttribute="leading" secondItem="2Iu-XD-EEp" secondAttribute="leading" id="nBz-pf-0fW"/>
                                    <constraint firstItem="407-oq-gXl" firstAttribute="top" secondItem="2Iu-XD-EEp" secondAttribute="top" constant="8" id="nQx-ME-KAD"/>
                                    <constraint firstItem="2T7-Ue-pJE" firstAttribute="top" secondItem="2Iu-XD-EEp" secondAttribute="top" id="o8J-B6-VML"/>
                                    <constraint firstAttribute="bottom" relation="greaterThanOrEqual" secondItem="vvb-2n-u7x" secondAttribute="bottom" constant="4" id="trN-Qp-Bem"/>
                                    <constraint firstItem="D1l-l5-Pkq" firstAttribute="leading" secondItem="407-oq-gXl" secondAttribute="trailing" constant="8" id="uPg-h6-FjO"/>
                                    <constraint firstItem="dr5-ay-SSU" firstAttribute="centerY" secondItem="IgK-M0-qGe" secondAttribute="centerY" id="xO5-lX-FZu"/>
                                    <constraint firstItem="407-oq-gXl" firstAttribute="leading" secondItem="2T7-Ue-pJE" secondAttribute="trailing" constant="4" id="zHp-lw-m1a"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                        <real key="value" value="4"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="hra-6Q-0LZ" customClass="RoundView" customModule="Booking_dev" customModuleProvider="target">
                                <rect key="frame" x="16" y="3.5" width="781" height="1028"/>
                                <subviews>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="bg3.png" translatesAutoresizingMaskIntoConstraints="NO" id="lOw-Dj-98y" customClass="RoundImageView" customModule="Booking_dev" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="-0.5" width="780.5" height="1028"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                <real key="value" value="4"/>
                                            </userDefinedRuntimeAttribute>
                                        </userDefinedRuntimeAttributes>
                                    </imageView>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="oa8-q4-vng">
                                        <rect key="frame" x="360.5" y="484" width="60" height="60"/>
                                        <state key="normal" image="ic_play"/>
                                        <connections>
                                            <action selector="playButtonPressed:" destination="KGk-i7-Jjw" eventType="touchUpInside" id="Ade-G9-4ZF"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="lOw-Dj-98y" firstAttribute="leading" secondItem="hra-6Q-0LZ" secondAttribute="leading" id="3Lp-2f-Xc4"/>
                                    <constraint firstAttribute="bottom" secondItem="lOw-Dj-98y" secondAttribute="bottom" id="Aqu-YO-Nmc"/>
                                    <constraint firstItem="lOw-Dj-98y" firstAttribute="top" secondItem="hra-6Q-0LZ" secondAttribute="top" id="CxW-Qd-JX1"/>
                                    <constraint firstItem="oa8-q4-vng" firstAttribute="centerX" secondItem="hra-6Q-0LZ" secondAttribute="centerX" id="F61-jj-KHc"/>
                                    <constraint firstAttribute="width" secondItem="hra-6Q-0LZ" secondAttribute="height" multiplier="114:150" id="ZNK-Ae-J8z"/>
                                    <constraint firstItem="oa8-q4-vng" firstAttribute="centerY" secondItem="hra-6Q-0LZ" secondAttribute="centerY" id="sXw-6P-yj2"/>
                                    <constraint firstAttribute="trailing" secondItem="lOw-Dj-98y" secondAttribute="trailing" id="vAG-I4-XD7"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                        <real key="value" value="4"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="point" keyPath="shadowOffset">
                                        <point key="value" x="0.0" y="4"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="number" keyPath="shadowRadius">
                                        <real key="value" value="4"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="number" keyPath="shadowOpacity">
                                        <real key="value" value="0.25"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="color" keyPath="shadowColor">
                                        <color key="value" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </view>
                        </subviews>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="hra-6Q-0LZ" firstAttribute="top" secondItem="J18-Wl-TVF" secondAttribute="top" constant="4" id="0IC-tz-aVh"/>
                            <constraint firstAttribute="trailing" secondItem="2Iu-XD-EEp" secondAttribute="trailing" constant="8" id="6df-3F-NzT"/>
                            <constraint firstItem="2Iu-XD-EEp" firstAttribute="top" secondItem="J18-Wl-TVF" secondAttribute="top" constant="12" id="JQv-YS-KWM"/>
                            <constraint firstItem="2Iu-XD-EEp" firstAttribute="leading" secondItem="J18-Wl-TVF" secondAttribute="leading" constant="8" id="elE-Vl-VWP"/>
                            <constraint firstAttribute="bottom" secondItem="hra-6Q-0LZ" secondAttribute="bottom" constant="12" id="mE9-lu-wXa"/>
                            <constraint firstItem="gfi-eJ-sJv" firstAttribute="width" secondItem="hra-6Q-0LZ" secondAttribute="width" id="oDI-jA-AgG"/>
                            <constraint firstItem="hra-6Q-0LZ" firstAttribute="width" secondItem="2Iu-XD-EEp" secondAttribute="width" multiplier="114:359" id="oEl-2E-MQ1"/>
                            <constraint firstItem="hra-6Q-0LZ" firstAttribute="leading" secondItem="J18-Wl-TVF" secondAttribute="leading" constant="16" id="ohS-FJ-awx"/>
                            <constraint firstAttribute="bottom" secondItem="2Iu-XD-EEp" secondAttribute="bottom" constant="4" id="yYy-fj-Fsp"/>
                        </constraints>
                    </view>
                </subviews>
                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                <constraints>
                    <constraint firstAttribute="bottom" secondItem="J18-Wl-TVF" secondAttribute="bottom" id="2ci-3p-FeM"/>
                    <constraint firstItem="J18-Wl-TVF" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" id="MiZ-2X-639"/>
                    <constraint firstItem="J18-Wl-TVF" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" id="Uz4-LU-kFb"/>
                    <constraint firstAttribute="trailing" secondItem="J18-Wl-TVF" secondAttribute="trailing" id="bI0-Na-kcE"/>
                </constraints>
            </tableViewCellContentView>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <connections>
                <outlet property="btPlay" destination="oa8-q4-vng" id="NaL-Tp-K1o"/>
                <outlet property="ivAgeRate" destination="dr5-ay-SSU" id="Lwd-OO-3RO"/>
                <outlet property="ivFilmLogo" destination="lOw-Dj-98y" id="K40-bK-1ep"/>
                <outlet property="ivTop" destination="D1l-l5-Pkq" id="1ss-3S-IZa"/>
                <outlet property="lbFilmDuration" destination="vvb-2n-u7x" id="KX9-ES-bUp"/>
                <outlet property="lbFilmFormat" destination="IgK-M0-qGe" id="PWC-Um-K8d"/>
                <outlet property="lbFilmName" destination="407-oq-gXl" id="PfG-qF-gXd"/>
                <outlet property="lbFilmType" destination="GN3-Q9-P07" id="IqG-yh-APJ"/>
                <outlet property="vLeft" destination="gfi-eJ-sJv" id="n7p-fz-3d9"/>
            </connections>
            <point key="canvasLocation" x="1139.5" y="292.5"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="bg3.png" width="320" height="568"/>
        <image name="ic_c13" width="56" height="27"/>
        <image name="ic_play" width="60" height="60"/>
        <image name="ic_top_1" width="48" height="73"/>
    </resources>
</document>
