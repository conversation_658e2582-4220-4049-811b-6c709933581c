{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984f18b1e230b0195ed69ce22594bef844", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f06ccaf9e6efd54768b152be621694b8", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9895e428256ec1499087342c635a62fc7d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98aae449228e047ff90fd6a75fe9e2aae8", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9895e428256ec1499087342c635a62fc7d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987af2e6fb20f3407e6c184816642b7a29", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fe5bddc423ee9534b6a6c7c3661f6301", "guid": "bfdfe7dc352907fc980b868725387e98327ad9b84710666de64526e1ece7598e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987055ce46f1d1286bf25198c30d750236", "guid": "bfdfe7dc352907fc980b868725387e98c43ea7ee4e19efcc2bf2862f68aa5ced"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988eda7b0b8b637173fd00614a39f85a2d", "guid": "bfdfe7dc352907fc980b868725387e98c90a13d5ed648a4e495bd854e3666c4f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839b7dd5abf71c5ab5f161b9083af1e7e", "guid": "bfdfe7dc352907fc980b868725387e98f99d10f2a74c6c48e2cffa528a1048e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de51ffa78ec45d7efeb6728c98dd5f7e", "guid": "bfdfe7dc352907fc980b868725387e98858b8c901213bb04748a6a48639a56ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98624af64bbd1fd9ce6482ad543ec52328", "guid": "bfdfe7dc352907fc980b868725387e982fbef9f24961c4dd567c9ebe6dfe5ee2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6f8f4e05ff7df7f628cb2af26b16b0d", "guid": "bfdfe7dc352907fc980b868725387e9890e7e8a10611ed13a014b793a46b86d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987872cdaff08c122c1d69e73e5ef5b967", "guid": "bfdfe7dc352907fc980b868725387e982ffc458c3a0013106a58ed5888070243"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982170c4dd3ec297e05e5930e65010a277", "guid": "bfdfe7dc352907fc980b868725387e98968a8cc407acd7921ec8f40bd18498cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1f8cf48f1edf442840921e76165ec9b", "guid": "bfdfe7dc352907fc980b868725387e985db9436d5b4e4467e103575cbe918332"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee3421d29c3c4d5a863c3c03593af525", "guid": "bfdfe7dc352907fc980b868725387e988801babf65b551475a98b40efb01c2ea", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985cf2e12792e20c1dfc962e7fb408add6", "guid": "bfdfe7dc352907fc980b868725387e98bd3737c40b7e55f4cb39be8ff8477eda", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cf6c899a8b3819f6b9f59c5341834b3", "guid": "bfdfe7dc352907fc980b868725387e98497297518d1fcee8cefb3f6595ebb86f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813ebfde6922d8bed336968b1aea0392d", "guid": "bfdfe7dc352907fc980b868725387e9895f7b44187a7d26c7e81bb61587c1593"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980dafc746c80282d8740168d0a40c87b9", "guid": "bfdfe7dc352907fc980b868725387e98b208dbb66c977f4f306ab449b53043f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e80e3b3cfe9c6a845dbc97657c870fed", "guid": "bfdfe7dc352907fc980b868725387e981438f983a333e2a197df3348e4f34d03"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c597c915bff1fb9021ab9d9ddf70f6f5", "guid": "bfdfe7dc352907fc980b868725387e98a26aba9554adb509fac50165e214ea90"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d779da698ae61b0bfb51544831e40e6f", "guid": "bfdfe7dc352907fc980b868725387e987cead860ff4959d11d992245e9ad8144", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b53e0e0f8a8af9008e67680cd208bd5", "guid": "bfdfe7dc352907fc980b868725387e985a90e1385e3ac016414eb55036cb3c7f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ba9c9e95e9bec7c923642e49402265c", "guid": "bfdfe7dc352907fc980b868725387e987a1d4871c750550e952bd6cc800663a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985616bdaad4f2629e3694aec4770f6e18", "guid": "bfdfe7dc352907fc980b868725387e98157a7f1329d5a8c6f589ef3d4127001f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae1b798b0b93a8be09619a29b1fd5dda", "guid": "bfdfe7dc352907fc980b868725387e98e9a0cd39a446bf3255450493cc9152ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985109c4e07e01cc74329b6563e82934f3", "guid": "bfdfe7dc352907fc980b868725387e98520d5eff0c67ec2360bbcb1d49e031bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec29884dc0c613a4018e01bb9748f37a", "guid": "bfdfe7dc352907fc980b868725387e9870f16bd9493f72518c95f0ee642085d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5943f774874ea5ad8da9dc8e37a44c8", "guid": "bfdfe7dc352907fc980b868725387e9824752c71290d4a3cbb7c2419f6b8ca4f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc3fd3a569ad974cdec11a9cf7c70d7c", "guid": "bfdfe7dc352907fc980b868725387e984bca09d71e2e7cb438dd2498272c5e40"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822b24d36ff0e814ad681cc941ec8f588", "guid": "bfdfe7dc352907fc980b868725387e9881acffc0ccc32f07f62995c1ea862b08"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d27911c4e867e09cf32dd69048faa7c6", "guid": "bfdfe7dc352907fc980b868725387e98c0b251f7f62da9cc345ee2072efe0337"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832b38e4b5ac0ed6ccf5a6c6fb26e0ad2", "guid": "bfdfe7dc352907fc980b868725387e980534a2d4c538996bb306da5e20ae53b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985bf0df905b5b9d014ad2a18c441938a2", "guid": "bfdfe7dc352907fc980b868725387e98b824cfe1909a8634a7ae75e4426ef8e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af9886221714421a6047cad21d0533e6", "guid": "bfdfe7dc352907fc980b868725387e98a3bf17c6bf7ae4b9c99490c5a9344a38"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98661f3d3a7de36bb539913df5c49b78b8", "guid": "bfdfe7dc352907fc980b868725387e9815729f1905afff444a58657ecbd95feb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98813d6542e08439ba5ba84ab84f0a9a59", "guid": "bfdfe7dc352907fc980b868725387e98426c033ecf84153e97a375ec4da874f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6f9de9418e92dfb616450337da795b2", "guid": "bfdfe7dc352907fc980b868725387e9895a6d8c0ffe9bdd8dabb7826e70f3bb8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e0058c84f29c1ebff72a9a02eb626bc", "guid": "bfdfe7dc352907fc980b868725387e98874caadf3fb116b845822d147d6db098"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852c27066414e139680f3c9736d4707be", "guid": "bfdfe7dc352907fc980b868725387e9866bf9326d7d624bc097a07b00b571e5b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988230664110fb5eff250bf609dd4e97bf", "guid": "bfdfe7dc352907fc980b868725387e98fb56d2cd06b8c078e23b0bdf3bf63799"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98694f6f65b53787f986cbaeb5b163ec85", "guid": "bfdfe7dc352907fc980b868725387e9890652382d6ab7f093b59ade3c54407d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c20b656a56cc92279d9beee4d4d925f0", "guid": "bfdfe7dc352907fc980b868725387e98b8c5f1ccafada7b8dcac2dcf4c4c6459"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d609f54306e8d8bad3b193c342b54c2", "guid": "bfdfe7dc352907fc980b868725387e98581689c074af32346e374ecee8325893"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aecc4b6e085f5ddeab2f8c9f2a04c594", "guid": "bfdfe7dc352907fc980b868725387e986a351e567b850d5d20da2bb9a6f161d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7d0c0b3b96cef8898340bf776ea4b5d", "guid": "bfdfe7dc352907fc980b868725387e98970621fc2eb555be53aad92b19ef6dc3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98906a5f38951e5147674b9a7e2833ee15", "guid": "bfdfe7dc352907fc980b868725387e989cb5245385523a8f85889ee70f9f21b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984366985146e0ca21fef2a0948190c659", "guid": "bfdfe7dc352907fc980b868725387e98e637422cea3aec55609f309a3d4316cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f767bcee28cb4f1a41cdfcdd2f3f8252", "guid": "bfdfe7dc352907fc980b868725387e98f8c596d201c454b3d67df53b16668b6a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825a0a135c572d20e39577c07cea399b6", "guid": "bfdfe7dc352907fc980b868725387e980e31a79cee04e5b4e1d22def8894a05b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a657dfb46aa004e14a38734dac601c73", "guid": "bfdfe7dc352907fc980b868725387e98ca11df4370ca389231ef2ef8f32cbe2d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809fa6ca8e59ef82daeb36d3b5994aa08", "guid": "bfdfe7dc352907fc980b868725387e98fe6a7adfa12445029280b7335cf14e93"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c757a2809139e75ecd1c8570134d849d", "guid": "bfdfe7dc352907fc980b868725387e988266499b52852dceca20d92c83863395"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ecc7e05968d275689b51f7036aeed24d", "guid": "bfdfe7dc352907fc980b868725387e98bf5a5f094a0f90b5c2e10d84ff487f21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0ff973ebce25750196c92251f09f1e7", "guid": "bfdfe7dc352907fc980b868725387e982d053c1de35c2f240c371ce1ed589fa3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864ab88f164e5e36eddaa737214d1fcc3", "guid": "bfdfe7dc352907fc980b868725387e982c4d28eeaf9b93c1958def59674621e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f527b85e24aa80f3229bb89a218d3fe", "guid": "bfdfe7dc352907fc980b868725387e98204ccf622243c637f8397b94a29ce275"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984bcfdd8adf34b9a31889b8351db062e0", "guid": "bfdfe7dc352907fc980b868725387e988212e4aaa745e724301b9b1fd8ea27a5"}], "guid": "bfdfe7dc352907fc980b868725387e98d0f795f71c52df1a04af851321a84674", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980ee00532d79df158f0d142e2afc9813b", "guid": "bfdfe7dc352907fc980b868725387e9813d17eade1f6d56ef8e497f166114bbd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98388b3dc7d9cf708f26546481af8c93a6", "guid": "bfdfe7dc352907fc980b868725387e98beb426eda52476d121824a3ba02ed4e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98483fbe6c0f01dc37b81d066f695388a9", "guid": "bfdfe7dc352907fc980b868725387e980f4a3486f7c53f942ca7263c267f31ab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820d5deb02fc30a7f354e25b44479678b", "guid": "bfdfe7dc352907fc980b868725387e98c7a8455b3e95af7f955695517e4324eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dae1cb3d762831c0777fcfef1defd571", "guid": "bfdfe7dc352907fc980b868725387e987f7b868f97a7558642ecc2265eaabe44"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bab71710997b1e0af9395b616ad47805", "guid": "bfdfe7dc352907fc980b868725387e98ee3a6a9ba7e612443233e5d8b17f572c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807ab112fbe424f267c7c88f25e93743a", "guid": "bfdfe7dc352907fc980b868725387e98cf260ce5e3272c569da3036922b6c97e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b09d117792ce2b2483b31db437f660a2", "guid": "bfdfe7dc352907fc980b868725387e989f1b613b52c2dbdf4cc0df9a0d98633c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3addc9ab8b5d1a9c55718437de1e043", "guid": "bfdfe7dc352907fc980b868725387e98b1ef948a3b52e087274a00daca3fd046"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889b2f94453ac3934d1068d4586432301", "guid": "bfdfe7dc352907fc980b868725387e98e27f6ed33c2ad66d37c4a28dd7ae833d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987830fc20cce0c661a98a934d8724ef64", "guid": "bfdfe7dc352907fc980b868725387e98e6d777ae5c2a4787d79a82236b8e5c56"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1d7975dc46b28876b5debac04ff675d", "guid": "bfdfe7dc352907fc980b868725387e981f243de61115eb9ffd42239d32480203"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1d86f418e7541024e504617c01be1df", "guid": "bfdfe7dc352907fc980b868725387e988f1caafa7f07c9f23dbd2037052aa421"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b39ec0384cd79aee6c723b29340a1755", "guid": "bfdfe7dc352907fc980b868725387e9878603427ed0058443051665fff2648c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0eb0d855218e9a8d348570870aa9cea", "guid": "bfdfe7dc352907fc980b868725387e9855ef75a264eff503bd3c359c13d692d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc43dca1b0dcc3bc86ff0509d27428b8", "guid": "bfdfe7dc352907fc980b868725387e98c590cf767ff63006430fed13be6ce364"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fe6ece16ec3f915aa2af632c180ba30", "guid": "bfdfe7dc352907fc980b868725387e982815fa5a66c9edf7829f77609080a13d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fff31a4820aa9bfd6dd8fc375ddfd032", "guid": "bfdfe7dc352907fc980b868725387e983d5107ecd938b9e7d66a9f9ded1664fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea54fde367d5dd6db74deedd8cff415c", "guid": "bfdfe7dc352907fc980b868725387e98ffc1c1a2ca14126cdf9ec615bb5e9959"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c69f56854cc1f3566ea180ec89fe39b3", "guid": "bfdfe7dc352907fc980b868725387e98ea4dbc838dc34b9885382d5bc1259dc4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7ef36c542b394f028ebbd6a19e51959", "guid": "bfdfe7dc352907fc980b868725387e98db4737080eaaa3931a346b663edcafcf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8a32b991fc33c03da5da509aff419d4", "guid": "bfdfe7dc352907fc980b868725387e98e104423804c36ddb3a7e14e2ec6225f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb1190759729e8ab762e9125c1df8411", "guid": "bfdfe7dc352907fc980b868725387e98145421edb3401878b6a71a48ca24bcbc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb012bce79be418b1c5f2552831344cd", "guid": "bfdfe7dc352907fc980b868725387e9852021d4a66c3fe893190beb38ae6cc09"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98580fa90b5f3305752ce2e35a5d0e8e8e", "guid": "bfdfe7dc352907fc980b868725387e987075641aa74401444520610b544053bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985efae14fd67fda1755c5b1f2cc284f1e", "guid": "bfdfe7dc352907fc980b868725387e98a7371ae88c965502e203deb93704cc83"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf0ad6aaed66e46d971d2a0e5fec84f0", "guid": "bfdfe7dc352907fc980b868725387e98fea845ef9fd11dcc0dedc480ee4e5802"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811b69d377364bb630f2944c6278f686a", "guid": "bfdfe7dc352907fc980b868725387e985fc32c687a17af938ce77275e4c7b4ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862f0456d65a237488355fcf83ad00826", "guid": "bfdfe7dc352907fc980b868725387e98fe137c28a2ad701c8a66c3063bb74aa2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c78901cae404bfea5210214f9e88a634", "guid": "bfdfe7dc352907fc980b868725387e986e02cdc8a167b0c691aaad2b6569853f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836f9c24097bb54ee0353117af389028a", "guid": "bfdfe7dc352907fc980b868725387e9856d0ca99ebd777448bcbfeb3b954066d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983153449e579f1948db1b9e14677180a2", "guid": "bfdfe7dc352907fc980b868725387e98c3e7a3496265ae3fdd6c8b049291f80f"}], "guid": "bfdfe7dc352907fc980b868725387e98d66a4b5b291eec4adb973c0aead9b89b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e98fcabbf8ed874d8ac739db94b11894b6f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5f5979f0786f4a5ad9848f0349b1159", "guid": "bfdfe7dc352907fc980b868725387e98139329d93c17e52c726df12d7a7e7c1f"}], "guid": "bfdfe7dc352907fc980b868725387e98bbf37576e42da5d9efe61208967d6a3d", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e983fcea3cd25046423470afd1e51ed020a", "targetReference": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab"}], "guid": "bfdfe7dc352907fc980b868725387e98bd8b523f2e76a8f7d76612babb0e4438", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations"}, {"guid": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab", "name": "FirebaseMessaging-FirebaseMessaging_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98d3c8dfff2c580c352f83d3850ad17775", "name": "GoogleDataTransport"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e980062393f91a1d2d94e3e5ed3a5aa5da9", "name": "nanopb"}], "guid": "bfdfe7dc352907fc980b868725387e983da17a3564c774dfaa331fa07754d2bc", "name": "FirebaseMessaging", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b3b0fadaedeb0138a07668440d83e3b3", "name": "FirebaseMessaging.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}