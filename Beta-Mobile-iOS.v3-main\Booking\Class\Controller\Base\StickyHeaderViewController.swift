//
//  StickyHeaderViewController.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 4/24/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit

class StickyHeaderViewController: BaseViewController {
    fileprivate var headerStickyView: UIView?
    fileprivate var originPosition: CGPoint = .zero
    fileprivate var observedScrollView: UIScrollView?

    override func viewDidLoad() {
        super.viewDidLoad()

        // Do any additional setup after loading the view.
    }

    override func didReceiveMemoryWarning() {
        super.didReceiveMemoryWarning()
        // Dispose of any resources that can be recreated.
    }

    override func willMove(toParentViewController parent: UIViewController?) {
        if parent == nil {
            observedScrollView?.removeObserver(self, forKeyPath: "contentOffset")
        }
    }

    func registerHeaderView(_ view: UIView, in scrollView: UIScrollView) {
        scrollView.addObserver(self, forKeyPath: "contentOffset", options: .new, context: nil)
        observedScrollView = scrollView
        self.headerStickyView = view
        self.originPosition = view.frame.origin
    }

    override func observeValue(forKeyPath keyPath: String?, of object: Any?, change: [NSKeyValueChangeKey : Any]?, context: UnsafeMutableRawPointer?) {
        if let offset = change?[.newKey] as? CGPoint, keyPath == "contentOffset" {
            self.scrollViewDidScroll(offset)
        }
    }
}

extension StickyHeaderViewController {
    func scrollViewDidScroll(_ offset: CGPoint) {
        var frame = self.headerStickyView?.frame
        if offset.y <= 0 {
            frame?.origin = CGPoint(x: originPosition.x, y: originPosition.y + offset.y)
        } else {
            frame?.origin = originPosition
            self.headerStickyView?.transform = CGAffineTransform(scaleX: 1, y: 1)
        }
        self.headerStickyView?.frame = frame ?? .zero
    }
}
