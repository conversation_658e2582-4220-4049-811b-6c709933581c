package vn.zenity.betacineplex.view.event

import android.os.Bundle
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import kotlinx.android.synthetic.main.fragment_listevent.*
import kotlinx.android.synthetic.main.item_event_home.view.*
import load
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.base.BaseFragment
import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.model.Event
import vn.zenity.betacineplex.model.NewModel
import vn.zenity.betacineplex.model.NewsModel

/**
 * Created by Zenity.
 */

class ListEventFragment : BaseFragment(), ListEventContractor.View {

    companion object {
        fun getInstance(id: String): ListEventFragment{
            val frag = ListEventFragment()
            frag.id = id
            return frag
        }
    }

    private lateinit var adapter: Adapter
    private var id = ""

    override fun showListEvents(listEvents: List<NewsModel>) {
        adapter.events = listEvents
        activity?.runOnUiThread {
            adapter.notifyDataSetChanged()
        }
    }

    override fun openEvent(event: NewsModel) {
        openFragment(EventDetailFragment.getInstance(event))
    }

    private val presenter = ListEventPresenter()

    override fun getPresenter(): IBasePresenter<IBaseView>? {
        return presenter as? IBasePresenter<IBaseView>
    }

    override fun isShowToolbar(): Boolean {
        return false
    }

    override fun getLayoutRes(): Int {
        return R.layout.fragment_listevent
    }

    override fun onRefresh() {
        super.onRefresh()
        refreshView?.finishRefreshing()
        presenter.getListEvent(id, 1)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        recyclerView.layoutManager = androidx.recyclerview.widget.LinearLayoutManager(context)
        adapter = Adapter(listOf())
        recyclerView.adapter = adapter
        presenter.getListEvent(id, 1)
    }

    private inner class Adapter(var events: List<NewsModel>) : RecyclerView.Adapter<Holder>() {

        override fun getItemCount(): Int {
            return events.size
        }

        override fun onBindViewHolder(holder: Holder, position: Int) {
            val event = events[position]
            holder.itemView.ivEvent.load(event.Duong_dan_anh_dai_dien)
            holder.itemView.tvEventTitle.text = event.Tieu_de
            holder.itemView.setOnClickListener {
                openEvent(event)
            }
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): Holder {
            val item = LayoutInflater.from(parent.context).inflate(R.layout.item_event_home, parent, false)
            return Holder(item)
        }
    }

    private inner class Holder(itemView: View) : androidx.recyclerview.widget.RecyclerView.ViewHolder(itemView)
}
