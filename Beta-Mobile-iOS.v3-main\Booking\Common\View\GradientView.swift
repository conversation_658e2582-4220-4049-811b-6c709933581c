//
//  GradientView.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 4/4/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit

@IBDesignable
class GradientView: RoundView {
    fileprivate var gradientLayer: CAGradientLayer {
        return self.layer as! CAGradientLayer
    }

    @IBInspectable var firstColor: UIColor = .gradientBg1 {
        didSet {
            setNeedsLayout()
        }
    }

    @IBInspectable var secondColor: UIColor = .gradientBg2 {
        didSet {
            setNeedsLayout()
        }
    }

    @IBInspectable var startPoint: CGPoint = CGPoint(x: 0, y: 0.5) {
        didSet {
            setNeedsLayout()
        }
    }

    @IBInspectable var endPoint: CGPoint = CGPoint(x: 1, y: 0.5) {
        didSet {
            setNeedsLayout()
        }
    }

    var colors: [UIColor]? {
        didSet { setNeedsLayout() }
    }

    override public class var layerClass: Swift.AnyClass {
        return CAGradientLayer.self
    }

    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
    }

    override func layoutSubviews() {
        super.layoutSubviews()
        if self.colors?.isEmpty == false {
            self.gradientLayer.colors = colors!.map { $0.cgColor }
        } else {
            self.gradientLayer.colors = [firstColor.cgColor, secondColor.cgColor]
        }
        self.gradientLayer.startPoint = startPoint
        self.gradientLayer.endPoint = endPoint
    }
}
