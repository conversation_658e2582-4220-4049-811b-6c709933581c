package vn.zenity.betacineplex.base

import android.Manifest
import android.app.Activity
import android.app.AlertDialog
import androidx.lifecycle.LifecycleObserver
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.content.IntentSender
import android.content.pm.PackageManager
import android.graphics.Color
import android.location.Location
import android.location.LocationListener
import android.location.LocationManager
import android.os.Build
import android.os.Bundle
import android.os.Looper
import android.provider.Settings
import androidx.core.app.ActivityCompat
import androidx.appcompat.app.AppCompatActivity
import android.util.DisplayMetrics
import android.util.Log
import android.view.View
import android.view.WindowManager
import com.google.android.gms.common.ConnectionResult
import com.google.android.gms.common.GoogleApiAvailability
import com.google.android.gms.common.api.ApiException
import com.google.android.gms.common.api.CommonStatusCodes
import com.google.android.gms.common.api.GoogleApiClient
import com.google.android.gms.location.LocationRequest
import com.google.android.gms.location.LocationServices
import com.google.android.gms.location.LocationSettingsRequest
import com.google.android.gms.location.LocationSettingsStatusCodes
import com.google.android.gms.safetynet.SafetyNet
import com.google.android.gms.tasks.OnFailureListener
import com.google.android.gms.tasks.OnSuccessListener
import vn.zenity.betacineplex.BuildConfig
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.app.App
import vn.zenity.betacineplex.global.Config
import vn.zenity.betacineplex.global.Constant
import vn.zenity.betacineplex.helper.extension.bind
import vn.zenity.betacineplex.helper.extension.getColor
import vn.zenity.betacineplex.helper.extension.getString
import vn.zenity.betacineplex.helper.extension.toast
import vn.zenity.betacineplex.helper.thirtypart.LocaleHelper
import java.lang.ref.WeakReference
import java.util.concurrent.Executor


/**
 * Created by tranduc on 1/5/18.
 */
abstract class BaseActivity : AppCompatActivity(), LifecycleObserver, LocationListener, Executor {
    protected val REQUEST_CHECK_SETTINGS = 127

    companion object {
        val TAG = "BaseActivity"
    }

    private var googleApiClient: GoogleApiClient? = null
    protected var locationManager: LocationManager? = null

    open fun contentFragment(): BaseFragment? = null
    open fun isUsingBaseContent() = true

    open fun addContentFragmentIfEmpty() {
        Log.d(TAG, "addContentFragmentIfEmpty ")
        var fragment = supportFragmentManager.findFragmentById(R.id.contentLayout)
        if (fragment != null) {
            return
        }
        fragment = contentFragment()
        if (fragment == null) {
            return
        }

        Log.d(TAG, "addContentFragmentIfEmpty begin")
        val transaction = supportFragmentManager.beginTransaction()
        transaction.add(R.id.contentLayout, fragment)
        transaction.addToBackStack("Root")
        transaction.commit()

        Log.d(TAG, "addContentFragmentIfEmpty done")
    }

    val listenerLocationChange: ArrayList<WeakReference<(Location?) -> Unit>> = arrayListOf()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        settingTranfStatus()
        if (isUsingBaseContent()) {
            setContentView(R.layout.activity_base)
//            val lp = (contentLayout.layoutParams as ConstraintLayout.LayoutParams)
//            lp.bottomMargin = getSoftButtonsBarSizePort()
//            contentLayout.layoutParams = lp
        }
    }

    public fun settingTranfStatus() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            val w = window // in Activity's onCreate() for instance
            w.addFlags(WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS/*,
                    WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS*/)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                w.navigationBarColor = R.color.colorPrimaryDark.getColor()
            }
            w.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN or WindowManager.LayoutParams.SOFT_INPUT_STATE_HIDDEN)
        }
    }

    public fun disableTranfStatus() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            val w = window // in Activity's onCreate() for instance
            w.clearFlags(WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS)
            w.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)
        }
    }

    override fun attachBaseContext(newBase: Context?) {
        super.attachBaseContext(LocaleHelper.onAttach(newBase))
    }

    open fun openFragment(fragment: BaseFragment, addToBackStack: Boolean = true, name: String? = null) {
        if (bind<View>(R.id.contentLayout) != null) {
            val transaction = supportFragmentManager
                    .beginTransaction()
            transaction.setCustomAnimations(R.anim.enter_from_right, R.anim.exit_to_left,
                    R.anim.enter_from_left, R.anim.exit_to_right)
            transaction.add(R.id.contentLayout, fragment)
            if (addToBackStack) {
                transaction.addToBackStack(name)
            }
            transaction.commitAllowingStateLoss()
        }
    }

    fun popBackStack(name: String, flags: Int) {
        supportFragmentManager.popBackStack(name, flags)
    }

    fun popBackStackImmediate(name: String, flags: Int): Boolean {
        return supportFragmentManager.popBackStackImmediate(name, flags)
    }


    fun showConfirm(title: String? = null, content: String, rightButtonTitle: String = "Đồng ý", handlerRight: ((DialogInterface) -> Unit)? = null, leftButtonTitle: String? = null, handlerLeft: ((DialogInterface) -> Unit)? = null, handleShow: ((DialogInterface) -> Unit)? = null) {
        val dialog = AlertDialog.Builder(this)
        dialog.setCancelable(false)
        dialog.setPositiveButton(rightButtonTitle, null)
        title?.let {
            dialog.setTitle(title)
        }
        leftButtonTitle?.let {
            dialog.setNegativeButton(it, null)
        }
        dialog.setMessage(content)
        val diaInterface = dialog.create()
        diaInterface.setOnShowListener { diai ->
            val positive = diaInterface.getButton(AlertDialog.BUTTON_POSITIVE)
            positive?.setOnClickListener {
                handlerRight?.invoke(diai)
            }

            val negative = diaInterface.getButton(AlertDialog.BUTTON_NEGATIVE)
            negative?.setOnClickListener {
                handlerLeft?.invoke(diai)
            }

            handleShow?.invoke(diai)
        }
        diaInterface.show()
    }

    fun showMessage(content: String, leftButtonTitle: String = "OK") {
        val dialog = AlertDialog.Builder(this)
        dialog.setCancelable(false)
        leftButtonTitle?.let {
            dialog.setNegativeButton(it, null)
        }
        dialog.setMessage(content)
        val diaInterface = dialog.create()
        diaInterface.show()
    }

    protected open fun getSoftButtonsBarSizePort(): Int {
        // getRealMetrics is only available with API 17 and +
        val metrics = DisplayMetrics()
        this.windowManager.defaultDisplay.getMetrics(metrics)
        val usableHeight = metrics.heightPixels
        this.windowManager.defaultDisplay.getRealMetrics(metrics)
        val realHeight = metrics.heightPixels
        return if (realHeight > usableHeight)
            realHeight - usableHeight
        else
            0

    }

    fun isHasPlayService(): Boolean {
        val gApi = GoogleApiAvailability.getInstance()
        val status = gApi.isGooglePlayServicesAvailable(this)
        return status == ConnectionResult.SUCCESS
    }

    private var location: Location? = null

    fun getMyLocation(): Location? {
        return getMyLocation(false)
    }

    fun getMyLocation(isShowEnableLocation: Boolean, isRefresh: Boolean = false): Location? {
        if (location != null && isRefresh) {
            return location
        }
        if (!checkLocationPermission(isShowEnableLocation)) return null
        if (locationManager == null) {
            locationManager = getSystemService(Context.LOCATION_SERVICE) as LocationManager
        }
        val provider = getEnabledLocationProvider(applicationContext)
        if (provider == null) {
            if (isShowEnableLocation) {
                toast(getString(R.string.your_location_could_not_be_determined))
                enableLocation()
            }
            locationManager?.requestSingleUpdate(LocationManager.GPS_PROVIDER, this, Looper.getMainLooper())
            return null
        } else {
            if (locationManager == null) return null
            try {
//                locationManager?.requestLocationUpdates(
//                        provider, 1000, 50f, this)
                location = locationManager?.getLastKnownLocation(provider)
                locationManager?.requestSingleUpdate(provider, this, Looper.getMainLooper())
                if (location == null) {
                    locationManager?.requestSingleUpdate(provider, this, Looper.getMainLooper())
                    location = locationManager?.getLastKnownLocation(LocationManager.GPS_PROVIDER)
                    locationManager?.requestSingleUpdate(LocationManager.GPS_PROVIDER, this, Looper.getMainLooper())
                }
                if (location == null) {
                    LocationServices.getFusedLocationProviderClient(this).lastLocation.addOnCompleteListener { runnable ->
                        location = runnable.result
                        onLocationChanged(location ?: return@addOnCompleteListener)
                    }
                }
                return location
            } catch (e: Exception) {
                //                e.printStackTrace();
                return null
            }

        }
    }

    private fun getEnabledLocationProvider(context: Context): String? {
        if (locationManager == null) return null
        var gpsEnabled = false
        var networkEnabled = false
        var bestProvide: String? = null
        try {
            gpsEnabled = locationManager?.isProviderEnabled(LocationManager.GPS_PROVIDER) ?: false
        } catch (ex: Exception) {
            ex.printStackTrace()
        }

        try {
            networkEnabled = locationManager?.isProviderEnabled(LocationManager.NETWORK_PROVIDER) ?: false
        } catch (ex: Exception) {
            ex.printStackTrace()
        }
        if (gpsEnabled) {
            bestProvide = LocationManager.GPS_PROVIDER
        } else if (networkEnabled) {
            bestProvide = LocationManager.NETWORK_PROVIDER
        }
        return bestProvide
    }

    fun checkLocationPermission(): Boolean {
        return checkLocationPermission(false)
    }

    private fun checkLocationPermission(isShowEnabled: Boolean): Boolean {
        val result = !(ActivityCompat.checkSelfPermission(applicationContext, Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED || ActivityCompat.checkSelfPermission(applicationContext, Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED)
        if (!result && isShowEnabled) {
            ActivityCompat.requestPermissions(this,
                    arrayOf(android.Manifest.permission.ACCESS_FINE_LOCATION),
                    12)
        }
        return result
    }

    fun enableLocation() {
        if (googleApiClient == null || googleApiClient?.isConnected != true) {
            googleApiClient = GoogleApiClient.Builder(this)
                    .addApi(LocationServices.API)
                    .addConnectionCallbacks(object : GoogleApiClient.ConnectionCallbacks {
                        override fun onConnected(bundle: Bundle?) {
                            showDialogEnable()
                        }

                        override fun onConnectionSuspended(i: Int) {
                            googleApiClient?.connect()
                        }
                    })
                    .addOnConnectionFailedListener { connectionResult -> Log.d("Location error", "Location error " + connectionResult.errorCode) }.build()
            googleApiClient?.connect()
        } else {
            showDialogEnable()
        }
    }

    private fun showDialogEnable() {
        if (!isHasPlayService()) {
            displayPromptForEnablingGPS(this)
            return
        }
        val locationRequest = LocationRequest.create()
        locationRequest.priority = LocationRequest.PRIORITY_HIGH_ACCURACY
        locationRequest.interval = 1000
        locationRequest.fastestInterval = 10
        val builder = LocationSettingsRequest.Builder()
                .addLocationRequest(locationRequest)

        builder.setAlwaysShow(true)

        val result = LocationServices.SettingsApi.checkLocationSettings(googleApiClient, builder.build())
        result.setResultCallback { result1 ->
            val status = result1.status
            when (status.statusCode) {
                LocationSettingsStatusCodes.RESOLUTION_REQUIRED -> try {
                    // Show the dialog by calling startResolutionForResult(),
                    // and check the result in onActivityResult().
                    status.startResolutionForResult(this, REQUEST_CHECK_SETTINGS)

                    //                        finish();
                } catch (e: IntentSender.SendIntentException) {
                    // Ignore the error.
                }

            }
        }
    }

    fun displayPromptForEnablingGPS(activity: Activity) {
        val builder = androidx.appcompat.app.AlertDialog.Builder(activity)
        val action = Settings.ACTION_LOCATION_SOURCE_SETTINGS
        val message = App.shared().getString(R.string.do_you_want_open_GPS)

        builder.setMessage(message)
                .setPositiveButton(R.string.open.getString(),
                        DialogInterface.OnClickListener { d, id ->
                            activity.startActivity(Intent(action))
                            d.dismiss()
                        })
                .setNegativeButton(R.string.cancel.getString(),
                        DialogInterface.OnClickListener { d, id -> d.cancel() })
        builder.create().show()
    }

    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<out String>, grantResults: IntArray) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == 12) {
            for ((index, per) in permissions.withIndex()) {
                if (per == Manifest.permission.ACCESS_FINE_LOCATION && grantResults[index] == PackageManager.PERMISSION_GRANTED) {
                    getMyLocation(true, true)
                }
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == REQUEST_CHECK_SETTINGS) {
            if (resultCode == Activity.RESULT_OK) {
                val location = getMyLocation()
                for (listen in listenerLocationChange) {
                    listen.get()?.invoke(location)
                }
            }
        }
    }

    override fun onLocationChanged(location: Location) {
        this.location = location
        for (listen in listenerLocationChange) {
            listen.get()?.invoke(location)
        }
    }

    override fun onStatusChanged(s: String, i: Int, bundle: Bundle) {
    }

    override fun onProviderEnabled(s: String) {
        if (location == null && locationManager != null) {
            if (ActivityCompat.checkSelfPermission(this, Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED && ActivityCompat.checkSelfPermission(this, Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
                return
            }
            location = locationManager?.getLastKnownLocation(s)
        }
    }

    override fun onProviderDisabled(s: String) {
    }

    fun setLightStatusBar() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            var flags = window.decorView.systemUiVisibility
            flags = flags or View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR
            window.decorView.systemUiVisibility = flags
            window.statusBarColor = Color.WHITE
        }
    }

    fun clearLightStatusBar() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            var flags = window.decorView.systemUiVisibility
            flags = flags and View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR.inv()
            window.decorView.systemUiVisibility = flags
            window.statusBarColor = R.color.colorPrimaryDark.getColor()
        }
    }

    fun checkCaptcha(listener: (String?) -> Unit) {
        if (GoogleApiAvailability.getInstance().isGooglePlayServicesAvailable(this)
                == ConnectionResult.SUCCESS) {
            // The SafetyNet Attestation API is available.
        } else {
            listener("")
            return
        }
        val weak = WeakReference(listener)
        SafetyNet.getClient(this).verifyWithRecaptcha(BuildConfig.RE_CAPTCHA_KEY)
                .addOnSuccessListener {
                    // Indicates communication with reCAPTCHA service was
                    // successful.
                    val userResponseToken = it.tokenResult
                    weak.get()?.invoke(userResponseToken)
                    weak.clear()
                }
                .addOnFailureListener { e ->
                    if (e is ApiException) {
                        // An error occurred when communicating with the
                        // reCAPTCHA service. Refer to the status code to
                        // handle the error appropriately.
                        Log.d(TAG, "Error: ${CommonStatusCodes.getStatusCodeString(e.statusCode)}")
                    } else {
                        // A different, unknown type of error occurred.
                        Log.d(TAG, "Error: ${e.message}")
                    }
                    weak.get()?.invoke(null)
                    weak.clear()
                }.addOnCanceledListener {
                    weak.get()?.invoke(null)
                    weak.clear()
                }
    }

    override fun execute(p0: Runnable?) {
        p0?.run()
    }
}