//
//  FilmTimeTableViewCell.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 4/22/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit
import SwiftDate

protocol FilmeTimeDelegate: class {
    func didSelect(_ film: ShowFilmModel?, show: ShowModel)
}

class FilmTimeTableViewCell: UITableViewCell {

    @IBOutlet weak var lbFilmName: UILabel!
    @IBOutlet weak var lbFilmType: UILabel!
    @IBOutlet weak var lbFilmDuration: UILabel!
    @IBOutlet weak var ivAgeRate: UIImageView!
    @IBOutlet weak var ivTop1: UIImageView!
    @IBOutlet weak var ivFilmLogo: RoundImageView!
    @IBOutlet weak var tableView: UITableView!
    @IBOutlet weak var imvHot: UIImageView!
    
    weak var delegate: FilmeTimeDelegate?
    var film: ShowFilmModel?
    var cinema: CinemaModel?

    let cellId = "CinemaFilmTimeTableCell"

    var onPlayFilmTrailer: (() -> Void)?

    override func awakeFromNib() {
        super.awakeFromNib()
        // Initialization code
        selectionStyle = .none
        tableView.registerCell(id: cellId)
        tableView.separatorStyle = .none
        tableView.isScrollEnabled = false
    }

    override func updateViewWithItem(_ item: TableItem, indexPath: IndexPath) {
        guard let filmShow = item.data as? ShowFilmModel else {
            return
        }

        lbFilmName.text = filmShow.getName()
        lbFilmType.text = filmShow.getFilmGenre()
        lbFilmDuration.text = "\(filmShow.duration ?? 0) " + "Home.Minute".localized
        if filmShow.filmRestrictAgeName == FilmModel.RestrictAge.c13 {
            ivAgeRate.image = #imageLiteral(resourceName: "ic_c13")
        } else if filmShow.filmRestrictAgeName == FilmModel.RestrictAge.c16 {
            ivAgeRate.image = #imageLiteral(resourceName: "ic_c16")
        } else if filmShow.filmRestrictAgeName == FilmModel.RestrictAge.c18 {
            ivAgeRate.image = #imageLiteral(resourceName: "ic_c18")
        } else if filmShow.filmRestrictAgeName == FilmModel.RestrictAge.p {
            ivAgeRate.image = #imageLiteral(resourceName: "ic_p")
        } else {
            ivAgeRate.image = nil
        }

        let imageURL = Config.BaseURLResource + (filmShow.mainPosterUrl ?? "")
        if let url = URL(string: imageURL) {
            ivFilmLogo.af_setImage(withURL: url)
        }
        imvHot.isHidden = !filmShow.IsHot
        ivTop1.isHidden = indexPath.row != 0
        self.film = filmShow
        tableView.reloadData()
    }
    
    @IBAction func playButtonPressed(_ sender: Any) {
        onPlayFilmTrailer?()
    }
}

extension FilmTimeTableViewCell: CinemaFilmTimeDelegate {
    func didSelect(_ cinema: CinemaModel?, show: ShowModel, index: IndexPath?) {
        film?.indexPath = index
        delegate?.didSelect(film, show: show)
    }
}

extension FilmTimeTableViewCell: UITableViewDelegate, UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return film?.listFilm?.count ?? 0
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let cell = tableView.dequeueReusableCell(withIdentifier: cellId) as? CinemaFilmTimeTableCell else {
            return CinemaFilmTimeTableCell()
        }
        cell.configure(film?.listFilm?[indexPath.row], indexPath: indexPath)
        return cell
    }

    func tableView(_ tableView: UITableView, willDisplay cell: UITableViewCell, forRowAt indexPath: IndexPath) {
        let filmCell = cell as? CinemaFilmTimeTableCell
        filmCell?.cinema = self.cinema
        filmCell?.delegate = self
    }
}
