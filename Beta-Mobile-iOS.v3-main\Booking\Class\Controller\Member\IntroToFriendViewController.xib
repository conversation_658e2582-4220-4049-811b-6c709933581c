<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14490.70" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14490.49"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="Oswald-Light.ttf">
            <string>Oswald-Light</string>
        </array>
        <array key="Oswald-Regular.ttf">
            <string>Oswald-Regular</string>
        </array>
        <array key="SourceSansPro-Regular.ttf">
            <string>SourceSansPro-Regular</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="IntroToFriendViewController" customModule="Booking_dev" customModuleProvider="target">
            <connections>
                <outlet property="bottomCell" destination="SZa-nW-pZG" id="q9T-WB-1QT"/>
                <outlet property="codeLabel" destination="M62-et-Q3S" id="FrF-ac-ab0"/>
                <outlet property="codeTextField" destination="Shz-Uq-hvl" id="h77-f4-q41"/>
                <outlet property="confirmButton" destination="T1d-Hh-2Xw" id="uXq-PA-l0I"/>
                <outlet property="lb1" destination="tZi-aN-aYP" id="Kke-8A-JH1"/>
                <outlet property="lb2" destination="gkO-Rd-lcV" id="YLB-Dg-LSG"/>
                <outlet property="lb3" destination="uGt-Ad-qUG" id="Y6V-99-z02"/>
                <outlet property="tableView" destination="niN-A1-aPe" id="ig6-02-TBp"/>
                <outlet property="topCell" destination="jYz-9b-3Gl" id="WLC-mQ-M9W"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" style="plain" separatorStyle="default" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="niN-A1-aPe">
                    <rect key="frame" x="0.0" y="44" width="414" height="818"/>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <connections>
                        <outlet property="dataSource" destination="-1" id="HCE-MH-UAT"/>
                        <outlet property="delegate" destination="-1" id="aM4-0F-ozi"/>
                    </connections>
                </tableView>
            </subviews>
            <color key="backgroundColor" red="0.95294117649999999" green="0.95294117649999999" blue="0.95294117649999999" alpha="1" colorSpace="calibratedRGB"/>
            <constraints>
                <constraint firstAttribute="trailing" secondItem="niN-A1-aPe" secondAttribute="trailing" id="AU6-S8-oJv"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="bottom" secondItem="niN-A1-aPe" secondAttribute="bottom" id="BeU-vv-B6G"/>
                <constraint firstItem="niN-A1-aPe" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" id="qKX-nz-p0W"/>
                <constraint firstItem="niN-A1-aPe" firstAttribute="top" secondItem="fnl-2z-Ty3" secondAttribute="top" id="y7B-qR-mVK"/>
            </constraints>
            <viewLayoutGuide key="safeArea" id="fnl-2z-Ty3"/>
        </view>
        <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="default" indentationWidth="10" rowHeight="545" id="jYz-9b-3Gl">
            <rect key="frame" x="0.0" y="0.0" width="414" height="545"/>
            <autoresizingMask key="autoresizingMask"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="jYz-9b-3Gl" id="aDh-s8-PIv">
                <rect key="frame" x="0.0" y="0.0" width="414" height="544.5"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="jJW-uG-61m" customClass="RoundView" customModule="Booking_dev" customModuleProvider="target">
                        <rect key="frame" x="8" y="8" width="398" height="528.5"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_intro" translatesAutoresizingMaskIntoConstraints="NO" id="IbI-et-ghP">
                                <rect key="frame" x="0.0" y="0.0" width="398" height="106.5"/>
                                <constraints>
                                    <constraint firstAttribute="width" secondItem="IbI-et-ghP" secondAttribute="height" multiplier="359:96" id="ZL3-5q-ntO"/>
                                </constraints>
                            </imageView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="tZi-aN-aYP">
                                <rect key="frame" x="16" y="138.5" width="366" height="20.5"/>
                                <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                <color key="textColor" red="0.070588235289999995" green="0.**********" blue="0.15686274510000001" alpha="1" colorSpace="calibratedRGB"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="gkO-Rd-lcV">
                                <rect key="frame" x="16" y="167" width="366" height="20.5"/>
                                <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                <color key="textColor" red="0.070588235289999995" green="0.**********" blue="0.15686274510000001" alpha="1" colorSpace="calibratedRGB"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="tuC-jG-LW2" customClass="DashedBorderView" customModule="Booking_dev" customModuleProvider="target">
                                <rect key="frame" x="16" y="384.5" width="366" height="56"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="C6DDFC" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="M62-et-Q3S">
                                        <rect key="frame" x="136" y="4" width="94" height="48"/>
                                        <fontDescription key="fontDescription" name="Oswald-Light" family="Oswald" pointSize="32"/>
                                        <color key="textColor" red="0.01176470588" green="0.34901960780000002" blue="0.61568627450000002" alpha="1" colorSpace="calibratedRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="56" id="WB9-dv-PNU"/>
                                    <constraint firstItem="M62-et-Q3S" firstAttribute="centerX" secondItem="tuC-jG-LW2" secondAttribute="centerX" id="gHd-Ro-5mN"/>
                                    <constraint firstItem="M62-et-Q3S" firstAttribute="centerY" secondItem="tuC-jG-LW2" secondAttribute="centerY" id="pad-l2-Eis"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                        <color key="value" red="0.011764705882352941" green="0.34901960784313724" blue="0.61568627450980395" alpha="1" colorSpace="calibratedRGB"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                        <real key="value" value="8"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="number" keyPath="dashPaintedSize">
                                        <integer key="value" value="4"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="number" keyPath="dashUnpaintedSize">
                                        <integer key="value" value="4"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </view>
                            <button opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="AwB-TS-noU" customClass="GradientButton" customModule="Booking_dev" customModuleProvider="target">
                                <rect key="frame" x="16" y="456.5" width="366" height="56"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="56" id="WCv-73-Sr5"/>
                                </constraints>
                                <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="20"/>
                                <state key="normal" title="ĐĂNG KÝ">
                                    <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                </state>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                        <real key="value" value="4"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="color" keyPath="firstColor">
                                        <color key="value" red="0.99215686270000003" green="0.15686274510000001" blue="0.0078431372550000003" alpha="0.97999999999999998" colorSpace="custom" customColorSpace="sRGB"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="color" keyPath="secondColor">
                                        <color key="value" red="0.99215686270000003" green="0.48627450979999998" blue="0.0078431372550000003" alpha="0.98999999999999999" colorSpace="custom" customColorSpace="sRGB"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="FilmDetail.Share"/>
                                </userDefinedRuntimeAttributes>
                                <connections>
                                    <action selector="shareTapped:" destination="-1" eventType="touchUpInside" id="9i8-CZ-UnA"/>
                                </connections>
                            </button>
                        </subviews>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="gkO-Rd-lcV" firstAttribute="leading" secondItem="jJW-uG-61m" secondAttribute="leading" constant="16" id="9Tl-A2-W7d"/>
                            <constraint firstItem="tZi-aN-aYP" firstAttribute="top" secondItem="IbI-et-ghP" secondAttribute="bottom" constant="32" id="F0K-ur-Gkz"/>
                            <constraint firstItem="AwB-TS-noU" firstAttribute="top" secondItem="tuC-jG-LW2" secondAttribute="bottom" constant="16" id="FXA-Z2-Sc7"/>
                            <constraint firstAttribute="trailing" secondItem="tZi-aN-aYP" secondAttribute="trailing" constant="16" id="FoG-dj-VsV"/>
                            <constraint firstItem="AwB-TS-noU" firstAttribute="leading" secondItem="jJW-uG-61m" secondAttribute="leading" constant="16" id="HR3-FT-BUe"/>
                            <constraint firstItem="gkO-Rd-lcV" firstAttribute="top" secondItem="tZi-aN-aYP" secondAttribute="bottom" constant="8" id="K7C-jz-M0b"/>
                            <constraint firstAttribute="trailing" secondItem="gkO-Rd-lcV" secondAttribute="trailing" constant="16" id="Uas-Vh-cXM"/>
                            <constraint firstAttribute="trailing" secondItem="AwB-TS-noU" secondAttribute="trailing" constant="16" id="VOD-Xa-Ruk"/>
                            <constraint firstAttribute="bottom" secondItem="AwB-TS-noU" secondAttribute="bottom" constant="16" id="cMG-C1-s5l"/>
                            <constraint firstAttribute="trailing" secondItem="IbI-et-ghP" secondAttribute="trailing" id="dzb-uk-BYx"/>
                            <constraint firstItem="tuC-jG-LW2" firstAttribute="trailing" secondItem="AwB-TS-noU" secondAttribute="trailing" id="ig5-XV-Baw"/>
                            <constraint firstItem="tZi-aN-aYP" firstAttribute="leading" secondItem="jJW-uG-61m" secondAttribute="leading" constant="16" id="ww4-vj-2mj"/>
                            <constraint firstItem="IbI-et-ghP" firstAttribute="leading" secondItem="jJW-uG-61m" secondAttribute="leading" id="xL8-GH-BwD"/>
                            <constraint firstItem="IbI-et-ghP" firstAttribute="top" secondItem="jJW-uG-61m" secondAttribute="top" id="yRe-tR-ajL"/>
                            <constraint firstItem="tuC-jG-LW2" firstAttribute="leading" secondItem="AwB-TS-noU" secondAttribute="leading" id="yzb-Yd-BpO"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                <real key="value" value="5"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </view>
                </subviews>
                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                <constraints>
                    <constraint firstItem="jJW-uG-61m" firstAttribute="leading" secondItem="aDh-s8-PIv" secondAttribute="leading" constant="8" id="1j2-O6-yww"/>
                    <constraint firstAttribute="bottom" secondItem="jJW-uG-61m" secondAttribute="bottom" constant="8" id="OTe-Od-6ch"/>
                    <constraint firstAttribute="trailing" secondItem="jJW-uG-61m" secondAttribute="trailing" constant="8" id="RRN-7x-s6e"/>
                    <constraint firstItem="jJW-uG-61m" firstAttribute="top" secondItem="aDh-s8-PIv" secondAttribute="top" constant="8" id="zJT-EX-KiQ"/>
                </constraints>
            </tableViewCellContentView>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <point key="canvasLocation" x="863.768115942029" y="-22.433035714285712"/>
        </tableViewCell>
        <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="default" indentationWidth="10" rowHeight="239" id="SZa-nW-pZG">
            <rect key="frame" x="0.0" y="0.0" width="414" height="239"/>
            <autoresizingMask key="autoresizingMask"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="SZa-nW-pZG" id="QWe-yH-yOl">
                <rect key="frame" x="0.0" y="0.0" width="414" height="238.5"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="BFO-9F-C8a" customClass="RoundView" customModule="Booking_dev" customModuleProvider="target">
                        <rect key="frame" x="8" y="8" width="398" height="222.5"/>
                        <subviews>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="uGt-Ad-qUG">
                                <rect key="frame" x="16" y="24" width="366" height="20.5"/>
                                <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                <color key="textColor" red="0.070588235289999995" green="0.**********" blue="0.15686274510000001" alpha="1" colorSpace="calibratedRGB"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="KIC-e1-Cho">
                                <rect key="frame" x="16" y="60.5" width="366" height="50"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="T1d-Hh-2Xw" customClass="GradientButton" customModule="Booking_dev" customModuleProvider="target">
                                        <rect key="frame" x="234" y="0.0" width="132" height="50"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="132" id="LJo-yM-Kae"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="16"/>
                                        <state key="normal" title="XÁC NHẬN">
                                            <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        </state>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                <real key="value" value="3"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Bt.Confirm"/>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <action selector="useTapped:" destination="-1" eventType="touchUpInside" id="ucH-PT-rPL"/>
                                        </connections>
                                    </button>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="G5a-TH-kp5" customClass="RoundView" customModule="Booking_dev" customModuleProvider="target">
                                        <rect key="frame" x="8" y="0.0" width="218" height="50"/>
                                        <subviews>
                                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Mã giới thiệu ..." textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="Shz-Uq-hvl">
                                                <rect key="frame" x="8" y="0.0" width="202" height="50"/>
                                                <color key="textColor" red="0.070588235289999995" green="0.**********" blue="0.15686274510000001" alpha="1" colorSpace="calibratedRGB"/>
                                                <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                                <textInputTraits key="textInputTraits"/>
                                            </textField>
                                        </subviews>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="bottom" secondItem="Shz-Uq-hvl" secondAttribute="bottom" id="jbg-Cn-l14"/>
                                            <constraint firstItem="Shz-Uq-hvl" firstAttribute="leading" secondItem="G5a-TH-kp5" secondAttribute="leading" constant="8" id="nAI-Cc-a8d"/>
                                            <constraint firstAttribute="trailing" secondItem="Shz-Uq-hvl" secondAttribute="trailing" constant="8" id="pRH-Cv-boc"/>
                                            <constraint firstItem="Shz-Uq-hvl" firstAttribute="top" secondItem="G5a-TH-kp5" secondAttribute="top" id="wmF-iX-jfo"/>
                                        </constraints>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                                <real key="value" value="1"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                                <color key="value" red="0.58431372549019611" green="0.58431372549019611" blue="0.58431372549019611" alpha="1" colorSpace="calibratedRGB"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                <real key="value" value="5"/>
                                            </userDefinedRuntimeAttribute>
                                        </userDefinedRuntimeAttributes>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="trailing" secondItem="T1d-Hh-2Xw" secondAttribute="trailing" id="2xt-m7-5bH"/>
                                    <constraint firstAttribute="bottom" secondItem="T1d-Hh-2Xw" secondAttribute="bottom" id="CeX-j8-GoI"/>
                                    <constraint firstAttribute="height" constant="50" id="Ogx-kg-Rto"/>
                                    <constraint firstItem="T1d-Hh-2Xw" firstAttribute="leading" secondItem="G5a-TH-kp5" secondAttribute="trailing" constant="8" id="bng-aO-w6m"/>
                                    <constraint firstAttribute="bottom" secondItem="G5a-TH-kp5" secondAttribute="bottom" id="eep-k1-mKw"/>
                                    <constraint firstItem="G5a-TH-kp5" firstAttribute="leading" secondItem="KIC-e1-Cho" secondAttribute="leading" constant="8" id="erk-qa-V3d"/>
                                    <constraint firstItem="T1d-Hh-2Xw" firstAttribute="centerY" secondItem="KIC-e1-Cho" secondAttribute="centerY" id="hbz-Rs-Saa"/>
                                    <constraint firstItem="G5a-TH-kp5" firstAttribute="top" secondItem="KIC-e1-Cho" secondAttribute="top" id="vJE-wo-Qm3"/>
                                    <constraint firstItem="T1d-Hh-2Xw" firstAttribute="top" secondItem="KIC-e1-Cho" secondAttribute="top" id="wZS-Zk-NLA"/>
                                </constraints>
                            </view>
                        </subviews>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="uGt-Ad-qUG" firstAttribute="leading" secondItem="BFO-9F-C8a" secondAttribute="leading" constant="16" id="4DA-cg-c8I"/>
                            <constraint firstItem="uGt-Ad-qUG" firstAttribute="top" secondItem="BFO-9F-C8a" secondAttribute="top" constant="24" id="5zk-Zi-mmP"/>
                            <constraint firstAttribute="trailing" secondItem="uGt-Ad-qUG" secondAttribute="trailing" constant="16" id="6F6-vZ-cSx"/>
                            <constraint firstItem="KIC-e1-Cho" firstAttribute="top" secondItem="uGt-Ad-qUG" secondAttribute="bottom" constant="16" id="7Vj-Cf-cNF"/>
                            <constraint firstAttribute="trailing" secondItem="KIC-e1-Cho" secondAttribute="trailing" constant="16" id="oAm-mx-8dD"/>
                            <constraint firstItem="KIC-e1-Cho" firstAttribute="leading" secondItem="BFO-9F-C8a" secondAttribute="leading" constant="16" id="suz-Z5-QuD"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                <real key="value" value="5"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </view>
                </subviews>
                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                <constraints>
                    <constraint firstAttribute="trailing" secondItem="BFO-9F-C8a" secondAttribute="trailing" constant="8" id="YWm-Bm-A6I"/>
                    <constraint firstItem="BFO-9F-C8a" firstAttribute="leading" secondItem="QWe-yH-yOl" secondAttribute="leading" constant="8" id="iOD-oC-lJF"/>
                    <constraint firstAttribute="bottom" secondItem="BFO-9F-C8a" secondAttribute="bottom" constant="8" id="rc0-sA-xGV"/>
                    <constraint firstItem="BFO-9F-C8a" firstAttribute="top" secondItem="QWe-yH-yOl" secondAttribute="top" constant="8" id="x6k-F7-7lx"/>
                </constraints>
            </tableViewCellContentView>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <point key="canvasLocation" x="860.86956521739137" y="276.22767857142856"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="ic_intro" width="359" height="96"/>
    </resources>
</document>
