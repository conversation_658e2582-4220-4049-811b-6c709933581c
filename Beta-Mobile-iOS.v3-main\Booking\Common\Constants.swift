//
//  Constants.swift
//  ProjectBase
//
//  Created by <PERSON><PERSON> on 3/10/18.
//  Copyright © 2018 Me. All rights reserved.
//

import Foundation

enum DefaultKey: String {
    case firstTime
    case user
    case locationEnable
    case lastNotificationDate
    
    case deviceToken
    case userName
    case password
}

struct Constant {
    static let CurrentLocalization = "CurrentLanguage"
    static let Lat = "Lat"
    static let Lng = "Lng"
}

// Color
extension UIColor {
    static let gradientBg1 = 0x03599D.toColor
    static let gradientBg2 = 0x3FB7F9FC.toColor

    static let selected = UIColor(red: 3, green: 89, blue: 157)
    static let unselected = UIColor(red: 73, green: 76, blue: 98)

    static let border = UIColor(red: 149, green: 149, blue: 149, alpha: Int(255 * 0.6))
    static let textTitle = UIColor(red: 185, green: 185, blue: 185)
    static let inputText = UIColor(red: 30, green: 31, blue: 40)

    static let linkText = UIColor(red: 3, green: 89, blue: 157)

    static let viewBg = UIColor(red: 243, green: 243, blue: 243)
    static let pageControlActive = 0x3fb7f9.toColor
}

extension UITableViewCell {
    static let selectedColor: UIColor = .selected
    static let unselectedColor: UIColor = .unselected
}

extension NSNotification.Name{
    static let ChangeLocalization = NSNotification.Name.init(rawValue: "ChangeLocalization")
    static let CheckMomoOrderStatus = NSNotification.Name.init(rawValue: "CheckMomoOrderStatus")
    static let CheckZaloPayOrderStatus = NSNotification.Name.init(rawValue: "CheckZaloPayOrderStatus")
}
