//
//  Storyboard+Quick.swift
//  Booking-dev
//
//  Created by <PERSON><PERSON> on 3/27/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation

extension UIStoryboard {
    subscript(_ identifier: String) -> UIViewController {
        return instantiateViewController(withIdentifier: identifier)
    }

    subscript(_ id: Id) -> UIViewController {
        return self[id.rawValue]
    }
}
