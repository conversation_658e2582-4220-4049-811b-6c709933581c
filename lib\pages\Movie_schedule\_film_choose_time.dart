import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_app/constants/index.dart';
import 'package:flutter_app/core/index.dart';
import 'package:flutter_app/cubit/index.dart';
import 'package:flutter_app/pages/Movie_schedule/model/Film_model.dart';
import 'package:flutter_app/pages/Movie_schedule/widget/cinema_film_time_list.dart';
import 'package:flutter_app/pages/cinema/choose/signalr_classic_example.dart';
import 'package:flutter_app/pages/cinema/model/cinema_model.dart';
import 'package:flutter_app/pages/cinema/widget/calendar_header.dart';
import 'package:flutter_app/pages/cinema/widgets/vip_zoom_confirmation_dialog.dart';
import 'package:flutter_app/pages/voucher/api/api_test.dart';
import 'package:flutter_app/service/location_service.dart';
import 'package:flutter_app/utils/index.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fl_location/fl_location.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:go_router/go_router.dart';

import '_detail_screen.dart';
import 'select_region_screen.dart';

class FilmChooseTimeScreen extends StatefulWidget {
  final FilmModel film;
  final bool fromHome;

  const FilmChooseTimeScreen({super.key, required this.film, this.fromHome = false});

  @override
  State<FilmChooseTimeScreen> createState() => _FilmChooseTimeScreenState();
}

class _FilmChooseTimeScreenState extends State<FilmChooseTimeScreen> {
  DateTime? _selectedDate;
  CinemaProvinceModel? _selectedRegion;
  List<ShowCinemaModel> _showCinemaList = [];
  List<ShowCinemaModel> _allCinemaList = []; // Store all cinemas before filtering

  List<DateTime> _calendarDates = [];
  bool _isLoading = true;
  bool _isLoadingDates = true;
  String? _error;
  Location? _userPosition;

  // Services
  late final _filmService = RepositoryProvider.of<Api>(context).film;
  final _locationService = LocationService();

  @override
  void initState() {
    super.initState();
    _fetchFilmShowDates();
    _checkLocationAndGetNearestCinemas();
  }

  Future<void> _checkLocationAndGetNearestCinemas() async {
    setState(() {});

    try {
      // Check if location services are enabled
      bool serviceEnabled = await FlLocation.isLocationServicesEnabled;
      if (!serviceEnabled) {
        _showLocationServicesDisabledDialog();
        // FlLocation.requestLocationPermission();
        setState(() {});
        return;
      }

      // Check location permission
      LocationPermission permission = await FlLocation.checkLocationPermission();

      if (permission == LocationPermission.denied) {
        permission = await FlLocation.requestLocationPermission();
        if (permission == LocationPermission.denied) {
          _showLocationPermissionDeniedDialog();
          setState(() {});
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        _showLocationPermissionPermanentlyDeniedDialog();
        setState(() {});
        return;
      }
      // bool granted = await UrlLauncher().checkAndRequestPermission();
      // if (granted){
      //   print ('Location permission granted');
      //   final pos = await UrlLauncher().determinePosition();
      //   // Get current position
      //   _userPosition = pos ;
        _userPosition = await _locationService.determinePosition(
          showError: false,
          context: context,
        );

      // }
      // else {
      //   print(granted);
      // }

      setState(() {});

      // If we already have cinemas, update their distances
      if (_allCinemaList.isNotEmpty) {
        _updateCinemaDistances();
      }
    } catch (e) {
      setState(() {});
      print('Error getting location: $e');
    }
  }

  Future<void> _fetchFilmShowDates() async {
    setState(() {
      _isLoadingDates = true;
      _error = null;
      _showCinemaList = [];
    });

    try {
      final response = await _filmService.getFilmShowDate(id: widget.film.FilmId ?? '');

      if (response != null) {
        final List<dynamic> dateStrings = response.data['content'] ?? [];

        // Parse date strings to DateTime objects
        final List<DateTime> dates = dateStrings
            .map((dateStr) => DateTime.tryParse(dateStr.toString()))
            .where((date) => date != null)
            .cast<DateTime>()
            .toList();

        // Sort dates
        dates.sort((a, b) => a.compareTo(b));

        setState(() {
          _calendarDates = dates;
          _isLoadingDates = false;

          if (_calendarDates.isNotEmpty) {
            _selectedDate = _calendarDates.first;
            _fetchFilmShowsForDate(_selectedDate!);
          } else {
            _isLoading = false;
          }
        });
      } else {
        throw Exception('Failed to load show dates');
      }
    } catch (e) {
      setState(() {
        _isLoadingDates = false;
        _isLoading = false;
        _error = "Failed to load show dates: $e";
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to load show dates: $e')),
      );
    }
  }

  void _updateCinemaDistances() {
    if (_userPosition == null || _allCinemaList.isEmpty) return;

    // Update distances for all cinemas
    for (var cinema in _allCinemaList) {
      if (cinema.latitude != null && cinema.longitude != null) {
        double? lat = double.tryParse(cinema.latitude!);
        double? lng = double.tryParse(cinema.longitude!);

        if (lat != null && lng != null) {
          cinema.distance = _locationService.calculateDistance(
            _userPosition!.latitude,
            _userPosition!.longitude,
            lat,
            lng,
          );
        }
      }
    }

    // Sort all cinemas by distance
    _allCinemaList.sort((a, b) {
      double distA = a.distance ?? double.infinity;
      double distB = b.distance ?? double.infinity;
      return distA.compareTo(distB);
    });

    // Apply region filter and update displayed list
    setState(() {
      _showCinemaList = _applyRegionFilter(_allCinemaList);
    });
  }

  Future<void> _fetchFilmShowsForDate(DateTime date) async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    final dateString = DateFormat('yyyy-MM-dd').format(date);

    try {
      final response = await _filmService.getFilmShow(
        id: widget.film.FilmId ?? '',
        dateShow: dateString,
      );

      if (response != null) {
        final List<dynamic> cinemaData = response.data['content'] ?? [];
        final List<ShowCinemaModel> cinemas = cinemaData.map((item) => ShowCinemaModel.fromJson(item)).toList();

        // Store all cinemas

        // Update distances if location is available
        if (_userPosition != null) {
          for (var cinema in cinemas) {
            if (cinema.latitude != null && cinema.longitude != null) {
              double? lat = double.tryParse(cinema.latitude!);
              double? lng = double.tryParse(cinema.longitude!);

              if (lat != null && lng != null) {
                cinema.distance = _locationService.calculateDistance(
                  _userPosition!.latitude,
                  _userPosition!.longitude,
                  lat,
                  lng,
                );
              }
            }
          }

          // Sort cinemas by distance
          cinemas.sort((a, b) {
            double distA = a.distance ?? double.infinity;
            double distB = b.distance ?? double.infinity;
            return distA.compareTo(distB);
          });
        }

        setState(() {
          _allCinemaList = cinemas; // Store all cinemas
          _showCinemaList = _applyRegionFilter(cinemas); // Apply current region filter
          _isLoading = false;
        });
      } else {
        setState(() {
          _showCinemaList = [];
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _error = "Failed to load shows for $dateString: $e";
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to load shows: $e')),
      );
    }
  }

  void _showLocationServicesDisabledDialog() {
    UDialog().showConfirm(
        title: 'Location.ServiceDisabled'.tr(),
        text: 'Location.ServiceDisabledMessage'.tr(),
        btnOkText: 'Location.OpenSettings'.tr(),
        btnCancelText: 'Bt.Cancel'.tr(),
        btnOkOnPress: () {
          Navigator.of(context).pop();
          openAppSettings();
        },
        btnCancelOnPress: () {
          Navigator.of(context).pop();
        });
  }

  void _showLocationPermissionDeniedDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Location.PermissionDenied'.tr()),
          content: Text('Location.PermissionDeniedMessage'.tr()),
          actions: <Widget>[
            TextButton(
              child: Text('Bt.Close'.tr()),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  void _showLocationPermissionPermanentlyDeniedDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Location.PermissionPermanentlyDenied'.tr()),
          content: Text('Location.PermissionPermanentlyDeniedMessage'.tr()),
          actions: <Widget>[
            TextButton(
              child: Text('Bt.Cancel'.tr()),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: Text('Location.OpenSettings'.tr()),
              onPressed: () async {
                Navigator.of(context).pop();
                // openAppSettings();
                await FlLocation.requestLocationPermission();

              },
            ),
          ],
        );
      },
    );
  }

  void _onDateSelected(DateTime date) {
    setState(() {
      _selectedDate = date;
    });
    _fetchFilmShowsForDate(date);
  }

  /// Apply region filter to cinema list
  List<ShowCinemaModel> _applyRegionFilter(List<ShowCinemaModel> cinemas) {
    if (_selectedRegion == null) {
      // No region selected, show all cinemas
      return cinemas;
    }

    // Filter cinemas by selected region
    final regionCinemaIds = _selectedRegion!.listCinema?.map((c) => c.cinemaId).toSet() ?? {};
    return cinemas.where((cinema) => regionCinemaIds.contains(cinema.cinemaId)).toList();
  }

  /// Navigate to region selection screen
  Future<void> _selectRegion() async {
    final selectedRegion = await Navigator.push<CinemaProvinceModel?>(
      context,
      MaterialPageRoute(
        builder: (context) => const SelectRegionScreen(),
      ),
    );

    if (selectedRegion != _selectedRegion) {
      setState(() {
        _selectedRegion = selectedRegion;
        // Apply new filter to existing cinema list
        _showCinemaList = _applyRegionFilter(_allCinemaList);
      });
    }
  }

  /// Get display text for selected region
  String _getRegionDisplayText() {
    return _selectedRegion?.cityName ?? 'FilmBooking.All'.tr();
  }

  // Time validation - tương tự iOS getTimeLockDate validation
  bool _validateShowTime(ShowModel show) {
    try {
      // Use startTime from ShowModel - tương tự iOS getStartDate()
      final startTime = show.startTime;
      if (startTime == null) return false;

      // Calculate lock time (TimeToLock minutes before show) - tương tự iOS getTimeLockDate()
      final timeToLock = show.timeToLock ?? 30; // Default 30 minutes
      final lockTime = startTime.subtract(Duration(minutes: timeToLock));

      // Check if current time is before lock time
      return DateTime.now().isBefore(lockTime);
    } catch (e) {
      print('Error validating show time: $e');
      return false;
    }
  }

  @override
  Widget build(BuildContext context) {
    final String filmName = widget.film.getName() ?? 'Film Name';
    final String filmType = widget.film.getHalfOptions();
    final String bannerUrl = widget.film.MainPosterUrl ?? "";
    final String regionText = _getRegionDisplayText();

    return Scaffold(
      appBar: appBar(
        title: 'FilmBooking.BookByFilm'.tr(),
        titleColor: Colors.white,
      ),
      backgroundColor: Colors.grey.shade100,
      body: ListView(
        children: [
          // Header Section with film banner and info
          _buildHeader(bannerUrl, filmName, filmType),

          // Calendar for date selection
          _isLoadingDates
              ? const SizedBox(
                  height: 80,
                  child: Center(child: CircularProgressIndicator()),
                )
              : _calendarDates.isEmpty
                  ? SizedBox(
                      height: 80,
                      child: Center(child: Text('FilmBooking.NoShowDate'.tr())),
                    )
                  : CalendarHeaderView(
                      dates: _calendarDates,
                      onDateSelected: _onDateSelected,
                      selectedDate: _selectedDate,
                    ),

          // Region selector with location status
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                // Region selector row
                InkWell(
                  onTap: _selectRegion,
                  child: Row(
                    children: [
                      Expanded(
                        child: Text('Film.SelectRegion'.tr(), style: const TextStyle(fontSize: 18)),
                      ),
                      Text(
                        regionText,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                        ),
                      ),
                      const Icon(Icons.chevron_right),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Show list by cinema
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _error != null
                  ? Center(child: Text(_error!))
                  : _showCinemaList.isEmpty
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(Icons.movie_outlined, size: 64, color: Colors.grey.shade300),
                              const SizedBox(height: 16),
                              Text(
                                'FilmBooking.NoShowTime'.tr(),
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Colors.grey.shade600,
                                ),
                              ),
                            ],
                          ),
                        )
                      : CinemaFilmTimeList(
                          cinemas: _showCinemaList,
                          film: widget.film,
                          // onShowSelected: _onShowTimeSelected,
                        ),
        ],
      ),
    );
  }

  Widget _buildHeader(String bannerUrl, String filmName, String filmType) {
    // Replicates the header structure with banner, gradient, name, type
    return Stack(
      alignment: Alignment.center,
      children: [
        // Banner image
        Container(
          height: 180,
          color: Colors.grey.shade200,
          child: bannerUrl.isNotEmpty
              ? Image.network(
                  '${ApiService.baseUrlImage}/$bannerUrl',
                  fit: BoxFit.cover,
                  width: double.infinity,
                  errorBuilder: (context, error, stackTrace) {
                    return const Center(
                      child: Icon(Icons.movie, size: 50, color: Colors.grey),
                    );
                  },
                )
              : const Center(
                  child: Icon(Icons.movie, size: 50, color: Colors.grey),
                ),
        ),
        // Gradient overlay
        Container(
          height: 180,
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.bottomCenter,
              end: Alignment.topCenter,
              colors: [Colors.white70, Colors.white60, Colors.black54],
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                filmName,
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      color: Colors.black,
                      fontWeight: FontWeight.bold,
                    ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                filmType,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: Colors.black, fontSize: 18),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              InkWell(
                  onTap: () {
                    Navigator.push(
                        context, MaterialPageRoute(builder: (context) => FilmDetailScreen(film: widget.film)));
                  },
                  child: Container(
                      padding: const EdgeInsets.all(CSpace.base),
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(25), border: Border.all(color: Colors.indigo.shade900)),
                      child: Text(
                        'FilmBooking.FilmDetail'.tr(),
                        style: const TextStyle(color: Colors.indigo, fontWeight: FontWeight.bold, fontSize: 16),
                      )))
            ],
          ),
        ),
      ],
    );
  }
}
