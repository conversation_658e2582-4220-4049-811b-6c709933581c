//
//  CinemaCollectionViewCell.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 8/19/19.
//  Copyright © 2019 ddkc. All rights reserved.
//

import UIKit

class CinemaCollectionViewCell: UICollectionViewCell {

    @IBOutlet weak var pictureImageView: UIImageView!
    @IBOutlet weak var cinemaNameLabel: UILabel!
    @IBOutlet weak var rangeLabel: UILabel!
    @IBOutlet weak var parentView: UIView!
    
    override func awakeFromNib() {
        super.awakeFromNib()
        parentView.layer.cornerRadius = 5.0
        parentView.clipsToBounds = true
    }

    func configure(_ cinema: CinemaModel) {
        if let url = cinema.pictureURL {
            pictureImageView.af_setImage(withURL: url)
            cinemaNameLabel.text = cinema.getName()
            rangeLabel.text = "\(cinema.getDistance()) km"
        }
    }

}
