//
//  NewsModel.swift
//  Booking
//
//  Created by Tinh Vu on 4/5/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation
import ObjectMapper

class NewsModel : Mappable {
     var StorylineID : String?
     var StorylineType : Int?
     var Tieu_de : String?
     var Tieu_de_ko_dau : String?
     var Tieu_de_phu : String?
     var Tieu_de_limit : String?
     var Tieu_de_phu_copy : String?
     var Truc_tiep : Int?
     var Duong_dan_anh_dai_dien : String?
     var Tieu_de_anh : String?
     var Tieu_de_anh_ko_dau : String?
     var Tom_tat_noi_dung : String?
     var Tom_tat_noi_dung_phu : String?
     var Tom_tat_noi_dung_limit : String?
     var Tom_tat_anh_dai_dien : String?
     var PublishOnDate : String?
     var Breadcrumbs : String?
     var NewsURI : String?
     var Duong_dan_video_dai_dien : String?
     var KeyWords : String?
    var Noi_dung_chi_tiet: [Content]?

    var contents: String {
        return (Noi_dung_chi_tiet?.map({ (content) -> String in
            return content.ParagraphData?.ParagraphContent ?? ""
        }) ?? []).joined(separator: "\n")
    }

    required init?(map: Map) {

    }

    func mapping(map: Map) {
        StorylineID          <- map["StorylineID"]
        StorylineType        <- map["StorylineType"]
        Tieu_de              <- map["Tieu_de"]
        Tieu_de_ko_dau       <- map["Tieu_de_ko_dau"]
        Tieu_de_phu          <- map["Tieu_de_phu"]
        Tieu_de_limit        <- map["Tieu_de_limit"]
        Tieu_de_phu_copy     <- map["Tieu_de_phu_copy"]
        Truc_tiep            <- map["Truc_tiep"]
        Duong_dan_anh_dai_dien <- map["Duong_dan_anh_dai_dien"]
        Tieu_de_anh          <- map["Tieu_de_anh"]
        Tieu_de_anh_ko_dau   <- map["Tieu_de_anh_ko_dau"]
        Tom_tat_noi_dung     <- map["Tom_tat_noi_dung"]
        Tom_tat_noi_dung_phu <- map["Tom_tat_noi_dung_phu"]
        Tom_tat_noi_dung_limit <- map["Tom_tat_noi_dung_limit"]
        Tom_tat_anh_dai_dien <- map["Tom_tat_anh_dai_dien"]
        PublishOnDate        <- map["PublishOnDate"]
        Breadcrumbs          <- map["Breadcrumbs"]
        NewsURI              <- map["NewsURI"]
        Duong_dan_video_dai_dien <- map["Duong_dan_video_dai_dien"]
        KeyWords        <- map["KeyWords"]
        Noi_dung_chi_tiet <- map["Noi_dung_chi_tiet"]
    }

    func getNewsURL() -> String {
        return Config.BaseURLWeb + (NewsURI ?? "")
    }
    
    func getStartDateString() -> String?{
        guard let dateString = PublishOnDate, let date = Date.dateFromServer(dateString) else {
            return ""
        }
        return date.toStringStandard()
    }
    
    func getStartDate() -> Date{
        guard let dateString = PublishOnDate else {return Date()}
        return Date.dateFromServer(dateString) ?? Date()
    }
}
