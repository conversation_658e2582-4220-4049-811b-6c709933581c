package vn.zenity.betacineplex.view.film

import com.google.gson.Gson
import io.reactivex.disposables.Disposable
import okhttp3.MediaType
import okhttp3.RequestBody
import org.json.JSONArray
import org.json.JSONException
import vn.zenity.betacineplex.Manager.Network.APIClient
import vn.zenity.betacineplex.global.Tracking
import vn.zenity.betacineplex.helper.extension.applyOn
import vn.zenity.betacineplex.helper.extension.toDate
import vn.zenity.betacineplex.model.RequestModel.CreateBookingModel
import java.lang.ref.WeakReference
import java.util.Date

/**
 * Created by Zenity.
 */

class BookingPaymentPresenter : BookingPaymentContractor.Presenter {

    private var disposable: Disposable? = null
    //TRACKING
    private var cinemaId: String? = null
    private var cinemaName: String? = null
    private var movieId: String? = null
    private var movieName: String? = null
    private var date: Date? = null
    private var time: Date? = null
    private var screen: String? = null
    private var normalSeats: Int? = null
    private var vipSeats: Int? = null
    private var doubleSeats: Int? = null
    private var totalSeats: Int? = null
    private var totalAmount: Int? = null
    private var discountAmount: Int? = null
    private var paymentAmount: Int? = null
    private var totalCombos: Int? = null
    private var totalComboAmount: Int? = null
    private var redeemVouchers: Int? = null
    private var redeemPoints: Int? = null
    private var paymentMethod: String? = null
    private var orderId: String? = null

    override fun getBookingPayment(booking: CreateBookingModel) {
        view?.get()?.showLoading()
        val body = RequestBody.create(MediaType.parse("application/json"), Gson().toJson(booking))
        disposable = APIClient.shared.bookingAPI.createBooking(body).applyOn()
                .subscribe({
                    this.view?.get()?.showHtmlBooking(it.string())
                    this.view?.get()?.hideLoading()
                }, { error ->
                    this.view?.get()?.hideLoading()
                    this.view?.get()?.showError(error.message ?: return@subscribe)
                })
    }

    override fun trackingConfirm(method: String) {
        paymentMethod = method
        setOrderId(null)
        this.view?.get()?.getWebView()?.evaluateJavascript("document.documentElement.innerHTML.toString()") { value ->
            if (!value.isNullOrEmpty()) {
                val html = value.replace("\\u003C","<")
                this.cinemaId =  this.view?.get()?.getFilmModel()?.CinemaId
                this.cinemaName = this.view?.get()?.getFilmModel()?.CinemaName
                this.movieId =  this.view?.get()?.getFilmModel()?.FilmId
                this.movieName = this.view?.get()?.getFilmModel()?.Name
                this.date = this.view?.get()?.getShowTimeModel()?.GioChieu?.toDate("yyyy-MM-dd'T'HH:mm:ss")
                this.time = this.view?.get()?.getShowTimeModel()?.GioChieu?.toDate("yyyy-MM-dd'T'HH:mm:ss")
                this.screen = this.view?.get()?.getShowTimeModel()?.PhongChieu
                this.normalSeats =  this.view?.get()?.getCreateBookingModel()?.Seats?.filter { v -> v.SeatType == "NORMAL"}?.size
                this. vipSeats =  this.view?.get()?.getCreateBookingModel()?.Seats?.filter { v -> v.SeatType == "VIP"}?.size
                this. doubleSeats = this.view?.get()?.getCreateBookingModel()?.Seats?.filter { v -> v.SeatType == "DOUBLE"}?.size

                this.totalSeats = 0
                if(this.normalSeats != null){
                    this.totalSeats =  this.totalSeats!! + this.normalSeats!!
                }
                if(this.vipSeats != null){
                    this.totalSeats =  this.totalSeats!! + this.vipSeats!!
                }
                if(this.doubleSeats != null){
                    this.totalSeats =  this.totalSeats!! + this.doubleSeats!!
                }

                var totalAmount = getElementByClassName(html ,"total-money-name")
                totalAmount = totalAmount?.replace(",", "")
                totalAmount = totalAmount?.replace( "đ", "")
                this.totalAmount = totalAmount?.toIntOrNull()
                var discountAmount = getElementByClassName(html , "coupon-discount")
                discountAmount = discountAmount?.replace(",", "")
                discountAmount = discountAmount?.replace( "đ", "")
                this.discountAmount = discountAmount?.toIntOrNull()
                var paymentAmount = getElementByClassName(html ,"money-need-pay")
                paymentAmount = paymentAmount?.replace(",", "")
                paymentAmount = paymentAmount?.replace( "đ", "")
                this.paymentAmount = paymentAmount?.toIntOrNull()
                var redeemVouchers = getElementByClassName(html ,"beta-voucher-value")
                redeemVouchers = redeemVouchers?.replace(",", "")
                redeemVouchers = redeemVouchers?.replace( "đ", "")
                this.redeemVouchers = redeemVouchers?.toIntOrNull()
                var redeemPoints = getElementByClassName(html ,"beta-point-value")
                redeemPoints = redeemPoints?.replace(",", "")
                redeemPoints = redeemPoints?.replace( "đ", "")
                this.redeemPoints = redeemPoints?.toIntOrNull()
                val comboInfo = getComboByHtml(html)
                this.totalCombos = comboInfo.first()
                this.totalComboAmount = comboInfo.last()
                Tracking.share().confirmPayment(
                view?.get()?.getViewContext(),
                this.cinemaId,
                this.cinemaName,
                this.movieId,
                 this.movieName,
                this.date,
                this.time,
                this.screen,
                this.normalSeats,
                this.vipSeats,
                this.doubleSeats,
                this.totalSeats,
                this.totalAmount,
                this.discountAmount,
                this.paymentAmount,
                this.totalCombos,
                this.totalComboAmount ,
                this.redeemVouchers,
                this.redeemVouchers,
                this.paymentMethod,
                this.orderId,
                "Mobile App")
            }
        }
    }

    override fun trackingPaySuccess() {
        Tracking.share().paySuccess(
                view?.get()?.getViewContext(),
                this.cinemaId,
                this.cinemaName,
                this.movieId,
                this.movieName,
                this.date,
                this.time,
                this.screen,
                this.normalSeats,
                this.vipSeats,
                this.doubleSeats,
                this.totalSeats,
                this.totalAmount,
                this.discountAmount,
                this.paymentAmount,
                this.totalCombos,
                this.totalComboAmount ,
                this.redeemVouchers,
                this.redeemVouchers,
                this.paymentMethod,
                this.orderId,
                "Mobile App")
    }

    override fun trackingPayFail(errorCode: String?, errorMsg: String?) {
        Tracking.share().payFail(
                view?.get()?.getViewContext(),
                this.cinemaId,
                this.cinemaName,
                this.movieId,
                this.movieName,
                this.date,
                this.time,
                this.screen,
                this.normalSeats,
                this.vipSeats,
                this.doubleSeats,
                this.totalSeats,
                this.totalAmount,
                this.discountAmount,
                this.paymentAmount,
                this.totalCombos,
                this.totalComboAmount ,
                this.redeemVouchers,
                this.redeemVouchers,
                this.paymentMethod,
                this.orderId,
        "Mobile App",
                errorCode,
                errorMsg)
    }

    private fun getElementByClassName(content: String, name: String): String?{
        var result: String? = null
        val index = content.indexOf(name)
        if(index != -1){
            val text = content.substring(index + name.length)
            val index = text.indexOf( ">")
            if(index != -1){
                val text = text.substring(index + ">".length)
                val index = text.indexOf( "<")
                if(index != -1){
                    result = text.substring(0,index)
                    print(result)
                }
            }
        }
        return result
    }

    private fun getComboByHtml(content: String): Array<Int?> {
        var comboList: JSONArray? = null
        val pattern = "var listCombo = JSON.parse("
        val index = content.indexOf(pattern)
        if(index != -1){
            var text =  content.substring(index+pattern.length)
            val pattern = ");"
            val index = text.indexOf( pattern)
            if(index != -1){
                text = text.substring(0,index)
                text = text.replace("'",  "")
                text = text.replace("\\\"",  "\"")

                try {
                    comboList = JSONArray(text)
                } catch (e: JSONException) {
                    print(e)
                }
            }
        }
        var comboTotal: Int? = null
        var comboAmount: Int? = null
        if(comboList != null){
            val list = Array(comboList.length()) {
                comboList.getJSONObject(it)
            }
            for (cb in list){
                var comboName: String?
                var comboPrice: Int?
                try{
                    comboName = cb.getJSONObject("Combo").getString("Name")
                    comboPrice = cb.getJSONObject("Combo").getInt("PriceAfterVAT")
                    val index = content.indexOf(comboName)
                    if(index != -1){
                        var text = content.substring(index+comboName.length)
                        val pattern = "combo-quantity"
                        val index = text.indexOf(pattern)
                        if(index != -1){
                            text =  text.substring(index+pattern.length)
                            val index = text.indexOf(">")
                            if(index != -1){
                                text =  text.substring(index+1)
                                val index = text.indexOf("<")
                                if(index != -1){
                                    text =  text.substring(0,index)
                                    val quantity = text.toIntOrNull()
                                    if(quantity != null){
                                        comboTotal = (comboTotal ?: 0) + quantity
                                        comboAmount = (comboAmount ?: 0) + (quantity * comboPrice)
                                    }
                                }
                            }
                        }
                    }
                }catch (e: JSONException){
                    print(e)
                }

            }
        }

        return arrayOf(comboTotal,comboAmount)
    }

    override fun setOrderId(id: String?) {
        orderId = id
    }

    private var view: WeakReference<BookingPaymentContractor.View?>? = null
    override fun attachView(view: BookingPaymentContractor.View) {
        this.view = WeakReference(view)
    }

    override fun detachView() {
        disposable?.dispose()
        this.view?.clear()
        this.view = null
    }
}
