//
//  SwitchTableCell.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 4/12/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit

class SwitchTableCell: UITableViewCell {
    @IBOutlet weak var lbTitle: UILabel!
    @IBOutlet weak var switchView: UISwitch!

    var onSwitchChangeValue: ((Bool) -> Void)?
    var onAwakeFromNib: (() -> Void)?

    override func awakeFromNib() {
        super.awakeFromNib()

        switchView.addTarget(self, action: #selector(switchChangedValue(_:)), for: .valueChanged)
        onAwakeFromNib?()
    }

    static func nib() -> SwitchTableCell {
        return Bundle(for: SwitchTableCell.self).loadNibNamed(self.className, owner: self, options: nil)?.first as! SwitchTableCell
    }

    override func updateViewWithItem(_ item: TableItem, indexPath: IndexPath) {
        lbTitle.text = item.title
    }

    @IBAction func switchChangedValue(_ sender: Any) {
        onSwitchChangeValue?(switchView.isOn)
    }
}
