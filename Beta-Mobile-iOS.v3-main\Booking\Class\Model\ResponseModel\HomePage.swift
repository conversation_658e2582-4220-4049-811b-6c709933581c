//
//  HomePage.swift
//  Booking
//
//  Created by <PERSON><PERSON>u on 8/31/19.
//  Copyright © 2019 ddkc. All rights reserved.
//

import Foundation
import ObjectMapper

class HomePage : Mappable {
    var accountId : String?
    var fullName : String?
    var picture : String?
    var classCode : String?
    var quantityOfVoucher : Int?
    var availablePoint : Int?

    required init?(map: Map) {

    }

    func mapping(map: Map) {

        accountId <- map["AccountId"]
        fullName <- map["FullName"]
        picture <- map["Picture"]
        classCode <- map["ClassCode"]
        quantityOfVoucher <- map["QuantityOfVoucher"]
        availablePoint <- map["AvailablePoint"]
    }

}
