//
//  RegisterVoucherModel.swift
//  Booking-dev
//
//  Created by <PERSON><PERSON> on 5/26/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation

class RegisterVoucherModel: BaseRequestModel {
    var pinCode: String
    var voucherCode: String
    var customerId: String
    var customerCard: String
    var cardTypeName: CardType
    
    init(pinCode: String, voucherCode: String, customerId: String, customerCard: String, cardTypeName: CardType) {
        self.pinCode = pinCode
        self.voucherCode = voucherCode
        self.customerId = customerId
        self.customerCard = customerCard
        self.cardTypeName = cardTypeName
    }
    
    override func toJSON() -> [String : Any] {
        return ["PinCode": pinCode,
                "VoucherCode": voucherCode,
                "CustomerId": customerId,
                "CustomerCard": customerCard,
                "CardTypeName": cardTypeName.description]
    }
}
