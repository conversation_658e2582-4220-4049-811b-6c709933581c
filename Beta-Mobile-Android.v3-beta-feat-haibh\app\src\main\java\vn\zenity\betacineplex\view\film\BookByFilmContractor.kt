package vn.zenity.betacineplex.view.film

import android.location.Location
import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.model.Film
import vn.zenity.betacineplex.model.FilmBooking
import vn.zenity.betacineplex.model.FilmModel
import vn.zenity.betacineplex.model.GroupFilmBooking
import java.util.*

/**
 * Created by Zenity.
 */

interface BookByFilmContractor {
    interface View : IBaseView {
        fun showTimeBooking(listTime: List<GroupFilmBooking>)
        fun showShowDates(dates: List<Calendar>)
        fun showFilmModel(film: FilmModel)
        fun showLoadMore()
        fun hideLoadMore()
    }

    interface Presenter : IBasePresenter<View> {
        fun getTimeBooking(film: FilmModel, date: Calendar, location: Location?, isLoadMore: Boolean)
        fun getShowDates(filmId: String)
        fun getFilmModel(filmId: String)


    }
}
