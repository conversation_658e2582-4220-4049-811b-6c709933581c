//
//  TopicModel.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 4/13/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation
import ObjectMapper

class TopicModel: Mappable {
    var FeedbackThreadId: String?
    var Title: String?
    var Description: String?                                                                                


    required init?(map: Map) {

    }

    func mapping(map: Map) {
        FeedbackThreadId     <- map["FeedbackThreadId"]
        Title                <- map["Title"]
        Description          <- map["Description"]
    }
}
