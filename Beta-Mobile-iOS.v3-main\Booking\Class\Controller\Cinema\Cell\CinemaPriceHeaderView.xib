<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14113" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14088"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="SourceSansPro-Bold.ttf">
            <string>SourceSansPro-Bold</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="iN0-l3-epB" customClass="CinemaPriceHeaderView" customModule="Booking_dev" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="375" height="59"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="yC5-om-Vff" customClass="RoundView" customModule="Booking_dev" customModuleProvider="target">
                    <rect key="frame" x="16" y="0.0" width="343" height="59"/>
                    <subviews>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="kX4-8p-Osi">
                            <rect key="frame" x="1" y="0.0" width="341" height="58"/>
                            <subviews>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Thứ 6,7,CN" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="DX5-pZ-vOG">
                                    <rect key="frame" x="20" y="10" width="301" height="38"/>
                                    <fontDescription key="fontDescription" name="SourceSansPro-Bold" family="Source Sans Pro" pointSize="16"/>
                                    <color key="textColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="calibratedRGB"/>
                                    <nil key="highlightedColor"/>
                                </label>
                            </subviews>
                            <color key="backgroundColor" red="0.97254901959999995" green="0.97254901959999995" blue="0.97254901959999995" alpha="1" colorSpace="calibratedRGB"/>
                            <constraints>
                                <constraint firstAttribute="trailing" secondItem="DX5-pZ-vOG" secondAttribute="trailing" constant="20" id="5Ka-Ja-HGT"/>
                                <constraint firstAttribute="bottom" secondItem="DX5-pZ-vOG" secondAttribute="bottom" constant="10" id="E6C-1p-i8I"/>
                                <constraint firstItem="DX5-pZ-vOG" firstAttribute="leading" secondItem="kX4-8p-Osi" secondAttribute="leading" constant="20" id="YXO-IF-fVL"/>
                                <constraint firstItem="DX5-pZ-vOG" firstAttribute="top" secondItem="kX4-8p-Osi" secondAttribute="top" constant="10" id="ef2-fT-t9Q"/>
                            </constraints>
                        </view>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstAttribute="trailing" secondItem="kX4-8p-Osi" secondAttribute="trailing" constant="1" id="L2r-Kr-L0M"/>
                        <constraint firstAttribute="bottom" secondItem="kX4-8p-Osi" secondAttribute="bottom" constant="1" id="V5U-0z-frJ"/>
                        <constraint firstItem="kX4-8p-Osi" firstAttribute="leading" secondItem="yC5-om-Vff" secondAttribute="leading" constant="1" id="mXd-N2-lHd"/>
                        <constraint firstItem="kX4-8p-Osi" firstAttribute="top" secondItem="yC5-om-Vff" secondAttribute="top" id="vY3-7V-hD1"/>
                    </constraints>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                            <color key="value" red="0.59215686270000001" green="0.59215686270000001" blue="0.59215686270000001" alpha="1" colorSpace="calibratedRGB"/>
                        </userDefinedRuntimeAttribute>
                        <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                            <real key="value" value="1"/>
                        </userDefinedRuntimeAttribute>
                    </userDefinedRuntimeAttributes>
                </view>
            </subviews>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="yC5-om-Vff" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" id="53o-6a-pbs"/>
                <constraint firstItem="vUN-kp-3ea" firstAttribute="trailing" secondItem="yC5-om-Vff" secondAttribute="trailing" constant="16" id="NeG-69-DFc"/>
                <constraint firstItem="yC5-om-Vff" firstAttribute="leading" secondItem="vUN-kp-3ea" secondAttribute="leading" constant="16" id="gJS-72-IZJ"/>
                <constraint firstAttribute="bottom" secondItem="yC5-om-Vff" secondAttribute="bottom" id="z8L-vy-Dog"/>
            </constraints>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <viewLayoutGuide key="safeArea" id="vUN-kp-3ea"/>
            <point key="canvasLocation" x="33.5" y="-215.5"/>
        </view>
    </objects>
</document>
