<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14490.70" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14490.49"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="FreeVoucherViewController" customModule="Booking_dev" customModuleProvider="target">
            <connections>
                <outlet property="tableView" destination="yGe-wL-WjX" id="P0W-Fq-aqu"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" style="plain" separatorStyle="default" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="yGe-wL-WjX">
                    <rect key="frame" x="0.0" y="52" width="414" height="810"/>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <connections>
                        <outlet property="dataSource" destination="-1" id="hDj-Mg-u7w"/>
                        <outlet property="delegate" destination="-1" id="FRL-zG-zDR"/>
                    </connections>
                </tableView>
            </subviews>
            <color key="backgroundColor" red="0.95294117647058818" green="0.95294117647058818" blue="0.95294117647058818" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
            <constraints>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="yGe-wL-WjX" secondAttribute="trailing" id="4PC-Uq-qE9"/>
                <constraint firstItem="yGe-wL-WjX" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" id="KlP-4y-9o8"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="bottom" secondItem="yGe-wL-WjX" secondAttribute="bottom" id="Nlh-1E-WqB"/>
                <constraint firstItem="yGe-wL-WjX" firstAttribute="top" secondItem="fnl-2z-Ty3" secondAttribute="top" constant="8" id="TFM-Sp-baa"/>
            </constraints>
            <viewLayoutGuide key="safeArea" id="fnl-2z-Ty3"/>
        </view>
    </objects>
</document>
