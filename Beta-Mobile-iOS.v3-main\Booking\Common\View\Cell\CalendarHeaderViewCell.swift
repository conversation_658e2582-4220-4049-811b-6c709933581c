//
//  CalendarHeaderViewCell.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 4/22/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit
import SwiftDate

class CalendarHeaderViewCell: UICollectionViewCell {
    var date: Date = Date() {
        didSet {
            updateDate(date)
        }
    }
    @IBOutlet weak var lbDay: UILabel!
    @IBOutlet weak var lbContent: UILabel!

    let selectedColor = UIColor(red: 253, green: 40, blue: 2)
    let unselectedColor = UIColor(red: 185, green: 185, blue: 185)

    override var isSelected: Bool {
        didSet {
            setSelected(isSelected)
        }
    }

    override func awakeFromNib() {
        super.awakeFromNib()
        // Initialization code
    }

    func updateDate( _ date: Date) {
        lbDay.text = date.toString(dateFormat: "dd")

        if Calendar.current.isDateInToday(date) {
            lbContent.text = "Today".localized
        } else {
            lbContent.text = date.toString(dateFormat: "<PERSON><PERSON>-E")
        }
    }

    func setSelected(_ selected: Bool) {
        lbDay.textColor = selected ? selectedColor : unselectedColor
        lbContent.textColor = selected ? selectedColor : unselectedColor
    }
}
