package vn.zenity.betacineplex.view.auth

import io.reactivex.disposables.Disposable
import vn.zenity.betacineplex.Manager.Network.APIClient
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.global.Global
import vn.zenity.betacineplex.helper.extension.DeviceHelper
import vn.zenity.betacineplex.helper.extension.applyOn
import vn.zenity.betacineplex.helper.extension.getString
import java.lang.ref.WeakReference

/**
 * Created by Zenity.
 */

class ChangePasswordPresenter : ChangePasswordContractor.Presenter {

    private var disposable: Disposable? = null

    override fun changePassword(oldPass: String, newPass: String, confirmPass: String) {
        if(oldPass.isNotEmpty() && newPass.isNotEmpty()&& confirmPass.isNotEmpty() && newPass == confirmPass) {
            this.view?.get()?.showLoading()
//            this.view?.get()?.changePasswordSuccess("change_password_success")
            val mapData = mapOf("UserName" to (Global.share().user?.Email ?: ""),
            "OldPassWord" to oldPass,
            "PassWord" to newPass,
            "DeviceId" to DeviceHelper.shared.deviceId())
            disposable = APIClient.shared.accountAPI.changePassword(mapData).applyOn()
                    .subscribe({
                        if (it.Data?.Result == true) {
                            view?.get()?.changePasswordSuccess(R.string.change_password_success.getString())
                        } else {
                            view?.get()?.showAlert(it.Message ?: R.string.change_password_error.getString())
                        }
                        view?.get()?.hideLoading()
                    }, {
                        view?.get()?.showAlert(R.string.change_password_error.getString())
                        view?.get()?.hideLoading()
                    })
        }
    }

    override fun updateFacebookPassword(newPass: String, confirmPass: String) {
        if(newPass.isNotEmpty()&& confirmPass.isNotEmpty() && newPass == confirmPass) {
            this.view?.get()?.showLoading()
//            this.view?.get()?.changePasswordSuccess("change_password_success")
            val mapData = mapOf("UserName" to (Global.share().user?.Email ?: ""),
                    "PassWord" to newPass,
                    "DeviceId" to DeviceHelper.shared.deviceId())
            disposable = APIClient.shared.accountAPI.updateFacebookPassword(mapData).applyOn()
                    .subscribe({
                        if (it.Data?.Result == true) {
                            view?.get()?.changePasswordSuccess(R.string.update_password_success.getString())
                        } else {
                            view?.get()?.showAlert(it.Message ?: R.string.update_password_error.getString())
                        }
                        view?.get()?.hideLoading()
                    }, {
                        view?.get()?.showAlert(R.string.update_password_error.getString())
                        view?.get()?.hideLoading()
                    })
        }
    }

    private var view: WeakReference<ChangePasswordContractor.View?>? = null
    override fun attachView(view: ChangePasswordContractor.View) {
        this.view = WeakReference(view)
    }

    override fun detachView() {
        disposable?.dispose()
        this.view?.clear()
        this.view = null
    }
}
