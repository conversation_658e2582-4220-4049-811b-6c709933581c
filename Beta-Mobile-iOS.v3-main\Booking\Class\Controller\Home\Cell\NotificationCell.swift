//
//  NotificationCell.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 9/23/19.
//  Copyright © 2019 ddkc. All rights reserved.
//

import UIKit

class NotificationCell: UITableViewCell {

    @IBOutlet weak var titleLabel: UILabel!
    @IBOutlet weak var dateLabel: UILabel!

    fileprivate var news: NewNotification?
    var indexPath: IndexPath!

    override func awakeFromNib() {
        super.awakeFromNib()
        // Initialization code
    }

    override func updateViewWithItem(_ tbItem: TableItem, indexPath: IndexPath) {
        self.indexPath = indexPath
        guard let item = tbItem.data as? NewNotification else {
            titleLabel.text = tbItem.title

            return
        }
        self.news = item

        titleLabel.text = item.Title

        if let status = item.ReadStatus, !status {
            self.titleLabel.font = UIFont(fontName: .SourceSansPro, style: .Bold, size: 16)
        } else {
            self.titleLabel.font = UIFont(fontName: .SourceSansPro, style: .Regular, size: 16)
        }

        dateLabe<PERSON>.text = item.date
    }
    
}
