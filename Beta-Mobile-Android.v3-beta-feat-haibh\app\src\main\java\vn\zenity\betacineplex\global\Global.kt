package vn.zenity.betacineplex.global

import io.reactivex.annotations.Beta
import vn.zenity.betacineplex.Manager.local.BetaDB
import vn.zenity.betacineplex.model.UserModel
import java.lang.ref.WeakReference

/**
 * Created by vinh on 4/2/18.
 */

class Global {
    companion object {
        var instance: Global? = null
        fun share(): Global {
            if (instance == null) {
                instance = Global()
            }
            if(instance?.user == null) {
                val user = BetaDB.getInstance().userDao().getCurrentUser()
                if(user != null) {
                    instance?.user = user
                }
            }
            return instance!!
        }
    }

    var user: UserModel? = null
        set(value) {
            field = value
            if (field != null) {
                BetaDB.getInstance().userDao().updateCurrentUser(field!!)
            } else {
                BetaDB.getInstance().userDao().removeUser()
            }
            listenerUserChange.forEach {
                it.get()?.invoke(field)
            }
        }

    var isLogin: Boolean = false
        get() = user != null
    var token: String? = null
        get() = user?.Token

    var listenerUserChange = mutableListOf<WeakReference<(UserModel?) -> Unit>>()
}