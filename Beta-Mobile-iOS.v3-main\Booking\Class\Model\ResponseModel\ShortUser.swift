//
//  ShortUser.swift
//  Booking-dev
//
//  Created by <PERSON><PERSON> on 8/13/19.
//  Copyright © 2019 ddkc. All rights reserved.
//

import Foundation
import ObjectMapper

class ShortUser : Mappable {
    var name : String?
    var email : String?
    var avatar : String?

    required init?(map: Map) {

    }

    func mapping(map: Map) {

        avatar <- map["Picture"]
        email <- map["Email"]
        name <- map["FullName"]
    }

}
