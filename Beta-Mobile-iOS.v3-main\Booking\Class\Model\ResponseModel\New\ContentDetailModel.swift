//
//  ContentDetailModel.swift
//  Booking
//
//  Created by <PERSON><PERSON> Vu on 4/5/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation
import ObjectMapper

class ContentDetailModel : Mappable {
     var ID : String?
     var Tieu_de : String?
     var StorylineID : String?
     var ObjectID : String?
     var TableName : String?
     var Thu_tu_hien_thi : Int?
     var CreatedByUserID : String?
     var CreatedOnDate : String?
     var LastModifiedByUserID : String?
     var LastModifiedOnDate : String?
     var StartDate : String?
     var EndDate : String?
     var ParagraphData : ParagraphDataModel?
//     var PlaylistData : PlaylistData?
//     var AlbumData : AlbumData?
//     var CmsNodeData : CmsNodeData?


    required init?(map: Map) {

    }

    func mapping(map: Map) {
        ID                   <- map["ID"]
        Tieu_de              <- map["Tieu_de"]
        StorylineID          <- map["StorylineID"]
        ObjectID             <- map["ObjectID"]
        TableName            <- map["TableName"]
        Thu_tu_hien_thi      <- map["Thu_tu_hien_thi"]
        CreatedByUserID      <- map["CreatedByUserID"]
        CreatedOnDate        <- map["CreatedOnDate"]
        LastModifiedByUserID <- map["LastModifiedByUserID"]
        LastModifiedOnDate   <- map["LastModifiedOnDate"]
        StartDate            <- map["StartDate"]
        EndDate              <- map["EndDate"]
        ParagraphData        <- map["ParagraphData"]
//        PlaylistData         <- map["PlaylistData"]
//        AlbumData            <- map["AlbumData"]
//        CmsNodeData          <- map["CmsNodeData"]
    }
}
