package vn.zenity.betacineplex.helper.extension

import android.widget.Toast
import vn.zenity.betacineplex.app.App

/**
 * Created by tranduc on 1/5/18.
 */
fun toast(message: Any, length: Int = Toast.LENGTH_LONG) {
    when (message) {
        is String -> Toast.makeText(App.shared(), message.mapCode(), length).show()
        is Int -> Toast.makeText(App.shared(), message, length).show()
        else -> throw IllegalArgumentException("Argument message type is invalid. The first argument is only accepted on Int or String")
    }
}