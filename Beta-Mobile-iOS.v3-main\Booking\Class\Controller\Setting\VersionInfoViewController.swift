//
//  VersionInfoViewController.swift
//  Booking-dev
//
//  Created by <PERSON><PERSON> on 4/8/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit

class VersionInfoViewController: BaseViewController {
    @IBOutlet weak var lbVersion: UILabel!
    @IBOutlet weak var lbLatest: UILabel!
    @IBOutlet weak var lbSupportiOS: UILabel!

    override func viewDidLoad() {
        super.viewDidLoad()

        localizableTitle = "AppVersion.Title"
        // Do any additional setup after loading the view.
        lbVersion.text = UIDevice.current.appVersion
        lbLatest.text = "App.UsingLatestVersion".localized

        lbSupportiOS.text = "App.VersionSupport".localized.replacingOccurrences(of: "XXX", with: "9.0")

        if let newVersion = UserDefaults.standard.string(forKey: "App.NewVersion") {
            compareVersion(newVersion)
        }
        checkNewVersion()
    }

    override func didReceiveMemoryWarning() {
        super.didReceiveMemoryWarning()
        // Dispose of any resources that can be recreated.
    }

    func checkNewVersion() {
        guard let info = Bundle.main.infoDictionary,
            let identifier = info["CFBundleIdentifier"] as? String,
            let url = URL(string: "https://itunes.apple.com/lookup?bundleId=\(identifier)") else {
                self.checkVersionFail()
                return
        }

        showLoading()
        let task = URLSession.shared.dataTask(with: url) { (data, response, error) in
            self.dismissLoading()
            do {
                if let _ = error {
                    self.checkVersionFail()
                    return
                }
                guard let data = data else {
                    self.checkVersionFail()
                    return
                }
                let json = try JSONSerialization.jsonObject(with: data, options: [.allowFragments]) as? [String: Any]
                guard let result = (json?["results"] as? [Any])?.first as? [String: Any], let version = result["version"] as? String else {
                    self.checkVersionFail()
                    return
                }
                DispatchQueue.main.async {
                    self.compareVersion(version)
                }
            } catch {
                self.checkVersionFail()
            }
        }
        task.resume()
    }


    func compareVersion(_ newVersion: String) {
        let curVersion = UIDevice.current.appVersion
        if newVersion.compare(curVersion, options: String.CompareOptions.numeric, range: curVersion.startIndex..<curVersion.endIndex, locale: nil) == .orderedDescending {
            lbLatest.text = "App.NewVersionAvailable".localized

            UserDefaults.standard.set(newVersion, forKey: "App.NewVersion")
        } else {
            lbLatest.text = "App.UsingLatestVersion".localized
        }
    }

    func checkVersionFail() {
        DispatchQueue.main.async {
            self.showAlert(message: "Alert.CheckNewVersionFailed".localized)
        }
    }
}
