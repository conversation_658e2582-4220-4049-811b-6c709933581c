<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="14113" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14088"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="Oswald-Regular.ttf">
            <string>Oswald-Regular</string>
        </array>
        <array key="SourceSansPro-Italic.ttf">
            <string>SourceSansPro-Italic</string>
        </array>
        <array key="SourceSansPro-Regular.ttf">
            <string>SourceSansPro-Regular</string>
        </array>
    </customFonts>
    <scenes>
        <!--Version Info View Controller-->
        <scene sceneID="CcA-bQ-QdS">
            <objects>
                <viewController storyboardIdentifier="VersionInfoViewController" id="CKA-di-EVW" customClass="VersionInfoViewController" customModule="Booking_dev" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="CAY-7E-XmY">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Phiên bản hiện tại" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="aDC-ku-28E" customClass="LocalizableLabel" customModule="Booking_dev" customModuleProvider="target">
                                <rect key="frame" x="20" y="46" width="104.5" height="24"/>
                                <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="16"/>
                                <color key="textColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <nil key="highlightedColor"/>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="App.CurrentVersion"/>
                                </userDefinedRuntimeAttributes>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" verticalHuggingPriority="249" text="1.0" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="DB5-Rj-jel">
                                <rect key="frame" x="131.5" y="44.5" width="84" height="25.5"/>
                                <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="20"/>
                                <color key="textColor" red="0.011764705882352941" green="0.34901960784313724" blue="0.61568627450980395" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Đây là bản mới nhất" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumScaleFactor="0.5" translatesAutoresizingMaskIntoConstraints="NO" id="9Nf-O8-ArQ">
                                <rect key="frame" x="223.5" y="49.5" width="131.5" height="20.5"/>
                                <fontDescription key="fontDescription" name="SourceSansPro-Italic" family="Source Sans Pro" pointSize="16"/>
                                <color key="textColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <view alpha="0.10000000000000001" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="lH1-Ui-zZM">
                                <rect key="frame" x="20" y="88" width="335" height="1"/>
                                <color key="backgroundColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="1" id="EW4-Id-mnF"/>
                                </constraints>
                            </view>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Hỗ trợ IOS 9.0 trở lên" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="mfo-yb-Kue">
                                <rect key="frame" x="20" y="107" width="335" height="20.5"/>
                                <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                <color key="textColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <color key="backgroundColor" red="0.95294117649999999" green="0.95294117649999999" blue="0.95294117649999999" alpha="1" colorSpace="calibratedRGB"/>
                        <constraints>
                            <constraint firstItem="9Nf-O8-ArQ" firstAttribute="leading" secondItem="DB5-Rj-jel" secondAttribute="trailing" constant="8" id="0fW-y5-3mX"/>
                            <constraint firstItem="azu-me-re5" firstAttribute="trailing" secondItem="mfo-yb-Kue" secondAttribute="trailing" constant="20" id="3J4-hn-2YD"/>
                            <constraint firstItem="lH1-Ui-zZM" firstAttribute="top" secondItem="aDC-ku-28E" secondAttribute="bottom" constant="18" id="4SK-XG-cGL"/>
                            <constraint firstItem="mfo-yb-Kue" firstAttribute="leading" secondItem="azu-me-re5" secondAttribute="leading" constant="20" id="4Y5-ar-Byy"/>
                            <constraint firstItem="DB5-Rj-jel" firstAttribute="bottom" secondItem="aDC-ku-28E" secondAttribute="bottom" id="NWd-wT-ACT"/>
                            <constraint firstItem="9Nf-O8-ArQ" firstAttribute="bottom" secondItem="DB5-Rj-jel" secondAttribute="bottom" id="Okz-7I-bxT"/>
                            <constraint firstItem="aDC-ku-28E" firstAttribute="leading" secondItem="azu-me-re5" secondAttribute="leading" constant="20" id="SMr-3t-JQe"/>
                            <constraint firstItem="mfo-yb-Kue" firstAttribute="top" secondItem="lH1-Ui-zZM" secondAttribute="bottom" constant="18" id="iSe-nH-pat"/>
                            <constraint firstItem="aDC-ku-28E" firstAttribute="top" secondItem="azu-me-re5" secondAttribute="top" constant="26" id="jvi-Fa-Esd"/>
                            <constraint firstAttribute="trailing" secondItem="9Nf-O8-ArQ" secondAttribute="trailing" constant="20" id="oKz-fK-Yla"/>
                            <constraint firstItem="lH1-Ui-zZM" firstAttribute="leading" secondItem="azu-me-re5" secondAttribute="leading" constant="20" id="sUU-Ju-nHR"/>
                            <constraint firstItem="DB5-Rj-jel" firstAttribute="leading" secondItem="aDC-ku-28E" secondAttribute="trailing" constant="7" id="vv1-me-3eW"/>
                            <constraint firstItem="azu-me-re5" firstAttribute="trailing" secondItem="lH1-Ui-zZM" secondAttribute="trailing" constant="20" id="w7t-4t-ey0"/>
                        </constraints>
                        <viewLayoutGuide key="safeArea" id="azu-me-re5"/>
                    </view>
                    <connections>
                        <outlet property="lbLatest" destination="9Nf-O8-ArQ" id="PhU-TE-kwg"/>
                        <outlet property="lbSupportiOS" destination="mfo-yb-Kue" id="KZA-Eb-oab"/>
                        <outlet property="lbVersion" destination="DB5-Rj-jel" id="O46-Re-3pa"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="SFZ-Zf-gPu" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-1241" y="13"/>
        </scene>
        <!--Setting View Controller-->
        <scene sceneID="jSn-Lw-DJf">
            <objects>
                <viewController storyboardIdentifier="SettingViewController" id="yHN-Tp-g71" customClass="SettingViewController" customModule="Booking_dev" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="XwG-NE-ZkJ">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="none" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="8SR-pI-ZWQ">
                                <rect key="frame" x="0.0" y="20" width="375" height="647"/>
                                <color key="backgroundColor" red="0.95294117649999999" green="0.95294117649999999" blue="0.95294117649999999" alpha="1" colorSpace="calibratedRGB"/>
                                <connections>
                                    <outlet property="delegate" destination="yHN-Tp-g71" id="NQZ-Vs-RFO"/>
                                </connections>
                            </tableView>
                        </subviews>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="Rif-u7-XaD" firstAttribute="bottom" secondItem="8SR-pI-ZWQ" secondAttribute="bottom" id="1Mo-AP-cXp"/>
                            <constraint firstItem="Rif-u7-XaD" firstAttribute="trailing" secondItem="8SR-pI-ZWQ" secondAttribute="trailing" id="OUF-RX-KRf"/>
                            <constraint firstItem="8SR-pI-ZWQ" firstAttribute="top" secondItem="Rif-u7-XaD" secondAttribute="top" id="Zbl-ko-QEM"/>
                            <constraint firstItem="8SR-pI-ZWQ" firstAttribute="leading" secondItem="Rif-u7-XaD" secondAttribute="leading" id="uDP-ig-7qD"/>
                        </constraints>
                        <viewLayoutGuide key="safeArea" id="Rif-u7-XaD"/>
                    </view>
                    <connections>
                        <outlet property="tableView" destination="8SR-pI-ZWQ" id="Zxy-Ih-SZs"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="pnH-8P-8VG" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-319.19999999999999" y="54.422788605697157"/>
        </scene>
        <!--Profile View Controller-->
        <scene sceneID="YNL-I0-Buj">
            <objects>
                <viewController storyboardIdentifier="ProfileViewController" id="fJW-xg-6Bq" customClass="ProfileViewController" customModule="Booking_dev" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="eAQ-ZJ-S8a">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <viewLayoutGuide key="safeArea" id="o00-Yt-nOL"/>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="d4N-nK-Wqk" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="406" y="56"/>
        </scene>
        <!--Other View Controller-->
        <scene sceneID="9FJ-AD-yM2">
            <objects>
                <viewController storyboardIdentifier="OtherViewController" id="Vdt-vx-P0J" customClass="OtherViewController" customModule="Booking_dev" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="O8o-Sv-NEa">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <textView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" editable="NO" textAlignment="justified" translatesAutoresizingMaskIntoConstraints="NO" id="1Gb-Td-u7Y">
                                <rect key="frame" x="0.0" y="20" width="375" height="647"/>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                <textInputTraits key="textInputTraits" autocapitalizationType="sentences"/>
                                <dataDetectorType key="dataDetectorTypes" phoneNumber="YES" link="YES"/>
                            </textView>
                        </subviews>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="1Gb-Td-u7Y" firstAttribute="leading" secondItem="2az-9s-4rS" secondAttribute="leading" id="DGy-Pe-36O"/>
                            <constraint firstItem="2az-9s-4rS" firstAttribute="bottom" secondItem="1Gb-Td-u7Y" secondAttribute="bottom" id="LyO-2M-vhM"/>
                            <constraint firstAttribute="trailing" secondItem="1Gb-Td-u7Y" secondAttribute="trailing" id="nFf-5Y-OPJ"/>
                            <constraint firstItem="1Gb-Td-u7Y" firstAttribute="top" secondItem="2az-9s-4rS" secondAttribute="top" id="pIT-1Y-MhX"/>
                        </constraints>
                        <viewLayoutGuide key="safeArea" id="2az-9s-4rS"/>
                    </view>
                    <connections>
                        <outlet property="textView" destination="1Gb-Td-u7Y" id="gat-XX-FUb"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="UFd-CI-Fx2" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1066" y="55"/>
        </scene>
        <!--FAQViewController-->
        <scene sceneID="U8Y-8l-f0Q">
            <objects>
                <viewController storyboardIdentifier="FAQViewController" id="Et6-uj-Miz" userLabel="FAQViewController" customClass="FAQViewController" customModule="Booking_dev" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="h2e-72-EKS">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="none" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="lwB-bN-zyF">
                                <rect key="frame" x="0.0" y="20" width="375" height="647"/>
                                <color key="backgroundColor" red="0.95294117649999999" green="0.95294117649999999" blue="0.95294117649999999" alpha="1" colorSpace="calibratedRGB"/>
                                <connections>
                                    <outlet property="dataSource" destination="Et6-uj-Miz" id="8vm-Vz-ZYg"/>
                                    <outlet property="delegate" destination="Et6-uj-Miz" id="v7u-vK-VZV"/>
                                </connections>
                            </tableView>
                        </subviews>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="lwB-bN-zyF" firstAttribute="top" secondItem="HoE-0x-vAt" secondAttribute="top" id="11p-iC-Ecq"/>
                            <constraint firstItem="HoE-0x-vAt" firstAttribute="trailing" secondItem="lwB-bN-zyF" secondAttribute="trailing" id="LLe-Af-rbq"/>
                            <constraint firstItem="lwB-bN-zyF" firstAttribute="leading" secondItem="HoE-0x-vAt" secondAttribute="leading" id="hHj-yl-oy1"/>
                            <constraint firstItem="HoE-0x-vAt" firstAttribute="bottom" secondItem="lwB-bN-zyF" secondAttribute="bottom" id="m5A-7o-kHo"/>
                        </constraints>
                        <viewLayoutGuide key="safeArea" id="HoE-0x-vAt"/>
                    </view>
                    <connections>
                        <outlet property="tableView" destination="lwB-bN-zyF" id="OZ6-TY-cFN"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="rND-tK-ASB" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1713" y="45"/>
        </scene>
        <!--Detail View Controller-->
        <scene sceneID="0nc-2H-yYK">
            <objects>
                <viewController storyboardIdentifier="FAQDetailViewController" id="kvC-bq-cKW" customClass="FAQDetailViewController" customModule="Booking_dev" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="Rok-Tg-Pcf">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="default" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="9I5-k8-oaM">
                                <rect key="frame" x="0.0" y="20" width="375" height="647"/>
                                <color key="backgroundColor" red="0.95294117649999999" green="0.95294117649999999" blue="0.95294117649999999" alpha="1" colorSpace="calibratedRGB"/>
                                <inset key="separatorInset" minX="1000" minY="0.0" maxX="0.0" maxY="0.0"/>
                                <connections>
                                    <outlet property="dataSource" destination="kvC-bq-cKW" id="4uo-LF-TJe"/>
                                    <outlet property="delegate" destination="kvC-bq-cKW" id="2Jn-Nz-iLE"/>
                                </connections>
                            </tableView>
                        </subviews>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="9I5-k8-oaM" firstAttribute="top" secondItem="QyF-0b-hcU" secondAttribute="top" id="9J1-Ez-ya5"/>
                            <constraint firstItem="QyF-0b-hcU" firstAttribute="trailing" secondItem="9I5-k8-oaM" secondAttribute="trailing" id="Bh0-3Z-AHf"/>
                            <constraint firstItem="9I5-k8-oaM" firstAttribute="leading" secondItem="QyF-0b-hcU" secondAttribute="leading" id="QN1-hD-4RW"/>
                            <constraint firstItem="QyF-0b-hcU" firstAttribute="bottom" secondItem="9I5-k8-oaM" secondAttribute="bottom" id="kqd-jY-w5r"/>
                        </constraints>
                        <viewLayoutGuide key="safeArea" id="QyF-0b-hcU"/>
                    </view>
                    <connections>
                        <outlet property="tableView" destination="9I5-k8-oaM" id="jX4-BJ-5gk"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="dDq-va-U19" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="2354" y="45"/>
        </scene>
    </scenes>
</document>
